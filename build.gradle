import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
	id 'org.springframework.boot' version '3.3.0'
	id 'io.spring.dependency-management' version '1.1.5'
	id 'java'
	id 'groovy'
	id 'idea'
	id 'jacoco'
	id 'org.jetbrains.kotlin.jvm' version '2.0.20'
	id 'org.jetbrains.kotlin.plugin.spring' version '2.0.20'
	id 'org.jetbrains.kotlin.plugin.noarg' version '2.0.20'
	id 'maven-publish'
	id 'org.sonarqube' version '2.8'
}

noArg {
	annotation("jakarta.persistence.Entity")
	annotation("jakarta.persistence.MappedSuperclass")
	annotation("jakarta.persistence.Embeddable")
}

dependencyManagement {
	imports {
		mavenBom 'org.springframework.cloud:spring-cloud-dependencies:2023.0.1'
	}
}

group = 'centuroglobal'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '22'

ext {
	nexusRepoUsername = System.getenv("NEXUS_REPO_USERNAME")
	nexusRepoPassword = System.getenv("NEXUS_REPO_PASSWORD")
	awsSdkVersion = '2.20.67'
	testcontainersVersion = System.getenv("TESTCONTAINERS_VERSION")?:'1.15.0-rc2'
}

//overriding snakeyaml version from spring boot to fix vulnerability
ext['snakeyaml.version'] = '2.0'

configurations {
	compile.exclude module: 'spring-boot-starter-tomcat'
	developmentOnly
	runtimeClasspath {
		extendsFrom developmentOnly
	}
}

repositories {
	mavenCentral()
	mavenLocal()
	maven {
		url 'https://centuro-global-162682813712.d.codeartifact.eu-west-2.amazonaws.com/maven/cg-shared-lib/'
		credentials {
			username "aws"
			password System.getenv("CODEARTIFACT_AUTH_TOKEN")

		}
	}
	flatDir {
		dirs 'libs'
	}
}

dependencies {
	implementation "com.centuroglobal:cg-shared-lib:1.8.12-SNAPSHOT"
	implementation 'org.springframework.cloud:spring-cloud-starter-config'
	implementation 'org.springframework.cloud:spring-cloud-config-server'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-undertow'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.codehaus.groovy:groovy-all:3.0.10'
	implementation 'org.jetbrains.kotlin:kotlin-reflect'
	implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
	implementation 'io.github.microutils:kotlin-logging:2.1.21'
	implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.13.2'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.2'
	implementation 'org.flywaydb:flyway-core'
	implementation 'io.micrometer:micrometer-registry-prometheus:1.13.0'
	implementation 'io.jsonwebtoken:jjwt:0.9.1'
	implementation 'com.opencsv:opencsv:5.9'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.0.2'
	implementation name : 'spring-async-executor-0.0.1'
	implementation 'com.stripe:stripe-java:20.13.0'
	implementation 'org.passay:passay:1.6.0'
	implementation 'org.apache.tika:tika-core:2.9.2'
	implementation 'commons-io:commons-io:2.16.1'
	implementation "software.amazon.awssdk:sdk-core:$awsSdkVersion"
	implementation "software.amazon.awssdk:secretsmanager:$awsSdkVersion"
	implementation "software.amazon.awssdk:s3:$awsSdkVersion"
	implementation 'com.google.code.gson:gson:2.9.0'
	implementation 'com.github.ben-manes.caffeine:caffeine'
	implementation 'com.googlecode.libphonenumber:libphonenumber:8.12.45'
	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
	implementation 'org.apache.poi:poi-ooxml:5.2.5'
	implementation ('org.xhtmlrenderer:flying-saucer-pdf:9.8.0') {
		exclude group: 'org.bouncycastle'
	}
	implementation 'org.springframework:spring-test'
	implementation 'org.json:json:20240303'
	implementation 'io.github.openfeign:feign-okhttp:10.12'
    
//	developmentOnly 'org.springframework.boot:spring-boot-devtools'

	runtimeOnly 'org.mariadb.jdbc:mariadb-java-client:2.7.5'

	implementation 'org.flywaydb:flyway-mysql'
	implementation 'javax.xml.bind:jaxb-api:2.3.1'


	testImplementation ('org.springframework.boot:spring-boot-starter-test') {
		exclude group: 'org.mockito', module: 'mockito-core'
	}
	testImplementation 'cglib:cglib-nodep:3.2.12'

	// Powermock + Mockito (For mocking java static methods)
	testImplementation "org.powermock:powermock-core:2.0.4"
	testImplementation "org.powermock:powermock-api-mockito2:2.0.4"
	testImplementation "org.powermock:powermock-module-junit4:2.0.4"
	testImplementation 'com.google.code.gson:gson:2.9.0'

	// Spring boot 3.x is Jakarta EE
	implementation 'jakarta.persistence:jakarta.persistence-api'
	implementation 'jakarta.servlet:jakarta.servlet-api'

	//chatGPT kotlin client
	implementation "com.aallam.openai:openai-client:3.7.1"
	implementation 'io.ktor:ktor-client-okhttp:2.2.4'

	testImplementation 'com.ninja-squad:springmockk:4.0.2'
	testImplementation 'org.springframework.security:spring-security-test'

	//user agent parser
	implementation 'com.github.ua-parser:uap-java:1.6.1'

	//kotlinx-coroutines-core for gpt assistant api
	runtimeOnly 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0'
	runtimeOnly 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor:1.8.0'
    testImplementation "org.jetbrains.kotlin:kotlin-test:2.0.20"
}

test{
	useJUnitPlatform()
}

sourceSets {
	main.kotlin.srcDirs += 'src/main/kotlin'
	main.java.srcDirs += 'src/main/java'
}

sonarqube {
	properties {
		property "sonar.projectKey", "cg-server-qa"
		property "sonar.projectName", "cg-server-qa"
		property "sonar.organization", "centuro-global"
		property "sonar.host.url", "http://**********"
		// property "sonar.host.url", "https://sonarcloud.io"
		property "sonar.login", System.getenv("SONAR_TOKEN")
		property "sonar.jacoco.reportPath", "$buildDir/jacoco/test.exec"
		property "sonar.coverage.jacoco.xmlReportPaths", "$buildDir/customJacocoReportDir/test/jacocoTestReport.xml"
		property 'sonar.coverage.exclusions',
				"""
                    **/data/*.kt,
                    **/data/**/*.kt,
                    **/config/*.kt,
                    **/annotation/*.kt,
                    **/exception/*.kt,
                    **/controller/*.kt,
                    **/facade/stripe/*.kt,
                    **/service/stripe/StripeEventLogService.kt,
                    **/scheduler/*.kt,
                    **/interceptor/*ExceptionControllerAdvice.kt,
                    **/util/AwsSecretsHelper.kt,
                    **/Application.kt
                """
		property 'sonar.cpd.exclusions',
				"""
                    **/data/pojo/*.kt,
                    **/data/pojo/**/*.kt,
                    **/data/payload/*.kt,
                    **/data/payload/**/*.kt,
                    **/controller/*.kt
                """
	}
}