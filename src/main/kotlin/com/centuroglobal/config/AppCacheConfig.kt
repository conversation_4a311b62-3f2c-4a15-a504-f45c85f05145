package com.centuroglobal.config

import com.centuroglobal.data.properties.AppCacheProperties
import com.github.benmanes.caffeine.cache.Caffeine
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.cache.support.SimpleCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.TimeUnit

@Configuration
@EnableCaching(proxyTargetClass = true)
class AppCacheConfig(private val appCacheProp: AppCacheProperties) {

    @Bean
    fun cacheManager(): CacheManager {
        val caches: MutableList<CaffeineCache> = mutableListOf()

        appCacheProp.settings?.forEach { k, v ->
            caches.add(
                CaffeineCache(
                    k,
                    Caffeine.newBuilder()
                        .expireAfterWrite(v, TimeUnit.MILLISECONDS)
                        .build()
                )
            )
        }

        val cacheManager = SimpleCacheManager()
        cacheManager.setCaches(caches)
        return cacheManager
    }
}