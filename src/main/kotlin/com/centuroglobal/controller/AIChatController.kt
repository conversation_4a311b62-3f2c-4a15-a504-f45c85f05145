package com.centuroglobal.controller

import com.centuroglobal.annotation.*
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.AIChatResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.AIChatService
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.AIThreadResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "AI Chat", description = "Centuro Global AI chat API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/ai-chat")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class AIChatController(val aiChatService: AIChatService) {

    @PostMapping("/message")
    @Operation(
        summary = "Post a chat Message",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "ASK_AI")
    fun postMessage(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestBody chatRequest: AIMessageRequest
    ): Response<AIChatResponse> {
        val messageResponse = aiChatService.createMessage(chatRequest, authenticatedUser)
        return Response(true, messageResponse)
    }

    @GetMapping("/threads/{threadId}/message")
    @Operation(
        summary = "Retrieve a chat Message",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getMessage(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable threadId: String
    ): Response<AIThreadResponse> {
        return Response(true, aiChatService.getMessages(threadId, authenticatedUser.userId))
    }

    @GetMapping("/threads")
    @Operation(
        summary = "List threads",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getThreads(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam("search") search: String?
    ): Response<List<AIThreadResponse>> {
        return Response(true, aiChatService.getThreads(search, authenticatedUser.userId))
    }

}