package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.service.CaseService
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.AICaseReporterRequest
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.JsonNode
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "AI Reporter", description = "Centuro Global AI Reporter API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/reporter")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class AIReporterController(
    private val pythonApiClient: PythonApiClient,
    private val caseService: CaseService

) {

    @PostMapping
    @Operation(
        summary = "AI Case Reporter",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun caseReporter(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody chatRequest: AICaseReporterRequest
    ): Response<JsonNode> {

        val caseIds = caseService.listCaseIds(authenticatedUser)

        chatRequest.caseIds = caseIds
        chatRequest.role = authenticatedUser.role

        val response = pythonApiClient.caseReporter(chatRequest)
        return Response(true, response)
    }
}
