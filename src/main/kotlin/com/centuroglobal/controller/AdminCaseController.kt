package com.centuroglobal.controller


import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrPartnerOrManageCaseFeeCorporate
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrPartner
import com.centuroglobal.service.CaseService
import com.centuroglobal.service.DocRepoService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.case.CaseFeesInvoiceRequest
import com.centuroglobal.shared.data.payload.case.CaseFeesRequest
import com.centuroglobal.shared.data.pojo.CaseReferenceData
import com.centuroglobal.shared.data.pojo.case.CaseDocumentActivityDetails
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Admin Case", description = "Centuro Global Admin Case API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/case")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CASES', 'CASES')")
@Validated
class AdminCaseController(
    val caseService: CaseService,
    val caseRepository: CaseRepository,
    val docRepoService: DocRepoService
) {

    @DeleteMapping("/{caseId}")
    @Operation(
        summary = "Delete Case",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @PathVariable("caseId") caseId: String,
        @PathVariable(value = "partnerId") partnerId: Long? = null,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<String> {
        val result = caseService.deleteCase(caseId.toLong(), user)
        return Response(true, result)
    }

    @PutMapping("/history/{caseHistoryId}")
    @Operation(
        summary = "Update Case History",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateCaseHistory(
        @PathVariable caseHistoryId: Long,
        @RequestBody request: Map<String, Any>,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        caseService.updateCaseHistory(caseHistoryId, request, authenticatedUser)
        return Response(true)
    }

    @GetMapping("/{caseId}/document/logs")
    @Operation(
        summary = "List Case document activity logs.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listLogs(
        @PathVariable caseId: Long,

        @Parameter(
            name = "logType",
            required = true,
            description = "Activity log type.",
            schema = Schema(allowableValues = ["ACTIVITY","STATUS   "])
        )
        @Pattern(regexp = "^(|ACTIVITY|STATUS)$")
        @RequestParam("logType")
        @Parameter(name = "logType", required = false, description = "Log Type")
        logType: String,

        @RequestParam("docType" , required = false)
        @Parameter(name = "docType", required = false, description = "Document Type")
        docType: String?,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CaseDocumentActivityDetails>> {
        val logs = caseService.listDocumentActivityLogs(caseId, logType, docType, authenticatedUser)
        return Response(true, logs)
    }

    @PutMapping("/{caseId}/request-cg")
    @Operation(
        summary = "Update Partner cg",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updatePartnerCg(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("caseId") caseId: Long,
        @RequestParam @Valid cgRequested: Boolean
    ): Response<Boolean> {
        return Response(true, caseService.updatePartnerCg(caseId, cgRequested, authenticatedUser))
    }

    @PutMapping("/{caseId}/invoice")
    @Operation(
        summary = "Update Case Invoice",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrPartnerOrManageCaseFeeCorporate
    fun caseInvoiceUpdate(
        @PathVariable caseId: Long,
        @Valid @RequestBody request: CaseFeesInvoiceRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        caseService.caseFeesInvoiceUpdate(caseId, request, authenticatedUser)
        return Response(true)
    }

    @PutMapping("/{caseId}/fees")
    @Operation(
        summary = "Save & Update Case Fees",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrPartnerOrManageCaseFeeCorporate
    fun caseFees(
        @PathVariable caseId: Long,
        @Valid @RequestBody request: CaseFeesRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        caseService.createCaseFees(caseId, request, authenticatedUser)
        return Response(true)
    }

    @PutMapping("/{caseId}/link")
    @Operation(
            summary = "Link Cases",
            responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
            security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrPartnerOrManageCaseFeeCorporate
    fun linkCase(
            @PathVariable caseId: Long,
            @RequestBody linkCaseIds: List<Long>,
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Boolean> {
        val result = caseService.linkCase(caseId, linkCaseIds, authenticatedUser)
        return Response(true, result)
    }

    @PutMapping("/{caseId}/unlink")
    @Operation(
            summary = "Un Link Cases",
            responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
            security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrPartnerOrManageCaseFeeCorporate
    fun unlinkCase(
            @PathVariable caseId: Long,
            @RequestBody linkCaseIds: List<Long>,
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Boolean> {
        val result = caseService.unlinkCase(caseId, linkCaseIds)
        return Response(true, result)
    }

    @GetMapping("/{userId}/owner")
    @Operation(
            summary = "Returns list of cases of caseOwner",
            responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
            security = [SecurityRequirement(name = "jwt")]
    )
    fun caseIdsByUser(
            @PathVariable("userId") userId: String,
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CaseReferenceData>> {
        val result = caseService.getCaseList(userId.toLong())
        return Response(true, result)
    }


    @GetMapping("/{caseId}/corporate-users")
    @Operation(
            summary = "Returns list of corporateUsers of CaseOwner's corporate",
            responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
            security = [SecurityRequirement(name = "jwt")]
    )
    fun listOfUsers(
            @PathVariable("caseId") caseId: String,
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<Any>> {
        val result = caseService.retrieveCorporateUsersList(caseId.toLong(), authenticatedUser)
        return Response(true, result)
    }

    @PostMapping("/{caseId}/cancel")
    @IsSuperAdminOrAdminOrPartner
    @Operation(
        summary = "Cancel a Case",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun cancelCase(
        @PathVariable("caseId") caseId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Boolean> {
        caseService.cancelCase(caseId, authenticatedUser)
        return Response(true)
    }


}