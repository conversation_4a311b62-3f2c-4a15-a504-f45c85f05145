package com.centuroglobal.controller


import com.centuroglobal.data.payload.case.CaseFormContent
import com.centuroglobal.data.payload.case.CaseFormRequest
import com.centuroglobal.service.CaseFormService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.CaseFormDetails
import com.centuroglobal.shared.data.pojo.case.CaseFormSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.JsonNode
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Admin Case Forms", description = "Centuro Global Admin Case Forms API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/case-form")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CASE_FORM', 'CASE_FORM')")
class AdminCaseFormController(open val caseFormService: CaseFormService) {

    @PostMapping
    @Operation(
        summary = "Create case form",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
     fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody request: CaseFormRequest
    ): Response<Long> {
        return Response(true, caseFormService.create(request, authenticatedUser))
    }

    @PutMapping("{id}")
    @Operation(
        summary = "Update case form",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long,
        @RequestBody request: CaseFormRequest
    ): Response<Boolean> {
        return Response(true, caseFormService.update(id, request, authenticatedUser))
    }

    @PostMapping("{id}/copy")
    @Operation(
        summary = "Copy a case form",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun copy(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long
    ): Response<Long> {
        return Response(true, caseFormService.copy(id, authenticatedUser))
    }

    @DeleteMapping("{id}")
    @Operation(
        summary = "Delete a case form",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun delete(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long
    ): Response<Boolean> {
        return Response(true, caseFormService.delete(id, authenticatedUser))
    }

    @PutMapping("{id}/status/{status}")
    @Operation(
        summary = "Update case form Status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun updateStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long,
        @PathVariable status: CaseFormStatus
    ): Response<Boolean> {
        return Response(true, caseFormService.updateStatus(id, status, authenticatedUser))
    }

    @PutMapping("{id}/content")
    @Operation(
        summary = "Update case form Content",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun updateContent(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long,
        @RequestBody request: CaseFormContent
    ): Response<Boolean> {
        return Response(true, caseFormService.updateContent(id, request, authenticatedUser))
    }
}