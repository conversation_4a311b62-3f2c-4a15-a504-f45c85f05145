package com.centuroglobal.controller

import com.centuroglobal.facade.AdminCircleFacade
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Pattern
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import org.springframework.web.multipart.MultipartFile

private val DEFAULT_SORT_ORDER_CLIENT = Sort.by(
    Sort.Order(Sort.Direction.DESC, "createdDate"),
    Sort.Order(Sort.Direction.ASC, "displayName", Sort.NullHandling.NULLS_LAST)
)

private val DEFAULT_SORT_ORDER = Sort.by(
    Sort.Order(Sort.Direction.DESC, "createdDate"),
    Sort.Order(Sort.Direction.ASC, "name", Sort.NullHandling.NULLS_LAST)
)

@RestController
@Tag(name = "Admin Circle API", description = "Centuro Global Admin Circle API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/circle")
@PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'CIRCLES')")
class AdminCircleController(
    private val adminCircleFacade: AdminCircleFacade
) {
    @GetMapping("/listing")
    @Operation(
        summary = "List Circles",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listing(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(
            name = "status",
            required = false,
            description = "Circle status to search.",
            schema = Schema(allowableValues = ["DRAFT","PUBLISH"])
        )
        @Pattern(regexp = "^(|DRAFT|PUBLISH)$")
        status: String,

        @RequestParam(name = "circleType", required = false, defaultValue = "")
        @Parameter(
            name = "circleType",
            required = false,
            description = "Circle type to search.",
            schema = Schema(allowableValues = ["PUBLIC","PRIVATE"])
        )
        @Pattern(regexp = "^(|PUBLIC|PRIVATE)$")
        circleType: String,

        @RequestParam(name = "from", required = false)
        @Parameter(
            name = "from",
            required = false,
            description = "Created date (From) in epoch milliseconds."
        )
        from: Long?,

        @RequestParam(name = "to", required = false)
        @Parameter(
            name = "to",
            required = false,
            description = "Created date (Until) in epoch milliseconds."
        )
        to: Long?,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Circle's name or about to search.")
        search: String,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,

        @RequestParam(name = "countryCode", required = false, defaultValue = "")
        @Parameter(name = "countryCodes", required = false, description = "Country of circle")
        countryCode: String,

        @RequestParam(name = "expertiseId", required = false, defaultValue = "")
        @Parameter(name = "expertiseIds", required = false, description = "Expertise id for circle.")
        expertiseId: String
    ): DeferredResult<Response<CircleListing>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.listCircle(
                CircleSearchFilter.Builder.build(status, circleType, from, to, search, countryCode, expertiseId),
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER), authenticatedUser
            )
        )
    }

    @GetMapping("/circle-details/{circleId}")
    @Operation(
        summary = "Create Details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleDetails(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable circleId: Long
    ): DeferredResult<Response<Circle>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.circleDetails(circleId, authenticatedUser)
        )
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    @Operation(
        summary = "Create Circle",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreateUpdateCircleRequest
    ): DeferredResult<Response<Circle>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.createCircle(request, authenticatedUser)
        )
    }

    @PutMapping("/{circleId}")
    @Operation(
        summary = "Update Circle",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @PathVariable circleId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreateUpdateCircleRequest
    ): DeferredResult<Response<Circle>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.updateCircle(
                circleId,
                request,
                authenticatedUser
            )
        )
    }

    @DeleteMapping("/{circleId}")
    @Operation(
        summary = "Delete Circle",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @PathVariable circleId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<Any>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.deleteCircle(
                circleId,
                authenticatedUser
            )
        )
    }

    @PostMapping("/circle-status-update")
    @Operation(
        summary = "Change Circle Status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun changeStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody changeCircleRequest: ChangeCircleStatus
    ): DeferredResult<Response<Any>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.circleStatusUpdate(
                changeCircleRequest.id,
                changeCircleRequest.status,
                authenticatedUser
            )
        )
    }

    @PostMapping("/approve-circle-member")
    @Operation(
        summary = "Approve Circle Member",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun approveMember(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody approveExpertRequest: ApproveExpertRequest
    ): DeferredResult<Response<Any>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.approveMember(
                approveExpertRequest.approve,
                approveExpertRequest.circleId,
                approveExpertRequest.expertId,
                authenticatedUser
            )
        )
    }

    @GetMapping("/circle-member-search/{circleId}")
    @Operation(
        summary = "Circle Member List",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleMemberSearch(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @PathVariable
        circleId: Long,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "User's name or company name to search.")
        search: String,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,

        @RequestParam(name = "countryCode", required = false, defaultValue = "")
        @Parameter(name = "countryCode", required = false, description = "Country of users")
        countryCode: String,

        @RequestParam(name = "expertiseId", required = false, defaultValue = "")
        @Parameter(
            name = "expertiseId",
            required = false,
            description = "Expertise id in case of userType is expert selected."
        )
        expertiseId: String
    ): DeferredResult<Response<PagedResult<ExpertProfileSummary>>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.circleMemberSearch(
                circleId,
                ExpertSearchFilter.Builder.build(search, countryCode, expertiseId),
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER_CLIENT),
                authenticatedUser
            )
        )
    }

    @GetMapping("/circle-request-search/{circleId}")
    @Operation(
        summary = "Circle Request List",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun circleRequestSearch(
        @PathVariable
        circleId: Long,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "User's name or company name to search.")
        search: String,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,

        @RequestParam(name = "countryCode", required = false, defaultValue = "")
        @Parameter(name = "countryCode", required = false, description = "Country of users")
        countryCode: String,

        @RequestParam(name = "expertiseId", required = false, defaultValue = "")
        @Parameter(
            name = "expertiseId",
            required = false,
            description = "Expertise id in case of userType is expert selected."
        )
        expertiseId: String
    ): DeferredResult<Response<PagedResult<ExpertProfileSummary>>> {
        return ResponseUtil.toDeferredResult(
            adminCircleFacade.circleRequestSearch(
                circleId,
                ExpertSearchFilter.Builder.build(search, countryCode, expertiseId),
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER_CLIENT)
            )
        )
    }


    @PutMapping(value = ["/{circleId}/banner-image"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Update Cover Picture. Return full path url of the image if successful.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadProfilePicture(
        @PathVariable circleId: Long,
        @RequestParam(value = "bannerImage") photo: MultipartFile,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<CircleBannerUploadResponse>> {
        return ResponseUtil.toDeferredResult(adminCircleFacade.uploadCoverPicture(circleId, photo, user))
    }

}