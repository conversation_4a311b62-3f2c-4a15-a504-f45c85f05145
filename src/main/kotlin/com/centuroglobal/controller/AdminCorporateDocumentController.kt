package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ClientDocType
import com.centuroglobal.shared.data.enums.UserDocType
import com.centuroglobal.shared.data.payload.CorporateDocumentRequest
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.CorporateDocumentResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserDocumentSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@Tag(name = "Admin Corporate Document Management", description = "Centuro Global Admin Corporate Document API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/corporate/document")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CORPORATE', 'USERS')")
class AdminCorporateDocumentController(
    private val adminUserService: AdminUserService
) {

    // api for cg admin and partner admin
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value=["/{corporateId}"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Operation(
        summary = "Upload corporate documents",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadDocs(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @RequestBody documentRequest: CorporateDocumentRequest,
        @PathVariable corporateId: Long
    ): Response<Boolean> {
        return Response(true, adminUserService.uploadDocumentCorporate(documentRequest, corporateId, user))
    }

    // api for cg admin and partner admin
    @GetMapping("/{corporateId}")
    @Operation(
        summary = "Corporate documents listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun docList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @PathVariable corporateId: Long,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Document name to search.")
        search: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "Country of Document.")
        country: String?,

        @RequestParam(name = "createdBy", required = false, defaultValue = "")
        @Parameter(name = "createdBy", required = false, description = "Uploaded user.")
        createdBy: Long?,

        @RequestParam(name = "uploadedType", required = false, defaultValue = "")
        @Parameter(name = "uploadedType", required = false, description = "File type of Document.")
        uploadedType: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): PagedResult<CorporateDocumentResponse> {
        return adminUserService.listDocuments(
            UserDocumentSearchFilter.Builder.build(
                search,
                country,
                createdBy,
                corporateId,
                UserDocType.CORPORATE,
                uploadedType,
                ClientDocType.CORPORATE
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser, true
        )
    }

    // api for cg admin and partner admin
    @DeleteMapping(value=["/{corporateId}/{id}"])
    @Operation(
        summary = "Delete corporate document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteDoc(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @PathVariable id: Long,
        @PathVariable corporateId: Long,
    ): Response<Boolean> {
        return Response(true, adminUserService.deleteDocumentCorporate(id, corporateId, user))
    }

    // api for cg admin and partner admin
    @GetMapping("/{corporateId}/{fileId}/view")
    @Operation(
        summary = "View Corporate document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun viewUrl(
        @PathVariable("fileId") fileId: Long,
        @PathVariable corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        return Response(true, adminUserService.getDocUrlCorporate(fileId, corporateId, authenticatedUser))
    }

    // api for cg admin and partner admin
    @GetMapping("/{corporateId}/download")
    @Operation(
        summary = "Download Corporate document",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun downloadDoc(
        @RequestParam("fileId") fileId: Long?,
        @PathVariable("corporateId") corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): ResponseEntity<StreamingResponseBody> {
        return adminUserService.downloadDocCorporate(fileId, corporateId, authenticatedUser)
    }

}