package com.centuroglobal.controller

import com.centuroglobal.data.payload.lead.UpdateLeadRequest
import com.centuroglobal.facade.LeadFacade
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult

@RestController
@Tag(name = "Admin Lead Management", description = "Centuro Global Admin Lead API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/lead")
@PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'LEADS')")
class AdminLeadController(
    private val leadFacade: LeadFacade
) {

    @PutMapping("/{leadId}")
    @Operation(
        summary = "Update a Lead",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @PathVariable leadId: String,
        @Valid @RequestBody request: UpdateLeadRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<LeadDetail>> {
        return ResponseUtil.toDeferredResult(leadFacade.update(leadId, request, authenticatedUser.userId))
    }

    @GetMapping
    @Operation(
        summary = "Search Leads",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun search(
        @Valid search: LeadsSearchCriteria
    ): DeferredResult<Response<PagedResult<AdminLeadSummary>>> {
        return ResponseUtil.toDeferredResult(
            leadFacade.searchLeadsForAdmin(
                LeadSearchFilter.Builder.build(search),
                PageRequest.of(
                    search.pageIndex,
                    search.pageSize,
                    SearchConstant.SORT_ORDER(
                        if (search.sortBy.isBlank()) "createdDate" else search.sortBy,
                        if (search.sort.isBlank()) "DESC" else search.sort
                    )
                )
            )
        )
    }

    @GetMapping("/{leadId}")
    @Operation(
        summary = "Retrieve a Lead",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(
        @PathVariable leadId: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<LeadDetail>> {
        return ResponseUtil.toDeferredResult(leadFacade.retrieve(leadId, authenticatedUser.userId))
    }

    @DeleteMapping("/{leadId}")
    @Operation(
        summary = "Delete a Lead",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @PathVariable leadId: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(leadFacade.delete(leadId, authenticatedUser.userId))
    }

    @GetMapping("/recent")
    @Operation(
        summary = "Retrieve recently posted Leads",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun recent(): DeferredResult<Response<List<RecentLeadSummary>>> {
        return ResponseUtil.toDeferredResult(leadFacade.recent())
    }
}
