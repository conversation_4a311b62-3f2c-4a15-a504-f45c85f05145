package com.centuroglobal.controller

import com.centuroglobal.data.pojo.*
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.PartnerEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import java.util.*


@RestController
@Tag(name = "Admin Partner Management", description = "Centuro Global Admin Partner API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/partner")
@PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'PARTNER')")
class AdminPartnerController(
    private val partnerService:PartnerService,
    private val corporateService: CorporateService
 ) {

    @PostMapping
    @Operation(
        summary = "Create Partner Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun createPartner(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
        @RequestBody @Valid createPartnerRequest:PartnerCreateRequest
    ):Response<Long>{
        return Response(true, partnerService.createPartner(createPartnerRequest,authenticatedUser))
    }

    @GetMapping("/listing")
    @Operation(
        summary = "partner listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "User's name, email or company.")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "User's country to search.")
        country: String?,

        @RequestParam(name = "expertCompanyId", required = false)
        @Parameter(name = "expertCompanyId", required = false, description = "expert company id to search.")
        expertCompany: Long?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "Account status to search.",
            schema = Schema(allowableValues = ["ACTIVE", "SUSPENDED", "PENDING_VERIFICATION"])
        )
        status: CorporateStatus?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,
        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<PartnerList?>> {

        val size = if (isDownload) Int.MAX_VALUE else pageSize

        return Response(true, partnerService.listPartner(
            PartnerSearchFilter.Builder.build(
                search,
                country,
                expertCompany,
                corporate,
                status
            ),
            PageRequest.of(pageIndex, size, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

    @PutMapping("/{partnerId}")
    @Operation(
            summary = "Update Partner Request",
            responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
            security = [SecurityRequirement(name =  "jwt")]
    )
    fun updatePartner(
            @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
            @PathVariable partnerId: Long,
            @RequestBody @Valid updatePartnerRequest:UpdatePartnerRequest
    ):Response<PartnerEntity>{
        return Response(true, partnerService.updatePartner(partnerId,updatePartnerRequest,authenticatedUser))
    }

    @GetMapping("/company")
    @Operation(
        summary = "Return corporate root user details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun listCorporateUsers(
        @RequestParam("companyId") companyId: Long,
        @RequestParam("companyType") companyType: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response <Map<String, Any?>> {
        val result = partnerService.retrieveRootUserDetails(companyType, companyId)
        return Response(true, result)
    }


    @GetMapping("/{partnerId}")
    @Operation(
        summary = "Return partner details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getAllPartnerDetails(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<AllPartnerList> {
        val result = partnerService.retrieveAllPartnerDetails(partnerId)
        return Response(true, result)
    }

    @PutMapping("/status")
    @Operation(
        summary = "Update company status Request",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,
        @RequestBody @Valid updateCompanyStatusRequest:UpdateCompanyStatusRequest
    ):Response<Boolean>{
        return Response(true, partnerService.updateCompanyStatus(updateCompanyStatusRequest,authenticatedUser))
    }

    @DeleteMapping("/{partnerId}")
    @Operation(
        summary = "Delete Partner",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deletePartner(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser:AuthenticatedUser,

    ):Response<Boolean> {
        return Response(true, partnerService.deletePartner(partnerId))
    }

    @GetMapping("/partner-listing")
    @Operation(
        summary = "list of name and id of partner",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listPartnerNameId(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<ReferenceData>> {

        val partners = partnerService.retrievePartners()
        return Response(true,partners)
    }

    @GetMapping("/users/listing")
    @Operation(
        summary = "Partner admins listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listPartnerUser(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Partner user's name or email")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Partner user's country to search.")
        country: String?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "Account status to search.",
            schema = Schema(allowableValues = ["ACTIVE", "SUSPENDED", "PENDING_VERIFICATION"])
        )
        status: AccountStatus?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "Partner id to search.")
        partnerId: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<PartnerUserList>> {
        return Response(
            true, partnerService.listAllPartnerUser(
                PartnerUserSearchFilter.Builder.build(
                    search,
                    country,
                    corporate,
                    status
                ),
                partnerId,
                PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
            )
        )
    }
    @GetMapping("/access/{partnerId}")
    @Operation(
        summary = "Return partner band accesses",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getPartnerBandAccesses(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<List<String?>> {
        val result = partnerService.retrievePartnerBandAccesses(partnerId)
        return Response(true, result)
    }
}