package com.centuroglobal.controller

import com.centuroglobal.data.payload.UpdateRfpRequest
import com.centuroglobal.service.RfpService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.RfpStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.RfpSearchFilter
import com.centuroglobal.shared.data.pojo.RfpStatsResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@Tag(name = "Admin Rfp", description = "Centuro Global Admin Rfp API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/rfp")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'PROPOSAL', 'PROPOSAL')")
class AdminRFPController(open val rfpService: RfpService) {
    @PutMapping("/edit")
    @Operation(
        summary = "Edit a rfp",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun editRfp(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody rfpRequest: UpdateRfpRequest,
        @PathVariable partnerId: Long?
    ): Response<Long> {
        val queryId = rfpService.editRfp(rfpRequest, authenticatedUser)
        return Response(true, queryId)
    }

    @GetMapping
    @Operation(
        summary = "Fetch all the rfp of the user based on search",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun listRfp(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "")
        search: String?,

        @RequestParam(name = "corporate", required = false, defaultValue = "")
        @Parameter(name = "corporate", required = false, description = "")
        corporateId: Long?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "")
        to: Long?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(name = "status", required = false, description = "Rfp status")
        @Schema(required = true, allowableValues = ["OPEN", "RESOLVED", "CANCELLED"])
        status: RfpStatus?,

        @RequestParam(name = "isDownload", required = false, defaultValue = false.toString())
        @Parameter(name = "isDownload", required = false, description = "Download RFPs")
        isDownload: Boolean?,

        @RequestParam(name = "account", required = false, defaultValue = "")
        @Parameter(
            name = "account",
            required = false,
            description = "based on account fetch all users and fetch rfp of them"
        )
        account: String?,

        @RequestParam(name = "user", required = false, defaultValue = "")
        @Parameter(name = "user", required = false, description = "")
        user: String?,

        @RequestParam(name = "responses", required = false, defaultValue = "")
        @Parameter(name = "responses", required = false, description = "")
        responses: String?,

        @RequestParam(name = "categories", required = false, defaultValue = "")
        @Parameter(name = "categories", required = false, description = "")
        categories: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "")
        country: String?,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "")
        partnerId: Long?,

        @RequestParam(name = "archive", required = false, defaultValue = "")
        @Parameter(name = "archive", required = false, description = "")
        archive: Boolean?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isPartner", required = false)
        @Parameter(name = "isPartner", required = false, description = "Partner RFP")
        isPartner: Boolean?,

        @RequestParam(name = "cgRequested", required = false)
        @Parameter(name = "cgRequested", required = false, description = "CG Requested cases")
        cgRequested: Boolean?

    ): Response<RfpStatsResponse> {
        val pageSize = if (isDownload == true) {
            Int.MAX_VALUE
        } else {
            pageSize
        }
        return Response(
            true,
            rfpService.listRfpForAdmin(
                RfpSearchFilter.Builder.build(
                    search, null, null, null, categories,
                    country, if (status != null) listOf(status) else listOf(RfpStatus.OPEN, RfpStatus.RESOLVED),
                    corporateId, from, to,authenticatedUser.partnerId ?: partnerId ,cgRequested,
                    isPartner
                ),
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            )
        )
    }

    @PostMapping(value = ["/{rfpId}/proposal"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Submit a proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
     fun postProposal(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long,
        @RequestParam("fileName") fileName: String,
        @RequestParam("fileType") fileType: String,
        @RequestParam("fileSize") fileSize: Long,
        @RequestParam("file") file: MultipartFile,
        @PathVariable partnerId: Long?

    ): Response<Long> {
        return Response(true, rfpService.createProposal(rfpId, fileName, fileType, fileSize, file, authenticatedUser))
    }


    @DeleteMapping("/{rfpId}/proposals/{proposalId}")
    @Operation(
        summary = "Delete a Rfp proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
     fun deleteProposal(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long,
        @PathVariable("proposalId") proposalId: Long,
        @PathVariable
        partnerId: Long?
    ): Response<Boolean> {
        return Response(true, rfpService.deleteProposal(rfpId, proposalId, authenticatedUser))
    }

    @PutMapping("/{rfpId}/request-cg")
    @Operation(
        summary = "Update Partner cg for rfp",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updatePartnerCg(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long,
        @RequestParam @Valid cgRequested: Boolean
    ): Response<Boolean> {
        return Response(true, rfpService.updatePartnerCg(authenticatedUser.partnerId!!, rfpId, cgRequested, authenticatedUser))
    }

}