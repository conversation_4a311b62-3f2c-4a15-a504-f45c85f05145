package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.data.payload.TaskStatusUpdateRequest
import com.centuroglobal.service.TaskService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.ShowBy
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskView
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.task.dto.TaskDetails
import com.centuroglobal.shared.data.pojo.task.dto.TaskSearchFilter
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskRequest
import com.centuroglobal.shared.data.pojo.task.response.TaskCountResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@Tag(name = "Admin Task", description = "Centuro Global Admin Task API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/task")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
@Validated
class AdminTaskController(
    private val taskService: TaskService
) {

    @PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, authentication.principal.userId, 'TASK', 'TASK')")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    @Operation(
        summary = "Create Task",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreateTaskRequest
    ): Response<Long> {
        return Response(true, taskService.createTask(request, authenticatedUser))
    }

    @GetMapping("/listing")
    @Operation(
        summary = "Fetch all the tasks with filter",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getTasks(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "name", required = false)
        @Parameter(name = "name", required = false, description = "")
        name: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(name = "status", required = false, description = "Task status")
        status: String?,

        @RequestParam(name = "partnerId", required = false,)
        @Parameter(name = "partnerId", required = false, description = "")
        partnerId: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "dueDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "createdBy", required = false)
        @Parameter(name = "createdBy", required = false, description = "createdBy")
        createdBy: Long?,

        @RequestParam(name = "taskView", required = false)
        @Parameter(name = "taskView", required = false, description = "View Tasks for")
        taskView: String?,

        @RequestParam(name = "type", required = false)
        @Parameter(name = "type", required = false, description = "Task type")
        type: String?,

        @RequestParam(name = "priority", required = false, defaultValue = "")
        @Parameter(name = "priority", required = false, description = "Priority")
        priority: String?,

        @RequestParam(name = "referenceId", required = false)
        @Parameter(name = "referenceId", required = false, description = "Reference ID")
        referenceId: Long?,

        @RequestParam(name = "showBy", required = false)
        @Parameter(name = "showBy", required = false, description = "Show By")
        showBy: ShowBy?,

        @RequestParam(name = "assignedTo", required = false)
        @Parameter(name = "assignedTo", required = false, description = "Assigned To")
        assignedTo: Long?,

    ): Response<PagedResult<TaskDetails>> {

        var view:String? = null

        if(taskView!=null)
            view=taskView.toString()

        val companyIdType = taskService.getCompanyIdAndType(authenticatedUser)
        var statusList: List<TaskStatus>? = null
        if (status?.isNotBlank() == true) {
            statusList = status.split(",").map { TaskStatus.valueOf(it) }
        }

        var referenceTypes: List<ReferenceType>? = null
        if (type?.isNotBlank() == true) {
            referenceTypes = type.let { it.split(",").filter { token -> token != "" }.map { t -> ReferenceType.valueOf(t) } }
        }

        val searchFilter = TaskSearchFilter.Builder.build(
            name, createdBy, referenceTypes, referenceId, statusList
            , authenticatedUser.userId,TaskVisibility.PUBLIC,view,priority,
            authenticatedUser.partnerId ?: partnerId,showBy, assignedTo, companyIdType.first, companyIdType.second
        )

        return Response(
            true,
            taskService.listTasks(searchFilter,
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            )
        )
    }

    @GetMapping("/countByStatus")
    @Operation(
        summary = "Fetch the count of tasks by status for a given month and year- Used for Calendar",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getTasksCount(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "month", required = true, defaultValue = "")
        @Parameter(name = "month", required = true, description = "")
        month: Int?,

        @RequestParam(name = "year", required = true, defaultValue = "")
        @Parameter(name = "year", required = true, description = "")
        year: Int?,

        @RequestParam(name = "taskView", required = false, defaultValue = "ALL_TASK")
        @Parameter(name = "taskView", required = false, description = "View Tasks for")
        taskView: TaskView?,

    ): List<TaskCountResponse>? {

        var view:String? = null

        if(taskView!=null)
            view=taskView.toString()

        val companyIdType = taskService.getCompanyIdAndType(authenticatedUser)

        return taskService.listTasksCount(TaskSearchFilter.Builder.build(month,year,authenticatedUser.partnerId,
            view, authenticatedUser.userId, TaskVisibility.PUBLIC, companyIdType.first, companyIdType.second)
            ,authenticatedUser)
    }

    @GetMapping("/details")
    @Operation(
        summary = "Fetch the task details which are due by the given date",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getTaskDetails(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "day", required = true, defaultValue = "")
            @Parameter(name = "day", required = true, description = "")
        day: Int,
        month: Int,
        year: Int,

        @RequestParam(name = "taskView", required = false, defaultValue = "ALL_TASK")
        @Parameter(name = "taskView", required = false, description = "View Tasks for")
        taskView: TaskView?

    ): Response<List<TaskDetails>> {

        val companyIdType = taskService.getCompanyIdAndType(authenticatedUser)

        return Response(true,
            taskService.getTaskDetails(TaskSearchFilter.Builder.build(LocalDate.of(year,month,day)
                ,authenticatedUser.partnerId, taskView, authenticatedUser.userId, TaskVisibility.PUBLIC, companyIdType.first, companyIdType.second), authenticatedUser))
    }

    @GetMapping("/{taskId}")
    @Operation(
        summary = "Retrieve a Task",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(
        @PathVariable taskId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<TaskDetails> {
        return Response(true, taskService.retrieve(taskId, authenticatedUser))
    }


    @PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, authentication.principal.userId, 'TASK', 'TASK')")
    @PutMapping("/{taskId}")
    @Operation(
        summary = "Update Task",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateTask(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("taskId") taskId: Long,
        @Valid @RequestBody request: CreateTaskRequest
    ): Response<Long> {
        return Response(true, taskService.updateTask(taskId, request, authenticatedUser))
    }


    @PutMapping("/{taskId}/status")
    @Operation(
        summary = "Update Task status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateTaskStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("taskId") taskId: Long,
        @RequestBody task: TaskStatusUpdateRequest
    ): Response<Boolean> {
        return Response(true, taskService.updateStatus(taskId, task.status, authenticatedUser))
    }

    @DeleteMapping("/{taskId}")
    @Operation(
        summary = "Delete a Task",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun delete(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("taskId") taskId: Long
    ): Response<Boolean> {
        return Response(true, taskService.delete(taskId, authenticatedUser))
    }

}