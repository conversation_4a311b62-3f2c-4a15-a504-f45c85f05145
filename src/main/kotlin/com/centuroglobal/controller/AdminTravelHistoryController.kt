package com.centuroglobal.controller

import com.centuroglobal.service.travel.TravelHistoryService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.travel.TravelHistoryRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.travel.TravelHistoryResponse
import com.centuroglobal.shared.data.pojo.travel.TravelHistorySearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@Tag(name = "User Travel History Management", description = "Centuro Global Admin User travel history API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/travel")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #userId, 'CORPORATE_USER', 'USERS')")
class AdminTravelHistoryController(
    private val travelHistoryService: TravelHistoryService
) {

    @PostMapping("/{userId}")
    @Operation(
        summary = "Create a travel history",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: List<TravelHistoryRequest>,
        @PathVariable userId: Long
    ): Response<List<Long>> {
        return Response(true, travelHistoryService.create(request, userId, authenticatedUser))
    }

    @PutMapping("/{userId}/{id}")
    @Operation(
        summary = "Update a travel history",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun update(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: TravelHistoryRequest,
        @PathVariable id: Long,
        @PathVariable userId: Long
    ): Response<Boolean> {
        return Response(true, travelHistoryService.update(id, request, userId, authenticatedUser))
    }

    @DeleteMapping("/{userId}/{id}")
    @Operation(
        summary = "Delete a travel history",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun delete(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long,
        @PathVariable userId: Long
    ): Response<Boolean> {
        return Response(true, travelHistoryService.delete(id, userId, authenticatedUser))
    }

    @GetMapping("/{userId}/{id}")
    @Operation(
        summary = "Get a travel history",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun get(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable id: Long,
        @PathVariable userId: Long
    ): Response<TravelHistoryResponse> {
        return Response(true, travelHistoryService.get(id, userId, authenticatedUser))
    }

    @GetMapping("/list")
    @Operation(
        summary = "List travel histories",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "originCountry", required = false)
        @Parameter(name = "originCountry", required = false, description = "origin country to search.")
        originCountry: String?,

        @RequestParam(name = "destinationCountry", required = false)
        @Parameter(name = "destinationCountry", required = false, description = "destination country to search.")
        destinationCountry: String?,

        @RequestParam(name = "purpose", required = false, defaultValue = "")
        @Parameter(name = "purpose", required = false, description = "Travel History Purpose to search")
        purpose: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "userId", required = false, defaultValue = "")
        @Parameter(
            name = "userId",
            required = false,
            description = "User Id."
        )
        userId: Long?

    ): Response<PagedResult<TravelHistoryResponse>> {
        val searchFilter = TravelHistorySearchFilter.Builder.build(originCountry, destinationCountry, purpose, null, userId)
        val pageRequest = PageRequest.of(
            pageIndex,
            if (isDownload) Int.MAX_VALUE else pageSize,
            SearchConstant.SORT_ORDER(sortBy, sort)
        )
        return Response(true, travelHistoryService.list(searchFilter, pageRequest, userId?:authenticatedUser.userId, authenticatedUser))
    }

    @PostMapping("{userId}/upload", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Upload a travel ticket/boarding pass to autofill details",
        responses = [ApiResponse(content = [Content(mediaType = MediaType.APPLICATION_JSON_VALUE)])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun uploadTicket(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestParam("file") file: MultipartFile,
        @PathVariable("userId") userId: Long
    ): Response<List<TravelHistoryRequest>> {
        return Response(true, travelHistoryService.uploadTicket(file, userId, authenticatedUser))
    }
}