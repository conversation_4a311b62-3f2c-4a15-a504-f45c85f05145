package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdmin
import com.centuroglobal.annotation.IsSuperAdminOrAdmin
import com.centuroglobal.facade.BackofficeFacade
import com.centuroglobal.service.AdminUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.HubspotResponse
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.CreateAdminUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateAdminUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.SharedAdminUserService
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import org.springframework.web.multipart.MultipartFile

@RestController
@Tag(name = "Admin User Management", description = "Centuro Global Admin User API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/user")
@IsSuperAdmin
class AdminUserController(
    private val sharedadminUserService: SharedAdminUserService,
    private val adminUserService: AdminUserService,
    private val backofficeFacade: BackofficeFacade
) {

    @GetMapping
    @Operation(
        summary = "List Users",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "User's name to search.")
        search: String?,

        @RequestParam(name = "role", required = false, defaultValue = "")
        @Parameter(
            name = "role",
            required = false,
            description = "role to search.",
            schema = Schema(allowableValues = ["ROLE_ADMIN", "ROLE_SUPER_ADMIN"])
        )
        @Pattern(regexp = "^(|ROLE_ADMIN|ROLE_SUPER_ADMIN)$")
        role: String,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(
            name = "status",
            required = false,
            description = "Account status to search.",
            schema = Schema(allowableValues = ["ACTIVE", "SUSPENDED", "PENDING_VERIFICATION"])
        )
        @Pattern(regexp = "^(|ACTIVE|SUSPENDED|PENDING_VERIFICATION)$")
        status: String,

        @RequestParam(name = "responsibility", required = false, defaultValue = "")
        @Parameter(
            name = "responsibility",
            required = false,
            description = "Responsibility to search.",
            schema = Schema(allowableValues = ["BLUEPRINT", "CIRCLES", "EVENTS", "CASES", "LEADS", "COUNTRIES", "MASTERS", "CLIENT_REFERRALS", "EXPERTS,USERS"])
        )
        @Pattern(regexp = "^(|BLUEPRINT|CIRCLES|EVENTS|CASES|LEADS|COUNTRIES|MASTERS|CLIENT_REFERRALS|EXPERTS|USERS)$")
        responsibility: String

    ): DeferredResult<Response<List<BackofficeUserList>>> {
        return ResponseUtil.toDeferredResult(
            backofficeFacade.retrieveBackofficeUsers(
                AdminUserSearchFilter.Builder.build(search, role, status, responsibility)
            )
        )
    }

    @GetMapping("/{userId}")
    @Operation(
        summary = "Retrieve User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieve(@PathVariable userId: Long): DeferredResult<Response<BackofficeUser>> {
        return ResponseUtil.toDeferredResult(backofficeFacade.retrieveBackofficeUser(userId))
    }

    @PutMapping("/{userId}")
    @Operation(
        summary = "Update User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun update(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: UpdateAdminUserRequest
    ): DeferredResult<Response<BackofficeUser>> {
        return ResponseUtil.toDeferredResult(
            backofficeFacade.updateBackofficeUser(
                userId,
                authenticatedUser.userId,
                request
            )
        )
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    @Operation(
        summary = "Create User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun create(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreateAdminUserRequest
    ): DeferredResult<Response<BackofficeUser>> {
        return ResponseUtil.toDeferredResult(
            backofficeFacade.createBackofficeUser(authenticatedUser.userId, request)
        )
    }

    @DeleteMapping("/{userId}")
    @Operation(
        summary = "Delete User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'USERS')")
    fun delete(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): DeferredResult<Response<String>> {
        return ResponseUtil.toDeferredResult(adminUserService.deleteUser(userId, authenticatedUser))
    }

    @PreAuthorize("@apiAuthService.isAdminOrSuperAdmin(authentication.principal, 'USERS')")
    @GetMapping("/client-users")
    @Operation(
        summary = "Retrieve User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieveClientUsers(): Response<List<ClientUser>> {
        return Response(true, adminUserService.getClientUsers())
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/hubspot")
    @Operation(
        summary = "Create Hubspot User",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun hubspot(): Response<MutableList<HubspotResponse>> {
        return Response(true, sharedadminUserService.createHubspotUsers())
    }

    @PutMapping(value = ["/photo/{userId}"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Update Profile Picture. Return full path url of the image if successful.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun uploadProfilePicture(
        @PathVariable userId: Long,
        @RequestParam(value = "photo") photo: MultipartFile,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<String> {
        return Response(true, adminUserService.uploadProfilePicture(userId, user.userId, photo))
    }

    @DeleteMapping("/photo/{userId}")
    @Operation(
        summary = "Delete Profile Picture",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteProfilePicture(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): Response<String> {
        return Response(true, adminUserService.deleteProfilePicture(userId, user.userId))
    }

}