package com.centuroglobal.controller


import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.WorkLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.subscription.WorkLogCorporateStats
import com.centuroglobal.shared.data.pojo.subscription.WorkLogRequest
import com.centuroglobal.shared.data.pojo.subscription.WorkLogResponse
import com.centuroglobal.shared.data.pojo.subscription.WorkLogSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Admin Work Log Management", description = "Centuro Global Admin Work Log API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/work-log")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CORPORATE', 'QUERY')")
class AdminWorkLogController(
    val workLogService: WorkLogService
) {

    @PostMapping
    @Operation(
        summary = "Create a work log",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "EXPERT_SUPPORT")
    fun create(
        @LogValue @Valid @RequestBody request: WorkLogRequest,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Long> {
        val id = workLogService.create(request, authenticatedUser.userId)
        return Response(true, id)
    }

    @GetMapping("listing")
    @Operation(
        summary = "Work logs listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun plans(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "corporateId", required = false, defaultValue = "")
        @Parameter(name = "corporateId", required = false, description = "Corporate Id to search.")
        corporateId: Long?,

        @RequestParam(name = "referenceId", required = false, defaultValue = "")
        @Parameter(name = "referenceId", required = false, description = "Reference Id to search.")
        referenceId: Long?,

        @RequestParam(name = "referenceType", required = false, defaultValue = "")
        @Parameter(name = "referenceType", required = false, description = "Reference Type to search.")
        referenceType: ReferenceType?,

        @RequestParam(name = "month", required = false, defaultValue = "")
        @Parameter(name = "month", required = false, description = "Month to search in format MM.",
            schema = Schema(pattern = "(0[1-9]|1[0-2])$"))
        month: Int?,

        @RequestParam(name = "year", required = false, defaultValue = "")
        @Parameter(name = "year", required = false, description = "Year to search in format YYYY.",
            schema = Schema(pattern = "[0-9]{4}$"))
        year: Int?,

        @RequestParam(name = "loggedBy", required = false, defaultValue = "")
        @Parameter(name = "loggedBy", required = false, description = "Work Logs by user to search.")
        loggedBy: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", defaultValue = "false")
        @Parameter(name = "isDownload", required = false, description = "Download work log list.")
        isDownload: Boolean
    ): Response<PagedResult<WorkLogResponse>> {
        val size = if (isDownload) Integer.MAX_VALUE else pageSize

        return Response(true, workLogService.list(
            WorkLogSearchFilter.Builder.build(corporateId, referenceId, referenceType, month, year, loggedBy),
            PageRequest.of(pageIndex, size, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser
        ))
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a work log",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun delete(
        @Valid @PathVariable id: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Boolean> {
        val result = workLogService.delete(id, authenticatedUser)
        return Response(true, result)
    }

    @GetMapping("/usage-stats/{corporateId}")
    @Operation(
        summary = "Corporate Usage stats",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun usageStats(
        @Valid @PathVariable corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<WorkLogCorporateStats> {
        val result = workLogService.usageStats(corporateId, authenticatedUser)
        return Response(true, result)
    }
    @GetMapping("/reference-ids/{referenceType}/{corporateId}")
    @Operation(
        summary = "Reference Id list by type",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun referenceIdList(
        @Valid @PathVariable referenceType: ReferenceType,
        @Valid @PathVariable corporateId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<Long>> {
        val result = workLogService.referenceIdList(referenceType, corporateId, authenticatedUser)
        return Response(true, result)
    }


}