package com.centuroglobal.controller

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.BandCardDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.BandService
import com.centuroglobal.shared.data.pojo.ReferenceData
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.Parameter

@RestController
@Tag(name = "Corporate User Bands", description = "Centuro Global Corporate User bands API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/band")
class BandController(
    private val bandService: BandService
) {
    @GetMapping()
    @Operation(
        summary = "Bands for Corporate",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun bands(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
    ): Response<List<ReferenceData>> {
        val result = bandService.listBands(authenticatedUser.companyId!!)
        return Response(true, result)
    }

    @GetMapping("/listing")
    @Operation(
        summary = "Bands Listing for Corporate",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
    ): Response<List<BandCardDetails>> {
        val result = bandService.bandsListing(authenticatedUser.companyId!!)
        return Response(true, result)
    }

}