package com.centuroglobal.controller

import com.centuroglobal.annotation.*
import com.centuroglobal.service.CaseDashboardService
import com.centuroglobal.service.CaseService
import com.centuroglobal.service.DocRepoService
import com.centuroglobal.service.TaskService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.CaseDocumentsEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.ShowBy
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.case.*
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.*
import com.centuroglobal.shared.data.pojo.task.dto.TaskDetails
import com.centuroglobal.shared.data.pojo.task.dto.TaskSearchFilter
import com.centuroglobal.shared.data.pojo.task.response.TaskWorkflowReport
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Case", description = "Centuro Global Case API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/case")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
@Validated
class CaseController(
    private val caseService: CaseService,
    private val caseDashboardService: CaseDashboardService,
    private val taskService: TaskService
) {

    @PutMapping("/{caseId}")
    @Operation(
        summary = "Update Case Status and documents",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrPartnerOrManageCaseCorporate
    fun update(
        @PathVariable caseId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: UpdateCaseRequest
    ): Response<String> {
        caseService.update(caseId, authenticatedUser, request, authenticatedUser.role)
        return Response(true)
    }

    @GetMapping("/{caseId}")
    @Operation(
        summary = "Get Case Details based on Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCaseData(
        @PathVariable("caseId") caseId: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<CaseViewDetails> {

        return caseService.getCaseDetailsById(caseId, authenticatedUser)
    }


    @GetMapping("/{caseId}/case-notes")
    @Operation(
        summary = "List Case notes.",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun listCaseNotes(

        @PathVariable caseId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser

    ): Response<List<CaseNotesDetails>> {

        val logs = caseService.listCaseNotes(caseId, authenticatedUser)

        return Response(true, logs)
    }


    @GetMapping("/listing")
    @Operation(
        summary = "Cases based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Case name or case description to search.")
        search: String?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(
            name = "status", required = false,
            description = "Case status.",
            schema = Schema(allowableValues = [" NOT_STARTED", "RESOLVED", "REJECTED", "IN_PROGRESS"])
        )
        status: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(
            name = "country", required = false,
            description = "Case country."
        )
        country: String?,

        @RequestParam(name = "category", required = false, defaultValue = "")
        @Parameter(
            name = "category", required = false,
            description = "Case category."
        )
        category: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "caseListType", required = false, defaultValue = "CASES_INITIATED")
        @Parameter(
            name = "caseListType", required = false,
            description = "Case category.",
            schema = Schema(allowableValues = ["CASES_RECEIVED", "CASES_INITIATED"])
        )
        caseListType: String,

        @RequestParam(name = "companyName", required = false, defaultValue = "")
        @Parameter(
            name = "companyName", required = false,
            description = "Case Company Name."
        )
        companyName: String?,

        @RequestParam(name = "download", defaultValue = "false")
        @Parameter(name = "download", required = false, description = "User's name to search.")
        download: Boolean,

        @RequestParam(name = "caseId", required = false)
        @Parameter(name = "caseId", required = false, description = "Case id for search ")
        caseId: String?,

        @RequestParam(name = "actionFor", required = false)
        @Parameter(name = "actionFor", required = false, description = "Action For ")
        actionFor: String?,

        @RequestParam(name = "archive", defaultValue = "false")
        @Parameter(name = "archive", required = false, description = "Archive")
        archive: Boolean,

        @RequestParam(name = "isPriorityCase")
        @Parameter(name = "isPriorityCase", required = false, description = "Is Priority Case")
        isPriorityCase: Boolean?,

        @RequestParam(name = "accountManagerId", required = false)
        @Parameter(name = "accountManagerId", required = false, description = "Account Manager id for search ")
        accountManagerId: String?,

        @RequestParam(name = "caseOwner", required = false)
        @Parameter(name = "caseOwner", required = false, description = "Case Owner id for search ")
        caseOwner: String?,

        @RequestParam(name = "minPercent", required = false)
        @Parameter(name = "minPercent", required = false, description = "Minimum Case completion percentage")
        minPercent: Int?,

        @RequestParam(name = "maxPercent", required = false)
        @Parameter(name = "maxPercent", required = false, description = "Maximum Case completion percentage")
        maxPercent: Int?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "CorporateId id for search ")
        corporateId: String?,

        @RequestParam(name = "accountId", required = false)
        @Parameter(name = "accountId", required = false, description = "Cases for account id")
        accountId: Long?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "Partner id for search")
        partnerId: Long?,

        @RequestParam(name = "isPartnerCases", required = false)
        @Parameter(name = "isPartnerCases", required = false, description = "Partner cases")
        isPartnerCases: Boolean?,

        @RequestParam(name = "cgRequested", required = false)
        @Parameter(name = "cgRequested", required = false, description = "CG Requested cases")
        cgRequested: Boolean?

        ): Response<PagedResult<CaseViewDetails>> {
        val result = caseService.listCases(
            CaseSearchFilter.Builder.build(caseId, status, category, country, companyName,
                search, actionFor, archive, isPriorityCase, accountManagerId,caseOwner, minPercent, maxPercent, accountId,
                corporateId, authenticatedUser.partnerId ?: partnerId, cgRequested, isPartnerCases),
            caseService.getPageRequest(download, pageIndex, pageSize, sortBy, sort, authenticatedUser.role),
            authenticatedUser, caseListType, archive
        )
        return Response(true, result)
    }

    @GetMapping("/download")
    @Operation(
        summary = "Cases based on filters to provide country of residence for single visa cases",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun caseDownload(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Case name or case description to search.")
        search: String?,

        @RequestParam(name = "createdFrom", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Starting date from which case is searched.")
        from: Long?,

        @RequestParam(name = "createdTo", required = false, defaultValue = "")
        @Parameter(name = "createdFrom", required = false, description = "Date up to which case is searched.")
        to: Long?,

        @RequestParam(name = "status", required = false, defaultValue = "")
        @Parameter(
            name = "status", required = false,
            description = "Case status.",
            schema = Schema(allowableValues = ["NOT_STARTED", "RESOLVED", "REJECTED", "IN_PROGRESS"])
        )
        status: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(
            name = "country", required = false,
            description = "Case country."
        )
        country: String?,

        @RequestParam(name = "category", required = false, defaultValue = "")
        @Parameter(
            name = "category", required = false,
            description = "Case category.",
            schema = Schema(allowableValues = ["BUSINESS_INCORPORATION", "IMMIGRATION", "BANK_ACCOUNT" ,"TAX_AND_ACCOUNTING",
                "INSURANCE","LEGAL" ,"HR_AND_PAYROLL", "OFFICE_SPACE"])
        )
        category: String?,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "caseListType", required = false, defaultValue = "CASES_INITIATED")
        @Parameter(
            name = "caseListType", required = false,
            description = "Case category.",
            schema = Schema(allowableValues = ["CASES_RECEIVED", "CASES_INITIATED"])
        )
        caseListType: String,

        @RequestParam(name = "companyName", required = false, defaultValue = "")
        @Parameter(
            name = "companyName", required = false,
            description = "Case Company Name."
        )
        companyName: String?,

        @RequestParam(name = "initiatedFor", required = false)
        @Parameter(name = "initiatedFor", required = false, description = "User's name to search.")
        initiatedFor: String?,

        @RequestParam(name = "caseId", required = false)
        @Parameter(name = "caseId", required = false, description = "Case id for search ")
        caseId: String?,

        @RequestParam(name = "actionFor", required = false)
        @Parameter(name = "actionFor", required = false, description = "Action For ")
        actionFor: String?,

        @RequestParam(name = "archive", defaultValue = "false")
        @Parameter(name = "archive", required = false, description = "Archive")
        archive: Boolean,

        @RequestParam(name = "isPriorityCase")
        @Parameter(name = "isPriorityCase", required = false, description = "Is Priority Case")
        isPriorityCase: Boolean?,

        @RequestParam(name = "accountManagerId", required = false)
        @Parameter(name = "accountManagerId", required = false, description = "Account Manager id for search ")
        accountManagerId: String?,

        @RequestParam(name = "caseOwner", required = false)
        @Parameter(name = "caseOwner", required = false, description = "Case Owner id for search ")
        caseOwner: String?,

        @RequestParam(name = "minPercent", required = false)
        @Parameter(name = "minPercent", required = false, description = "Minimum Case completion percentage")
        minPercent: Int?,

        @RequestParam(name = "maxPercent", required = false)
        @Parameter(name = "maxPercent", required = false, description = "Maximum Case completion percentage")
        maxPercent: Int?,

        @RequestParam(name = "accountId", required = false)
        @Parameter(name = "accountId", required = false, description = "Cases for account id")
        accountId: Long?,

        @RequestParam(name = "corporateId", required = false)
        @Parameter(name = "corporateId", required = false, description = "CorporateId id for search ")
        corporateId: String?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "Partner id for search")
        partnerId: Long?,

        @RequestParam(name = "isPartnerCases", required = false)
        @Parameter(name = "isPartnerCases", required = false, description = "Partner cases")
        isPartnerCases: Boolean?,

        @RequestParam(name = "cgRequested", required = false)
        @Parameter(name = "cgRequested", required = false, description = "CG Requested cases")
        cgRequested: Boolean?

    ): Response<List<CaseViewForDownload>> {
        val result = caseService.downloadCases(
            CaseSearchFilter.Builder.build(caseId, status, category, country,companyName,
                search, actionFor, archive, isPriorityCase, accountManagerId, caseOwner, minPercent, maxPercent,
                accountId, corporateId, partnerId, cgRequested, isPartnerCases),
            authenticatedUser, caseListType, archive
        )
        return Response(true, result)
    }


    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/{caseCategory}")
    @Operation(description = "Create Case", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    @UserAction(action = "CASE")
    fun createGenericCase(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @PathVariable("caseCategory") caseCategory: String,
        @LogValue @Valid @RequestBody request: GenericCaseRequest
    ): Response<Map<String, List<String?>>> {
        val (caseIdList, docNeeded) = caseService.createCase(caseCategory, request, authenticatedUser)
        val strCaseList = caseIdList.map { it.toString() }
        return Response(true, mapOf("caseId" to strCaseList,
            "isDocumentUpload" to listOf(docNeeded.toString())))
    }

    @PutMapping("/edit/{caseId}")
    @Operation(description = "Edit Case content", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    @IsSuperAdminOrAdminOrExpertOrPartnerOrEditCaseCorporate
    fun updateGenericCase(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable caseId: Long,
        @Valid @RequestBody request: GenericCaseRequest
    ): Response<Map<String, String>> {
        val case = caseService.updateCase(caseId, request, authenticatedUser)
        return Response(true, mapOf("caseId" to case.id.toString()))
    }

    @PutMapping("/{caseId}/approve")
    @Operation(description = "Approve Case Fees", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
    fun approveCaseFees(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable caseId: Long
    ): Response<ApproverDetailsRequest?> {
        val approvalDetails=caseService.caseFeesApprovalForCaseOwner(caseId, authenticatedUser)
        return Response(true, approvalDetails)
    }

    @GetMapping("/ids")
    @Operation(
        summary = "Cases Id Listing - Returns cases created by user",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun casesIdListing(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<MutableList<ApplicantCaseReferenceData>> {
        val result = caseService.listCaseIds(authenticatedUser.userId)
        return Response(true, result)
    }

    @GetMapping("/applicant-dashboard-details/{caseId}")
    @Operation(
        summary = "Applicant Cases Dashboard details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @Transactional(readOnly = true)
    fun applicantCaseDetails(
        @PathVariable caseId: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<CaseViewDetails> {
        val caseData = getCaseData(caseId, authenticatedUser)
        val caseOutput = caseData.payload?.let { caseDashboardService.getApplicantCaseDetails(authenticatedUser, it) }
        return Response(true, caseOutput)
    }

    @PostMapping("/reminder/{caseId}")
    @UserAction(action = "COMPLIANCE_CALENDAR")
    @Operation(description = "Add Case reminders", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    fun addCaseReminders(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @PathVariable caseId: Long,
        @LogValue @Valid @RequestBody request: CaseTracking
    ): Response<Boolean> {
        val result = caseService.createReminders(caseId, request, authenticatedUser)
        return Response(true, result)
    }

    @GetMapping("/reminder/{caseId}")
    @Operation(description = "Get List of Case reminders", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    fun getCaseReminders(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable caseId: Long
    ): Response<CaseTracking?> {
        return Response(true, caseService.getReminders(caseId, authenticatedUser))
    }

    @GetMapping("/{caseId}/linked")
    @Operation(
        summary = "Linked Cases for a case Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun linkedCaseList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("caseId") caseId: Long

    ): Response<PagedResult<CaseViewDetails>> {
        val result = caseService.getLinkedCases(caseId)
        return Response(true, result)
    }

    @PutMapping("/documents")
    @Operation(
        summary = "Case Documents - Upload",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction("COMPLIANCE_CALENDAR")
    fun caseDocuments(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @Valid @RequestBody request: CaseDocumentsRequest
    ): Response<List<CaseDocumentsEntity>> {
        val case = caseService.uploadDocuments(request, authenticatedUser)
        return Response(true, case)
    }

    @GetMapping("/{assessmentId}/generate")
    @Operation(
        summary = "Generate a Case for a assessment",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun generateCase(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("assessmentId") assessmentId: Long,
        @RequestParam caseType: String?,
        @RequestParam caseFormId: Long?

    ): Response<Map<String, Any?>> {
        val result = caseService.generateCase(assessmentId, caseType, caseFormId, authenticatedUser)
        return Response(true, result)
    }

    @PutMapping("/{id}/replace/{caseType}")
    @Operation(
        summary = "Replace existing case with new case data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun replaceCase(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("id") id: Long,
        @PathVariable caseType: String,
        @RequestBody genericCaseRequest: GenericCaseRequest

    ): Response<Boolean> {
        val result = caseService.replaceCase(id, caseType, genericCaseRequest, authenticatedUser)
        return Response(true, result)
    }

    @GetMapping("/{id}/report")
    @Operation(
        summary = "Get Task workflow report",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getReport(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("id") id: Long
    ): Response<List<TaskWorkflowReport>> {
        return Response(true, caseService.getWorkflowReport(id, authenticatedUser))
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/form/{caseCategory}/{caseFormId}")
    @Operation(description = "Create Case using case form", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    @UserAction(action = "CASE")
    fun createCaseUsingForm(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @Valid @RequestBody request: GenericCaseRequest,
        @LogValue @PathVariable caseCategory: String,
        @LogValue @PathVariable caseFormId: Long
    ): Response<Map<String, List<String?>>> {
        val (caseIdList, docNeeded) = caseService.createCaseUsingForm(caseCategory,request, caseFormId, authenticatedUser)
        val strCaseList = caseIdList.map { it.toString() }
        return Response(true, mapOf("caseId" to strCaseList,
            "isDocumentUpload" to listOf(docNeeded.toString())))
    }

    @ResponseStatus(HttpStatus.OK)
    @PutMapping("/form/{caseId}")
    @Operation(description = "Update Case using case form", responses = [ApiResponse(content = [Content(mediaType = "application/json")])], security = [SecurityRequirement(name =  "jwt")])
    fun updateCaseUsingForm(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @Valid @RequestBody request: GenericCaseRequest,
        @LogValue @PathVariable caseId: Long
    ): Response<Map<String, String>> {
        val case = caseService.updateCaseUsingForm(request, caseId, authenticatedUser)
        return Response(true, mapOf("caseId" to case.id.toString()))
    }

    @PutMapping("/form/{id}/replace/{caseFormId}")
    @Operation(
        summary = "Replace existing case created using form with new case form",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun replaceCaseForm(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("id") id: Long,
        @PathVariable caseFormId: Long,
        @RequestBody genericCaseRequest: GenericCaseRequest

    ): Response<Boolean> {
        val result = caseService.replaceCaseForm(id, caseFormId, genericCaseRequest, authenticatedUser)
        return Response(true, result)
    }

    @GetMapping("/tasks/{caseId}")
    @Operation(
        summary = "Fetch all the case tasks with filter",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun getTasks(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("caseId") id: Long,

        @RequestParam(name = "status", required = false)
        @Parameter(name = "status", required = false, description = "Task status")
        status: String?,


        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "dueDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String
        ): Response<PagedResult<TaskDetails>> {

        //call to make sure user has access to the case
        caseService.fetchCaseEntity(id, authenticatedUser)

        return Response(
            true,
            taskService.getTasksByReferenceIdAndType(id, "CASE,WORKFLOW", status,
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            )
        )
    }
}