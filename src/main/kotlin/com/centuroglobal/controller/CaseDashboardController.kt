package com.centuroglobal.controller

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.dashboard.CaseStatistics
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.PendingTasksResponse
import com.centuroglobal.shared.data.pojo.case.CaseStatusHistoryView
import com.centuroglobal.shared.data.pojo.case.CountByCountry
import com.centuroglobal.shared.data.pojo.case.DashboardCaseSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.CaseDashboardService
import com.centuroglobal.shared.data.payload.dashboard.CaseTrackingData
import com.centuroglobal.shared.data.payload.dashboard.CaseTrackingStatistics
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.data.domain.PageRequest
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.constraints.Min

@RestController
@Tag(name = "Dashboard", description = "Centuro Country Dashboard API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/dashboard/case")
class CaseDashboardController(
    private val caseDashboardService: CaseDashboardService
) {

    @GetMapping("/statistics")
    @Operation(
        summary = "Get Aggregate Data",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun aggregateData(
        @RequestParam(name = "accountId", required = false)
        @Parameter(name = "accountId", required = false, description = "account id for search")
        accountId: Long?,

        @RequestParam(name = "userId", required = false)
        @Parameter(name = "userId", required = false, description = "User ID")
        userId: Long?,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<CaseStatistics> {
        val output = caseDashboardService.generateAggregateCaseDataForUser(authenticatedUser,userId,accountId)
        return Response(true, output)
    }

    @GetMapping("/pending-tasks")
    @Operation(
        summary = "Get pending tasks",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun pendingTasks(
        @RequestParam(name = "accountId", required = false)
        @Parameter(name = "accountId", required = false, description = "account id for search")
        accountId: Long?,

        @RequestParam(name = "userId", required = false)
        @Parameter(name = "userId", required = false, description = "User ID")
        userId: Long?,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<PendingTasksResponse> {
        val output = caseDashboardService.getPendingTasks(authenticatedUser,userId,accountId)
        return Response(true, output)
    }

    @GetMapping("/counts-by-country")
    @Operation(
        summary = "Get counts per country",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun countsByCountry(
        @RequestParam(name = "accountId", required = false)
        @Parameter(name = "accountId", required = false, description = "account id for search")
        accountId: String?,

        @RequestParam(name = "userId", required = false)
        @Parameter(name = "userId", required = false, description = "User ID")
        userId: String?,

        @RequestParam(name = "category", required = false)
        @Parameter(name = "category", required = false, description = "category")
        category: String?,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CountByCountry>> {

        val output = caseDashboardService.casesByCountry(authenticatedUser,DashboardCaseSearchFilter.Builder.build(category, userId, accountId))
        return Response(true, output)
    }

    @GetMapping("/case-status-history")
    @Operation(
        summary = "Get case counts by status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun countsByStatus(
        @RequestParam(name = "caseId", required = false)
        @Parameter(
            name = "caseId",
            required = false,
            description = "case Id to search"
        )
        caseId: String?,

        @RequestParam(name = "accountId", required = false)
        @Parameter(name = "accountId", required = false, description = "account id for search")
        accountId: String?,

        @RequestParam(name = "actionFor", required = false)
        @Parameter(name = "actionFor", required = false, description = "ActionFor")
        actionFor: String?,

        @RequestParam(name = "category", required = false)
        @Parameter(name = "category", required = false, description = "category")
        category: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "country")
        country: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(name = "status", required = false, description = "status")
        status: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<PagedResult<CaseStatusHistoryView>> {

        val output = caseDashboardService.caseStatusHistory(authenticatedUser,
            DashboardCaseSearchFilter.Builder.build(category, null, accountId, actionFor, country, caseId),
            status,
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)))
        return Response(true, output)
    }

    @GetMapping("/tracking-aggregates")
    @Operation(
        summary = "Get Case tracking Aggregates",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun reminderAggregateCounts(
        @RequestParam(name = "from", required = true)
        @Parameter(name = "from", required = true, description = "From Date")
        from: Long,

        @RequestParam(name = "to", required = true)
        @Parameter(name = "to", required = true, description = "To Date")
        to: Long,

        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CaseTrackingStatistics>> {
        val output = caseDashboardService.getTrackingCounts(from, to, authenticatedUser)
        return Response(true, output)
    }
    @GetMapping("/tracking-aggregates-data")
    @Operation(
        summary = "Get Case tracking Aggregate data for a date",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun reminderAggregateData(
        @RequestParam(name = "date", required = true)
        @Parameter(name = "date", required = true, description = "Case tracking Date")
        date: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<CaseTrackingData>> {
        val output = caseDashboardService.getTrackingData(date, authenticatedUser)
        return Response(true, output)
    }
}