package com.centuroglobal.controller

import com.centuroglobal.service.CorporateService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.AddAccountRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Corporate Account", description = "Centuro Global Account API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/admin/account")
@PreAuthorize("@apiAuthService.isAdminOrPartnerExpert(authentication.principal, #partnerId, #corporateId, 'CORPORATE', 'USERS')")
class CorporateAccountController (
    private val corporateService: CorporateService
) {

    @PostMapping
    @Operation(
        summary = "Create new Account Entity",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun addAccount(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody accountRequest: AddAccountRequest
    ): Response<String> {
        return Response(true, corporateService.addAccount(accountRequest,accountRequest.corporateId!!,authenticatedUser).toString())
    }

    @GetMapping("/listing")
    @Operation(
        summary = "account based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listAccounts(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Search by name/email of account")
        search: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(name = "status", required = false, description = "status of account")
        status: AccountStatus?,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "corporateId", required = true)
        @Parameter(name = "corporateId", required = true, description = "corporate id to search")
        corporateId: Long,

    ): Response<PagedResult<AccountListing>> {
        return Response(true, corporateService.retrieveAccountsForAdmin(
            AccountSearchFilter.Builder.build(search, status),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser, corporateId))
    }

    @PutMapping
    @Operation(
        summary = "Update Account Entity",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateAccount(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody accountRequest: AddAccountRequest
    ): Response<AccountEntity> {
        return Response(true,
            corporateService.updateAccount(
                accountRequest.id!!,
                authenticatedUser,
                accountRequest
            )
        )
    }

    @GetMapping("/{accountId}")
    @Operation(
        summary = "Account - Get",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getUser(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("accountId") accountId: Long
    ): Response<AccountEntity> {
        return Response(true, corporateService.getAccountDetails(accountId))
    }

    @GetMapping("/account-users")
    @Operation(
        summary = "account based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listAccounts(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "corporateId", required = true)
        @Parameter(name = "corporateId", required = true)
        corporateId: Long,
    ): Response<MutableList<Account>> {

        return Response(true, corporateService.retrieveForAdmins(authenticatedUser, corporateId))
    }

    @GetMapping
    @Operation(
        summary = "Corporate Accounts listing- Only Name and Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun corporateAccounts(@AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

                          @RequestParam(name = "corporateId", required = true)
                          @Parameter(name = "corporateId", required = true)
                          corporateId: Long,
                          ): Response<List<ReferenceData>> {

        return Response(true, corporateService.retrieveCorporateAccounts(corporateId))
    }
}