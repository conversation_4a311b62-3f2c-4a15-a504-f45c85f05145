package com.centuroglobal.controller

import com.centuroglobal.annotation.*
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.CaseDocumentAction
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.case.CaseDocumentDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.DocRepoService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.servlet.http.HttpServletRequest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@Tag(name = "Corporate Case Documents Repository", description = "Centuro Global Corporate Case documents repository API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/doc_repo")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class DocumentRepositoryController(
    private val docRepoService: DocRepoService
) {

    @PostMapping(value = ["/upload"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Doc Repo - upload files",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @CanUploadDocument
    fun upload(
        @RequestParam("caseId") caseId: Long,
        @RequestParam("fileName") fileName: String,
        @RequestParam("docType") docType: String,
        @RequestParam("fileType") fileType: String,
        @RequestParam("fileSize") fileSize: Long,
        @RequestParam("file") file: MultipartFile,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<Map<String,String>> {

        val uploadedEntity = docRepoService.upload(caseId, fileName, docType, fileType, fileSize, file, authenticatedUser, httpServletRequest)
        return Response(true, mapOf(Pair("docFileId",uploadedEntity.id.toString())))
    }

    @GetMapping("/list/{caseId}")
    @Operation(
        summary = "List case documents",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listDocuments(
        @PathVariable("caseId") caseId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "docType", required = false, defaultValue = "")
        @Parameter(name = "docType", required = false, description = "Doc Type optional value")
        docType: String?,

        ): Response<List<CaseDocumentDetails>> {

        val caseDocumentDetails = docRepoService.list(caseId, authenticatedUser, docType)
        return Response(true, caseDocumentDetails)
    }

    @GetMapping("/download")
    @Operation(
        summary = "Download case documents",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
    fun download(
        @RequestParam("caseId") caseId: Long,
        @RequestParam(value = "docType", required = false) docType: String?,
        @RequestParam(value = "fileId", required = false) fileId: Long?,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): ResponseEntity<StreamingResponseBody> {
        return docRepoService.download(caseId, docType, fileId, authenticatedUser, httpServletRequest, CaseDocumentAction.DOWNLOAD)
    }

    @GetMapping("/view")
    @Operation(
        summary = "View case document",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @CanViewDocument
    fun view(
        @RequestParam("caseId") caseId: Long,
        @RequestParam("docType") docType: String,
        @RequestParam("fileId") fileId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): ResponseEntity<StreamingResponseBody> {
        return docRepoService.download(caseId, docType, fileId, authenticatedUser, httpServletRequest, CaseDocumentAction.VIEW)
    }

    @GetMapping("/view-url")
    @Operation(
        summary = "View case document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @CanViewDocument
    fun viewUrl(
        @RequestParam("caseId") caseId: Long,
        @RequestParam("docType") docType: String,
        @RequestParam("fileId") fileId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<String> {
        return Response(true, docRepoService.getUrl(caseId, docType, fileId, authenticatedUser, httpServletRequest))
    }

    @DeleteMapping("/file/{fileId}")
    @Operation(
        summary = "Delete case document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @CanDeleteDocument
    fun deleteFile(
        @PathVariable("fileId") fileId: Long,
        @RequestParam("caseId") caseId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<String> {
        docRepoService.delete(caseId, fileId, authenticatedUser, httpServletRequest)
        return Response(true, "SUCCESS")
    }

    @PostMapping("/notify-upload/{caseId}")
    @Operation(
        summary = "Notifies case document upload to admins",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @CanUploadDocument
    fun notifyUpload(
        @PathVariable("caseId") caseId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<String> {
        docRepoService.sendUploadNotification(caseId, authenticatedUser, httpServletRequest)
        return Response(true, "SUCCESS")
    }


//
//    @GetMapping("/doc")
//    @Operation(
//        summary = "Doc Repo",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun list(
//        @RequestParam("type") type: String,
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
//    ): ResponseEntity<ByteArray> {
//
//        var file = ""
//        if(type == "png"){
//            file = "png-file.png"
//        }
//        else if(type == "pdf"){
//            file = "pdf-file.pdf"
//        }
//        else if(type == "jpg"){
//            file = "jpg-file.jpg"
//        }
//        else{
//            throw ApplicationException(ErrorCode.NOT_FOUND)
//        }
//        val bytes = Thread.currentThread().contextClassLoader.getResourceAsStream(file).readAllBytes()
//
//        return ResponseEntity<ByteArray>(bytes, HttpStatus.OK)
//    }
}