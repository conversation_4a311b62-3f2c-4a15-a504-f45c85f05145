package com.centuroglobal.controller

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter

@RestController
@Tag(name = "Expert Account Management", description = "Centuro Global Expert Account API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/expert/account")
class ExpertAccountController(
    private val expertFacade: ExpertFacade
) {
    @GetMapping
    @Operation(
        summary = "List expert accounts for expert user ",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(@AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser): DeferredResult<Response<Map<String, Any>>> {
        return ResponseUtil.toDeferredResult(expertFacade.listAccounts(user.userId))
    }


}