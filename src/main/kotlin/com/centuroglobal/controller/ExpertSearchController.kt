package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Pattern

private val DEFAULT_SORT_ORDER = Sort.by(
    Sort.Order(Sort.Direction.ASC, "displayName")
)

@RestController
@Tag(name = "Expert Search", description = "Centuro Global Expert Search API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/expert-search")
class ExpertSearchController(
    private val expertFacade: ExpertFacade
) {

    @GetMapping
    @Operation(
        summary = "Search Experts",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
    fun search(
        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "User's display name to search.")
        search: String?,

        @RequestParam(name = "countryCode", required = false)
        @Parameter(name = "countryCode", required = false, description = "User's countryCode to search.")
        @Pattern(regexp = "^(|[A-Za-z]{2})$")
        countryCode: String?,

        @RequestParam(name = "expertiseId", required = false)
        @Parameter(name = "expertiseId", required = false, description = "User's expertise to search.")
        expertiseId: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int
    ): DeferredResult<Response<PagedResult<ExpertProfileSummary>>> {
        return ResponseUtil.toDeferredResult(
            expertFacade.retrieveActiveProfileSummary(
                ExpertSearchFilter.Builder.build(search, countryCode, expertiseId),
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER)
            )
        )
    }

    @GetMapping("/{userId}")
    @Operation(
        summary = "Retrieve Expert Details",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
    fun retrieve(
        @PathVariable userId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser
    ): DeferredResult<Response<ExpertProfileSummary>> {
        return ResponseUtil.toDeferredResult(
            expertFacade.retrieveProfileSummary(
                userId,
                AdminAccessUtil.hasAdminAccess(user.role)
            )
        )
    }
}
