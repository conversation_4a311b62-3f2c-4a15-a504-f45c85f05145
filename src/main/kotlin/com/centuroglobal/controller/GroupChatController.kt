package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdmin
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrPartner
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.chat.GroupChatMessageRequest
import com.centuroglobal.shared.data.pojo.chat.GroupChatMessageResponse
import com.centuroglobal.shared.data.pojo.chat.GroupChatParticipantResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.GroupChatService
import com.centuroglobal.shared.data.pojo.chat.GroupChatDetails
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*


@RestController
@Tag(name = "Group Chat", description = "Centuro Global Group chat API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/group-chat")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class GroupChatController(val groupChatService: GroupChatService) {
    @PostMapping("/message")
    @Operation(
        summary = "Post a chat Message",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun postMessage(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody groupChatMessageRequest: GroupChatMessageRequest
    ): Response<GroupChatMessageResponse> {
        val messageResponse = groupChatService.createMessage(groupChatMessageRequest, authenticatedUser)
        return Response(true, messageResponse)
    }

    @GetMapping("/message")
    @Operation(
        summary = "Retrieve a chat Message",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getMessage(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam chatType: ChatType,
        @RequestParam referenceId: Long,
        @RequestParam lastMessageDateTime: Long
    ): Response<List<GroupChatMessageResponse>> {
        return Response(true, groupChatService.getMessages(chatType, referenceId, lastMessageDateTime,
            authenticatedUser))
    }

    @DeleteMapping("/message/{messageId}/{hide}")
    @Operation(
        summary = "Deletes a chat Message",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @IsSuperAdminOrAdminOrPartner
    fun deleteMessage(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("messageId") messageId: Long,
        @PathVariable("hide") hide: Boolean
    ): Response<String> {
        groupChatService.deleteMessage(messageId, hide, authenticatedUser)
        return Response(true, "success")
    }

    @GetMapping("/participants")
    @Operation(
        summary = "Retrieve a chat Participants",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getParticipants(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam chatType: ChatType,
        @RequestParam referenceId: Long
    ): Response<GroupChatDetails> {
        return Response(true, groupChatService.getParticipants(chatType, referenceId, authenticatedUser))
    }

}