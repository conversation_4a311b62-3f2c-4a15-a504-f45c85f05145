package com.centuroglobal.controller

import com.centuroglobal.annotation.IsPartner
import com.centuroglobal.service.ClientService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.signup.UpdateAccountStatusRequest
import com.centuroglobal.shared.data.pojo.AccessPassword
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.client.ClientListing
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.Pattern
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture

@RestController
@Tag(name = "Partner Client Management", description = "Centuro Global  Partner  API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/partner")
@IsPartner
class PartnerClientController(partnerService:PartnerService,
                              clientService: ClientService
):BasePartnerClientController(partnerService, clientService)
{
//    override fun resendVerificationEmail(
//        @PathVariable userId: Long,
//        @PathVariable partnerId:Long
//    ): CompletableFuture<Any> {
//        return super.resendVerificationEmail(userId,partnerId)
//    }
//
//    override fun generateVerificationLink(
//        @PathVariable userId: Long,
//        @PathVariable partnerId: Long,
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
//    ): Response<String> {
//        return super.generateVerificationLink(userId,partnerId,authenticatedUser)
//    }
//
//    override fun accessPassword(
//        @PathVariable userId: Long,
//        @PathVariable partnerId: Long,
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
//    ): DeferredResult<Response<AccessPassword>> {
//        return super.accessPassword(userId,partnerId,authenticatedUser)
//    }
//
//    override fun listPartnerUsers(
//        @PathVariable
//        partnerId: Long?,
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
//        @RequestParam(name = "status", required = false, defaultValue = "")
//        @Parameter(
//            name = "status",
//            required = false,
//            description = "Account status to search.",
//            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION"])
//        )
//        @Pattern(regexp = "^(|ACTIVE|SUSPENDED|PENDING_VERIFICATION)$")
//        status: String,
//
//        @RequestParam(name = "userType", required = false, defaultValue = "")
//        @Parameter(
//            name = "userType",
//            required = false,
//            description = "User type to search.",
//            schema = Schema(allowableValues = ["EXPERT","CORPORATE"])
//        )
//        @Pattern(regexp = "^(|EXPERT|CORPORATE)$")
//        userType: String,
//
//        @RequestParam(name = "from", required = false)
//        @Parameter(
//            name = "from",
//            required = false,
//            description = "Created date (From) in epoch milliseconds."
//        )
//        from: Long?,
//
//        @RequestParam(name = "to", required = false)
//        @Parameter(
//            name = "to",
//            required = false,
//            description = "Created date (Until) in epoch milliseconds."
//        )
//        to: Long?,
//
//        @RequestParam(name = "search", required = false, defaultValue = "")
//        @Parameter(name = "search", required = false, description = "User's name or company name to search.")
//        search: String,
//
//        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
//        @Parameter(
//            name = "pageIndex",
//            required = false,
//            description = "Request page number (0-index). Default is 0. If value <0 it will give all results."
//        )
//        @Min(-1)
//        pageIndex: Int,
//
//        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
//        @Parameter(
//            name = "pageSize",
//            required = false,
//            description = "Requested page size. Default is 20. This value cannot be <5."
//        )
//        @Min(5)
//        pageSize: Int,
//
//        @RequestParam(name = "countryCodes", required = false, defaultValue = "")
//        @Parameter(name = "countryCodes", required = false, description = "Country of users comma separated")
//        countryCodes: String,
//
//        @RequestParam(name = "expertiseIds", required = false, defaultValue = "")
//        @Parameter(
//            name = "expertiseIds",
//            required = false,
//            description = "Expertise ids comma separated in case of userType is expert selected."
//        )
//        expertiseIds: String,
//
//        @RequestParam(name = "corporateId", required = false)
//        @Parameter(name = "corporateId", required = false, description = "CorporateId id for search ")
//        corporateId: Long?,
//        @RequestParam(name = "expertCompanyId", required = false)
//        @Parameter(name = "expertCompanyId", required = false, description = "expertCompanyId id for search ")
//        expertCompanyId: Long?,
//
//        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
//        @Parameter(
//            name = "isDownload",
//            required = false,
//            description = "is download or not."
//        )
//        isDownload: Boolean
//
//    ): Response<ClientListing> {
//        return super.listPartnerUsers(partnerId, authenticatedUser, status, userType, from, to, search, pageIndex, pageSize, countryCodes, expertiseIds, corporateId, expertCompanyId, isDownload)
//    }
    override fun updateUserStatus(
        @PathVariable userId: Long,
        @PathVariable partnerId: Long,
        @Valid @RequestBody request: UpdateAccountStatusRequest
    ): DeferredResult<Response<Any>> {
        return super.updateUserStatus(userId,partnerId,request)
    }

    override fun getUserProfiles(
        @PathVariable partnerId:Long,
        @RequestParam userIds: String
    ): List<UserProfile> {
        return partnerService.getUserProfiles(partnerId, userIds.split(",").map { it.toLong() })
    }
}