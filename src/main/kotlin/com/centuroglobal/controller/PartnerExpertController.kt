package com.centuroglobal.controller

import com.centuroglobal.annotation.IsPartner
import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.account.CreatePrimaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.CreateSecondaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateExpertUserRequest
import com.centuroglobal.shared.data.payload.partner.UpdateExpertPartnerCompanyRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Partner Expert Management", description = "Centuro Global Partner Expert API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/partner")
@IsPartner
class PartnerExpertController(private val partnerService: PartnerService,
                              private val expertUserService: ExpertUserService
) :BasePartnerExpertController(partnerService, expertUserService){

    @GetMapping("/experts")
    @Operation(
        summary = "Partner expert company listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listPartnerExperts(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Expert company's name")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "User's country to search.")
        country: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status",
            required = false,
            description = "Membership status to search.",
        )
        status: String?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean

    ): Response<ListingWithStatsDetails<ExpertList>> {

        return Response(
            true, partnerService.listExpertStats(
                authenticatedUser.partnerId,
                PartnerExpertSearchFilter.Builder.build(
                    search,
                    country,
                    status,
                    true
                ),
                PageRequest.of(
                    pageIndex,
                    if (isDownload) Int.MAX_VALUE else pageSize,
                    SearchConstant.SORT_ORDER(sortBy, sort)
                ), authenticatedUser
            )
        )
    }

    override fun updateExpertUserPartner(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable partnerId: Long,
        @PathVariable expertId: Long,
        @RequestBody @Valid updateExpertPartnerRequest: UpdateExpertUserRequest
    ): Response<Long> {
        return super.updateExpertUserPartner(authenticatedUser,partnerId ,expertId,updateExpertPartnerRequest)
    }

    override fun inviteExpert(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreatePrimaryExpertUserRequest
    ): Response<String> {
        return super.inviteExpert(partnerId, authenticatedUser, request)
    }

    @GetMapping("/{partnerId}/expert-companies")
    @Operation(
        summary = "list of expert company associated with partner",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    override fun listExpertCompany(
        @PathVariable("partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<List<ReferenceData>> {

        val corporate = partnerService.retrieveExpertCompany(partnerId)
        return Response(true,corporate)
    }

    override fun createSecondaryExpert(
        @PathVariable(value = "partnerId") partnerId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: CreateSecondaryExpertUserRequest
    ): Response<Unit> {
        return super.createSecondaryExpert(partnerId, authenticatedUser, request)
    }

    override fun retrieveExpertUser(
        @PathVariable("partnerId") partnerId: Long,
        @PathVariable("expertUserId") expertUserId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ):Response<ExpertUserResponse> {
        return super.retrieveExpertUser(partnerId, expertUserId, authenticatedUser)
    }

    override fun expertUsersByCompanyIds(
        @PathVariable("partnerId") partnerId: Long,
        @RequestParam("companyIds") companyIds: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser) : Response<List<ExpertUserReferenceData>> {
        return super.expertUsersByCompanyIds(partnerId, companyIds, authenticatedUser)
    }
    override fun updateExpertCompany(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable partnerId: Long,
        @PathVariable expertCompanyId: Long,
        @RequestBody @Valid updateExpertPartnerRequest: UpdateExpertPartnerCompanyRequest
    ): Response<Long> {
        return super.updateExpertCompany(authenticatedUser, partnerId, expertCompanyId, updateExpertPartnerRequest)
    }

    @GetMapping("/{companyId}/secondary")
    @Operation(
        summary = "Retrieve Secondary Experts",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieveSecondaryExpertList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable companyId: Long
    ): Response<MutableList<Client>> {
        return Response(true,expertUserService.retrieveSecondaryExpert(companyId,authenticatedUser.partnerId ))
    }

    @GetMapping("/experts/users")
    @Operation(
        summary = "expert user listing based on filters",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun listExpertUser(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false)
        @Parameter(name = "search", required = false, description = "Search expert user's name or email")
        search: String?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "expert user's country to search.")
        countryCode: String?,

        @RequestParam(name = "companyId", required = false)
        @Parameter(name = "companyId", required = false, description = "expert user's company-id to search.")
        companyId: Long?,

        @RequestParam(name = "expertType", required = false)
        @Parameter(name = "expertType", required = false, description = "expert user's expert-type to search.")
        expertType: String?,

        @RequestParam(name = "status", required = false)
        @Parameter(
            name = "status", required = false, description = "status to search.",
            schema = Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"])
        )
        status: String?,

        @RequestParam(name = "joinedFrom", required = false, defaultValue = "")
        @Parameter(name = "joinedFrom", required = false, description = "Starting date from which corporate user has joined.")
        joinedFrom: Long?,

        @RequestParam(name = "joinedTo", required = false, defaultValue = "")
        @Parameter(name = "joinedTo", required = false, description = "Date up to which corporate user had joined.")
        joinedTo: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean

    ): Response<ListingWithStatsDetails<ExpertUserDetails>?> {

        return Response(true, expertUserService.listExpertUser(
            ExpertUserDetailsFilter.Builder.build(
                search,
                countryCode,
                companyId,
                expertType,
                status,
                joinedFrom,
                joinedTo,
                true,
                authenticatedUser.partnerId,
                null,
                null,
                null
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }
}