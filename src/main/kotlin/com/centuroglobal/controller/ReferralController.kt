package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporate
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.data.payload.referral.CreateReferralCodeRequest
import com.centuroglobal.shared.data.pojo.ReferralResponse
import com.centuroglobal.shared.data.pojo.client.ClientListing
import com.centuroglobal.facade.AdminClientFacade
import com.centuroglobal.facade.ReferralFacade
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.ResponseUtil
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.async.DeferredResult
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.constraints.Min

@RestController
@Tag(name = "Client Referral", description = "Centuro Global Referral API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/referral")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class ReferralController(
    private val referralFacade: ReferralFacade,
    private val adminClientFacade: AdminClientFacade
) {

    @PostMapping("/user-referral-code")
    @Operation(
        summary = "Get User Referral Code",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getUserReferralCode(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody request: CreateReferralCodeRequest
    ): DeferredResult<Response<ReferralResponse>> {
        var userId: Long = request.userId ?: authenticatedUser.userId
        if (userId == -1L) {
            userId = authenticatedUser.userId
        }
        return ResponseUtil.toDeferredResult(referralFacade.getReferralCode(userId, authenticatedUser.userId))
    }

    private val DEFAULT_SORT_ORDER = Sort.by(
        Sort.Order(Sort.Direction.DESC, "createdDate"),
        Sort.Order(Sort.Direction.ASC, "fullName", Sort.NullHandling.NULLS_LAST)
    )

    @GetMapping("/referral-listing/{referredBy}")
    @Operation(
        summary = "Referred Clients",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun list(
        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,

        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @PathVariable referredBy: Long
    ): DeferredResult<Response<ClientListing>> {
        return ResponseUtil.toDeferredResult(
            adminClientFacade.listClientsReferredBy(
                referredBy,
                PageRequest.of(pageIndex, pageSize, DEFAULT_SORT_ORDER)
            )
        )
    }
}
