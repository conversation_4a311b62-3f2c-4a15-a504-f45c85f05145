package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.data.payload.RfpRequest
import com.centuroglobal.service.RfpService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.RfpStatus
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.query.ProposalCardDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@Tag(name = "Proposals", description = "Centuro Global Rfp")
@RequestMapping("/api/${AppConstant.API_VERSION}/rfp")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner

open class RfpController(val rfpService: RfpService) {
    @PostMapping
    @Operation(
        summary = "create a draft RFP",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun generateRfpId(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<Long> {
        val rfpId = rfpService.createDraftRfp(authenticatedUser)
        return Response(true, rfpId)
    }

    @PutMapping
    @Operation(
        summary = "Create a rfp",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun postQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestBody rfpRequest: RfpRequest
    ): Response<Long> {
        val queryId = rfpService.createRfp(rfpRequest, authenticatedUser)
        return Response(true, queryId)
    }

    @GetMapping
    @Operation(
        summary = "Fetch all the rfp of the user based on search",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getQueries(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "partnerId", required = false, defaultValue = "")
        @Parameter(name = "partnerId", required = false, description = "")
        @PathVariable partnerId: Long?,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "")
        search: String?,

        @RequestParam(name = "account", required = false, defaultValue = "")
        @Parameter(name = "account", required = false, description = "based on account fetch all users and fetch rfp of them")
        account: String?,

        @RequestParam(name = "user", required = false, defaultValue = "")
        @Parameter(name = "user", required = false, description = "")
        user: String?,

        @RequestParam(name = "responses", required = false, defaultValue = "")
        @Parameter(name = "responses", required = false, description = "")
        responses: String?,

        @RequestParam(name = "categories", required = false, defaultValue = "")
        @Parameter(name = "categories", required = false, description = "")
        category: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "")
        country: String?,

        @RequestParam(name = "archive", required = false, defaultValue = "")
        @Parameter(name = "archive", required = false, description = "")
        archive: Boolean?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "lastUpdatedDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,


        @RequestParam(name = "isPartner", required = false)
        @Parameter(name = "isPartner", required = false, description = "Partner RFP")
        isPartner: Boolean?,

        @RequestParam(name = "cgRequested", required = false)
        @Parameter(name = "cgRequested", required = false, description = "CG Requested cases")
        cgRequested: Boolean?,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean

    ): Response<PagedResult<RfpCardDetails>> {
        val status = if(archive == null) null else if(archive) (listOf(RfpStatus.RESOLVED, RfpStatus.CANCELLED)) else listOf(
            RfpStatus.OPEN)

        return Response(true, rfpService.listRfp(
            RfpSearchFilter.Builder.build(
                search,
                account,
                user,
                responses,
                category,
                country,
                status,
                null,
                null,
                null,
                partnerId,
                cgRequested,
                isPartner
            ),
            PageRequest.of(pageIndex, if(isDownload) Int.MAX_VALUE else pageSize, SearchConstant.SORT_ORDER(sortBy, sort)), authenticatedUser)
        )
    }

    @GetMapping("/{rfpId}")
    @Operation(
        summary = "Retrieve a rfp by id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getQueryById(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long
    ): Response<RfpCardDetails> {
        return Response(true, rfpService.getRfpById(rfpId, authenticatedUser))
    }

    @GetMapping("/{rfpId}/documents")
    @Operation(
        summary = "GET list of documents of RFP - based on rfp Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getDocumentsByRfp(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long
    ): Response<List<RfpDocumentCardDetails>> {
        return Response(true, rfpService.getDocumentsByRfp(rfpId, authenticatedUser))
    }

    @GetMapping("/{rfpId}/document/{documentId}/download")
    @Operation(
        summary = "Download document",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun download(
        @PathVariable("rfpId") rfpId: Long,
        @PathVariable("documentId") documentId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): ResponseEntity<StreamingResponseBody> {
        return rfpService.downloadDocument(rfpId, documentId, authenticatedUser)
    }




    @GetMapping("/{rfpId}/document/{documentId}/view")
    @Operation(
        summary = "View document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun viewDocument(
        @PathVariable("rfpId") rfpId: Long,
        @PathVariable("documentId") documentId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<String> {
        return Response(true, rfpService.viewDocumentUrl(rfpId, documentId, authenticatedUser))
    }


    @PutMapping("/{rfpId}")
    @Operation(
        summary = "Update Rfp status",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateRfpStatus(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long,
        @Schema(required = true, allowableValues = ["OPEN", "RESOLVED", "CANCELLED"])
        @RequestParam("status") rfpStatus: RfpStatus
    ): Response<Boolean> {
        return Response(true, rfpService.updateStatus(rfpId, rfpStatus, authenticatedUser))
    }


    @PostMapping(value= ["/{rfpId}/document"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(
        summary = "Submit a RFP document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun postDocument(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long,
        @RequestParam("fileName") fileName: String,
        @RequestParam("fileType") fileType: String,
        @RequestParam("fileSize") fileSize: Long,
        @RequestParam("file") file: MultipartFile
    ): Response<Long> {
        return Response(true, rfpService.createDocument(rfpId, fileName, fileType, fileSize, file, authenticatedUser))
    }


    @DeleteMapping("/{rfpId}/document/{documentId}")
    @Operation(
        summary = "Delete a Rfp Document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun deleteDocument(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long,
        @PathVariable("documentId") documentId: Long
    ): Response<Boolean> {
        return Response(true, rfpService.deleteDocument(rfpId, documentId, authenticatedUser))
    }

    @PutMapping("/{rfpId}/approve")
    @Operation(
        summary = "Approve Rfp",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun approveQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long
    ): Response<Boolean> {
        return Response(true, rfpService.approveRfpProposals(rfpId, authenticatedUser))
    }

    @GetMapping("/{rfpId}/proposals")
    @Operation(
        summary = "GET list of proposals - based on rfp Id",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getProposalsByQuery(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable("rfpId") rfpId: Long
    ): Response<List<ProposalCardDetails>> {
        return Response(true, rfpService.getProposalsByRfp(rfpId, authenticatedUser))
    }


    @GetMapping("/{rfpId}/proposals/{proposalId}/download")
    @Operation(
        summary = "Download proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun downloadProposal(
        @PathVariable("rfpId") rfpId: Long,
        @PathVariable("proposalId") proposalId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): ResponseEntity<StreamingResponseBody> {
        return rfpService.downloadProposal(rfpId, proposalId, authenticatedUser)
    }


    @GetMapping("/{rfpId}/proposals/{proposalId}/view")
    @Operation(
        summary = "View proposal",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun viewProposal(
        @PathVariable("rfpId") rfpId: Long,
        @PathVariable("proposalId") proposalId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        httpServletRequest: HttpServletRequest
    ): Response<String> {
        return Response(true, rfpService.viewProposalUrl(rfpId, proposalId, authenticatedUser))
    }

}