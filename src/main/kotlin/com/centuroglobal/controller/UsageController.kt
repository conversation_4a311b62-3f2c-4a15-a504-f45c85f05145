package com.centuroglobal.controller

import com.centuroglobal.service.BlueprintService
import com.centuroglobal.service.UsageService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UsageLogSearchFilter
import com.centuroglobal.shared.data.pojo.subscription.usage.CostOfLivingUsageResponse
import com.centuroglobal.shared.data.pojo.usage.AIChatUsageLogResponse
import com.centuroglobal.shared.data.pojo.usage.AIChatUsageLogSearchFilter
import com.centuroglobal.shared.data.pojo.usage.BlueprintUsageLogResponse
import com.centuroglobal.shared.data.pojo.usage.DocRepoUsageLogResponse
import com.centuroglobal.shared.data.pojo.usage.DocRepoUsageLogSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Usage", description = "Centuro Global Usage API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/usage")
class UsageController(
    private val usageService: UsageService,
    private val blueprintService: BlueprintService
) {

    @PutMapping("{sessionId}/close")
    @Operation(
        summary = "End a Session",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun closeSession(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable sessionId: String
    ): Response<Boolean> {
        return Response(true, usageService.closeSession(sessionId, authenticatedUser))
    }

    @PutMapping("close")
    @Operation(
        summary = "End a Session for feature",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun closeSessionByType(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @RequestParam feature: String,
        @RequestParam sessionId: String
    ): Response<Boolean> {
        return Response(true, usageService.closeSessionByType(feature, sessionId, authenticatedUser))
    }

    @GetMapping("/blueprint")
    @Operation(
        summary = "Blueprint usage listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun blueprint(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "country to search.")
        country: String?,

        @RequestParam(name = "corporate", required = false)
        @Parameter(name = "corporate", required = false, description = "Corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "partner id to search.")
        partnerId: Long?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "From date to search")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "To date to search")
        to: Long?,

        @RequestParam(name = "isPartner", required = false, defaultValue = "false")
        @Parameter(
            name = "isPartner",
            required = false,
            description = "Partner logs."
        )
        isPartner: Boolean = false,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<BlueprintUsageLogResponse>> {
        val pageRequest = PageRequest.of(
            pageIndex,
            if (isDownload) Int.MAX_VALUE else pageSize,
            SearchConstant.SORT_ORDER(sortBy, sort)
        )

        val filter = UsageLogSearchFilter.Builder.build(country, corporate, from, to, isPartner, partnerId)
        return Response(true, blueprintService.listUsage(filter, pageRequest))
    }

    @GetMapping("/ai-chat")
    @Operation(
        summary = "AI Chat usage listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun aiChat(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "question", required = false)
        @Parameter(name = "question", required = false, description = "Question to search.")
        question: String?,

        @RequestParam(name = "band", required = false)
        @Parameter(name = "band", required = false, description = "Band to search.")
        band: String?,

        @RequestParam(name = "corporate", required = false)
        @Parameter(name = "corporate", required = false, description = "Corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "partner id to search.")
        partnerId: Long?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "From date to search")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "To date to search")
        to: Long?,

        @RequestParam(name = "isPartner", required = false, defaultValue = "false")
        @Parameter(
            name = "isPartner",
            required = false,
            description = "Partner logs."
        )
        isPartner: Boolean = false,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<AIChatUsageLogResponse>> {
        val pageRequest = PageRequest.of(
            pageIndex,
            if (isDownload) Int.MAX_VALUE else pageSize,
            SearchConstant.SORT_ORDER(sortBy, sort)
        )

        val filter = AIChatUsageLogSearchFilter.Builder.build(question, corporate, from, to, isPartner, partnerId)
        val result = usageService.aiChat(filter, pageRequest)
        return Response(true, result)
    }

    @GetMapping("/doc-repo")
    @Operation(
        summary = "Documents repository usage listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun documentRepository(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "docType", required = false)
        @Parameter(name = "docType", required = false, description = "Document type to search.")
        docType: String?,

        @RequestParam(name = "sizeFrom", required = false)
        @Parameter(name = "sizeFrom", required = false, description = "Size from to search.")
        sizeFrom: Long?,

        @RequestParam(name = "sizeTo", required = false)
        @Parameter(name = "sizeTo", required = false, description = "Size to to search.")
        sizeTo: Long?,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "Country to search.")
        country: String?,

        @RequestParam(name = "corporate", required = false)
        @Parameter(name = "corporate", required = false, description = "Corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "partner id to search.")
        partnerId: Long?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "From date to search")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "To date to search")
        to: Long?,

        @RequestParam(name = "isPartner", required = false, defaultValue = "false")
        @Parameter(
            name = "isPartner",
            required = false,
            description = "Partner logs."
        )
        isPartner: Boolean = false,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<DocRepoUsageLogResponse>> {
        val pageRequest = PageRequest.of(
            pageIndex,
            if (isDownload) Int.MAX_VALUE else pageSize,
            SearchConstant.SORT_ORDER(sortBy, sort)
        )
        val filter = DocRepoUsageLogSearchFilter.Builder.build(docType, sizeFrom, sizeTo, country,
             corporate, from, to, isPartner, partnerId)
        val result = usageService.docRepo(filter, pageRequest)
        return Response(true, result)
    }

    @GetMapping("/cost-of-living")
    @Operation(
        summary = "Cost of living usage listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun costOfLiving(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "country", required = false)
        @Parameter(name = "country", required = false, description = "country to search.")
        country: String?,

        @RequestParam(name = "corporate", required = false)
        @Parameter(name = "corporate", required = false, description = "Corporate id to search.")
        corporate: Long?,

        @RequestParam(name = "partnerId", required = false)
        @Parameter(name = "partnerId", required = false, description = "partner id to search.")
        partnerId: Long?,

        @RequestParam(name = "from", required = false, defaultValue = "")
        @Parameter(name = "from", required = false, description = "From date to search")
        from: Long?,

        @RequestParam(name = "to", required = false, defaultValue = "")
        @Parameter(name = "to", required = false, description = "To date to search")
        to: Long?,

        @RequestParam(name = "isPartner", required = false, defaultValue = "false")
        @Parameter(
            name = "isPartner",
            required = false,
            description = "Partner logs."
        )
        isPartner: Boolean = false,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean
    ): Response<PagedResult<CostOfLivingUsageResponse>> {
        val pageRequest = PageRequest.of(
            pageIndex,
            if (isDownload) Int.MAX_VALUE else pageSize,
            SearchConstant.SORT_ORDER(sortBy, sort)
        )
        val filter = UsageLogSearchFilter.Builder.build(country, corporate, from, to, isPartner, partnerId)
        return Response(true, usageService.costOfLiving(filter, pageRequest))
    }
}
