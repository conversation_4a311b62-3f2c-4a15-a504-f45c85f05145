package com.centuroglobal.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.data.payload.UserActionRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.UserActionService
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement

import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.Valid

@RestController
@Tag(name = "User Action Management", description = "Centuro Global User Action API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/userAction/")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartnerOrSupplier
class UserActionController(
    private val userActionService: UserActionService
) {
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    @Operation(
        summary = "Add User Action",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun createList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @Valid @RequestBody request: List<UserActionRequest>
    ): Response<Boolean> {
        return Response(true, userActionService.addList(request, authenticatedUser.userId))
    }

//    @GetMapping
//    @ApiOperation(
//        value = "Retrieve User Action",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun retrieve(): Response<MutableList<UserActionEntity>> {
//        return Response(true, userActionService.retrieve())
//    }
}
