package com.centuroglobal.controller


import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.ClientDocType
import com.centuroglobal.shared.data.enums.UserDocType
import com.centuroglobal.shared.data.payload.*
import com.centuroglobal.shared.data.pojo.CorporateDocumentResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserDocumentSearchFilter
import com.centuroglobal.shared.data.pojo.passportvisa.DocumentMetadataGeneric
import com.centuroglobal.shared.data.pojo.passportvisa.GetPassportResponse
import com.centuroglobal.shared.data.pojo.passportvisa.GetVisaResponse
import com.centuroglobal.shared.data.pojo.passportvisa.PassportVisaResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.core.JsonProcessingException
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.constraints.Min
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@Tag(name = "User Profile Management", description = "Centuro Global User Profile API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/user-profile")
class UserProfileController(
    private val adminUserService: AdminUserService,
    private val corporateService: CorporateService
) {

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/doc"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Operation(
        summary = "Upload user documents",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "USER_UPLOAD_DOC")
    fun uploadDocs(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @LogValue @RequestBody request: UserDocumentRequest
    ): Response<Boolean> {

        if(!corporateService.canUpdateUser(request.userId, user)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.uploadDocumentUser(request, user, request.userId))
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/passport"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Operation(
        summary = "Upload user passport",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "USER_UPLOAD_PASSPORT")
    fun updatePassport(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @LogValue @RequestBody request: PassportDocumentRequest
    ): Response<Boolean> {

        if(!corporateService.canUpdateUser(request.userId, user)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.updatePassport(request, user, request.userId))
    }

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(value = ["/visa"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Operation(
        summary = "Upload user visa",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "USER_UPLOAD_VISA")
    fun updateVisa(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @LogValue @RequestBody request: VisaDocumentRequest
    ): Response<Boolean> {

        if(!corporateService.canUpdateUser(request.userId, user)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.updateVisa(request, user, request.userId))
    }


    @PostMapping(value = ["/extract-doc"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Operation(
        summary = "Extract data from document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    @UserAction(action = "EXTRACT_DOC_DATA")
    fun extractDocsData(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @LogValue @RequestBody request: ExtractDocDetailsRequest
    ): Response<DocumentMetadataGeneric> {
        try {
        var respBody = adminUserService.extractDocumentMetadataResponse(request)
            return Response(true, respBody)
        } catch (e: JsonProcessingException) {
            e.printStackTrace()
            throw ApplicationException(ErrorCode.CAN_NOT_EXTRACT_DOC_DETAILS)
        }
    }

    @GetMapping("/passport-visa/{userId}")
    @Operation(
        summary = "passport visa listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun docList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long
        ): Response<PassportVisaResponse> {

        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(
            true, adminUserService.listPassportVisa(authenticatedUser, userId)
        )
    }

    @GetMapping("/{userId}/passport/{passportId}")
    @Operation(
        summary = "Retrieve passport",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrievePassport(
        @PathVariable passportId: Long,
        authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long
    ): Response<GetPassportResponse> {

        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.retrievePassport(authenticatedUser, passportId, userId))
    }

    @GetMapping("/{userId}/visa/{visaId}")
    @Operation(
        summary = "Retrieve visa",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun retrieveVisa(
        @PathVariable visaId: Long,
        authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long
    ): Response<GetVisaResponse> {

        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.retrieveVisa(authenticatedUser, visaId, userId))
    }

    @GetMapping("/docs/{reqUserId}")
    @Operation(
        summary = "User documents listing",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun docList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,

        @RequestParam(name = "search", required = false, defaultValue = "")
        @Parameter(name = "search", required = false, description = "Document name to search.")
        search: String?,

        @RequestParam(name = "country", required = false, defaultValue = "")
        @Parameter(name = "country", required = false, description = "Country of Document.")
        country: String?,

        @RequestParam(name = "uploadedType", required = false, defaultValue = "")
        @Parameter(name = "uploadedType", required = false, description = "File type of Document.")
        uploadedType: String?,

        @RequestParam(name = "createdBy", required = false, defaultValue = "")
        @Parameter(name = "createdBy", required = false, description = "Uploaded user.")
        createdBy: Long?,

        @RequestParam(name = "userId", required = false, defaultValue = "")
        @Parameter(name = "UserId", required = false, description = "User id.")
        userId: Long?,

        @RequestParam(name = "pageIndex", required = false, defaultValue = "0")
        @Parameter(
            name = "pageIndex",
            required = false,
            description = "Request page number (0-index). Default is 0. This value cannot be <0."
        )
        @Min(0)
        pageIndex: Int,
        @RequestParam(name = "pageSize", required = false, defaultValue = "20")
        @Parameter(
            name = "pageSize",
            required = false,
            description = "Requested page size. Default is 20. This value cannot be <5."
        )
        @Min(5)
        pageSize: Int,
        @RequestParam(name = "sort", required = false, defaultValue = "DESC")
        @Parameter(
            name = "sort",
            required = false,
            description = "Sort order"
        )
        sort: String,

        @RequestParam(name = "sortBy", required = false, defaultValue = "createdDate")
        @Parameter(
            name = "sortBy",
            required = false,
            description = "Column to sort by."
        )
        sortBy: String,

        @RequestParam(name = "isDownload", required = false, defaultValue = "false")
        @Parameter(
            name = "isDownload",
            required = false,
            description = "is download or not."
        )
        isDownload: Boolean,

        @PathVariable reqUserId: Long
    ): Response<PagedResult<CorporateDocumentResponse>> {

        if(!corporateService.canUpdateUser(reqUserId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(
            true, adminUserService.listDocuments(
                UserDocumentSearchFilter.Builder.build(
                    search,
                    country,
                    createdBy,
                    reqUserId,
                    UserDocType.USER,
                    uploadedType,
                    ClientDocType.USER
                ),
                PageRequest.of(
                    pageIndex,
                    if (isDownload) Int.MAX_VALUE else pageSize,
                    SearchConstant.SORT_ORDER(sortBy, sort)
                ),
                authenticatedUser, false
            )
        )
    }

    @DeleteMapping(value = ["{userId}/doc/{id}"])
    @Operation(
        summary = "Delete user document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun deleteDoc(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @PathVariable id: Long,
        @PathVariable userId: Long
    ): Response<Boolean> {

        if(!corporateService.canUpdateUser(userId, user)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.deleteDocumentUser(id, user, userId))
    }

    @DeleteMapping(value = ["{userId}/passport-visa/{docId}"])
    @Operation(
        summary = "Delete passport visa",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun deletePasssportVisa(
        @AuthenticationPrincipal @Parameter(hidden = true) user: AuthenticatedUser,
        @PathVariable docId: Long,
        @PathVariable userId: Long
    ): Response<Boolean> {

        if(!corporateService.canUpdateUser(userId, user)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.deletePassportVisa(docId, user, userId))
    }

    @GetMapping("{userId}/view-doc/{fileId}")
    @Operation(
        summary = "View User document",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun viewUrl(
        @PathVariable("fileId") fileId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long
    ): Response<String> {

        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return Response(true, adminUserService.getDocUrlUser(fileId, authenticatedUser, userId))
    }

    @GetMapping("/view-passport-visa")
    @Operation(
        summary = "View Passport Visa",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun viewPassportVisa(
        @RequestParam("s3key") s3key: String,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
    ): Response<String> {
        return Response(true, adminUserService.viewPassportVisa(s3key))
    }


    @GetMapping("/{userId}/passport-visa/{docId}")
    @Operation(
        summary = "Download Passport/visa",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun downloadPassportVisa(
        @PathVariable docId: Long,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long
    ): ResponseEntity<StreamingResponseBody> {

        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return adminUserService.downloadPassportVisaFiles(docId, authenticatedUser, userId)
    }

    @GetMapping("/download-doc/{userId}")
    @Operation(
        summary = "Download User document",
        responses = [ApiResponse(content = [Content(mediaType = "application/octet-stream")])],
        security = [SecurityRequirement(name = "jwt")]
    )
    fun downloadDoc(
        @RequestParam("fileId") fileId: Long?,
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @PathVariable userId: Long
    ): ResponseEntity<StreamingResponseBody> {

        if(!corporateService.canUpdateUser(userId, authenticatedUser)){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        return adminUserService.downloadDocUser(fileId, authenticatedUser, userId)
    }

}