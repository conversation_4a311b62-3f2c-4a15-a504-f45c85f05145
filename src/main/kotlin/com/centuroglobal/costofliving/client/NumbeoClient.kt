package com.centuroglobal.costofliving.client

import com.centuroglobal.costofliving.data.payload.CityList
import com.centuroglobal.costofliving.data.payload.CityPriceList
import com.centuroglobal.costofliving.data.payload.ExchangeRateList
import com.centuroglobal.data.payload.dashboard.CountryIndices
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam

@FeignClient(name = "NumbeoClient", url = "https://www.numbeo.com/")
interface NumbeoClient {

    @RequestMapping("/api/cities")
    fun findAllCity(@RequestParam(name = "api_key") apiKey: String, @RequestParam("country") country: String): CityList

    @RequestMapping("/api/currency_exchange_rates")
    fun findAllExchangeRates(@RequestParam(name = "api_key") apiKey: String): ExchangeRateList

    @RequestMapping("/api/city_prices")
    fun findAllCityPrice(@RequestParam(name = "api_key") apiKey: String, @RequestParam("city_id") cityId: Long, @RequestParam("currency") currency: String): CityPriceList

    @RequestMapping("/api/country_prices")
    fun findAllCountryPrice(@RequestParam(name = "api_key") apiKey: String, @RequestParam("country") country: String, @RequestParam("currency") currency: String): CityPriceList

    @RequestMapping("/api/country_indices")
    fun findAllCountryIndices(@RequestParam(name = "api_key") apiKey: String, @RequestParam("country") country: String): CountryIndices
}