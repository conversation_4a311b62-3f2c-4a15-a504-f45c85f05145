package com.centuroglobal.costofliving.controller

import com.centuroglobal.annotation.IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
import com.centuroglobal.annotation.LogValue
import com.centuroglobal.annotation.UserAction
import com.centuroglobal.costofliving.data.entity.CityEntity
import com.centuroglobal.costofliving.data.entity.ExchangeRateEntity
import com.centuroglobal.costofliving.data.payload.CityPriceDto
import com.centuroglobal.costofliving.service.CostOfLivingService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.AppConstant.REQUEST_ID_HEADER
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.security.AuthenticatedUser
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import org.springframework.security.core.annotation.AuthenticationPrincipal

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "CostOfLiving", description = "Centuro Cost Of Living API v1")
@RequestMapping("/api/${AppConstant.API_VERSION}/costofliving")
@IsSuperAdminOrAdminOrExpertOrCorporateSubscriberOrCorporateOrPartner
@Validated
class CostOfLivingController(
    private val costOfLivingService: CostOfLivingService
) {

    @GetMapping("/city")
    @Operation(
        summary = "Get City list",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    @UserAction(action = "COST_OF_LIVING")
    fun getCityList(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestParam(name = "countryCode", required = true)
        countryCode: String,
        @LogValue @RequestAttribute(value = REQUEST_ID_HEADER, required = false) @Parameter(hidden = true) requestId: String?
    ): Response<List<CityEntity>> {
        val cityList = costOfLivingService.getCityList(countryCode)
        return Response(true, cityList)
    }

    @PostMapping("/exchangeRates")
    @Operation(
        summary = "Get Exchange Rates list",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun updateExchangeRatesList(
    ): Response<List<ExchangeRateEntity>> {
        val exchangeRatesList = costOfLivingService.updateExchangeRatesList()
        return Response(true, exchangeRatesList)
    }

    @GetMapping("/cityPrice")
    @Operation(
        summary = "Get City Price",
        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
        security = [SecurityRequirement(name =  "jwt")]
    )
    fun getCityPrice(
        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser,
        @LogValue @RequestParam(name = "sourceCountryCode", required = true) sourceCountryCode: String,
        @RequestParam(name = "sourceCityId", required = false, defaultValue = "99999") sourceCityId: String,
        @RequestParam(name = "currency", required = false, defaultValue = "USD") currency: String,
        @LogValue @RequestParam(name = "targetCountryCode", required = false, defaultValue = "") targetCountryCode: String,
        @RequestParam(name = "targetCityId", required = false, defaultValue = "99999") targetCityId: String
    ): Response<MutableMap<String?, MutableList<CityPriceDto>?>> {
        val cityPriceList = costOfLivingService.getCityPrice(
            sourceCountryCode,
            sourceCityId.toLong(),
            currency,
            targetCountryCode,
            targetCityId.toLong()
        )
        return Response(true, cityPriceList)
    }
}