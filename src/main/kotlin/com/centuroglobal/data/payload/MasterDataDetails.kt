package com.centuroglobal.data.payload

import com.centuroglobal.shared.data.entity.MasterDataEntity
import java.time.ZoneId

data class MasterDataDetails(

    var templateType: String,

    var title: String,

    var lastUpdatedOn: Long? = null,

    var lastUpdatedBy: String,

    var actionType: String,

    var displayOrder: Long
) {
    object ModelMapper {
        fun from(data: MasterDataEntity): MasterDataDetails {
            return MasterDataDetails(
                templateType = data.docType,
                title = data.docName,
                lastUpdatedOn = data.lastUploadDate?.atZone(ZoneId.systemDefault())?.toInstant()?.epochSecond?.let { it * 1000 },
                lastUpdatedBy = data.lastUpdateByName,
                actionType = data.actionType,
                displayOrder = data.displayOrder
            )
        }
    }
}