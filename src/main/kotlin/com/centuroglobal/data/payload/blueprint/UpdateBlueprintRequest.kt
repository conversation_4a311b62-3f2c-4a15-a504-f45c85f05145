package com.centuroglobal.data.payload.blueprint

import com.centuroglobal.shared.data.pojo.StepRequest
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Pattern

data class UpdateBlueprintRequest(
    @Pattern(regexp = "^(SAVE|PUBLISH|DISCARD|INACTIVE|ACTIVE)$")
    @Schema(
        required = true,
        allowableValues = ["SAVE|PUBLISH|DISCARD|INACTIVE|ACTIVE"]
    )
    val action: String,

    @Schema()
    val steps: List<StepRequest>? = null
)

