package com.centuroglobal.data.payload.case

import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.fasterxml.jackson.databind.JsonNode

data class CaseFormRequest(

    val name: String,

    val description: String,

    val countries: List<String>,

    val category: String,

    val fields: JsonNode?,

    val visibility: TaskVisibility,

    val status: CaseFormStatus,

    val fieldCount: Long?,

    val defaultDocumentList: List<String>?,

    val isDefault: Boolean
)