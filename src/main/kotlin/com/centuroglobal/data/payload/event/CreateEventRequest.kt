package com.centuroglobal.data.payload.event

import com.centuroglobal.shared.data.enums.EventStatus
import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.format.annotation.DateTimeFormat
import java.util.*
import jakarta.validation.constraints.NotBlank

data class CreateEventRequest(
    var id: Long? = null,
    @field:NotBlank
    val name: String,
    val about: String,

    var location: String?,

    var locationDetails: String?,

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val startDate: Date,
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val endDate: Date,
    @DateTimeFormat(pattern = "hh:mm a")
    @JsonFormat(pattern = "hh:mm a")
    val startTime: Date,
    @DateTimeFormat(pattern = "hh:mm a")
    @JsonFormat(pattern = "hh:mm a")
    val endTime: Date,
    val timeZone: String,
    val status: EventStatus
)
