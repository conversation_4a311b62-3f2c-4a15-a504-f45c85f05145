package com.centuroglobal.data.pojo

import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs

data class UpdatePartnerRequest(
    
    val name: String?,
    val country: String?,
    val startDate: Long,
    val endDate: Long,
    val casesManagedBy : PartnerCaseType,
    val queryManagedBy : PartnerCaseType,
    val primaryColor: String,
    val secondaryColor: String,
    var rootUserDetails: UpdatePartnerUserDetails?,
    val features:List<String>? = mutableListOf(),
    val companyLogo: String?,
    val onboardingDocs: List<OnboardingDocs>? = null,
    val corporateFeatures: List<String>
)
