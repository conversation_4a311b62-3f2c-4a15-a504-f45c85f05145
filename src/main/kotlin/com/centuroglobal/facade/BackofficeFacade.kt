package com.centuroglobal.facade

import com.centuroglobal.shared.data.payload.account.CreateAdminUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateAdminUserRequest
import com.centuroglobal.shared.data.pojo.AdminUserSearchFilter
import com.centuroglobal.shared.data.pojo.BackofficeUser
import com.centuroglobal.shared.data.pojo.BackofficeUserList
import com.centuroglobal.service.BackofficeUserService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class BackofficeFacade(private val backofficeUserService: BackofficeUserService) {

    @Async
    @Throws(InterruptedException::class)
    fun retrieveBackofficeUsers(search: AdminUserSearchFilter): CompletableFuture<List<BackofficeUserList>> {
        return CompletableFuture.completedFuture(backofficeUserService.retrieveBackofficeUsers(search))
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieveBackofficeUser(userId: Long): CompletableFuture<BackofficeUser> {
        return CompletableFuture.completedFuture(backofficeUserService.retrieveBackofficeUser(userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun updateBackofficeUser(
        userId: Long,
        requesterUserId: Long,
        request: UpdateAdminUserRequest
    ): CompletableFuture<BackofficeUser> {
        return CompletableFuture.completedFuture(
            backofficeUserService.updateBackofficeUser(
                userId,
                requesterUserId,
                request
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun createBackofficeUser(
        requesterUserId: Long,
        request: CreateAdminUserRequest
    ): CompletableFuture<BackofficeUser> {
        return CompletableFuture.completedFuture(
            backofficeUserService.createBackofficeUser(
                requesterUserId,
                request
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun resend(userId: Long): CompletableFuture<String> {
        return CompletableFuture.completedFuture(backofficeUserService.resend(userId))
    }

}
