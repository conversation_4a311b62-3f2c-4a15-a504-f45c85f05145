package com.centuroglobal.facade

import com.centuroglobal.shared.data.payload.ConciergeRequest
import com.centuroglobal.service.ConciergeService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class ConciergeFacade(private val conciergeService: ConciergeService) {

    @Async
    @Throws(InterruptedException::class)
    fun post(request: ConciergeRequest, userId: Long): CompletableFuture<String> {
        return CompletableFuture.completedFuture(conciergeService.createNotification(request, userId))
    }
}
