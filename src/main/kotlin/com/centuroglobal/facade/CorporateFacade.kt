package com.centuroglobal.facade

import com.centuroglobal.service.CorporateService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.payload.account.AddAccountRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateProfileRequest
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class CorporateFacade(private val corporateService: CorporateService) {

    @Async
    @Throws(InterruptedException::class)
    fun retrieve(userId: Long): CompletableFuture<Corporate> {
        return CompletableFuture.completedFuture(corporateService.retrieveCorporate(userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun update(
        corporateUserId: Long,
        requesterUserId: Long,
        requesterUserType: String,
        profileRequest: UpdateCorporateProfileRequest
    ): CompletableFuture<Corporate> {
        return CompletableFuture.completedFuture(
            corporateService.updateCorporate(corporateUserId, requesterUserId, requesterUserType, profileRequest)
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun signUp(request: SignUpRequest): CompletableFuture<String> {
        return CompletableFuture.completedFuture(doSignUp(request))
    }

    private fun doSignUp(request: SignUpRequest): String {
        corporateService.createCorporate(request)
        return AppConstant.SUCCESS_RESPONSE_STRING
    }

    @Async
    @Throws(InterruptedException::class)
    fun resend(corporateUserId: Long): CompletableFuture<String> {
        return CompletableFuture.completedFuture(corporateService.resend(corporateUserId))
    }

    /*@Async
    @Throws(InterruptedException::class)
    fun retrieveCorporateUsers(): CompletableFuture<List<CorporateUsers>>? {
        return CompletableFuture.completedFuture(corporateService.retrieveCorporateUsers())
    }*/

    @Async
    @Throws(InterruptedException::class)
    fun addAccount(request: AddAccountRequest,corporateId: Long,authenticatedUser: AuthenticatedUser): CompletableFuture<String> {
        corporateService.addAccount(request,corporateId,authenticatedUser)
        return CompletableFuture.completedFuture(AppConstant.SUCCESS_RESPONSE_STRING)

    }


    @Async
    @Throws(InterruptedException::class)
    fun updateAccount(
        accountId: Long,
        authenticatedUser: AuthenticatedUser,
        request: AddAccountRequest
    ): CompletableFuture<AccountEntity> {
        return CompletableFuture.completedFuture(
            corporateService.updateAccount(accountId,authenticatedUser,request)
        )
    }

    fun retrieveSecondaryCorporateUsers(corporateId: Long): CompletableFuture<List<CorporateUserDetail>> {
        return CompletableFuture.completedFuture(
            corporateService.retrieveSecondaryCorporateUsers(corporateId)
        )
    }

    fun listCorporates(isPartnerCompany: Boolean?): CompletableFuture<List<ReferenceData>>? {
        return CompletableFuture.completedFuture(
            corporateService.retrieveCorporates(isPartnerCompany, null)
        )
    }

    fun listCorporateUsers(corporateId: Long): CompletableFuture<List<CorporateUsers>>? {
        return CompletableFuture.completedFuture(
            corporateService.retrieveCorporateUsers(corporateId)
        )
    }

    fun listCorporateUserAccounts(userId: Long): CompletableFuture<List<ReferenceData>>? {
        return CompletableFuture.completedFuture(
            corporateService.retrieveCorporateUserAccounts(userId)
        )
    }

    fun listCorporateAccounts(corporateId: Long): List<ReferenceData> {
        return corporateService.retrieveCorporateAccounts(corporateId)
    }

    fun retrieveByCorporateId(corporateId: Long): CorporateResponse {
        return corporateService.retrieveByCorporateId(corporateId)
    }

}