package com.centuroglobal.facade

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.CorporateUserService
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class CorporateUserFacade(private val corporateUserService: CorporateUserService) {


    fun list(searchFilter: CorporateUserSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<PagedResult<UserCardDetails>> {
        return CompletableFuture.completedFuture(corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser))
    }

    fun update(userId: Long, corporateUserRequest: CorporateUserRequest, authenticatedUser: AuthenticatedUser): CompletableFuture<String> {
        return CompletableFuture.completedFuture(doUpdate(userId, corporateUserRequest, authenticatedUser))
    }

    private fun doUpdate(userId: Long, corporateUserRequest: CorporateUserRequest, authenticatedUser: AuthenticatedUser): String? {
        corporateUserService.updateCorporateUser(userId, corporateUserRequest, authenticatedUser)
        return AppConstant.SUCCESS_RESPONSE_STRING
    }

    fun fetchUser(userId: Long, corporateId: Long): CompletableFuture<CorporateUserResponse> {
        return CompletableFuture.completedFuture(corporateUserService.fetchUser(userId, corporateId))
    }

}