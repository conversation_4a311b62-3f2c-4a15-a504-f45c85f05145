package com.centuroglobal.facade

import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.event.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.EventService
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class EventFacade(
    private val eventService: EventService
) {

    @Async
    @Throws(InterruptedException::class)
    fun retrieve(eventId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<EventDetails> {
        return CompletableFuture.completedFuture(eventService.retrieve(eventId, authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun listEvents(
        filter: EventSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<PagedResult<EventDetails>> {
        return CompletableFuture.completedFuture(eventService.listing(filter = filter,pageRequest = pageRequest,authenticatedUser = authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun speakerList(
        eventId: Long,
        pageable: Pageable,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<PagedResult<EventSpeaker>> {
        return CompletableFuture.completedFuture(eventService.speakerListing(eventId, pageable, authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun sessionList(
        eventId: Long,
        pageIndex: Int,
        authenticatedUser: AuthenticatedUser
    ): CompletableFuture<List<List<EventSession>>> {
        return CompletableFuture.completedFuture(eventService.sessionListing(eventId, pageIndex))
    }

    @Async
    @Throws(InterruptedException::class)
    fun inviteeList(eventId: Long, pageable: Pageable, authenticatedUser: AuthenticatedUser): CompletableFuture<PagedResult<EventClientProfile>> {
        return CompletableFuture.completedFuture(eventService.inviteesList(eventId,pageable,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun attendeeList(eventId: Long, pageable: Pageable, authenticatedUser: AuthenticatedUser): CompletableFuture<PagedResult<EventClientProfile>> {
        return CompletableFuture.completedFuture(eventService.attendeesList(eventId,pageable,authenticatedUser))
    }

    @Async
    @Throws(InterruptedException::class)
    fun rsvp(eventId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<String> {
        return CompletableFuture.completedFuture(eventService.rsvp(eventId, authenticatedUser))
    }
}