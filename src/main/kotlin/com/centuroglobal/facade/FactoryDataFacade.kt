package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.pojo.EnumValue
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class FactoryDataFacade {

    @Async
    @Throws(InterruptedException::class)
    fun retrieveCompanySizeList(): CompletableFuture<List<EnumValue<String>>> {
        val result: () -> List<EnumValue<String>> = {
            CompanySize.values().map { EnumValue(id = it.name, displayName = it.displayName) }
        }
        return CompletableFuture.completedFuture(result())
    }
}
