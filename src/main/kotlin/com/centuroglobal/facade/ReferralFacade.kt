package com.centuroglobal.facade

import com.centuroglobal.shared.data.pojo.ReferralResponse
import com.centuroglobal.service.ReferralCodeService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class ReferralFacade
    (private val referralCodeService: ReferralCodeService) {
    @Async
    @Throws(InterruptedException::class)
    fun getReferralCode(userId: Long, loginUserId: Long): CompletableFuture<ReferralResponse> {
        return CompletableFuture.completedFuture(ReferralResponse(referralCodeService.getReferralCode(userId, loginUserId)))
    }
}