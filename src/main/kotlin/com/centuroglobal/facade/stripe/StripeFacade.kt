package com.centuroglobal.facade.stripe

import com.centuroglobal.shared.data.payload.stripe.CheckoutSessionRequest
import com.centuroglobal.shared.data.payload.stripe.SubscriptionPaymentRequest
import com.centuroglobal.shared.data.pojo.stripe.CheckoutSession
import com.centuroglobal.shared.data.pojo.stripe.PaymentMethodObject
import com.centuroglobal.shared.data.pojo.stripe.SubscriptionObject
import com.centuroglobal.shared.data.pojo.stripe.UserSubscription
import com.centuroglobal.shared.service.stripe.StripeService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class StripeFacade(private val stripeService: StripeService) {

    @Async
    @Throws(InterruptedException::class)
    fun createSubscription(
        userId: Long,
        subscriptionPaymentRequest: SubscriptionPaymentRequest
    ): CompletableFuture<SubscriptionObject> {
        return CompletableFuture.completedFuture(stripeService.createSubscription(userId, subscriptionPaymentRequest))
    }

    @Async
    @Throws(InterruptedException::class)
    fun cancelSubscription(
        userId: Long,
        subscriptionId: String
    ): CompletableFuture<SubscriptionObject> {
        return CompletableFuture.completedFuture(
            stripeService.cancelSubscription(
                userId,
                subscriptionId
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun undoCancelSubscription(
        userId: Long,
        subscriptionId: String
    ): CompletableFuture<SubscriptionObject> {
        return CompletableFuture.completedFuture(
            stripeService.undoCancelSubscription(
                userId,
                subscriptionId
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieveSubscriptionPaymentMethod(
        userId: Long,
        subscriptionId: String
    ): CompletableFuture<PaymentMethodObject?> {
        return CompletableFuture.completedFuture(
            stripeService.retrieveSubscriptionPaymentMethod(
                userId,
                subscriptionId
            )
        )
    }

    @Async
    @Throws(InterruptedException::class)
    fun retrieveCustomerPaymentMethods(userId: Long): CompletableFuture<List<PaymentMethodObject>> {
        return CompletableFuture.completedFuture(stripeService.retrieveCustomerPaymentMethods(userId))
    }

    @Async
    @Throws(InterruptedException::class)
    fun createCheckoutSession(
        userId: Long,
        subscriptionId: String,
        data: CheckoutSessionRequest
    ): CompletableFuture<CheckoutSession> {
        return CompletableFuture.completedFuture(stripeService.createCheckoutSession(userId, subscriptionId, data))
    }

    @Async
    @Throws(InterruptedException::class)
    fun getActiveSubscription(userId: Long): CompletableFuture<UserSubscription> {
        return CompletableFuture.completedFuture(stripeService.getActiveSubscription(userId))
    }
}
