package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerCaseEmailService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class CaseFeesApprovalReminderJob (private val caseEmailService: SchedulerCaseEmailService) {

    @Scheduled(cron = "\${app.case-fees-reminder-email.cron-schedule}")
    fun doCompletedCaseArchive() {
        log.trace("******  CaseFeesApprovalReminderJob Job [STARTED] *********")
        caseEmailService.caseFeesReminderEmail()
        log.trace("******  CaseFeesApprovalReminderJob Job [ENDED] *********")
    }

}