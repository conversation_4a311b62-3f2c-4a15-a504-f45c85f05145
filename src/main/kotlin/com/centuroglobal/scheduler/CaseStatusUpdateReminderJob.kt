package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerCaseEmailService
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class CaseStatusUpdateReminderJob (
    private val caseEmailService: SchedulerCaseEmailService,

    @Value("\${app.case-status-reminder-email.start-date}")
    private val fromCreatedDate: String

) {

    @Scheduled(cron = "\${app.case-status-reminder-email.cron-schedule}")
    fun doCompletedCaseArchive() {
        log.trace("******  CaseStatusUpdateReminderJob Job [STARTED] *********")
        val startDate = LocalDateTime.parse(fromCreatedDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
        caseEmailService.caseStatusUpdateReminderEmail(startDate)
        log.trace("******  CaseStatusUpdateReminderJob Job [ENDED] *********")
    }
}