package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerCaseEmailService
import com.centuroglobal.scheduler.service.SchedulerGroupChatService
import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.data.enums.NotificationType
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}
private const val CASE_GROUP_CHAT_EMAIL_ALERT_TEMPLATE = "email/case_group_chat_email"
private const val CASE_GROUP_CHAT_EMAIL_ALERT_TEMPLATE_MODIFIED = "email/case_group_chat_email_modified"
private const val QUERY_GROUP_CHAT_EMAIL_ALERT_TEMPLATE = "email/query_group_chat_email"

@Profile("!test")
@Service
class GroupChatNotificationJob (
    private val groupChatService: SchedulerGroupChatService,
    private val caseEmailService: SchedulerCaseEmailService

) {

    @Scheduled(cron = "\${app.case-group-chat-notification-email.cron-schedule}")
    fun caseGroupChatReminders() {
        log.info("******  Case GroupChatNotificationJob Job [STARTED] *********")
        var userChatReferenceMap = groupChatService.getUserChatMap(ChatType.CASE, 5, NotificationType.CASE_GCHAT_EMAIL)

        if (!userChatReferenceMap.isNullOrEmpty()) {
            caseEmailService.groupChatNotificationsCases(
                userChatReferenceMap,
                CASE_GROUP_CHAT_EMAIL_ALERT_TEMPLATE_MODIFIED,
                "New message(s) on Case Group Chat - (All)",
                "All")
        }else{
            log.info("No new messages to send case group chat notifications")
        }

        userChatReferenceMap = groupChatService.getUserChatMap(ChatType.CLIENT_CASE, 5, NotificationType.CASE_GCHAT_EMAIL)

        if (!userChatReferenceMap.isNullOrEmpty()) {
            caseEmailService.groupChatNotificationsCases(
                userChatReferenceMap,
                CASE_GROUP_CHAT_EMAIL_ALERT_TEMPLATE_MODIFIED,
                "New message(s) on Case Group Chat - (Client Chat)",
                "Client Chat")
        }else{
            log.info("No new messages to send client case group chat notifications")
        }

        userChatReferenceMap = groupChatService.getUserChatMap(ChatType.APPLICANT_CASE, 5, NotificationType.CASE_GCHAT_EMAIL)

        if (!userChatReferenceMap.isNullOrEmpty()) {
            caseEmailService.groupChatNotificationsCases(
                userChatReferenceMap,
                CASE_GROUP_CHAT_EMAIL_ALERT_TEMPLATE_MODIFIED,
                "New message(s) on Case Group Chat - (Applicant Chat)",
                "Applicant Chat")
        }else{
            log.info("No new messages to send applicant case group chat notifications")
        }

        log.info("******  Case GroupChatNotificationJob Job [ENDED] *********")
    }
    @Scheduled(cron = "\${app.query-group-chat-notification-email.cron-schedule}")
    fun queryGroupChatReminders() {
        log.info("******  Query GroupChatNotificationJob Job [STARTED] *********")
        val userChatReferenceMap = groupChatService.getUserChatMapQueries(ChatType.QUERY, 5, NotificationType.QUERY_GCHAT_EMAIL)

        if (!userChatReferenceMap.isNullOrEmpty()) {
            caseEmailService.groupChatNotificationsQueries(
                userChatReferenceMap,
                QUERY_GROUP_CHAT_EMAIL_ALERT_TEMPLATE,
                "New message(s) on Query Group Chat",
                null
            )
        }else{
            log.info("No new messages to send  query group chat notifications")
        }
        log.info("******  Query GroupChatNotificationJob Job [ENDED] *********")
    }
}