package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerExpertService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class PartnerSupplierContractReminderJob (
    private val expertService: SchedulerExpertService

) {

    @Scheduled(cron = "\${app.partner-supplier-contract.expiry-reminder-email.cron-schedule}")
    fun doCompletedCaseArchive() {
        log.trace("******  PartnerSupplierContractReminderJob Job [STARTED] *********")
        expertService.contractExpiryReminder(30)
        expertService.contractExpiryReminder(60)
        log.trace("******  PartnerSupplierContractReminderJob Job [ENDED] *********")
    }
}