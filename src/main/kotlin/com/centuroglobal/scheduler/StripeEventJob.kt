package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.stripe.StripeEventJobService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class StripeEventJob(private val stripeEventJobService: StripeEventJobService) {

    @Scheduled(initialDelay = 1000, fixedRateString = "\${app.stripe.scheduler-fixed-rate.process-events}")
    fun processEvents() {
        log.trace("******  processEvents Job [STARTED] *********")
        stripeEventJobService.processEvents()
        log.trace("******  processEvents Job [ENDED] *********")
    }
}
