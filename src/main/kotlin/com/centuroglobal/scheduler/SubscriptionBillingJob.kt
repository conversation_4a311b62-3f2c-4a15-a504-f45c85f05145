package com.centuroglobal.scheduler

import com.centuroglobal.shared.service.subscription.SharedSubscriptionService
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Profile("!test")
@Service
class SubscriptionBillingJob (
    private val subscriptionBillingService: SharedSubscriptionService

) {

    @Scheduled(cron = "\${app.subscription-billing.cron-schedule}")
    fun generateBilling() {

        subscriptionBillingService.generate()
    }
}