package com.centuroglobal.scheduler//package com.centuroglobal.scheduler
//
//import com.centuroglobal.shared.service.SubscriptionService
//import mu.KotlinLogging
//import org.springframework.context.annotation.Profile
//import org.springframework.scheduling.annotation.Scheduled
//import org.springframework.stereotype.Service
//
//private val log = KotlinLogging.logger {}
//
//@Profile("!test")
//@Service
//class SubscriptionJob(private val subscriptionService: SubscriptionService) {
//
//    @Scheduled(cron = "\${app.subscription.cron-schedule}")
//    fun updateSubscription() {
//        log.trace("****** Update Subscription Job [STARTED] *********")
//        subscriptionService.updateSubscription()
//        log.trace("****** Update Subscription Job [ENDED] *********")
//    }
//}