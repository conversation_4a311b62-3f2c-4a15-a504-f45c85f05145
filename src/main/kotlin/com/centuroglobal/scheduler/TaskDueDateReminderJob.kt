package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerTaskService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class TaskDueDateReminderJob (
    private val taskService: SchedulerTaskService
) {

    @Scheduled(cron = "\${app.task-dueDate-reminder-email.cron-schedule}")
    fun dueDateReminder() {
        log.trace("******  TaskDueDateReminderJob Job [STARTED] *********")
        taskService.dueDateReminder()
        log.trace("******  TaskDueDateReminderJob Job [ENDED] *********")
    }

    @Scheduled(cron = "\${app.task-daily-reminder-email.cron-schedule}")
    fun dailyConsolidatedReminder() {
        log.trace("******  dailyConsolidatedReminder Job [STARTED] *********")
        taskService.pendingAndDueToday()
        log.trace("******  dailyConsolidatedReminder Job [ENDED] *********")
    }
}