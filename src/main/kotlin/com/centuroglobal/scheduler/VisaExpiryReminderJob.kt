package com.centuroglobal.scheduler

import com.centuroglobal.scheduler.service.SchedulerCaseEmailService
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Profile("!test")
@Service
class VisaExpiryReminderJob (
    private val caseEmailService: SchedulerCaseEmailService

) {

    @Scheduled(cron = "\${app.case-visa-expiry-reminder-email.cron-schedule}")
    fun doCompletedCaseArchive() {
        log.trace("******  VisaExpiryReminderJob Job [STARTED] *********")
        caseEmailService.visaExpiryReminder(30)
        caseEmailService.visaExpiryReminder(90)
        caseEmailService.visaExpiryReminder(180)
        log.trace("******  VisaExpiryReminderJob Job [ENDED] *********")
    }
}