package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.client.HubspotApiClient
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.*
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.*
import com.centuroglobal.shared.data.payload.case.*
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.case.*
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.*
import com.centuroglobal.shared.service.*
import com.google.gson.Gson
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.*


private val log = KotlinLogging.logger {}

@Service
class SchedulerCaseService(

    @Value("\${app.completed-case-archive.diff-days}")
    private val cleanupAfterSentInDays: Int,

    private val caseRepository: CaseRepository,

    @Value("\${app.hubspot-case-updated-in-hours:24}")
    private val updatedBeforeInHours: Long,

    @Value("\${app.hubspot.apiKey}")
    private val apiKey: String,
    private val hubspotApiClient: HubspotApiClient,
    private val caseStatusMasterRepository: CaseStatusMasterRepository,
    private val genericEmailService: GenericEmailService,
    private val sharedAdminUserService: SharedAdminUserService,
    private val loginAccountRepository: LoginAccountRepository

) {
    @Transactional
    fun updateCaseArchiveForCompletedStatus() {
        val caseListCompleted = caseRepository.findAllByStatusInAndArchive(
            listOf( "CANCELLED", "CASE_COMPLETE","CASE_DECLINED", "COMPLETED"),false).toList()

        if(caseListCompleted.isNotEmpty()) {
            val updateList: MutableList<CaseEntity> = mutableListOf()
            caseListCompleted.forEach {
                val diff: Int = ChronoUnit.DAYS.between(it.lastUpdatedDate, LocalDateTime.now()).toInt()
                if(diff > cleanupAfterSentInDays) {
                    log.info("Case Moved to Archived  "+it.id)
                    it.archive = true
                    updateList.add(it)
                }
            }
            if(updateList.isNotEmpty()) {
                log.info("Cases updated to Archived")
                caseRepository.saveAll(updateList)
            }
        }
    }

    fun createHubspotDeals() {

        val cutOffDate = LocalDateTime.now().minusHours(updatedBeforeInHours).truncatedTo(ChronoUnit.HOURS)
        //val cases = caseRepository.findAllById(mutableListOf(1241L,1242L))
        //val cases = caseRepository.searchCasesPendingForStatusUpdate(cutOffDate, null)
        val cases = caseRepository.findAllById(mutableListOf(1499L))
        val gson = Gson()
        try {
            val failedCaseList: MutableList<Long> = mutableListOf()
            val successCaseList: MutableList<Long> = mutableListOf()
            cases.forEach {
                // get or create company id
                val companyId = it.createdBy?.company?.let { it1 -> getCompanyId(it1) }
                // get or create contact id
                val contactId = it.createdBy?.let { it1 -> getContactId(it1) }

                //update hubspot deal
                if (it.dealId != null) {

                    val dealRequest = getHubspotDealRequest(it, companyId, contactId)
                    log.info("Update Deal request: ${gson.toJson(dealRequest)}")
                    val hubspotResponse = hubspotApiClient.updateDeal(it.dealId!!, apiKey, dealRequest)
                    log.debug("Hubspot deal for case id: ${it.id} updated.")
                    log.info("Update Deal response: ${gson.toJson(hubspotResponse)}")
                    successCaseList.add(it.id!!)
                } else {
                    //create hubspot deal
                    val dealRequest = getHubspotDealRequest(it, companyId, contactId)
                    log.info("create Deal request: ${gson.toJson(dealRequest)}")
                    try {
                        val hubspotResponse = hubspotApiClient.saveDeal(apiKey, dealRequest)
                        log.debug("Hubspot deal for case id: ${it.id} created.")
                        log.info("Create Deal response: ${gson.toJson(hubspotResponse)}")
                        //save deal id to case
                        if (hubspotResponse.id != null) {
                            it.dealId = hubspotResponse.id!!.toLong()
                            caseRepository.save(it)
                            successCaseList.add(it.id!!)
                        } else {
                            log.error("Can not create hubspot deal for case id: ${it.id}")
                            failedCaseList.add(it.id!!)
                        }
                    }
                    catch (ex: Exception) {
                        log.error(ex.message, ex)
                        failedCaseList.add(it.id!!)
                    }
                }
            }
            if(failedCaseList.isNotEmpty()){
                log.info ("Hubspot Cases Failed : $failedCaseList")
                log.info("Hubspot process end at ${TimeZone.getDefault().id}")
                genericEmailService.sendHubSpotNotification("Hubspot job for ${successCaseList.size} case(s) completed successfully at: ${LocalDateTime.now()}")
            }
        }
        catch (ex: Exception) {
            log.error(ex.message, ex)
            genericEmailService.sendHubSpotNotification("Hubspot job for cases failed with below error: <br> ${ex.message}")
        }
    }

    private fun getCompanyId(domain: String): String? {
        try {
            val result = sharedAdminUserService.findHubspotCompany(domain)
            if (result.isEmpty()) {
                val response = sharedAdminUserService.createCompany(domain)
                return response.id
            }
            return result[0].properties?.hs_object_id
        }
        catch (ex: Exception) {
            log.error(ex.message, ex)
        }
        return null
    }

    private fun getContactId(userDetails: ClientView): String? {
        try {
            val email = userDetails.email ?: return null
            val result = sharedAdminUserService.findHubspotUser(email)
            if (result.isEmpty()) {
                val loginUser = loginAccountRepository.findByEmail(email)
                if (loginUser!=null){
                    sharedAdminUserService.createHubspotUser(loginUser)
                }
                else {
                    log.warn("User with email: $email does not exist in database creating hubspot user.")
                    val firstName = userDetails.fullName.split(" ")[0]
                    val lastName = userDetails.fullName.split(" ").drop(1).joinToString(" ")
                    val response = sharedAdminUserService.createHubspotUser(email, firstName, lastName, userDetails.jobTitle)
                    return response.id
                }
            }
            return result[0].properties?.hs_object_id
        }
        catch (ex: Exception) {
            log.error(ex.message, ex)
        }
        return null

    }

    private fun getHubspotDealRequest(caseEntity: CaseEntity, companyId: String?, contactId: String?): HubspotDealRequest {

        val dealRequest = HubspotDealRequest()
        val properties = DealProperties()

        properties.dealname = "${caseEntity.id}-${caseEntity.initiatedFor}-${caseEntity.category.subCategoryName}-${caseEntity.personalDetails?.companyName}"
        properties.applicant_name = caseEntity.initiatedFor
        properties.case_category = caseEntity.category.parentCategoryName
        properties.case_id_unique = caseEntity.id
        properties.case_type = caseEntity.category.subCategoryName
        properties.pipeline = 11062285
        properties.consular_fee = caseEntity.caseFees?.feesDetails?.governmentFees?.toString()
        properties.other_third_party_fees = caseEntity.caseFees?.feesDetails?.thirdPartyFees?.toString()
        properties.professional_fees = caseEntity.caseFees?.feesDetails?.professionalFees?.toString()
        properties.translation = caseEntity.caseFees?.feesDetails?.translationFees?.toString()
        properties.deal_currency_code = caseEntity.caseFees?.feesDetails?.feesCurrency
        properties.amount = caseEntity.caseFees?.feesDetails?.totalFees

        if(contactId!=null || companyId!=null) {

            val associations = mutableListOf<Association>()
            contactId?.let { associations.add(createAssociation(it, "3")) }
            companyId?.let { associations.add(createAssociation(it, "341")) }

            dealRequest.associations = associations
        }

        val caseStatusMaster = caseStatusMasterRepository.findBySubCategoryAndStatus(
            caseEntity.category.subCategoryId, caseEntity.status)

        properties.dealstage = caseStatusMaster.dealStatusId

        dealRequest.properties = properties
        return dealRequest
    }

    private fun createAssociation(associationToId: String, id: String): Association {

        val association = Association()
        val associationTo = AssociationTo()
        associationTo.id = associationToId

        val associationTypes = mutableListOf<AssociationType>()
        val associationType = AssociationType()
        associationType.associationTypeId = id.toLong()
        associationType.associationCategory = "HUBSPOT_DEFINED"

        associationTypes.add(associationType)

        association.to = associationTo
        association.types = associationTypes

        return association
    }

}
