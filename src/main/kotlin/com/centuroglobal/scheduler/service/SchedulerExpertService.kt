package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.repository.ExpertCompanyProfileRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"
private const val SUPPLIER_CONTRACT_EXPIRY_EMAIL_ALERT_TEMPLATE = "email/supplier_contract_expiry_email"

@Service
class SchedulerExpertService(
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val mailSendingService: MailSendingService,
    private val awsS3Service: AwsS3Service
) {
    @Transactional(readOnly = true)
    fun contractExpiryReminder(days: Long) {

        val expiringDate = LocalDateTime.now().plusDays(days)

        val companies = expertCompanyProfileRepository.findAllByEffectiveEndDateAndCompanyType(expiringDate, ExpertCompanyType.SUPPLIER)

        if(companies.isEmpty()){
            log.debug("No expert companies found")
            return
        }

        companies.forEach {

            val recipientSet = it.associatedPartners.map {it.partnerUsers.map { it.email }}.flatten()

            val ctx = Context()
            ctx.setVariable("COMPANY_NAME", it.name)
            ctx.setVariable("CONTRACT_EXPIRING_IN", days)
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))

            if(recipientSet.isNotEmpty()) {
                mailSendingService.sendEmail(
                    MailTemplate(
                        templateName = SUPPLIER_CONTRACT_EXPIRY_EMAIL_ALERT_TEMPLATE,
                        subject = "Supplier Contract Expiring",
                        context = ctx,
                        recipient = recipientSet.joinToString(separator = ", ")
                    )
                )
            }
        }
    }

}