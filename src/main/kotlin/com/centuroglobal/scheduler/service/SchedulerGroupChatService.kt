package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.GroupChatEntity
import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.GroupChatMessageRepository
import com.centuroglobal.shared.repository.GroupChatParticipantRepository
import com.centuroglobal.shared.repository.query.QueryAssigneeRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}
@Service
class SchedulerGroupChatService(


    val groupChatMessageRepository: GroupChatMessageRepository,
    val groupChatParticipantRepository: GroupChatParticipantRepository,

    val caseRepository: CaseRepository,
    val queryRepository: QueryRepository,
    val queryAssigneeRepository: QueryAssigneeRepository


) {
    fun getUserChatMap(chatType: ChatType, updatedInMinutes: Long, notificationType: NotificationType): MutableMap<Long, MutableSet<Map<String, Any>>>? {
        val updatedAt = LocalDateTime.now().minusMinutes(updatedInMinutes)
        val messages = groupChatMessageRepository.findByCreatedDateGreaterThanAndGroupChatChatType(updatedAt, chatType) ?: return null
        val userChatReferenceMap = mutableMapOf<Long, MutableSet<Map<String, Any>>>()

        log.debug("found message ids for chat type: ${chatType.name}: ${messages.map { it.id }.joinToString(",")}")

        messages.groupBy { it.groupChat.id }.forEach {
            val groupChatEntity = it.value[0].groupChat
            //fetch all participants for current group chat
            val participants = groupChatParticipantRepository.findAllByGroupChatAndIsActive(groupChatEntity,
                true)
            //sort chat messages to get last chat message
            val lastGroupChatMessage = it.value.sortedByDescending { it1->it1.createdDate }[0]
            val participantsUserIds = participants?.filter { participant ->
                // Filter corporate users who enabled notifications
                if (participant.user is CorporateUserEntity) {
                    (participant.user as CorporateUserEntity).notificationPreferences.any {
                            notification -> notification.notificationKey == notificationType && notification.value
                    }
                } else {
                    true
                }
            }?.map { participant -> participant.user.id }?.toMutableList()

            //Do not send notification to user who sent last message
            participantsUserIds?.remove(lastGroupChatMessage.createdBy)
            log.debug("chatId: ${groupChatEntity.id},  participantsUserIds: ${participantsUserIds?.joinToString(",")}")

            val referenceMap = frameReferenceMap(groupChatEntity)

            participantsUserIds?.forEach { notificationUserId->
                userChatReferenceMap.getOrPut(notificationUserId!!) { mutableSetOf() }.add(referenceMap)
            }
        }
        return userChatReferenceMap
    }


    fun getUserChatMapQueries(chatType: ChatType, updatedInMinutes: Long, notificationType: NotificationType): MutableMap<Long, MutableSet<Map<String, Any>>>? {
        val updatedAt = LocalDateTime.now().minusMinutes(updatedInMinutes)
        val messages = groupChatMessageRepository.findByCreatedDateGreaterThanAndGroupChatChatType(updatedAt, chatType) ?: return null
        val userChatReferenceMap = mutableMapOf<Long, MutableSet<Map<String, Any>>>()

        log.debug("found message ids for chat type: ${chatType.name}: ${messages.map { it.id }.joinToString(",")}")

        messages.groupBy { it.groupChat.id }.forEach {
            val groupChatEntity = it.value[0].groupChat

            val recipientIds: MutableList<Long> = mutableListOf()

            if(groupChatEntity.chatType == ChatType.QUERY){
                val query = queryRepository.findById(groupChatEntity.referenceId).get()

                recipientIds.add(query.createdBy.id!!)

                val assigneesList = queryAssigneeRepository.findAllByQuery(query).map { assignee -> assignee.user.id }

                assigneesList.forEach { assignee -> if(assignee!= null) recipientIds.add(assignee!!) }
            }

            val referenceMap = frameReferenceMap(groupChatEntity)

            recipientIds?.forEach { notificationUserId->
                userChatReferenceMap.getOrPut(notificationUserId!!) { mutableSetOf() }.add(referenceMap)
            }
        }
        return userChatReferenceMap
    }

    private fun frameReferenceMap(groupChat: GroupChatEntity): Map<String, Any> {
        return when (groupChat.chatType) {
            ChatType.CASE, ChatType.CLIENT_CASE, ChatType.APPLICANT_CASE -> {
                val case = caseRepository.findById(groupChat.referenceId).get()
                //mapOf("id" to case.id!!, "title" to case.category.subCategoryName)
                mapOf("case" to case)
            }
            ChatType.QUERY -> {
                val query = queryRepository.findById(groupChat.referenceId).get()
                mapOf("id" to query.id!!, "title" to query.heading)
            }
            else -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
        }
    }
}