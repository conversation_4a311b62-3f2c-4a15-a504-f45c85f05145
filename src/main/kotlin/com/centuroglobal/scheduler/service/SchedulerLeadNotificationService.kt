package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.entity.LeadEntity
import com.centuroglobal.shared.data.entity.LeadNotificationEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.email.LeadResponseContext
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.data.pojo.email.NewLeadContext
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

private const val NEW_LEAD_EMAIL_TEMPLATE_NAME = "email/new-lead"
private const val LEAD_RESPONSE_EMAIL_TEMPLATE_NAME = "email/lead-response"

private const val NEW_LEAD_EMAIL_SUBJECT = "A new lead has been posted"
private const val LEAD_RESPONSE_EMAIL_SUBJECT = "Someone has responded to your request"

private const val DUMMY_ID = 0L

private val newLeadUrl: (webUrl: String, leadId: String) -> String =
    { webUrl, leadId -> "${webUrl}/leads/${leadId}/expert" }

private val leadResponseUrl: (webUrl: String, leadId: String) -> String =
    { webUrl, leadId -> "${webUrl}/leads/${leadId}" }

@Service
class SchedulerLeadNotificationService(
    @Value("\${app.web-url}")
    private val webUrl: String,
    @Value("\${app.lead-notification.cleanup-after-sent-in-days}")
    private val cleanupAfterSentInDays: Int,
    @Value("\${app.lead-notification.max-email-per-cron}")
    private val maxEmailPerCron: Int,
    @Value("\${app.lead-notification.centuro-address}")
    private val cgEmail: String,
    private val leadNotificationRepository: LeadNotificationRepository,
    private val leadRepository: LeadRepository,
    private val loginAccountRepository: LoginAccountRepository,

    private val corporateRepository: CorporateRepository,

    private val mailSendingService: MailSendingService,
    private val awsS3Service: AwsS3Service,
    private val countryService: CountryService,

) {
    @Transactional
    fun cleanUp() {
        // compute the cutoff date
        val cutoffDate = LocalDateTime.now().minusDays(cleanupAfterSentInDays.toLong()).truncatedTo(ChronoUnit.DAYS)
        leadNotificationRepository.deleteAllByStateInAndLastUpdatedDateBefore(
            listOf(LeadNotificationState.EMAIL_SENT, LeadNotificationState.INVALID), cutoffDate
        )
    }

    @Transactional
    fun sendNotifications() {
        val leadNotifications = leadNotificationRepository.findAllByState(
            LeadNotificationState.CREATED,
            PageRequest.of(0, maxEmailPerCron, Sort.by(Sort.Direction.ASC, "lastUpdatedDate"))
        ).toList()

        if (leadNotifications.isEmpty()) {
            return
        }
        val updateList: MutableList<LeadNotificationEntity> = mutableListOf()
        val leadList = leadRepository.findAllByIdInAndStatus(leadNotifications.map { it.leadId }, LeadStatus.ACTIVE)
        val leadMap = leadList.map { it.id to it }.toMap()

        val corporateMap = corporateRepository.findAllByIdInAndStatus(
            leadList.filter { it.leadType == LeadType.CORPORATE }.map { it.leadTypeId!! },
            CorporateStatus.ACTIVE
        ).map { it.id to it }.toMap()
        val loginAccountMap = loginAccountRepository.findAllByIdInAndStatus(
            leadNotifications.map { it.userId },
            AccountStatus.ACTIVE
        ).map { it.id to it }.toMap()

        leadNotifications.forEach {
            // If recipient is dummy Id or valid
            if (it.userId == DUMMY_ID ||
                (leadMap[it.leadId] != null && loginAccountMap[it.userId] != null &&
                        (loginAccountMap[it.userId]?.getUserType() != UserType.CORPORATE || corporateMap[leadMap[it.leadId]?.leadTypeId] != null))
            ) {
                val bccList = mutableListOf<String>()
                val recipientEmail = if (it.userId != DUMMY_ID) {
                    bccList.add(cgEmail)
                    loginAccountMap.getValue(it.userId).email
                } else {
                    cgEmail
                }

                val result = sendEmail(recipientEmail, bccList, leadMap.getValue(it.leadId), it.type)
                if (result) {
                    it.state = LeadNotificationState.EMAIL_SENT
                    updateList.add(it)
                }

            } else {
                it.state = LeadNotificationState.INVALID
                updateList.add(it)
            }
        }

        if (updateList.isNotEmpty()) {
            leadNotificationRepository.saveAll(updateList)
        }
    }
    @Transactional
    fun sendEmail(to: String, bcc: List<String>, lead: LeadEntity, type: LeadNotificationType): Boolean {
        val template: String
        val subject: String

        when (type) {
            LeadNotificationType.NEW_LEAD -> {
                template = NEW_LEAD_EMAIL_TEMPLATE_NAME
                subject = NEW_LEAD_EMAIL_SUBJECT
            }
            LeadNotificationType.RESPOND -> {
                template = LEAD_RESPONSE_EMAIL_TEMPLATE_NAME
                subject = LEAD_RESPONSE_EMAIL_SUBJECT
            }
        }

        return mailSendingService.sendEmail(
            MailTemplate(
                templateName = template,
                bccRecipients = bcc,
                subject = subject,
                recipient = to,
                context = getContext(type, lead)
            )
        )
    }
    private fun getContext(
        type: LeadNotificationType,
        lead: LeadEntity
    ): Context {
        val compositeLeadId = "${lead.leadType}-${lead.leadTypeId}-${lead.id}"
        return when (type) {
            LeadNotificationType.NEW_LEAD ->
                NewLeadContext.ModelMapper.toContext(
                    NewLeadContext(
                        newLeadUrl(webUrl, compositeLeadId),
                        lead.title,
                        lead.expertises.joinToString { it.name },
                        countryService.retrieveByCountryCode(lead.countryCode).name,
                        awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                    )
                )
            LeadNotificationType.RESPOND ->
                LeadResponseContext.ModelMapper.toContext(
                    LeadResponseContext(
                        leadResponseUrl(webUrl, compositeLeadId),
                        lead.title,
                        awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
                    )
                )
        }
    }
}