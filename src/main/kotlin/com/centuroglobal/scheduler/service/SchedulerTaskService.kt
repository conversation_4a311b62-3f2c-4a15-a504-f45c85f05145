package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.repository.TaskAssigneeRepository
import com.centuroglobal.shared.repository.TaskRepository
import com.centuroglobal.shared.service.GenericEmailService
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.format.DateTimeFormatter

private const val DUE_DATE_REMINDER_TEMPLATE = "email/task_due_date_reminder"
private const val DAILY_CONSOLIDATED_REMINDER_TEMPLATE = "email/task_daily_consolidated_reminder"

private val log = KotlinLogging.logger {}

private val taskUrl: (webUrl: String) -> String = { webUrl -> "${webUrl}/task-management/my-tasks" }

private val dateFormat = DateTimeFormatter.ofPattern("dd-MMM-yyyy")

@Service
class SchedulerTaskService(

    @Value("\${app.web-url}")
    private val webUrl: String,

    private val taskRepository: TaskRepository,
    private val taskAssigneeRepository: TaskAssigneeRepository,
    private val genericEmailService: GenericEmailService

    ) {

    @Transactional
    fun dueDateReminder() {
        val tasks = taskRepository.searchForDueDateReminder(LocalDate.now())

        log.info("Found ${tasks.size} Tasks to send due date reminder emails")

        val usersList = mutableListOf<Long>()

        tasks.forEach {
            it.assignee?.forEach { assignee ->
                val userEntity = assignee.assigneeId
                if(userEntity != null) {
                    genericEmailService.sendEmail(
                        subject = "Task Reminder - (${it.name})",
                        templateName = DUE_DATE_REMINDER_TEMPLATE,
                        variableMap = mapOf(
                            "TASK_USER" to "${userEntity.firstName} ${userEntity.lastName}",
                            "TASK_NAME" to it.name,
                            "DUE_DATE" to (it.dueDate?.toLocalDate()?.format(dateFormat)
                                ?: ""),
                            "TASK_TYPE" to it.referenceType.name,
                            "REFERENCE_ID" to "${it.referenceId}",
                            "TASK_STATUS" to (it.status?.label ?: ""),
                            "TASK_URL" to taskUrl(webUrl)
                        ),
                        recipient = listOf(userEntity.email)
                    )
                    usersList.add(userEntity.id!!)
                }
                else {
                    log.warn("Task id: ${it.id} has null assignee")
                }
            }
        }
        log.info("Task due date email sent to userIds: ${usersList.joinToString(", ")}")
    }

    @Transactional
    fun pendingAndDueToday() {

        val workflowTaskAssignees = taskAssigneeRepository.searchByExpectedDueDateCriteria(LocalDate.now(), listOf(AccountStatus.ACTIVE))
        val taskAssignees =
            taskAssigneeRepository.searchByDueDateCriteria(LocalDate.now(), listOf(AccountStatus.ACTIVE))

        val groupByAssignee = taskAssignees.plus(workflowTaskAssignees).groupBy { it.assigneeId }
        log.info("Found ${groupByAssignee.size} Task users to send reminder emails")

        val usersList = mutableListOf<Long>()

        groupByAssignee.forEach {
            val tasks = it.value.map { task -> task.task }

            val taskData = tasks.mapIndexed { index, taskEntity ->
                    mapOf(
                        "sno" to index.plus(1).toString(),
                        "name" to taskEntity!!.name,
                        "dueDate" to (taskEntity.expectedDueDate?: taskEntity.dueDate)?.toLocalDate()
                            ?.format(dateFormat),
                        "referenceId" to taskEntity.referenceId.toString(),
                        "status" to taskEntity.status!!.label
                    )
            }

            genericEmailService.sendEmail(
                subject = "Today's Tasks (${LocalDate.now().format(dateFormat)})",
                templateName = DAILY_CONSOLIDATED_REMINDER_TEMPLATE,
                variableMap = mapOf(
                    "TASK_USER" to "${it.key!!.firstName} ${it.key!!.lastName}",
                    "TASK_DATA" to taskData,
                    "TASK_URL" to taskUrl(webUrl)
                ),
                recipient = listOf(it.key!!.email)
            )
            usersList.add(it.key!!.id!!)
        }
        log.info("Task reminder email sent to userIds: ${usersList.joinToString(", ")}")
    }
}