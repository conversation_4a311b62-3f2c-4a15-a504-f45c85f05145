package com.centuroglobal.scheduler.service

import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.repository.task.TaskWorkflowRepository
import com.centuroglobal.shared.service.task.SharedTaskWorkflowService
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

private val log = KotlinLogging.logger {}

@Service
class SchedulerTaskWorkflowService(

    private val sharedTaskWorkflowService: SharedTaskWorkflowService,
    private val taskWorkflowRepository: TaskWorkflowRepository

) {

    @Transactional
    fun pendingAndDueToday() {

        val currentDateTime = TimeUtil.atStartOfDay(LocalDate.now())

        val statusList = listOf(TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS)

        val workflows =
            taskWorkflowRepository.findAllByReferenceTypeAndTaskTemplatesTaskStatusIn(
                "CASE",
                statusList,
            )

        log.info("Found total {} workflows to update timelines.", workflows.size)

        val updatedWorkflowIds = mutableListOf<Long>()

        workflows.forEach {


            val workflowId = it.id!!

            val templates = it.taskTemplates

            if (templates.any { it1->it1.task==null }) {
                log.debug("workflow id: {} has no tasks created", workflowId)
                return@forEach
            }

            var task = it.taskTemplates.firstOrNull { it1->it1.task?.status == TaskStatus.IN_PROGRESS }

            if (task == null) {
                task = it.taskTemplates.sortedBy { it1->it1.displayOrder }.firstOrNull { it1->it1.task?.status == TaskStatus.NOT_STARTED }
            }

            if (task == null) {
                return@forEach
            }

            val dueDate = task.task!!.expectedDueDate?:task.task!!.dueDate!!

            if (dueDate.isBefore(currentDateTime)) {
                log.info("Processing tasks from workflow: {}", workflowId)

                val filteredTasks = it.taskTemplates.sortedBy { it1 -> it1.displayOrder }.filter {
                        it2 -> it2.displayOrder!! > task.displayOrder!!
                }

                log.debug("Found total: {} filtered tasks in workflow", filteredTasks.size)

                /*var currentStartDate = TimeUtil.getNthWorkingDay(dueDate, 1)

                // if start date already present then use same as current start date
                if (filteredTasks.isNotEmpty()) {
                    val firstStartDate = filteredTasks.sortedBy { it1->it1.displayOrder }[0].task!!.startDate
                    if (firstStartDate!=null) {
                        currentStartDate = firstStartDate
                    }
                }*/
                
                sharedTaskWorkflowService.populateTimelines(filteredTasks, currentDateTime)
                updatedWorkflowIds.add(workflowId)

                log.info("Processing complete for workflow: {}", workflowId)
            }
        }
    }
}