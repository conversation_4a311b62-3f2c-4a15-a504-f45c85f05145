package com.centuroglobal.scheduler.service.stripe.event

import com.centuroglobal.scheduler.service.SchedulerCorporateUserService
import com.centuroglobal.shared.data.entity.stripe.StripeAccountEntity
import com.centuroglobal.shared.data.entity.stripe.StripeEventEntity
import com.centuroglobal.shared.data.entity.stripe.StripeTransactionEntity
import com.centuroglobal.shared.data.enums.stripe.StripeEventJobCompletionStatus
import com.centuroglobal.shared.data.enums.stripe.StripeRequestType
import com.centuroglobal.shared.data.enums.stripe.StripeTransactionStatus
import com.centuroglobal.shared.util.TimeUtil
import com.stripe.Stripe
import com.stripe.model.Invoice
import com.stripe.model.PaymentIntent
import com.stripe.net.ApiResource
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class StripeInvoiceEventProcessor(
    @Value("\${app.stripe.secret-key}")
    private val secretKey: String,
    private val corporateUserService: SchedulerCorporateUserService,
    private val helper: StripeEventProcessorHelper
) : StripeEventProcessor {

    init {
        Stripe.apiKey = secretKey
    }

    override fun processEvent(event: StripeEventEntity, account: StripeAccountEntity): StripeEventJobCompletionStatus {
        // Parse the invoice
        val invoice = ApiResource.GSON.fromJson(event.response, Invoice::class.java)

        // Skip if subscription not found
        val createdSubscription = helper.retrieveSubscription(invoice.subscription, false, event)
            ?: return StripeEventJobCompletionStatus.SKIPPED

        val amount =
            if (invoice.lines.data[0].price.unitAmount > 0) BigDecimal(invoice.lines.data[0].price.unitAmount).divide(
                BigDecimal(100)
            ) else BigDecimal.ZERO

        val eventStatus = StripeTransactionStatus.fromKey(invoice.status)
        val subscriptionActive = invoice.paid == true
        val startTime = TimeUtil.fromInstant(invoice.lines.data[0].period.start)
        val endTime = TimeUtil.fromInstant(invoice.lines.data[0].period.end)

        // log into transaction table
        helper.logTransaction(
            StripeTransactionEntity(
                customerId = invoice.customer,
                requestType = StripeRequestType.INVOICE,
                requestTypeId = invoice.id,
                paymentMethodId = retrievePaymentMethod(invoice),
                amount = amount,
                response = event.response,
                status = eventStatus
            )
        )

        // update the subscription
        createdSubscription.priceId = invoice.lines.data[0].price.id
        createdSubscription.subscriptionAmount = amount
        createdSubscription.currentPeriodStartDate = startTime
        createdSubscription.currentPeriodEndDate = endTime
        createdSubscription.latestInvoiceId = invoice.id
        createdSubscription.latestInvoiceStatus = eventStatus
        createdSubscription.subscriptionActive = subscriptionActive
        helper.saveSubscriptionOnErrorThrows(invoice.customer, createdSubscription)

        // update corporate subscription status
        if (subscriptionActive) {
            corporateUserService.updateSubscriptionForUser(account.userId, endTime)
        }

        return StripeEventJobCompletionStatus.COMPLETED
    }

    private fun retrievePaymentMethod(invoice: Invoice): String? {
        return if (!invoice.defaultPaymentMethod.isNullOrBlank()) {
            invoice.defaultPaymentMethod
        } else {
            val paymentIntent = invoice.paymentIntentObject ?: retrievePaymentIntentFromStripe(invoice.paymentIntent)
            if (paymentIntent?.lastPaymentError != null) {
                paymentIntent.lastPaymentError?.source?.id
            } else if (!paymentIntent?.paymentMethod.isNullOrBlank()) {
                paymentIntent?.paymentMethod
            } else {
                paymentIntent?.source
            }
        }
    }

    private fun retrievePaymentIntentFromStripe(paymentIntentId: String?): PaymentIntent? {
        return try {
            PaymentIntent.retrieve(paymentIntentId)
        } catch (ex: Exception) {
            null
        }
    }
}