package com.centuroglobal.scheduler.service.stripe.event

import com.centuroglobal.scheduler.service.SchedulerCorporateUserService
import com.centuroglobal.shared.data.entity.stripe.StripeAccountEntity
import com.centuroglobal.shared.data.entity.stripe.StripeEventEntity
import com.centuroglobal.shared.data.entity.stripe.StripeTransactionEntity
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.stripe.StripeEventJobCompletionStatus
import com.centuroglobal.shared.data.enums.stripe.StripeRequestType
import com.centuroglobal.shared.data.enums.stripe.StripeTransactionStatus
import com.centuroglobal.shared.util.TimeUtil
import com.stripe.Stripe
import com.stripe.model.Customer
import com.stripe.model.Subscription
import com.stripe.net.ApiResource
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class StripeSubscriptionEventProcessor(
    @Value("\${app.stripe.secret-key}")
    private val secretKey: String,
    private val corporateUserService: SchedulerCorporateUserService,
    private val helper: StripeEventProcessorHelper
) : StripeEventProcessor {

    init {
        Stripe.apiKey = secretKey
    }

    override fun processEvent(event: StripeEventEntity, account: StripeAccountEntity): StripeEventJobCompletionStatus {
        // Parse the subscription
        val subscription = ApiResource.GSON.fromJson(event.response, Subscription::class.java)

        // Skip if no subscription not found
        val activeSubscription =
            helper.retrieveSubscription(subscription.id, false, event) ?: return StripeEventJobCompletionStatus.SKIPPED

        val amount =
            if (subscription.items.data[0].price.unitAmount > 0) BigDecimal(subscription.items.data[0].price.unitAmount).divide(
                BigDecimal(100)
            ) else BigDecimal.ZERO

        val eventStatus = StripeTransactionStatus.fromKey(subscription.status)
        val subscriptionActive = subscription.status == "active"
        val startTime = TimeUtil.fromInstant(subscription.currentPeriodStart)
        val endTime = TimeUtil.fromInstant(subscription.currentPeriodEnd)

        val paymentMethodId =
            if (!subscription.defaultPaymentMethod.isNullOrBlank()) {
                subscription.defaultPaymentMethod
            } else if (!subscription.defaultSource.isNullOrBlank()) {
                subscription.defaultSource
            } else {
                retrievePaymentMethodIdFromStripe(account.customerId)
            }

        // log into transaction table
        helper.logTransaction(
            StripeTransactionEntity(
                customerId = subscription.customer,
                requestType = StripeRequestType.SUBSCRIPTION,
                requestTypeId = subscription.id,
                paymentMethodId = paymentMethodId,
                amount = amount,
                response = event.response,
                status = eventStatus
            )
        )

        // update the subscription

        // A date in the future at which the subscription will automatically get canceled
        val cancelAt =
            if (subscription.cancelAt != null) TimeUtil.fromInstant(subscription.cancelAt) else null

        // If the subscription has been canceled, this will be the date of that cancellation.
        // If the subscription was canceled with [cancel_at_period_end],
        // this will still reflect the date of the initial cancellation request,
        // not the end of the subscription period when the subscription is automatically moved to a canceled state
        val cancelledAt =
            if (subscription.canceledAt != null) TimeUtil.fromInstant(subscription.canceledAt) else null

        // If subscription is `active` and the current latestInvoiceStatus == 'paid', we don't want to update that to status `active`
        val activeSubscriptionStatus =
            if (subscriptionActive && activeSubscription.latestInvoiceStatus == StripeTransactionStatus.PAID)
                StripeTransactionStatus.PAID
            else
                eventStatus

        // If this event was triggered by a new setup intent created due to update payment method details,
        // we will update that with the correct paymentMethodId
        val activeSubscriptionPaymentMethod =
            if (!paymentMethodId.isNullOrBlank())
                paymentMethodId
            else
                activeSubscription.paymentMethodId

        activeSubscription.priceId = subscription.items.data[0].price.id
        activeSubscription.subscriptionAmount = amount
        activeSubscription.paymentMethodId = activeSubscriptionPaymentMethod
        activeSubscription.currentPeriodStartDate = startTime
        activeSubscription.currentPeriodEndDate = endTime
        activeSubscription.latestInvoiceId = subscription.latestInvoice
        activeSubscription.latestInvoiceStatus = activeSubscriptionStatus
        activeSubscription.subscriptionActive = subscriptionActive
        activeSubscription.cancelAtPeriodEnd = subscription.cancelAtPeriodEnd ?: false
        activeSubscription.cancelAt = cancelAt
        activeSubscription.cancelledAt = cancelledAt

        helper.saveSubscriptionOnErrorThrows(subscription.customer, activeSubscription)

        // update corporate subscription status
        if (!subscriptionActive && cancelledAt != null) {
            corporateUserService.updateSubscriptionForUser(
                account.userId,
                cancelledAt,
                Role.ROLE_CORPORATE,
                subscriptionActive
            )
        }

        return StripeEventJobCompletionStatus.COMPLETED
    }

    private fun retrievePaymentMethodIdFromStripe(customerId: String): String? {
        return try {
            val customer = Customer.retrieve(customerId)
            if (!customer.invoiceSettings.defaultPaymentMethod.isNullOrBlank()) {
                customer.invoiceSettings.defaultPaymentMethod
            } else {
                customer.defaultSource
            }
        } catch (ex: Exception) {
            null
        }
    }
}