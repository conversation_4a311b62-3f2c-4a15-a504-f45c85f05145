package com.centuroglobal.security

import com.centuroglobal.shared.data.enums.GrantType
import com.centuroglobal.shared.data.enums.Scope
import org.springframework.security.authentication.AbstractAuthenticationToken

class AuthenticationToken(
    private val grantType: GrantType,
    private val scope: Scope?,
    private val credentials: Map<String, String?>,
    private val headers: Map<String, String?> = mapOf()
) :
    AbstractAuthenticationToken(null) {

    override fun getCredentials(): String {
        return if (grantType == GrantType.PASSWORD) {
            credentials.getOrDefault("password", "") ?: ""
        } else if (grantType == GrantType.REFRESH_TOKEN) {
            credentials.getOrDefault("refreshToken", "") ?: ""
        } else {
            credentials.getOrDefault("redirectURL", "") ?: ""
        }
    }

    override fun getPrincipal(): String {
        return if (grantType == GrantType.PASSWORD) {
            credentials.getOrDefault("username", "") ?: ""
        } else if (grantType == GrantType.REFRESH_TOKEN) {
            credentials.getOrDefault("refreshToken", "") ?: ""
        } else {
            credentials.getOrDefault("code", "") ?: ""
        }
    }

    fun getKeepMeInformed(): String?{
       return credentials.getOrDefault("keepMeInformed",null)
    }

    fun getMfaCode(): String? {
        return credentials.getOrDefault("mfaCode",null)
    }

    fun getHeaders(): Map<String, String?> {
        return headers
    }

    fun getGrantType(): GrantType {
        return grantType
    }

    fun getScope(): Scope? {
        return scope
    }
}