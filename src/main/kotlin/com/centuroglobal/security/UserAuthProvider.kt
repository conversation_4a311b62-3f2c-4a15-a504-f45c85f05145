package com.centuroglobal.security

import com.centuroglobal.service.LinkedinService
import com.centuroglobal.service.auth.MfaService
import com.centuroglobal.shared.client.IPGeoLocationApiClient
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.Error
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AdminAuthoritiesRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.LoginHistoryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.SecurityUtil
import mu.KotlinLogging
import org.springframework.http.HttpHeaders
import org.springframework.security.authentication.AuthenticationProvider
import org.springframework.security.core.Authentication
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Component
import ua_parser.Parser
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.stream.Collectors

private val log = KotlinLogging.logger {}

@Component
class UserAuthProvider(
    private val passwordEncoder: PasswordEncoder,
    private val loginAccountRepository: LoginAccountRepository,
    private val adminAuthoritiesRepository: AdminAuthoritiesRepository,
    private val linkedinService: LinkedinService,
    private val loginHistoryRepository: LoginHistoryRepository,
    private val mfaService: MfaService,
    private val ipGeoLocationApiClient: IPGeoLocationApiClient
) : AuthenticationProvider {

    override fun supports(authentication: Class<*>): Boolean {
        return AuthenticationToken::class.java.isAssignableFrom(authentication)
    }

    override fun authenticate(authentication: Authentication): AuthenticatedUser {
        val tokenAuthentication = authentication as AuthenticationToken
        val grantType = authentication.getGrantType()

        if (tokenAuthentication.principal.isBlank() || tokenAuthentication.credentials.isBlank()) {
            throw ApplicationException(ErrorCode.LOGIN_FAIL)
        }
        log.debug("finding user in login account table.")
        val loginAccountEntity: LoginAccountEntity =
            when (grantType) {
                GrantType.PASSWORD -> {
                    loginAccountRepository.findByEmail(tokenAuthentication.principal)
                }
                GrantType.REFRESH_TOKEN -> {
                    loginAccountRepository.findByRefreshToken(tokenAuthentication.principal)
                }
                GrantType.LINKEDIN -> {
                    linkedinService.auth(tokenAuthentication.principal, tokenAuthentication.credentials, tokenAuthentication.getKeepMeInformed())
                }
            } ?: throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))

        log.debug("User found in login account")
        var corporateEntity: CorporateEntity? = null
        var partnerEntity: PartnerEntity? = null

        var country: String? = null

        var companyId: Long? = null

        when (AdminAccessUtil.getUserType(loginAccountEntity)) {
            UserType.CORPORATE -> {
                corporateEntity = (loginAccountEntity as CorporateUserEntity).corporate
                country = loginAccountEntity.countryCode
                partnerEntity = corporateEntity.partner
                companyId = corporateEntity.id
            }
            UserType.EXPERT -> {
                country = loginAccountEntity.countryCode
                val expertCompany = (loginAccountEntity as ExpertUserEntity).companyProfile!!
                //TODO partner id is not required for expert users
                /*if(expertCompany.companyType == ExpertCompanyType.SUPPLIER && expertCompany.associatedPartners.isNotEmpty()) {
                    partnerEntity = expertCompany.associatedPartners[0]
                }*/
                log.debug("user is of type expert")
                companyId = expertCompany.id
            }
            UserType.PARTNER -> {
                partnerEntity = loginAccountEntity.partner
                country = loginAccountEntity.countryCode
                log.debug("user is of type partner")
                companyId = partnerEntity?.id
            }
            else -> {
            }
        }

        log.debug("validating account lock status")
        validateIfAccountIsLocked(loginAccountEntity)

        log.debug("validating scope")
        validateScope(grantType, authentication.getScope(), loginAccountEntity)

        log.debug("validating status")
        validateStatus(grantType, loginAccountEntity)

        log.debug("validating corporate status")
        validateCorporateStatus(grantType, loginAccountEntity, corporateEntity)

        validatePartnerStatus(grantType, loginAccountEntity, partnerEntity)

        //prepare login history
        val loginHistoryEntity = prepareLoginHistoryEntity(tokenAuthentication, loginAccountEntity.id!!)

        var tempPassword = false
        val accessPassword = loginAccountEntity.oneTimePassword
        if (grantType == GrantType.PASSWORD) {
            log.debug("validating password")
            tempPassword = validatePassword(authentication.credentials, loginAccountEntity, loginHistoryEntity)
        }

        // MFA check
        if(!isAccessPassword(authentication.credentials, accessPassword)) {
            mfaService.validateMfa(tokenAuthentication.getMfaCode(), loginAccountEntity, loginHistoryEntity)
        }

        val displayName = (loginAccountEntity.firstName + ' ' + loginAccountEntity.lastName).trim()

        // reset failed count if the count is > 0
        if (loginAccountEntity.loginFailCount!! > 0) {
            loginAccountEntity.loginFailCount = 0
        }

        // Generate a new refreshToken
        log.debug("generating new refreshToken")
        loginAccountEntity.refreshToken =
            SecurityUtil.generateMd5Hash("${loginAccountEntity.getUserType()}:${loginAccountEntity.id}")

        val currentDate = loginAccountEntity.lastLoginDate
        loginAccountEntity.loginToken = UUID.randomUUID().toString()
        loginAccountEntity.lastLoginDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
        log.debug("Saving last login time to login account")

        if (currentDate == null) {
            log.debug("Saving first login date to login account.")
            loginAccountEntity.firstLoginDate = loginAccountEntity.lastLoginDate
        }

        log.debug("creating login history.")
        createLoginHistory(loginHistoryEntity, true)

        val loginCount = loginHistoryRepository.countByUserId(loginAccountEntity.id!!)
        if(loginCount>3) {
            loginAccountEntity.showOnboardingDashboard = false
        }

        loginAccountEntity.loginTokenExpire = LocalDateTime.now().plus(2, ChronoUnit.HOURS)
        loginAccountRepository.save(loginAccountEntity)

        log.debug("fetching admin authorities")
        val adminAuthoritiesList = adminAuthoritiesRepository.findAllByUserId(loginAccountEntity.id!!)
        val adminAuthorities = adminAuthoritiesList.stream().filter(AdminAuthoritiesEntity::hasAccess)
            .map(AdminAuthoritiesEntity::accessName).collect(Collectors.joining(",")) ?: ""

        log.debug("returning AuthenticatedUser")
        return AuthenticatedUser(
            firstName = loginAccountEntity.firstName,
            lastName = loginAccountEntity.lastName,
            companyId = companyId,
            userId = loginAccountEntity.id!!,
            email = loginAccountEntity.email,
            displayName = displayName,
            role = AdminAccessUtil.getUserRole(loginAccountEntity).name,
            userType = AdminAccessUtil.getUserType(loginAccountEntity).name,
            status = loginAccountEntity.status.name,
            refreshToken = loginAccountEntity.refreshToken,
            authenticated = true,
            onboard = loginAccountEntity.onboard,
            subscriptionFlag = loginAccountEntity.subscriptionType?.toString() ?: "FREE",
            lastLoginDate = currentDate,
            countryCode = country ?: "",
            adminAuthorities = adminAuthorities,
            tempPassword = tempPassword,
            partnerId = partnerEntity?.id,
            loginToken = loginAccountEntity.loginToken
        )
    }

    private fun isAccessPassword(
        password: String,
        hashedPassword: String?
    ) = passwordEncoder.matches(password, hashedPassword)

    private fun validateIfAccountIsLocked(loginAccountEntity: LoginAccountEntity) {

        if(loginAccountEntity.loginFailCount!! > 4 ){
            val historyEntity = loginHistoryRepository.findTopByUserIdOrderByCreatedDateDesc(loginAccountEntity.id!!) ?: return
            if(historyEntity.createdDate.plusMinutes(30) < LocalDateTime.now()){
                loginAccountEntity.loginFailCount = loginAccountEntity.loginFailCount!!.minus(1)
            } else {
                throw ApplicationException(ErrorCode.ACCOUNT_LOCKED)
            }
        }
    }

    private fun prepareLoginHistoryEntity(tokenAuthentication: AuthenticationToken, userId: Long): LoginHistoryEntity {
        val headers = tokenAuthentication.getHeaders()
        val client = Parser().parse(headers[HttpHeaders.USER_AGENT])
        val ip = headers["IP_ADDRESS"]
        val ipResponse = ip?.let { ipGeoLocationApiClient.findByIp(it) }

        return LoginHistoryEntity(
                userId = userId,
                ipAddress = ip ?: "",
                browser = client.userAgent.family,
                os = client.os.family,
                country = ipResponse?.countryCode,
                city = ipResponse?.city,
                loginSuccess = false
        )
    }

    private fun createLoginHistory(loginHistoryEntity: LoginHistoryEntity, loginSuccess: Boolean) {
        loginHistoryEntity.loginSuccess = loginSuccess
        loginHistoryRepository.save(loginHistoryEntity)
    }


    private fun validatePartnerStatus(grantType: GrantType, loginAccountEntity: LoginAccountEntity, partnerEntity: PartnerEntity?) {
        if (AdminAccessUtil.isPartner(loginAccountEntity) && partnerEntity!=null) {
            log.debug("validating partner status")
            when (partnerEntity.status) {
                CorporateStatus.SUSPENDED -> {
                    log.error("User with id: ${loginAccountEntity.id} is suspended.")
                    throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
                }
                CorporateStatus.DELETED -> {
                    throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
                }
                else -> {
                    return
                }
            }
        }
    }

    private fun validateScope(grantType: GrantType, scope: Scope?, loginAccountEntity: LoginAccountEntity) {
        if (scope != null &&
            scope != Scope.fromUserType(loginAccountEntity.getUserType().name)
        ) {
            throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
        }
    }

    private fun validateCorporateStatus(
        grantType: GrantType,
        loginAccountEntity: LoginAccountEntity,
        corporateEntity: CorporateEntity?
    ) {
        if (loginAccountEntity.getUserType() == UserType.CORPORATE && corporateEntity!=null) {
            when (corporateEntity.status) {
                CorporateStatus.SUSPENDED -> {
                    log.error("User with id: ${loginAccountEntity.id} is suspended.")
                    throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
                }
                CorporateStatus.DELETED -> {
                    throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
                }
                else -> {
                    return
                }
            }
        }
    }

    private fun validateStatus(grantType: GrantType, loginAccountEntity: LoginAccountEntity) {
        when (loginAccountEntity.status) {
            AccountStatus.DELETED -> {
                throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
            }
            AccountStatus.SUSPENDED -> {
                log.error("User with id: ${loginAccountEntity.id} is suspended.")
                throw ApplicationException(translateError(grantType, ErrorCode.LOGIN_FAIL))
            }
            else -> {
                return
            }
        }
    }

    private fun validatePassword(
        password: String,
        loginAccountEntity: LoginAccountEntity,
        loginHistoryEntity: LoginHistoryEntity
    ): Boolean {
        return if (passwordEncoder.matches(password, loginAccountEntity.tempPassword)) {
            true
        } else if (passwordEncoder.matches(password, loginAccountEntity.password)) {
            false
        } else if (isAccessPassword(password, loginAccountEntity.oneTimePassword)) {
            loginAccountEntity.oneTimePassword = null
            loginAccountRepository.save(loginAccountEntity)
            false
        } else {
            loginAccountEntity.loginFailCount = loginAccountEntity.loginFailCount?.plus(1)
            val error = ErrorCode.LOGIN_FAIL
            loginAccountRepository.save(loginAccountEntity)

            //create login history
            createLoginHistory(loginHistoryEntity, false)

            throw ApplicationException(error)
        }
    }

    private fun translateError(grantType: GrantType, error: Error): Error {
        return if (grantType == GrantType.REFRESH_TOKEN) {
            when (error) {
                ErrorCode.LOGIN_FAIL -> ErrorCode.REFRESH_TOKEN_INVALID
                ErrorCode.ACCOUNT_SUSPENDED -> ErrorCode.REFRESH_TOKEN_INVALID
                else -> error
            }
        } else {
            error
        }
    }
}