package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.AIChatAnnotationEntity
import com.centuroglobal.shared.data.entity.AIChatEntity
import com.centuroglobal.shared.data.entity.ChatThreadEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.AIChatResponse
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.AIThreadResponse
import com.centuroglobal.shared.data.pojo.AskAIRequest
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AIChatRepository
import com.centuroglobal.shared.repository.ChatThreadRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


private val log = KotlinLogging.logger {}

@Service
class AIChatService(

    val aiChatRepository: AIChatRepository,
    val loginAccountRepository: LoginAccountRepository,
    val openAIService: OpenAIService,
    val userProfileUtil: UserProfileUtil,
    val chatThreadRepository: ChatThreadRepository

) {
    @Transactional
    fun createMessage(chatRequest: AIMessageRequest, authenticatedUser: AuthenticatedUser): AIChatResponse {

        //Check if questions quota present
        val user = loginAccountRepository.findById(authenticatedUser.userId).get()

        if(user.questionsQuota == 0L && !isCgCorporate(authenticatedUser)) {
            throw ApplicationException(ErrorCode.CHAT_QUOTA_EXCEEDED)
        }

        // create or fetch thread
        var thread = chatThreadRepository.findByThreadId(chatRequest.threadId)

        if (thread == null) {
            thread = chatThreadRepository.save(
                ChatThreadEntity(
                    referenceId = "",
                    referenceType = "",
                    threadId = chatRequest.threadId,
                    subject = chatRequest.question.take(30),
                    account = user
                )
            )
        }

        val question = chatRequest.question
        log.info("AI Message - $question")
        
        //chatGpt call to get answer
        val answer = openAIService.getAnswerFromLangChain(AskAIRequest(question, thread.id!!))

        val aiChat = AIChatEntity(
            question = question,
            answer = answer.text,
            chatThread = thread
        )
        aiChat.annotations = answer.annotations.map {
            AIChatAnnotationEntity(
                endIndex = it.endIndex,
                startIndex = it.startIndex,
                title = it.title,
                type = it.type,
                url = it.url,
                aiChat = aiChat
            )
        }
        val chat = aiChatRepository.save(aiChat)
        log.info(chat.answer)

        //decrease question quota by one
        if (authenticatedUser.userType!=UserType.CORPORATE.name){
            user.questionsQuota -= 1
            loginAccountRepository.save(user)
        }

        return AIChatResponse(
            chat.id,
            chat.question,
            chat.answer,
            userProfileUtil.retrieveProfile(chat.createdBy!!),
            TimeUtil.toEpochMillis(chat.createdDate),
            questionsQuota = user.questionsQuota,
            answer.annotations.map {
                com.centuroglobal.shared.data.payload.openai.Annotation.ModelMapper.from(it)
            }
        )
    }

    private fun isCgCorporate(authenticatedUser: AuthenticatedUser): Boolean {
        return authenticatedUser.userType == UserType.CORPORATE.name && authenticatedUser.partnerId == null
    }

    @Transactional(readOnly = true)
    fun getMessages(threadId: String, userId: Long): AIThreadResponse {

        val user = loginAccountRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val thread = chatThreadRepository.findByThreadId(threadId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        val userProfile = userProfileUtil.retrieveProfile(userId)

        val chats =thread.chats.map {
            AIChatResponse(
                it.id,
                it.question,
                it.answer,
                userProfile,
                TimeUtil.toEpochMillis(it.createdDate),
                questionsQuota = user.questionsQuota,
                it.annotations?.map { annotation ->
                    com.centuroglobal.shared.data.payload.openai.Annotation.ModelMapper.fromEntity(annotation)
                }?: emptyList()
            )
        }
        return AIThreadResponse(thread.id, thread.threadId, thread.subject,
            TimeUtil.toEpochMillis(thread.createdDate), chats)
    }

    fun getThreads(search: String?, userId: Long): List<AIThreadResponse> {
        val user = loginAccountRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val searchText = if (search.isNullOrBlank()) null else "%$search%"

        val threads = chatThreadRepository.searchByCriteria(searchText, user.id!!)

        return threads.map {
            AIThreadResponse(
                it.id,
                it.threadId,
                it.subject,
                TimeUtil.toEpochMillis(it.createdDate)
            )
        }
    }

}