package com.centuroglobal.service

import com.centuroglobal.shared.costofliving.data.entity.CountryEntity
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.subscription.SubscriptionDetailsEntity
import com.centuroglobal.shared.data.entity.subscription.SubscriptionPlanEntity
import com.centuroglobal.shared.data.entity.travel.TravelAssessmentFeedbackEntity
import com.centuroglobal.shared.data.enums.Common
import com.centuroglobal.shared.data.enums.stripe.TemplateType
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.subscription.SubscriptionPlanRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentFeedbackRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.DownloadWrapper
import com.centuroglobal.shared.util.MEDIA_TYPE_OCTET_STREAM
import mu.KotlinLogging
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.ResponseEntity
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.InputStream
import java.io.OutputStream
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlin.io.path.fileSize
import kotlin.io.path.inputStream


private val log = KotlinLogging.logger {}

private const val SHEET_PROCESSING = "sheet processing "

private const val TCA_FEEDBACK = "TCA_FEEDBACK"

@Service
class AdminMasterDataService(
    @Value("\${app.aws.s3.master-data-bucket}")
    private val masterDateS3Bucket: String,
    private val s3Service: AwsS3Service,
    private val countryTaxRepository: CountryTaxRepository,
    private val countryHighlightsRepository: CountryHighlightsRepository,
    private val countryRepository: CountryRepository,
    private val masterDataRepository: MasterDataRepository,
    private val masterContentRepository: MasterContentRepository,
    private val immigrationRequirementRepository: ImmigrationRequirementRepository,
    private val entityTypeRepository: EntityTypeRepository,
    private val authoritiesRepository: AuthoritiesRepository,
    private val milestonesRepository: MilestonesRepository,
    private val caseStatusMasterRepository: CaseStatusMasterRepository,
    private val subscriptionPlanRepository: SubscriptionPlanRepository,
    private val businessIndustryRepository: BusinessIndustryRepository,
    private val excelService: ExcelService,
    private val tcaFeedbackRepository: TravelAssessmentFeedbackRepository
) {

    @Transactional
    fun downloadTemplate(templateType: String): ResponseEntity<StreamingResponseBody> {
        return try {
            DownloadWrapper.downloadFile(
                "${templateType}.xlsx",
                MEDIA_TYPE_OCTET_STREAM
            ) { out: OutputStream -> downloadExcel(out, templateType) }
        } catch (ex: ApplicationException) {
            val streamError = StreamingResponseBody { out ->
                out.write(ex.error.errorMessage.toByteArray(Charsets.UTF_8))
                out.flush()
                out.close()
            }
            ResponseEntity(streamError, ex.error.httpStatus!!)
        }
    }

    private fun downloadExcel(out: OutputStream, fileName: String) {
        var input: InputStream? = null
        try {
            input = s3Service.downLoadFile(masterDateS3Bucket, fileName)
            input.copyTo(out)
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> throw ApplicationException(ErrorCode.NOT_FOUND)
            }
        } finally {
            try {
                input?.close()
            } catch (ex: Exception) {
                log.debug(ex.message)
            }
        }
    }


    fun setTaxData(sheet: XSSFSheet, templateType: String, userId: Long, displayName: String, template: MultipartFile) {
        val countryTaxList = ArrayList<CountryTaxEntity>()
        for (rowNo in 1..sheet.lastRowNum) {
            val countryCode = excelService.setValue(sheet, rowNo, 1)
            if (countryCode != null) {
                val countryTax = countryTaxRepository.findAllByCountryCode(countryCode)
                when (templateType) {
                    TemplateType.CORPORATE_TAX.name -> countryTax.corporateTaxRate =
                        excelService.setValue(sheet, rowNo, 2)?.toLong()
                    TemplateType.EMPLOYEE_TAX.name -> countryTax.employeeTaxRate =
                        excelService.setValue(sheet, rowNo, 2)?.toLong()
                    TemplateType.EMPLOYER_TAX.name -> countryTax.employerTaxRate =
                        excelService.setValue(sheet, rowNo, 2)?.toLong()
                }
                countryTaxList.add(countryTax)
            }
        }
        countryTaxRepository.saveAll(countryTaxList)
        setMasterData(templateType, userId, displayName)
        s3TemplateUpload(template, templateType)
    }

    fun setCountryHighlightsData(
        sheet: XSSFSheet,
        templateType: String,
        userId: Long,
        displayName: String,
        template: MultipartFile
    ) {
        countryHighlightsRepository.deleteAll()
        val countryHighlightsList = ArrayList<CountryHighlightsEntity>()
        for (rowNo in 1..sheet.lastRowNum) {
            val countryHighlights = CountryHighlightsEntity(
                highlights = excelService.setValueWithoutTruncate(sheet, rowNo, 0)
            )
            countryHighlightsList.add(countryHighlights)
        }
        countryHighlightsRepository.saveAll(countryHighlightsList)
        setMasterData(templateType, userId, displayName)
        s3TemplateUpload(template, templateType)
    }

    fun setGlobalRankingData(
        sheet: XSSFSheet,
        templateType: String,
        userId: Long,
        displayName: String,
        template: MultipartFile
    ) {
        val countryList = ArrayList<CountryEntity>()
        for (rowNo in 1..sheet.lastRowNum) {
            val countryCode = excelService.setValue(sheet, rowNo, 1)
            if (countryCode != null) {
                val country = countryRepository.findAllByCountryCode(countryCode)
                country.overallRating = excelService.setValue(sheet, rowNo, 3)?.toLong()
                country.entrepreneurshipRating = excelService.setValue(sheet, rowNo, 4)?.toLong()
                country.openForBusinessRating = excelService.setValue(sheet, rowNo, 5)?.toLong()
                country.qualityOfLifeRating = excelService.setValue(sheet, rowNo, 6)?.toLong()
                country.socialPurposeRating = excelService.setValue(sheet, rowNo, 7)?.toLong()
                country.globalTravelRiskRating = excelService.setValue(sheet, rowNo, 8)?.toLong()
                country.gdpRating = excelService.setValue(sheet, rowNo, 9)?.toLong()
                country.economicFreedomRating = excelService.setValue(sheet, rowNo, 10)?.toLong()
                country.highestInflationRateRating = excelService.setValue(sheet, rowNo, 11)?.toLong()
                country.lowestInflationRateRating = excelService.setValue(sheet, rowNo, 12)?.toLong()
                country.dialCode = excelService.setValue(sheet, rowNo, 13)?.toString()
                countryList.add(country)
            }
        }
        countryRepository.saveAll(countryList)
        setMasterData(templateType, userId, displayName)
        s3TemplateUpload(template, templateType)
    }


    private fun setMasterData(templateType: String, userId: Long, displayName: String) {
        val masterData = masterDataRepository.findByDocType(templateType)
        masterData.lastUpdateByName = displayName
        masterData.lastUpdateBy = userId
        masterData.lastUploadDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
        masterDataRepository.save(masterData)
    }

    @Transactional
    fun getMasterData(): List<MasterDataEntity> {
        return masterDataRepository.findAll()
    }

    fun getMasterContent(templateType: String): MasterContentEntity {
        return masterContentRepository.findByDocType(templateType)
    }

    fun uploadMasterContent(
        templateType: String,
        userId: Long,
        content: String,
        displayName: String
    ): MasterContentEntity {
        val masterContent = MasterContentEntity(
            content = content,
            docType = templateType,
            lastUpdateBy = userId,
            lastUploadDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
        )
        masterContentRepository.save(masterContent)
        setMasterData(templateType, userId, displayName)
        return masterContent
    }

    @Async
    fun setImmigrationRequirement(
        workbook: XSSFWorkbook,
        templateType: String,
        userId: Long,
        displayName: String,
        template: MultipartFile
    ) {
        val tempDir: Path = Files.createTempDirectory("temp-files")
        val tempFile: Path = tempDir.resolve(template.getOriginalFilename())

        // Transfer the contents of the multipart file to the temporary file
        Files.copy(template.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING)

        log.info("Immigration master data upload processing started ")
        immigrationRequirementRepository.deleteAll()
        for (i in 3 until workbook.numberOfSheets) {
            val immigrationRequirementList = ArrayList<ImmigrationRequirementEntity>()
            val sheet = workbook.getSheetAt(i)
            log.info(SHEET_PROCESSING +sheet.sheetName)
            val source = excelService.setValueWithoutTruncate(sheet, 1, 0)
            for (rowNo in 1..sheet.lastRowNum) {
                val data = ImmigrationRequirementEntity(
                    sourceCountry = source,
                    destCountry = excelService.setValueWithoutTruncate(sheet, rowNo, 2),
                    businessVisa = visaFlag(sheet, rowNo, 4),
                    workVisa = visaFlag(sheet, rowNo, 5)
                )
                immigrationRequirementList.add(data)
            }
            log.info("Immigration master data upload processing completed ")
            immigrationRequirementRepository.saveAll(immigrationRequirementList)
        }
        setMasterData(templateType, userId, displayName)

        s3Service.uploadFile(tempFile.inputStream(),templateType,masterDateS3Bucket,tempFile.fileSize())
//        s3TemplateUpload(template, templateType)
    }


    @Async
    fun setEntityType(
        workbook: XSSFWorkbook,
        templateType: String,
        userId: Long,
        displayName: String,
        template: MultipartFile
    ) {
        log.info("Entity Type master data upload processing started ")
        entityTypeRepository.deleteAll()
        for (i in 2 until workbook.numberOfSheets) {
            val entityTypeList = ArrayList<EntityTypeEntity>()
            val sheet = workbook.getSheetAt(i)
            log.info(SHEET_PROCESSING + sheet.sheetName)
            val countryCode = excelService.setValueWithoutTruncate(sheet, 1, 0)

            for (rowNo in 1..sheet.lastRowNum) {
                val entityType = excelService.setValueWithoutTruncate(sheet, rowNo, 2)
                val data = EntityTypeEntity(countryCode = countryCode, entityType = entityType)
                if (entityType != null && countryCode != null) entityTypeList.add(data)
            }
            entityTypeRepository.saveAll(entityTypeList)
        }
        setMasterData(templateType, userId, displayName)
        s3TemplateUpload(template, templateType)
    }

    private fun visaFlag(sheet: XSSFSheet, rowNo: Int, i: Int): String {
        val data: String? = excelService.setValue(sheet, rowNo, i)
        return if (data.equals("Not Required", ignoreCase = true)) {
            Common.NOTREQUIRED.name
        } else {
            Common.REQUIRED.name
        }
    }

    private fun s3TemplateUpload(
        uploadedExcel: MultipartFile,
        templateType: String
    ) {
        try {
            s3Service.uploadFile(uploadedExcel, templateType, masterDateS3Bucket)
        } catch (e: Exception) {
            log.error("file upload failed", e)
           // throw ApplicationException(ErrorCode.FILE_UPLOAD_FAIL)
        }
    }

    fun getAuthorities(): List<AuthoritiesEntity> {
        return authoritiesRepository.findAll()
    }

    fun getEntityType(countryCode: String): List<EntityTypeEntity> {
        return entityTypeRepository.findAllByCountryCode(countryCode)
    }
    fun setMilestoneStatusData(workbook: XSSFWorkbook, templateType: String, userId: Long, displayName: String, template: MultipartFile) {

        milestonesRepository.deleteAll()
        caseStatusMasterRepository.deleteAll()

        for (i in 0 until workbook.numberOfSheets) {
            val milestonesEntityList = ArrayList<MilestonesEntity>()
            val caseStatusMasterEntityList = ArrayList<CaseStatusMasterEntity>()

            val sheet = workbook.getSheetAt(i)
            log.info(SHEET_PROCESSING + sheet.sheetName)

            for (rowNo in 1..sheet.lastRowNum) {
                val subCategory = sheet.sheetName
                val milestone = excelService.setValue(sheet, rowNo, 1).toString()
                val milestoneKey = excelService.setValue(sheet, rowNo, 2).toString()
                val statesText = excelService.setValue(sheet, rowNo, 3).toString()
                val status = excelService.setValue(sheet, rowNo, 4).toString()
                val percentage = excelService.setValue(sheet, rowNo, 5)?.toLongOrNull()
                val sequence = excelService.setValue(sheet, rowNo, 0)?.toLongOrNull()
                val actionFor = excelService.setValue(sheet, rowNo, 6).toString()
                val dealStatusId= excelService.setValue(sheet, rowNo, 7)?.toLongOrNull()
                val showValue= excelService.setValue(sheet, rowNo, 8)

                val show = showValue?.uppercase() == "YES"

                if (sequence != null && sequence>0 && !milestoneKey.isNullOrEmpty()) {
                    val entity = MilestonesEntity(
                        sequence = sequence,
                        milestone = milestone,
                        milestoneKey = milestoneKey,
                        status = status,
                        caseSubCategory = subCategory)
                    milestonesEntityList.add(entity)
                }
                if(percentage!=null) {
                    val entity = CaseStatusMasterEntity(
                        subCategory = subCategory,
                        status = status,
                        statusDisplayText = statesText,
                        percentage = percentage,
                        actionFor = actionFor,
                        dealStatusId = dealStatusId,
                        showStatus = show)
                    caseStatusMasterEntityList.add(entity)
                }
            }
            log.info(SHEET_PROCESSING + sheet.sheetName+" end")
            milestonesRepository.saveAll(milestonesEntityList)
            caseStatusMasterRepository.saveAll(caseStatusMasterEntityList)
            log.info("data saved")
        }
        setMasterData(templateType, userId, displayName)
        s3TemplateUpload(template, templateType)
        log.info("File uploaded to S3")
    }

    @Transactional
    fun setSubscriptionData(workbook: XSSFWorkbook, userId: Long, displayName: String) {

        subscriptionPlanRepository.deleteAllByCompanyId(-1)

        val plans = mutableListOf<SubscriptionPlanEntity>()
        for (i in 0 until workbook.numberOfSheets) {

            val sheet = workbook.getSheetAt(i)
            log.info(SHEET_PROCESSING + sheet.sheetName)

            val planName = sheet.sheetName
            val planDetails = mutableListOf<SubscriptionDetailsEntity>()

            val plan = SubscriptionPlanEntity(
                name = planName,
                currency = excelService.setValue(sheet, i, 6)!!,
                companyId = -1,
                companyType = "CORPORATE",
                price = excelService.setValue(sheet, i, 8)!!.toFloat(),
                startDate = null,
                endDate = null
            )

            for (rowNo in 1..sheet.lastRowNum) {

                planDetails.add(
                    SubscriptionDetailsEntity(
                        name = excelService.setValue(sheet, rowNo, 1)!!,
                        code = excelService.setValue(sheet, rowNo, 2)!!,
                        threshold = excelService.setValue(sheet, rowNo, 3)!!.toLong(),
                        unit = excelService.setValue(sheet, rowNo, 4)!!,
                        overageRate = excelService.setValue(sheet, rowNo, 5)!!.toFloat(),
                        trackingDuration = excelService.setValue(sheet, rowNo, 7)!!,
                        plan = plan
                    )
                )
            }
            plan.modules = planDetails
            plans.add(plan)
        }
        subscriptionPlanRepository.saveAll(plans)
    }

    fun getBusinessIndustries(): List<BusinessIndustryEntity> {
        return businessIndustryRepository.findAll()
    }

    fun updateTCAFeedback(source: String, destination: String, content: String, userId: Long, displayName: String): MasterContentEntity {

        var tcaFeedback = tcaFeedbackRepository.findBySourceAndDestination(source, destination)

        if (tcaFeedback == null) {
                tcaFeedback = TravelAssessmentFeedbackEntity(
                    source = source,
                    destination = destination,
                    content = content
            )
        } else {
            tcaFeedback.content = content
        }

        tcaFeedback = tcaFeedbackRepository.save(tcaFeedback)
        setMasterData(TCA_FEEDBACK, userId, displayName)

        return tcaFeedbackToMasterContent(tcaFeedback)
    }
    fun getTCAFeedback(source: String, destination: String): MasterContentEntity {
        val feedback = tcaFeedbackRepository.findBySourceAndDestination(source, destination)

        return tcaFeedbackToMasterContent(feedback)
    }

    private fun tcaFeedbackToMasterContent(feedback: TravelAssessmentFeedbackEntity?): MasterContentEntity {
        return MasterContentEntity(
            TCA_FEEDBACK, feedback?.content ?: "",
            LocalDateTime.now(), feedback?.updatedBy
        )
    }

}