package com.centuroglobal.service

import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.ReminderEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.*
import com.centuroglobal.shared.data.payload.account.UpdateCompanyInfoRequest
import com.centuroglobal.shared.data.pojo.ClientUser
import com.centuroglobal.shared.data.pojo.CorporateDocumentResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserDocumentSearchFilter
import com.centuroglobal.shared.data.pojo.aws.AwsS3FileMetadata
import com.centuroglobal.shared.data.pojo.passportvisa.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.CorporateAccessUtil
import com.centuroglobal.util.DownloadUtil
import com.centuroglobal.util.UserAccessUtil
import feign.Response
import mu.KotlinLogging
import org.apache.commons.lang3.RandomStringUtils
import org.apache.tika.Tika
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.ResponseEntity
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayInputStream
import java.io.InputStreamReader
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletableFuture

private val log = KotlinLogging.logger {}


@Service
class AdminUserService(
    @Value("\${app.aws.s3.user-profile-folder}")
    private val userProfileFolder: String,
    @Value("\${app.user-profile.picture.allowed.file.types:image/png, image/jpeg, image/jpg}")
    private val profilePictureAllowedFileTypes: List<String>,
    @Value("\${app.aws.s3.corporate-doc-folder}")
    private val corporateDocsFolder: String,
    @Value("\${app.aws.s3.user-doc-folder}")
    private val userDocsFolder: String,

    private val loginAccountRepository: LoginAccountRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val corporateRepository: CorporateRepository,
    private val validationTokenRepository: ValidationTokenRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val passwordEncoder: PasswordEncoder,
    private val onBoardingRepository: OnBoardingRepository,
    private val backofficeUserRepository: BackofficeUserRepository,
    private val tokenVerificationService: TokenVerificationService,
    private val awsS3Service: AwsS3Service,
    private val adminAuthoritiesRepository: AdminAuthoritiesRepository,
    private val bandsRepository: BandsRepository,
    private val clientDocRepository: ClientDocRepository,
    private val reminderRepository: ReminderRepository,
    private val passportVisaRepository: PassportVisaRepository,
    private val clientDocFileRepository: ClientDocFileRepository,
    private val openAIService: OpenAIService,
    private val corporateAccessUtil: CorporateAccessUtil

) {

    @Autowired
    lateinit var pyApiClient: PythonApiClient

    @Transactional
    fun deleteUser(userId: Long, authenticatedUser: AuthenticatedUser): CompletableFuture<String> {
        try {
            val loginAccount =
                loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
            when (loginAccount.getUserType()) {
                UserType.CORPORATE -> deleteCorporateByRootUserId(loginAccount.id!!)
                UserType.EXPERT -> deleteExpertUser(loginAccount.id!!)
                UserType.BACKOFFICE -> deleteBackofficeUser(loginAccount.id!!)
                else -> {
                    throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
                }

            }
            return CompletableFuture.completedFuture(AppConstant.SUCCESS_RESPONSE_STRING)
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> {
                    if (ex.error == ErrorCode.INTERNAL_SERVER_ERROR) {
                        throw ApplicationException(ErrorCode.DELETE_USER_FAIL)
                    } else {
                        throw ex
                    }
                }

                else -> {
                    ex.printStackTrace()
                    throw throw ApplicationException(ErrorCode.DELETE_USER_FAIL)
                }
            }
        }
    }

    private fun deleteBackofficeUser(id: Long) {
        adminAuthoritiesRepository.deleteAllByUserId(id)
        backofficeUserRepository.deleteById(id)
//        loginAccountRepository.deleteById(id)
    }

    fun deleteCorporateByRootUserId(loginAccountId: Long) {
        onBoardingRepository.deleteAllByRootUserId(loginAccountId)
        corporateUserRepository.deleteById(loginAccountId)
        bandsRepository.deleteByCorporateRootUserId(loginAccountId)
        corporateRepository.deleteAllByRootUserId(loginAccountId)
        validationTokenRepository.deleteAllByUserId(loginAccountId)
    }

    private fun deleteExpertUser(loginAccountId: Long) {
        val users =
            expertUserRepository.findByIdOrNull(loginAccountId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val expertUser = expertUserRepository.findAllByCompanyProfile(users.companyProfile)
        if (expertUser.size == 1) {
            expertCompanyProfileRepository.deleteById(users.companyProfile?.id!!)
        }
        expertUserRepository.deleteById(loginAccountId)
        validationTokenRepository.deleteAllByUserId(loginAccountId)
    }

    fun temporaryPassword(userId: Long, authenticatedUser: AuthenticatedUser): String {
        val user = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val password = password(userId, true)
        val validationToken = validationTokenRepository.findByUserId(userId)
        if (validationToken != null) {
            validationToken.expiryDate = LocalDateTime.now().plusDays(7)
            validationTokenRepository.save(validationToken)
        } else if (user.status != AccountStatus.ACTIVE) {
            val type = getUserValidationType(user)
            tokenVerificationService.createToken(type, user, ValidationState.EMAIL_SENT)
        } else {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }
        return password
    }

    private fun getUserValidationType(user: LoginAccountEntity): ValidationType {

        return when (AdminAccessUtil.getUserType(user)) {
            UserType.EXPERT -> {
                if ((user as ExpertUserEntity).companyProfile?.companyType == ExpertCompanyType.SUPPLIER) {
                    ValidationType.INVITE_PARTNER_EXPERT
                } else {
                    ValidationType.INVITE_EXPERT
                }
            }

            UserType.BACKOFFICE -> ValidationType.INVITE_BACKOFFICE
            UserType.CORPORATE -> {
                if ((user as CorporateUserEntity).corporate.partner != null) {
                    ValidationType.INVITE_PARTNER_CORPORATE
                } else {
                    ValidationType.CORPORATE_SIGNUP
                }
            }

            UserType.PARTNER -> ValidationType.PARTNER_SIGNUP
        }
    }

    fun accessPassword(userId: Long, authenticatedUser: AuthenticatedUser): String {
        return password(userId, false)
    }

    private fun password(userId: Long, temporaryPassword: Boolean): String {
        try {
            val loginAccount =
                loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
            val tempPassword = RandomStringUtils.randomAlphanumeric(12)
            val password = passwordEncoder.encode(tempPassword)
            if (temporaryPassword) {
                loginAccount.tempPassword = password
            } else {
                if (loginAccount.status != AccountStatus.ACTIVE) {
                    throw ApplicationException(ErrorCode.PASSWORD_FOR_ACTIVE_USER_ONLY)
                }
                loginAccount.oneTimePassword = password
            }
            loginAccountRepository.save(loginAccount)
            return tempPassword
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> {
                    if (ex.error == ErrorCode.INTERNAL_SERVER_ERROR) {
                        throw ApplicationException(ErrorCode.DELETE_USER_FAIL)
                    } else {
                        throw ex
                    }
                }

                else -> throw throw ApplicationException(ErrorCode.DELETE_USER_FAIL)
            }
        }
    }

    fun getClientUsers(): List<ClientUser> {
        return loginAccountRepository.findByStatusIn(
            listOf(
                AccountStatus.ACTIVE.name,
                AccountStatus.PENDING_VERIFICATION.name,
                AccountStatus.DRAFT.name
            )
        )
    }

    fun getClientExpertAndAdminUsers(): List<ClientUser> {
        return loginAccountRepository.findByStatusAndRoleIn(
            AccountStatus.ACTIVE.name, listOf(
                Role.ROLE_SUPER_ADMIN.name,
                Role.ROLE_ADMIN.name,
                Role.ROLE_EXPERT.name,
            )
        )
    }

    fun uploadProfilePicture(userId: Long, requestedByUserId: Long, photo: MultipartFile): String? {
        val loginUser = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        checkValidPictureType(photo)
        val existingProfileKey = loginUser.profilePhotoUrl.orEmpty()
        if (existingProfileKey.isNotBlank()) {
            awsS3Service.deleteFileByS3Key(existingProfileKey)
        }

        val key = uploadFileToS3(photo, userProfileFolder)
        loginUser.profilePhotoUrl = key
        loginUser.lastUpdatedBy = requestedByUserId
        loginAccountRepository.save(loginUser)
        return awsS3Service.getS3Url(key!!)
    }

    private fun checkValidPictureType(file: MultipartFile): Boolean {
        val mimeType = Tika().detect(file.bytes)
        log.debug("Uploaded profile picture file mime type: $mimeType")
        val isValid = profilePictureAllowedFileTypes.contains(mimeType.lowercase())
        if (isValid) {
            return true
        } else {
            log.error("Invalid profile picture file: ${file.originalFilename} with mime type: $mimeType")
            throw ApplicationException(ErrorCode.PICTURE_TYPE_INVALID)
        }
    }

    private fun uploadFileToS3(file: MultipartFile?, folder: String, isPublicAccess: Boolean = false): String? {
        return if (file != null) {
            val awsFileData: AwsS3FileMetadata? = awsS3Service.uploadFile(file, folder, isPublicAccess)
            awsFileData?.key
        } else {
            throw ApplicationException(ErrorCode.FILE_UPLOAD_FAIL)
        }
    }

    @Transactional
    fun deleteProfilePicture(userId: Long, requestedById: Long): String? {
        val loginUser = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val photoKey = loginUser.profilePhotoUrl
        if (!photoKey.isNullOrBlank()) {
            awsS3Service.deleteFileByS3Key(photoKey)
        }

        loginUser.profilePhotoUrl = null
        loginUser.lastUpdatedBy = requestedById
        loginAccountRepository.save(loginUser)
        return AppConstant.SUCCESS_RESPONSE_STRING
    }

    //TODO move all the APIs related to documents to new service DocumentService
    @Transactional
    fun uploadDocumentCorporate(
        request: CorporateDocumentRequest,
        corporateId: Long,
        user: AuthenticatedUser
    ): Boolean {

        if (!UserAccessUtil.hasAccessToCompanyDocuments(user)) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        val s3KeyPath = "$corporateDocsFolder/${corporateId}/${request.s3Key}"
        awsS3Service.uploadFromTmp(request.s3Key, s3KeyPath)

        val docEntity = clientDocRepository.save(
            ClientDocEntity(
                docType = ClientDocType.CORPORATE,
                docName = request.docName,
                docKey = s3KeyPath,
                fileName = request.fileName,
                fileType = request.fileType,
                fileSize = request.fileSize,
                country = request.country,
                expiryDate = request.expiryDate?.let { TimeUtil.fromInstantMillis(it) },
                referenceId = corporateId,
                referenceType = UserDocType.CORPORATE,
                createdBy = user.userId
            )
        )
        if (docEntity.expiryDate != null) {
            val reminder = ReminderEntity(
                referenceId = corporateId,
                trackType = CaseTrackType.EXACT,
                date = docEntity.expiryDate,
                deadlineAgainst = CaseDeadlineType.COMPANY_DOCUMENT_EXPIRY,
                documentId = docEntity.id
            )
            reminderRepository.save(reminder)
        }
        return true
    }

    @Transactional
    fun uploadDocumentUser(request: UserDocumentRequest, user: AuthenticatedUser, userId: Long): Boolean {

        val requestUserId = request.userId ?: userId

        val loginAccountEntity = corporateAccessUtil.getUser(requestUserId, user, userId)

       /* if (request.docSubType == DocSubType.RESUME.name) {
            val docRequest = ExtractDocDetailsRequest(
                doc_type = request.docSubType,
                bucket = awsS3Service.getBucket(),
                s3Key = request.fileData.map { it.s3Key }.toMutableList()//mutableListOf(s3KeyPath)
            )
            request.docName = docRequest.doc_type + "-" + request.issueCountry
            val res = extractDocumentMetadata(docRequest)
            saveClientDoc(user, res, request, loginAccountEntity)
        } else {*/
            saveClientDoc(user, null, request, loginAccountEntity)
        //}

        return true
    }


    private fun saveClientDoc(
        user: AuthenticatedUser, res: String?,
        request: UserDocumentRequest,
        loginAccountEntity: CorporateUserEntity
    ): Long? {

        val clientDoc = ClientDocEntity(
            docType = ClientDocType.USER,
            docName = request.docName,
            docKey = "",
            fileName = "",
            fileType = "",
            fileSize = request.fileData.sumOf { it.fileSize },
            expiryDate = request.expiryDate?.let { TimeUtil.fromInstantMillis(it) },
            referenceId = loginAccountEntity.id!!,
            referenceType = UserDocType.USER,
            createdBy = user.userId,
            docSubType = request.docSubType,
            issueCountry = request.issueCountry,
            metadata = res
        )

        val clientDocEntity = clientDocRepository.save(clientDoc)

        clientDoc.clientDocFile = request.fileData.map {

            val s3KeyPath = "$userDocsFolder/${loginAccountEntity.id}/${it.s3Key}"
            awsS3Service.uploadFromTmp(it.s3Key, s3KeyPath)

            clientDocFileRepository.save(
                ClientDocFileEntity(
                    clientDoc = clientDoc,
                    docKey = s3KeyPath,
                    fileName = it.fileName,
                    fileType = it.fileType,
                    fileSize = it.fileSize
                )
            )
        }.toMutableList()

        return clientDocEntity.id
    }

    @Transactional
    fun updatePassport(request: PassportDocumentRequest, user: AuthenticatedUser, userId: Long): Boolean {

        val requestUserId = request.userId ?: userId

        val loginUser = corporateAccessUtil.getUser(requestUserId, user, userId)

        val clientDocId = processDocFiles(user, request.docFiles, request.expiryDate, request.nationality, loginUser)

        passportVisaRepository.save(
            PassportVisaEntity(
                user = loginUser,
                birthPlace = request.birthPlace,
                birthDate = request.birthDate,
                issuePlace = request.issuePlace,
                issueDate = request.issueDate,
                nationality = request.nationality,
                gender = request.gender,
                expiryDate = request.expiryDate,
                docType = "passport",
                clientDocId = clientDocId
            )
        )

        return true;
    }


    private fun processDocFiles(
        user: AuthenticatedUser,
        docFiles: MutableList<UserDocumentFileRequest>?,
        expiryDate: String,
        nationality: String,
        loginAccountEntity: CorporateUserEntity
    ): Long? {
        if (!docFiles.isNullOrEmpty()) {
            val docFileRequest = docFiles.map {
                UserDocumentFileRequest(
                    s3Key = it.s3Key,
                    fileName = it.fileName,
                    fileType = it.fileType,
                    fileSize = it.fileSize
                )
            }.toMutableList()

            val docType = docFileRequest[0].fileType
            val expiryDateLong =
                TimeUtil.toEpochMillis(LocalDate.parse(expiryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")))
            val userDocumentRequest = UserDocumentRequest(
                docName = "$docType-${loginAccountEntity.firstName.substring(0,1)}-${loginAccountEntity.lastName}-${loginAccountEntity.countryCode}",
                fileData = docFileRequest,
                expiryDate = expiryDateLong,
                docSubType = docType,
                issueCountry = getCountryCode(nationality),
                userId = loginAccountEntity.id!!
            )

            return saveClientDoc(user, null, userDocumentRequest, loginAccountEntity)

        }
        return 0
    }

    fun getCountryCode(nationality: String): String {

        val thread = openAIService.createThread()
        val question =
            "Please provide the 2-character country code (ISO 3166-1 alpha-2 code) for the following country or " +
                    "nationality:$nationality? " +
                    "For example, if the nationality is 'Indian', the answer should be 'IN'. " +
                    "Answer only Country Code in UPPER case. if not found answer as NOT_FOUND"

        val messages = openAIService.getAnswerFromThread(thread.id, question)

        val answerText = openAIService.readMessageText(messages)

        return if(answerText == "NOT_FOUND") {
            ""
        } else {
            answerText
        }
    }

    @Transactional
    fun updateVisa(request: VisaDocumentRequest, user: AuthenticatedUser, userId: Long): Boolean {

        val requestUserId = request.userId ?: userId

        val loginUser = corporateAccessUtil.getUser(requestUserId, user, userId)

        val clientDocId = processDocFiles(user, request.docFiles, request.expiryDate, request.country, loginUser)

        passportVisaRepository.save(
            PassportVisaEntity(
                user = loginUser,
                visaType = request.visaType,
                nationality = request.country,
                issueDate = request.issueDate,
                expiryDate = request.expiryDate,
                docType = "visa",
                clientDocId = clientDocId
            )
        )

        return true;
    }

    @Transactional
    fun migrateClientDocsFiles(user: AuthenticatedUser): Boolean {

        val loginUser =
            loginAccountRepository.findByIdOrNull(user.userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        val clientDocs = clientDocRepository.findAll()

        clientDocs.forEach { doc ->
            clientDocFileRepository.save(
                ClientDocFileEntity(
                    clientDoc = doc,
                    docKey = doc.docKey,
                    fileName = doc.fileName,
                    fileType = doc.fileType,
                    fileSize = doc.fileSize
                )
            )
        }
        return true;
    }

    fun extractDocumentMetadata(request: ExtractDocDetailsRequest): String {

        request.bucket = awsS3Service.getBucket()
        val response = pyApiClient.extractDocDetails(request)

        val respBody = getResponseBody(response)
        if (response.status() == 200) {

            return respBody

        } else {
            log.error { "Could not extract details from the document, error - $respBody" }
            throw ApplicationException(ErrorCode.CAN_NOT_EXTRACT_DOC_DETAILS)
        }
    }

    fun extractDocumentMetadataResponse(request: ExtractDocDetailsRequest): DocumentMetadataGeneric {

        request.bucket = awsS3Service.getBucket()
        return pyApiClient.extractDocDetailsResponse(request)

//        val respBody = getResponseBody(response)
//        if (response.status() == 200) {
//
//            return respBody
//
//        } else {
//            log.error { "Could not extract details from the document, error - $respBody" }
//            throw ApplicationException(ErrorCode.CAN_NOT_EXTRACT_DOC_DETAILS)
//        }
    }


    private fun getResponseBody(response: Response): String {

        var bodyContent = ""
        response.body()

        response.body()?.let { responseBody ->
            InputStreamReader(responseBody.asInputStream()).use { reader ->
                bodyContent = reader.readText()
                log.info("Response Body: $bodyContent")
            }
        } ?: log.info("No Response Body")

        return bodyContent
    }

    fun listPassportVisa(user: AuthenticatedUser, userId : Long?): PassportVisaResponse {

        val pas = passportVisaRepository.searchByUserId(userId ?: user.userId)

        val passportVisaData = pas.map {
            PassportVisaDto(
                id = it.id,
                issueDate = it.issueDate,
                docType = it.docType,
                nationality = it.nationality,
                visa = it.visaType,
                expiryDate = it.expiryDate,
                updatedBy = it.updatedBy,
                lastUpdatedDate = it.lastUpdatedDate,
                filesUploaded = it.clientDocId?.let { it1 -> passportVisaRepository.getCountDocFiles(it1) }
            )
        }

        val passportResponse = passportVisaData.filter { it.docType == "passport" }.map {
            PassportResponse(
                id = it.id,
                nationality = it.nationality,
                issueDate = it.issueDate,
                expiryDate = it.expiryDate,
                updatedBy = it.updatedBy?.let { it1 -> loginAccountRepository.getFirstNameLastNameById(it1) },
                updatedDate = it.lastUpdatedDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                filesUploaded = it.filesUploaded
            )
        }

        val visaResponse = passportVisaData.filter { it.docType == "visa" }.map {
            VisaResponse(
                id = it.id,
                visaType = it.visa,
                country = it.nationality,
                issueDate = it.issueDate,
                expiryDate = it.expiryDate,
                updatedBy = it.updatedBy?.let { it1 -> loginAccountRepository.getFirstNameLastNameById(it1) },
                updatedDate = it.lastUpdatedDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                filesUploaded = it.filesUploaded
            )
        }

        return PassportVisaResponse(
            passportList = passportResponse,
            visaList = visaResponse
        )
    }

    fun retrievePassport(user: AuthenticatedUser, passportId: Long, userId: Long?): GetPassportResponse {

        val passportEntity = passportVisaRepository.retrievePassportVisaByDocType(
            userId ?: user.userId, passportId,
            "passport"
        )

        if (passportEntity == null)
            throw ApplicationException(ErrorCode.NOT_FOUND)

        var listClientDocFiles = passportEntity.clientDocId?.let { passportVisaRepository.getClientDocFiles(it) }

        var listClientDocFileRes: List<CLientDocFileDto> = emptyList()

        if (listClientDocFiles != null) {
            listClientDocFileRes = listClientDocFiles.map {
                CLientDocFileDto(
                    fileName = it.fileName,
                    fileSize = it.fileSize,
                    fileType = it.fileType,
                    docKey = it.docKey
                )
            }
        }

        return GetPassportResponse(
            id = passportEntity.id,
            birthPlace = passportEntity.birthPlace,
            birthDate = passportEntity.birthDate,
            issuePlace = passportEntity.issuePlace,
            gender = passportEntity.gender,
            nationality = passportEntity.nationality,
            issueDate = passportEntity.issueDate,
            expiryDate = passportEntity.expiryDate,
            updatedBy = passportEntity.updatedBy?.let { it1 -> loginAccountRepository.getFirstNameLastNameById(it1) },
            updatedDate = passportEntity.lastUpdatedDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
            filesUploaded = listClientDocFileRes
        )
    }

    fun retrieveVisa(user: AuthenticatedUser, visaId: Long, userId: Long?): GetVisaResponse {

        val visaEntity = passportVisaRepository.retrievePassportVisaByDocType(
            userId ?: user.userId, visaId,
            "visa"
        )

        if (visaEntity == null)
            throw ApplicationException(ErrorCode.NOT_FOUND)

        val listClientDocFiles = visaEntity.clientDocId?.let { passportVisaRepository.getClientDocFiles(it) }

        var listClientDocFileRes: List<CLientDocFileDto> = emptyList()

        if (listClientDocFiles != null) {
            listClientDocFileRes = listClientDocFiles.map {
                CLientDocFileDto(
                    fileName = it.fileName,
                    fileSize = it.fileSize,
                    fileType = it.fileType,
                    docKey = it.docKey
                )
            }
        }

        return GetVisaResponse(
            id = visaEntity.id,
            visaType = visaEntity.visaType,
            country = visaEntity.nationality,
            issueDate = visaEntity.issueDate,
            expiryDate = visaEntity.expiryDate,
            updatedBy = visaEntity.updatedBy?.let { it1 -> loginAccountRepository.getFirstNameLastNameById(it1) },
            updatedDate = visaEntity.lastUpdatedDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
            filesUploaded = listClientDocFileRes
        )
    }

    @Transactional(readOnly = true)
    fun listDocuments(
        filter: UserDocumentSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser,
        isCorporate: Boolean
    ): PagedResult<CorporateDocumentResponse> {

        if (isCorporate && !UserAccessUtil.hasAccessToCompanyDocuments(authenticatedUser)) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        val documents = clientDocRepository.searchByCriteria(filter, pageRequest)

        val documentResponse = documents.map {
            val user = it.createdBy?.let { it1 -> loginAccountRepository.findByIdOrNull(it1) }
            CorporateDocumentResponse.ModelMapper.from(it, "${user!!.firstName} ${user.lastName}")
        }.toList()

        return PagedResult.ModelMapper.from(documents, documentResponse)
    }

    @Transactional
    fun deleteDocumentCorporate(id: Long, corporateId: Long, user: AuthenticatedUser): Boolean {

        if (!UserAccessUtil.hasAccessToCompanyDocuments(user)) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        val doc = clientDocRepository.findByIdAndReferenceId(id, corporateId) ?: throw ApplicationException(
            ErrorCode.NOT_FOUND
        )
        clientDocRepository.deleteById(id)
        awsS3Service.deleteFileByS3Key(doc.docKey)
        reminderRepository.deleteAllByDeadlineAgainstAndDocumentIdIn(
            CaseDeadlineType.COMPANY_DOCUMENT_EXPIRY,
            listOf(id)
        )
        return true
    }

    @Transactional
    fun deleteDocumentUser(id: Long, user: AuthenticatedUser, userId: Long?): Boolean {

        val doc = clientDocRepository.findByIdAndReferenceId(id, userId?:user.userId) ?: throw ApplicationException(
            ErrorCode.NOT_FOUND
        )
        clientDocRepository.deleteById(id)
        doc.clientDocFile?.forEach {
            awsS3Service.deleteFileByS3Key(it.docKey)
        }
        if (doc.docKey.isNotEmpty()){
            awsS3Service.deleteFileByS3Key(doc.docKey)
        }

        return true
    }

    @Transactional
    fun deletePassportVisa(id: Long, user: AuthenticatedUser, userId: Long?): Boolean {

        val passportVisaEntity = passportVisaRepository.retrievePassportVisa(userId?:user.userId, id)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        var listClientDocFiles = passportVisaEntity.clientDocId?.let { passportVisaRepository.getClientDocFiles(it) }

        listClientDocFiles?.forEach { awsS3Service.deleteFileByS3Key(it.docKey) }

        passportVisaEntity.clientDocId?.let { clientDocRepository.deleteById(it) }

        passportVisaRepository.deleteById(id)

        return true
    }

    fun getDocUrlCorporate(fileId: Long, corporateId: Long, user: AuthenticatedUser): String? {

        if (!UserAccessUtil.hasAccessToCompanyDocuments(user)) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val doc = getDocumentEntity(fileId, corporateId)
        return awsS3Service.getS3Url(doc.docKey)
    }

    fun getDocUrlUser(fileId: Long, user: AuthenticatedUser, userId: Long?): String? {

        //val doc = getDocumentEntity(fileId, userId?:user.userId)
        val file = clientDocFileRepository.findByIdOrNull(fileId)
        if (file != null) {
            return awsS3Service.getS3Url(file.docKey)
        } else
            throw ApplicationException(ErrorCode.NOT_FOUND)
    }

    fun viewPassportVisa(s3key: String): String {
        return awsS3Service.getS3Url(s3key)
    }

    private fun getDocumentEntity(
        fileId: Long,
        refId: Long
    ): ClientDocEntity {
        return clientDocRepository.findByIdAndReferenceId(fileId, refId) ?: throw ApplicationException(
            ErrorCode.NOT_FOUND
        )
    }

    @Transactional
    fun updateCorporateInfo(updateRequest: UpdateCompanyInfoRequest, user: AuthenticatedUser): Boolean {

        val corporate =
            corporateRepository.findById(user.companyId!!).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        corporate.name = updateRequest.companyName
        corporate.countryCode = updateRequest.countryCode

        corporateRepository.save(corporate)
        return true
    }

    fun downloadDocCorporate(
        fileId: Long?,
        corporateId: Long,
        user: AuthenticatedUser
    ): ResponseEntity<StreamingResponseBody> {

        if (!UserAccessUtil.hasAccessToCompanyDocuments(user)) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        var fileName = "corporate-docs.zip"

        val inputStream = if (fileId != null) {
            val doc = getDocumentEntity(fileId, corporateId)
            fileName = doc.fileName
            //awsS3Service.downLoadFile("/${doc.docKey}")
            val docs = mutableListOf(doc)
            DownloadUtil.copyFilesAsZip(
                docs.associate { it.fileName to awsS3Service.downLoadFile("/${it.docKey}") }
            )
        } else {
            val docs = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(
                corporateId,
                UserDocType.CORPORATE,
                ClientDocType.CORPORATE
            )
            DownloadUtil.copyFilesAsZip(
                docs.associate { it.fileName to awsS3Service.downLoadFile("/${it.docKey}") }
            )
        }

        return DownloadUtil.downloadFile(fileName, inputStream)
    }

    fun downloadPassportVisaFiles(pvId: Long, user: AuthenticatedUser, userId: Long?): ResponseEntity<StreamingResponseBody> {

        var fileName = "passport-visa-docs.zip"

        val passportVisaEntity = passportVisaRepository.retrievePassportVisa(userId ?: user.userId, pvId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        if(passportVisaEntity.clientDocId == null || passportVisaEntity.clientDocId == 0L)
            throw ApplicationException(ErrorCode.NOT_FOUND)

        val inputStream = getInputStreamForDownload(passportVisaEntity.clientDocId!!)

        return DownloadUtil.downloadFile(fileName, inputStream)
    }

    fun getInputStreamForDownload(clientDocId: Long): ByteArrayInputStream {
        val clientDocFiles = passportVisaRepository.getClientDocFiles(clientDocId)

        return when {
            clientDocFiles.isNullOrEmpty() ->{
                throw ApplicationException(ErrorCode.NOT_FOUND)
            }

            else -> {
                DownloadUtil.copyFilesAsZip(
                    clientDocFiles.associate { it.fileName to awsS3Service.downLoadFile("/${it.docKey}") }
                )
            }
        }
    }

    fun downloadDocUser(fileId: Long?, user: AuthenticatedUser, userId: Long?): ResponseEntity<StreamingResponseBody> {

        var fileName = "user-docs.zip"

        val inputStream = if (fileId != null) {
            val doc = getDocumentEntity(fileId, userId ?: user.userId)

            if(doc.docKey.isNullOrEmpty()){
                getInputStreamForDownload(doc.id!!)
            } else{
                //fileName = doc.fileName
                val docs = mutableListOf(doc)
                DownloadUtil.copyFilesAsZip(
                    docs.associate { it.fileName to awsS3Service.downLoadFile("/${it.docKey}") }
                )
                //awsS3Service.downLoadFile("/${doc.docKey}")
            }
        } else {
            val docs = clientDocRepository
                .findByReferenceIdAndReferenceTypeAndDocType(userId ?: user.userId, UserDocType.USER, ClientDocType.USER)
            DownloadUtil.copyFilesAsZip(
                docs.associate { it.fileName to awsS3Service.downLoadFile("/${it.docKey}") }
            )
        }
        return DownloadUtil.downloadFile(fileName, inputStream)
    }

}