package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

@Service("apiAuthService")
class ApiAuthService(
    private val corporateRepository: CorporateRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateService: CorporateService
) {

    fun isAdminOrPartnerExpert(authenticatedUser: AuthenticatedUser, partnerId: Long?, id: Long?, type: String, microAccess: String?=""): Boolean {

        return when (authenticatedUser.role) {
            Role.ROLE_ADMIN.name, Role.ROLE_SUPER_ADMIN.name -> {
                isAdminOrSuperAdmin(authenticatedUser, microAccess!!)
            }
            Role.ROLE_PARTNER.name -> {

                if (partnerId!=null && authenticatedUser.partnerId != partnerId) {
                    return false
                }

                when (type) {

                    "CORPORATE" -> {
                        if(id!=null){
                            corporateRepository.findByIdAndPartnerId(id, authenticatedUser.partnerId!!) != null
                        }
                        else {
                            true
                        }
                    }
                    "CORPORATE_USER" -> {
                        if(id!=null) {
                            corporateUserRepository.findByIdAndCorporatePartnerId(id, authenticatedUser.partnerId!!) != null
                        }
                        else {
                            true
                        }
                    }
                    "EXPERT" -> {
                        if(id!=null) {
                            expertCompanyProfileRepository.searchByCompanyAndPartner(id, authenticatedUser.partnerId!!).isNotEmpty()
                        }
                        else {
                            true
                        }
                    }
                    "EXPERT_USER" -> {
                        if(id!=null) {
                            expertUserRepository.searchByCompanyAndPartner(id, authenticatedUser.partnerId!!).isNotEmpty()
                        }
                        else {
                            true
                        }
                    }
                    "PARTNER_USER" -> {
                            true
                    }
                    "USER" -> {
                        val user = loginAccountRepository.findById(id!!).getOrNull()
                        when {
                            user?.getUserType() == UserType.CORPORATE -> {
                                corporateUserRepository.findByIdAndCorporatePartnerId(id, authenticatedUser.partnerId!!) != null
                            }
                            user?.getUserType() == UserType.EXPERT -> {
                                expertUserRepository.searchByCompanyAndPartner(id, authenticatedUser.partnerId!!).isNotEmpty()
                            }
                            else -> {
                                false
                            }
                        }
                    }
                    "CASES" -> {
                        true
                    }
                    "QUERY"->{
                        true
                    }
                    "PROPOSAL" -> {
                        true
                    }
                    "TASK"->{
                        true
                    }
                    "CASE_FORM"->{
                        true
                    }
                    else -> {
                        false
                    }
                }
            }
            Role.ROLE_EXPERT.name, Role.ROLE_SUPPLIER.name -> {
                when(type) {
                    "TASK" -> {
                        true
                    }
                    else -> {
                        false
                    }
                }
            }
            Role.ROLE_CORPORATE.name -> {
                when(type) {
                    "USER" -> {
                        corporateService.canUpdateUser(id!!, authenticatedUser)
                    }
                    else -> {
                        false
                    }
                }
            }
            else -> {
                false
            }
        }
    }

    private fun hasAuthority(authenticatedUser: AuthenticatedUser, authorityKey: String?): Boolean {

        val authorities = authenticatedUser.adminAuthorities.split(",")

        return authorities.contains(authorityKey)
    }
    fun isAdminOrSuperAdmin(authenticatedUser: AuthenticatedUser, authorityKey: String): Boolean {

        return when (authenticatedUser.role) {
            Role.ROLE_SUPER_ADMIN.name -> {
                true
            }
            Role.ROLE_ADMIN.name -> {
                hasAuthority(authenticatedUser, authorityKey)
            }
            else -> {
                false
            }
        }
    }

    fun validateUser(userId: Long, authenticatedUser: AuthenticatedUser, type: String, microAccess: String?): Map<String, Any?> {

        val hasAccess = isAdminOrPartnerExpert(authenticatedUser, null, userId, type, microAccess)
        if (hasAccess){
            return authenticatedUser.getClaims()
        }
        else {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
    }
}