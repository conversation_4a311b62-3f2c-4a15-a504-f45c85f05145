package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.JwtClaim
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.JwtHelper
import com.centuroglobal.shared.service.aws.AwsS3Service
import mu.KotlinLogging
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.stream.Collectors

private val log = KotlinLogging.logger {}
@Service
class AuthService(
    private val corporateUserRepository: CorporateUserRepository,
    private val bandsDetailsRepository: BandsDetailsRepository,
    private val partnerUserRepository: LoginAccountRepository,
    private val userRoleRepository: UserRoleRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val loginHistoryRepository: LoginHistoryRepository,
    private val awsS3Service: AwsS3Service,
    private val jwtHelper: JwtHelper,
    private val partnerRepository: PartnerRepository
) {


    fun getUserAccess(userId: Long, role: String): Triple<List<UserAccess>, String?, String?>? {

        val pair = getUserBandAndCompany(userId, role) ?: return null

        val bandDetails = bandsDetailsRepository.findByBandId(pair.second?.id)
        val bandName = pair.second?.name

        val accesses = bandDetails.filter { it.access!=null }.groupBy{it.access!!.featureKey}.map {
            UserAccess(
                feature = it.key!!,
                accesses = it.value.stream().map{b->b.access?.accessLevel}.collect(Collectors.toList())
            )
        }
        return Triple(accesses, bandName, pair.first)
    }

    private fun getUserBandAndCompany(userId: Long, role: String): Pair<String?, BandsEntity?>? {

        return when (role) {
            Role.ROLE_CORPORATE.name -> {
                val user =
                    corporateUserRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
                Pair(user.corporate.name, user.band)

            }
            Role.ROLE_PARTNER.name -> {
                val user =
                    partnerUserRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
                Pair(user.partner?.name, user.partnerBand)
            }
            else -> {
                null
            }
        }
    }

    fun getUserVisibilities(userId: Long, role: String): List<UserAccess>? {

        val pair = getUserBandAndCompany(userId, role) ?: return null
        val bandDetails = bandsDetailsRepository.findByBandId(pair.second?.id)

        val visibilities = bandDetails.filter { it.visibility!=null }.groupBy { it.visibility!!.featureKey }.map {
            UserAccess(
                feature = it.key!!,
                accesses = it.value.stream().map{b->b.visibility?.visibility}.collect(Collectors.toList())
            )
        }
        return visibilities
    }

    fun getUserRoles(userId: Long): List<String> {
        val userRoles = userRoleRepository.findAllByUserId(userId)
        return userRoles.map { it.role.name }
    }

    fun generateTokenResult(claims: MutableMap<String, Any?>, refreshToken: String?, lastLoginDate: LocalDateTime?,
                            userId: Long?, role: String?, loginToken: String?, loginAccountEntity: LoginAccountEntity? = null): TokenResult? {

        if(userId == null || role == null) {
            return null
        }

        log.debug("getting user accesses")
        val userAccess = getUserAccess(userId, role)
        log.debug("getting user visibilities")
        val visibilities = getUserVisibilities(userId, role)

        log.debug("fetching user roles")
        val userRoles = getUserRoles(userId)

        claims[JwtClaim.USER_ACCESS.key] = userAccess?.first
        claims[JwtClaim.USER_VISIBILITY.key] = visibilities

        val loginAccount = loginAccountEntity
            ?: loginAccountRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val partnerId = when (loginAccount) {
            is CorporateUserEntity -> {
                loginAccount.corporate.partner?.id
            }
            is ExpertUserEntity -> {
                loginAccount.companyProfile!!.invitedBy
            }
            else -> {
                null
            }
        }
        claims[JwtClaim.PARTNER_ID.key] = claims[JwtClaim.PARTNER_ID.key] ?: partnerId
        log.debug("generating jwt token")
        val tokenResult: TokenResult? = jwtHelper.generateTokenResult(refreshToken, claims, false, lastLoginDate)
        log.debug("token generated")

        if (tokenResult != null) {
            tokenResult.userAccess = userAccess?.first
            tokenResult.userVisibilities = visibilities
            tokenResult.bandName = userAccess?.second
            tokenResult.companyName = userAccess?.third

            tokenResult.profilePhotoUrl = getProfilePhotoUrl(loginAccount)
            tokenResult.aiMessageCount = loginAccount.questionsQuota
            tokenResult.userRoles = userRoles
            tokenResult.loginToken = loginToken
            tokenResult.showOnboardingDashboard = loginAccount.showOnboardingDashboard
            tokenResult.onboardingSwitchAvailable = isOnboardingSwitchAvailable(loginAccount.id!!)
            tokenResult.onboard = loginAccount.onboard
            tokenResult.casesManagedBy = loginAccount.partner?.casesManaged
            tokenResult.queryManagedBy = loginAccount.partner?.queriesManaged
            tokenResult.companyLogo = getPartnerLogo(partnerId ?: loginAccount.partner?.id)
            tokenResult.isPartnerUser = (partnerId !=null)
        }
        return tokenResult
    }

    private fun getPartnerLogo(partnerId: Long?): String? {
        if(partnerId == null) {
            return null
        }
        val partner = partnerRepository.findByIdOrNull(partnerId)
        return partner?.companyLogo?.let { awsS3Service.getS3Url(it) }
    }

    private fun getProfilePhotoUrl(loginAccountEntity: LoginAccountEntity): String? {
        log.debug("getting profile picture url")
        return loginAccountEntity.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url)}
    }

    private fun isOnboardingSwitchAvailable(userId: Long): Boolean {
        return (loginHistoryRepository.countByUserId(userId) in 4..10)
    }

}