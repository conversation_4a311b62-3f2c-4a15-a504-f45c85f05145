package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.util.UserAccessUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull

private val log = KotlinLogging.logger {}

abstract class BaseService<T, I : Any> {

    @Autowired
    private lateinit var corporateUserRepo: CorporateUserRepository

    @Autowired
    private lateinit var expertUserRepo: ExpertUserRepository

    @Autowired
    private lateinit var corporateRepo: CorporateRepository

    @Autowired
    private lateinit var partnerRepo: PartnerRepository

    @Autowired
    private lateinit var partnerUserRepo: LoginAccountRepository

     fun list(filter: AbstractSearchFilter, user: AuthenticatedUser, pageRequest: PageRequest,
              repository: BaseRepository<T, I>, accessKey: String): Page<T> {
         if(user.role == "ROLE_ADMIN" || user.role == "ROLE_SUPER_ADMIN"){
             return repository.searchByCriteriaForAdmin(filter,pageRequest)
         }else if(user.role=="ROLE_PARTNER") {
           filter.partnerId=user.partnerId
             return repository.searchByCriteriaForAdmin(filter,pageRequest)
         }
         else if (AdminAccessUtil.isExpert(user.role) && accessKey=="TASK") {
             return repository.searchByCriteriaForAdmin(filter,pageRequest)
         }

         val accesses = UserAccessUtil.getAccessesByKey(user, accessKey)

         return  when {

             accesses.contains("FULL") -> {
                 repository.searchByCriteriaForFullAccess(filter, pageRequest)
             }

             accesses.contains("REPORTEES") -> {
                 val users = corporateUserRepo.findIdByManagersManagerId(user.userId).map { it.id }
                 filter.reportees = users.plus(user.userId)
                 repository.searchByCriteria(filter, filter.reportees!!, pageRequest)
             }

             accesses.contains("OWN") -> {
                 filter.reportees = listOf(user.userId)
                 repository.searchByCriteria(filter, filter.reportees!!, pageRequest)
             }

             else -> {
                 log.error("User with id: ${user.userId} is having invalid $accessKey accesses.")
                 throw ApplicationException(ErrorCode.FORBIDDEN)
             }
         }
     }

    fun getReportees(user: AuthenticatedUser, accessKey: String): List<Long>? {

        val accesses = UserAccessUtil.getAccessesByKey(user, accessKey)
        return  when {
            accesses.contains("FULL") -> {
                emptyList()
            }
            accesses.contains("REPORTEES") -> {
                val users = corporateUserRepo.findIdByManagersManagerId(user.userId).map { it.id }
                users.plus(user.userId)
            }
            accesses.contains("OWN") -> {
                listOf(user.userId)
            }
            else -> {
                emptyList()
            }
        }

    }

    fun get(id: I, user: AuthenticatedUser, repository: BaseRepository<T, I>, accessKey: String): T {


       return when (UserType.valueOf(user.userType)) {
            UserType.CORPORATE -> {
                getForCorporate(id, user, repository, accessKey)
            }
            UserType.EXPERT -> {
                getForExpert(id, user, repository, accessKey)
            }
            UserType.PARTNER -> {
                repository.findByIdAndPartnerId(id, user.partnerId!!)
                    .orElseThrow {
                        log.error("Can not find Entity with id: $id for Partner Id: ${user.partnerId}")
                        ApplicationException(ErrorCode.NOT_FOUND)
                    }
            }
            UserType.BACKOFFICE -> {
                repository.findById(id).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
            }
        }
    }

    private fun getForExpert(id: I, user: AuthenticatedUser, repository: BaseRepository<T, I>, accessKey: String): T {
        val expertUser = expertUserRepo.findById(user.userId)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

       return repository.findByIdForExpert(id as Long, user.userId, expertUser.companyProfile?.id!!)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

    }

    private fun getForCorporate(id: I, user: AuthenticatedUser, repository: BaseRepository<T, I>, accessKey: String): T {

        val corporateId = user.companyId
        if(corporateId == null) {
            log.error("Corporate Id not found in authenticated user object")
            throw ApplicationException(ErrorCode.CORPORATE_NOT_FOUND)
        }

        val accesses = UserAccessUtil.getAccessesByKey(user, accessKey)

        return when {
            accesses.contains("FULL")->{
                val corporateEntity = corporateRepo.findIdById(corporateId) ?: throw ApplicationException(ErrorCode.CORPORATE_NOT_FOUND)
                repository.findByIdAndCreatedByCorporateId(id as Long, corporateEntity.id).orElseThrow {
                    log.error("No entity found for id: $id and createdBy user in corporate id: $corporateId")
                    ApplicationException(ErrorCode.NOT_FOUND)
                }
            }
            accesses.contains("REPORTEES") -> {
                val reporters = corporateUserRepo.findIdByManagersManagerId(user.userId)
                repository.findByIdAndCreatedByIdIn(id as Long, reporters.map { it.id}.plus(user.userId).toMutableList()).orElseThrow {
                    log.error("Entity with id: $id not found for reportees")
                    ApplicationException(ErrorCode.NOT_FOUND)
                }
            }
            accesses.contains("OWN")->{
                val userEntity = corporateUserRepo.findIdById(user.userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
                repository.findByIdAndCreatedByIdIn(id as Long, mutableListOf(userEntity.id)).orElseThrow {
                    log.error("Entity with id: $id not found for reportees")
                    ApplicationException(ErrorCode.NOT_FOUND)
                }
            }
            else -> {
                log.error("User with id: ${user.userId} is having invalid $accessKey accesses.")
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }
    }

    private fun getForPartner(id: I, user: AuthenticatedUser, repository: BaseRepository<T, I>, accessKey: String): T {

        val partnerId = user.partnerId
        if(partnerId == null) {
            log.error("Partner Id not found in authenticated user object")
            throw ApplicationException(ErrorCode.PARTNER_NOT_FOUND)
        }

        val accesses = UserAccessUtil.getAccessesByKey(user, accessKey)

        return when {
            accesses.contains("FULL")->{
                val partnerEntity = partnerRepo.findById(partnerId).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }
                repository.findByIdAndPartnerId(id, partnerEntity.id!!).orElseThrow {
                    log.error("No entity found for id: $id and createdBy user in partner id: $partnerId")
                    ApplicationException(ErrorCode.NOT_FOUND)
                }
            }
            accesses.contains("REPORTEES") -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
            accesses.contains("OWN")->{
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
            else -> {
                log.error("User with id: ${user.userId} is having invalid $accessKey accesses.")
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }
    }

    fun getCorporateTeamEmails(corporateUserId: Long?): List<String>? {
       return corporateUserId?.let {
            val corporateUser = corporateUserRepo.findByIdOrNull(corporateUserId)
            return getCorporateTeamEmails(corporateUser)
       }
    }

    fun getCorporateTeamEmails(corporateUser: CorporateUserEntity?): List<String>? {
        return corporateUser?.let {
            val corporate = corporateUser.corporate
            if (corporate.isTeamEmail) {
                return corporate.team.map { it.user.email }
            }
            return null
        }

    }
}