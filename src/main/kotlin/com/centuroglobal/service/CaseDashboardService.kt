package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.case.CaseStatusHistory
import com.centuroglobal.shared.data.payload.dashboard.*
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.PendingTasksResponse
import com.centuroglobal.shared.data.pojo.RecentUpdate
import com.centuroglobal.shared.data.pojo.case.CaseStatusHistoryView
import com.centuroglobal.shared.data.pojo.case.CaseViewDetails
import com.centuroglobal.shared.data.pojo.case.CountByCountry
import com.centuroglobal.shared.data.pojo.case.DashboardCaseSearchFilter
import com.centuroglobal.shared.repository.AccountEntityRepository
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.CaseStatusHistoryRepository
import com.centuroglobal.shared.repository.ReminderRepository
import com.centuroglobal.shared.repository.view.CaseTrackingViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import mu.KotlinLogging
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.function.Predicate
import java.util.stream.Collectors

private val log = KotlinLogging.logger {}

@Service
class CaseDashboardService(
    private val caseRepository: CaseRepository,
    private val statusHistoryRepository: CaseStatusHistoryRepository,
    private val accountRepository: AccountEntityRepository,
    private val reminderRepository: ReminderRepository,
    private val caseTrackingViewRepository: CaseTrackingViewRepository,
    private val taskService: TaskService,
    private val userProfileUtil: UserProfileUtil,
    private val corporateService: CorporateService
) {

    /*fun retrieveProfile(userId: Long, companyName: String?): UserProfile {
        return userProfileUtil.retrieveProfile(userId, companyName)
    }
    fun retrieveProfile(user: LoginAccountEntity, companyName: String?): UserProfile {
        return userProfileUtil.retrieveProfile(user, companyName)
    }
    fun retrieveProfile(userId: Long): UserProfile {
        return userProfileUtil.retrieveProfile(userId)
    }

    fun retrieveCorporateProfile(userId: Long, companyName: String? = null): CorporateUserProfile {
        return userProfileUtil.retrieveCorporateProfile(userId, companyName)
    }

    fun retrieveProfileWithUserType(user: LoginAccountEntity, userType: PartnerCaseType?): UserProfile {
        return userProfileUtil.retrieveProfileWithUserType(user, userType)
    }*/

    fun generateAggregateCaseDataForUser(user: AuthenticatedUser, userId: Long?, accountId: Long?): CaseStatistics {

        log.info("Request for Admin Case data for user "+user.userId)

        val  caseList = getEligibleCaseList(user, userId, accountId)

        var activeCases = 0
        var yetToStartCases = 0
        var initiatedCases = 0
        var inCompletionCases = 0

        val list1 = mutableListOf<KeyValuePair>()
        val list2 = mutableListOf<KeyValuePair>()

        var stage1 =0
        var stage2 =0
        var stage3 =0
        var stage4 =0
        var stage5 =0
        var stage6 =0

        log.info("Case List "+caseList.count())

        caseList.forEach {
            if (!it.archive)
                activeCases++
            else
                return@forEach

            if (it.percentCompletion == 0) {
                yetToStartCases++
            } else if (it.percentCompletion in 1..99) {
                initiatedCases++
            }

            if (it.percentCompletion in 81..99)
                inCompletionCases++

            when (it.percentCompletion) {
                in 0..10 -> {
                    stage1++
                }
                in 11..20 -> {
                    stage2++
                }
                in 21..40 -> {
                    stage3++
                }
                in 41..60 -> {
                    stage4++
                }
                in 61..80 -> {
                    stage5++
                }
                in 81..99 -> {
                    stage6++
                }
            }
        }

        val caseCountSummary = CaseCountSummary(activeCases, yetToStartCases, initiatedCases, inCompletionCases)
        list1.add(KeyValuePair("stage1", stage1.toString()))
        list1.add(KeyValuePair("stage2", stage2.toString()))
        list1.add(KeyValuePair("stage3", stage3.toString()))
        list1.add(KeyValuePair("stage4", stage4.toString()))
        list1.add(KeyValuePair("stage5", stage5.toString()))
        list1.add(KeyValuePair("stage6", stage6.toString()))

        val result: Map<String, Long> = caseList.stream().filter(Predicate.not(CaseEntity::archive))
            .collect(
                Collectors.groupingBy(
                    CaseEntity::parentCategoryId,
                    Collectors.counting()
                )
            )
        for(entry in result.entries) {
            list2.add(KeyValuePair(entry.key, entry.value.toString()))
        }
        return CaseStatistics(caseCountSummary, list1, list2)
    }

    private fun getEligibleCaseList(
        user: AuthenticatedUser,
        userId: Long?,
        accountId: Long?
    ):  MutableList<CaseEntity> {

        val triple = corporateService.getEligibleClientList(user, userId, accountId)
        val accountFilterSearch: Boolean = triple.first
        val userList = triple.second

        val caseList = if (accountFilterSearch) {
            val account = accountRepository.findById(accountId!!).orElseThrow()
            caseRepository.findAllByAccountAndArchiveAndCreatedByIn(account,false, userList)
        } else {
            caseRepository.findAllByArchiveAndCreatedByIn(false, userList)
        }
        return caseList
    }


    @Transactional(readOnly = true)
    fun getPendingTasks(authenticatedUser: AuthenticatedUser, userId: Long?, accountId: Long?): PendingTasksResponse {
        val caseList = getEligibleCaseList(authenticatedUser, userId, accountId)
        val cases = caseList.filter { it.actionFor.let { action-> action.equals("CLIENT") ||
                action.equals("APPLICANT") } }.sortedByDescending { it.lastUpdatedDate }.take(4)

        val tasks = taskService.listPendingTasks(authenticatedUser, userId, accountId)

        val recentUpdateCases = statusHistoryRepository.findAllByCaseInAndIsDeleted(cases, false)

        val recentUpdates = recentUpdateCases.sortedByDescending { it.lastUpdatedDate }.take(3).map{
            val profile = userProfileUtil.retrieveProfile(it.lastUpdatedBy)
            RecentUpdate(
                status = it.status,
                statusUpdate = it.statusUpdate,
                statusUpdatedBy = profile.firstName + " " + profile.lastName,
                caseId = it.case.id!!,
                lastUpdatedDate = TimeUtil.toEpochMillis(it.lastUpdatedDate)
            )
        }.toMutableList()

        return PendingTasksResponse(
            pendingTasks = tasks,
            recentUpdates = recentUpdates
        )
    }


    fun casesByCountry(authenticatedUser: AuthenticatedUser, filter: DashboardCaseSearchFilter,
    ): List<CountByCountry> {

        val pair = corporateService.getEligibleClientList(authenticatedUser, filter.userId, filter.accountId)

        filter.users = pair.second

        val cases = caseRepository.searchByCriteria(filter)

        return cases.groupBy { it.country }.map {
            CountByCountry(
                it.key!!,
                it.value.stream().map { case -> case.account?.id }.distinct().count(),
                it.value.size.toLong(),
                it.value.stream().filter { c -> !c.archive }.count(),
                it.value.stream().filter { c -> c.percentCompletion in 1..80 }.count(),
                it.value.stream().filter { c -> c.percentCompletion in 81..99 }.count()
            )
        }
    }

    fun caseStatusHistory(
        authenticatedUser: AuthenticatedUser,
        filter: DashboardCaseSearchFilter,
        status: String?,
        pageRequest: PageRequest
    ): PagedResult<CaseStatusHistoryView>? {

        val pair = corporateService.getEligibleClientList(authenticatedUser, filter.userId, filter.accountId)
        filter.users = pair.second

        val cases = caseRepository.searchByCriteria(filter)
        val statusHistory = statusHistoryRepository.searchByCriteria(cases, status,filter.actionFor, pageRequest)
        val caseStatusHistoryView = statusHistory.map {
            CaseStatusHistoryView.ModelMapper.from(
                it,
                userProfileUtil.retrieveProfile(it.lastUpdatedBy)
            )
        }

        return PagedResult.ModelMapper.from(statusHistory, caseStatusHistoryView.toList())
    }

    fun getTrackingCounts(from: Long, to: Long, authenticatedUser: AuthenticatedUser): List<CaseTrackingStatistics> {

        val clients = corporateService.getEligibleClientList(authenticatedUser, null, null).second

        val docReferenceId = getDocReferenceId(authenticatedUser)

        return reminderRepository.getCountByCaseOwnerAndGroupByDate(
            TimeUtil.fromInstantMillis(from),
            TimeUtil.fromInstantMillis(to),
            clients,
            docReferenceId
        )
    }

    fun getTrackingData(date: Long, authenticatedUser: AuthenticatedUser): List<CaseTrackingData> {

        val clients = corporateService.getEligibleClientList(authenticatedUser, null, null).second.map { it.userId }

        val docReferenceId = getDocReferenceId(authenticatedUser)

        val reminders =
            caseTrackingViewRepository.findByDateAndCaseOwnerIn(
                TimeUtil.fromInstantMillis(date).toLocalDate(),
                clients,
                docReferenceId
            )

        return reminders.map { CaseTrackingData.ModelMapper.from(it) }
    }

    private fun getDocReferenceId(authenticatedUser: AuthenticatedUser): Long? {

        val canAccessDocs =
            authenticatedUser.accesses.firstOrNull { it.feature == "COMPANY_INFO" }?.accesses?.contains("VIEW_DOCUMENTS")
                ?: false

        return if (canAccessDocs) authenticatedUser.companyId else null
    }

    fun getApplicantCaseDetails(authenticatedUser: AuthenticatedUser, caseData: CaseViewDetails): CaseViewDetails {

        val history = caseData.caseDetails?.statusHistory
        val sortedHistory = history?.sortedByDescending { it.lastUpdatedDate }
        var prevObj: CaseStatusHistory? = null
        val removeConsecutiveDuplicates = Predicate<CaseStatusHistory> {
            if (prevObj != null && it.status == prevObj!!.status) {
                false
            } else {
                prevObj = it
                true
            }
        }
        val recentUpdates = sortedHistory?.filter { removeConsecutiveDuplicates.test(it) }
        val recentUpdatesView = recentUpdates?.map {
            CaseStatusHistoryView.ModelMapper.from(
                it,
                userProfileUtil.retrieveProfile(it.lastUpdatedBy)
            )
        }

        caseData.taskPending =
            taskService.listPendingTasks(authenticatedUser, authenticatedUser.userId, null, caseData.caseId)
        caseData.recentUpdates = recentUpdatesView
        return caseData
    }
}