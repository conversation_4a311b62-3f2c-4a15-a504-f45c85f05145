package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.entity.view.UserView
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.email.*
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context
import com.centuroglobal.repository.DependentVisaRepository
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.util.PartnerEmailUtil
import kotlin.jvm.optionals.getOrNull

private val log = KotlinLogging.logger {}

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"
private const val CASE_SUBMITTED_EMAIL_TEMPLATE_NAME = "email/case_submitted"
private const val CASE_SUBMITTED_PARTNER_EMAIL_TEMPLATE_NAME = "email/case_submitted_partner"
private const val CASE_ASSIGNED_EMAIL_TEMPLATE_NAME = "email/case_assigned"
private const val CASE_SUBMITTED_EMAIL_SUBJECT = "Case Submitted"
private const val CASE_ASSIGNED_EMAIL_SUBJECT = "Case Assigned"
private const val PUBLIC_CASE_SUBMITTED_EMAIL_TEMPLATE_NAME = "email/public_case_submitted"
private const val CASE_STATUS_CHANGE_EMAIL_TEMPLATE = "email/case_status_change"
private const val CASE_STATUS_CHANGE_EMAIL_APPLICANT_TEMPLATE = "email/case_status_change_applicant"
private const val CASE_FEES_APPROVAL_EMAIL_TEMPLATE = "email/case_fees_approval_email"
private const val CASE_FEES_APPROVED_EMAIL_TEMPLATE = "email/case_fees_approved_email"

private const val CASE_DOCUMENT_REQUESTED_EMAIL_TEMPLATE = "email/case_document_requested_email"
private const val CASE_DOCUMENT_RECEIVED_EMAIL_TEMPLATE = "email/case_document_received_email"
private const val CASE_REQUEST_SUPPORT_EMAIL_TEMPLATE = "email/case_request_support_email"
private const val CASE_REQUEST_SUPPORT_ACCEPT_EMAIL_TEMPLATE = "email/case_request_support_accept_email"

private const val CASE_TYPE_CHANGE_TEMPLATE_NAME = "email/case_converted"
private const val CASE_CANCELLED_EMAIL_TEMPLATE = "email/case_cancelled_email"




private val caseUrl: (webUrl: String, caseId: Long?, case: String?) -> String =
    { webUrl, caseId, case -> "${webUrl}/cases/view-case/${caseId}/${case}/FEES" }

private val caseOverviewUrl: (webUrl: String, caseId: Long?, case: String?) -> String =
    { webUrl, caseId, case -> "${webUrl}/cases/view-case/${caseId}/${case}/CASE_OVERVIEW" }


private val caseUrlForFeesPublic: (webUrl: String ,uuid: String ,feeToken: String) -> String =
    { webUrl, uuid, feeToken -> "${webUrl}/cases/fees-approval/${uuid}?feeToken=${feeToken}" }

private val caseUrlForPublic: (webUrl: String, caseId: Long?, validationToken: String) -> String =
    { webUrl, validationToken, caseId -> "${webUrl}/auth/case-validate/${validationToken}/${caseId}" }

@Service

class CaseEmailService(

    @Value("\${app.web-url}")
    private val webUrl: String,

    @Value("\${spring.mail.case-initiate-email.to}")
    private val adminEmail: String,

    @Value("\${spring.mail.case-initiate-email.cc}")
    private val ccEmail: List<String>,

    @Value("\${spring.mail.cg-support.case.to:}")
    private val cgSupportEmail: List<String>,

    private val caseRepository: CaseRepository,
    private val mailSendingService: MailSendingService,
    private val awsS3Service: AwsS3Service,
    private val loginAccountRepository: LoginAccountRepository,
    private val validationTokenRepository: ValidationTokenRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val singleVisaRepository: SingleVisaRepository,
    private val rightToWorkCheckRepository: RightToWorkCheckRepository,
    private val dependentVisaRepository: DependentVisaRepository,
    private val partnerRepository: PartnerRepository,
    private val partnerEmailUtil: PartnerEmailUtil) {

    @Async
    @Transactional(readOnly = true)
    fun caseSubmitEmail(case: CaseEntity, isPublicCase: Boolean = false, emails: List<String>?) {

        if (case.partnerId!=null) {
            return caseSubmitEmailForPartner(case, emails)
        }

        val template = if(isPublicCase) {
            PUBLIC_CASE_SUBMITTED_EMAIL_TEMPLATE_NAME
        } else {
            CASE_SUBMITTED_EMAIL_TEMPLATE_NAME
        }

        /*
        Send email to corporate expert bands if present
        */
        val (newCcEmail, toEmail) = caseSubmissionEmailRecipients(case, emails)

        val subject = CASE_SUBMITTED_EMAIL_SUBJECT
        if(toEmail!=null){
            caseEmail(case.category?.subCategoryId, getCaseSubCategoryName(case), case.id, case.createdBy!!.fullName, toEmail, template, subject, emptyList(), isPublicCase)
        }
        caseEmail(case.category?.subCategoryId, getCaseSubCategoryName(case), case.id, case.createdBy!!.fullName,
            adminEmail, template, subject, newCcEmail )


    }

    private fun caseSubmissionEmailRecipients(
        case: CaseEntity,
        emails: List<String>?
    ): Pair<MutableList<String>, String?> {
        val newCcEmail = mutableListOf<String>()
        newCcEmail.addAll(ccEmail)
        if (case.createdBy != null && case.createdBy!!.userType == UserType.CORPORATE) {

            val userId = case.createdBy!!.userId
            val corporateUser = corporateUserRepository.findById(userId)
            if (corporateUser.isPresent) {
                val expertBandUsers = corporateUser.get().corporate.users.filter {
                    it.band != null && it.band!!.name == "Partner"
                }
                expertBandUsers.forEach {
                    val email = getActiveUserEmail(loginAccountRepository.findById(it.id!!).get())
                    if (email != null) {
                        newCcEmail.add(email)
                    }
                }
            }
        }

        val caseOwner =
            if (case.notifyCaseOwner) filterDraftUser(case.createdBy!!.email, case.createdBy!!.status) else null

        val toEmail = emails?.joinToString(",") ?: caseOwner
        return Pair(newCcEmail, toEmail)
    }

    private fun caseSubmitEmailForPartner(case: CaseEntity, emails: List<String>?) {

        val ctx = Context()
        if (case.createdBy != null) {
            ctx.setVariable("CASE_ID", case.id)
            ctx.setVariable("CASE_CATEGORY", getCaseSubCategoryName(case))
            ctx.setVariable("CASE_COUNTRY", case.country)
            ctx.setVariable("CORPORATE_USER_NAME", case.createdBy!!.fullName)
            ctx.setVariable("CORPORATE_NAME", case.createdBy!!.company)
            ctx.setVariable("CASE_URL", caseOverviewUrl(webUrl, case.id, case.category?.subCategoryId))
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))

            val user = loginAccountRepository.findById(case.createdBy!!.userId).getOrNull()
            user?.let { partnerEmailUtil.updateContext(ctx, it) }
        }

        val recipientSet = mutableSetOf<String>()

        if (emails!=null) {
          recipientSet.addAll(emails)
        }
        else {
            val partner = partnerRepository.findById(case.partnerId!!).get()
            recipientSet.addAll(partner.partnerUsers.map { it.email })
            if (case.notifyCaseOwner) {
                filterDraftUser(case.createdBy!!.email, case.createdBy!!.status)?.let { recipientSet.add(it) }
            }
        }

        if(recipientSet.isEmpty()){
            log.warn("Recipients list is empty no emails will be sent for case submission by partner.")
            return
        }

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = CASE_SUBMITTED_PARTNER_EMAIL_TEMPLATE_NAME,
                subject = CASE_SUBMITTED_EMAIL_SUBJECT,
                context = ctx,
                recipient = recipientSet.joinToString(separator = ", ")
            )
        )
    }

    @Async
    fun caseAssignEmail(case: CaseEntity, assignedTo: String, email: String) {
        val template = CASE_ASSIGNED_EMAIL_TEMPLATE_NAME
        val subject = CASE_ASSIGNED_EMAIL_SUBJECT
        caseEmail(case.category?.subCategoryId, getCaseSubCategoryName(case), case.id,
            assignedTo, email, template, subject, emptyList())
    }

    @Async
    fun caseEmailStatusUpdate( country: String, case: CaseEntity, updatedByEmail: String) {

        val id = case.id
        val recipientList = fetchEmailRecipients(case)
        recipientList.addAll(case.assignee.filter{getActiveUser(it, NotificationType.CASE_UPDATE_EMAIL)}.map { it.email }.toMutableList())

        recipientList.addAll(case.managers.filter{ getActiveUser(it, NotificationType.CASE_UPDATE_EMAIL) }.map { it.email }.toMutableList())

        case.accountManager?.let { getActiveUserEmail(it, NotificationType.CASE_UPDATE_EMAIL)?.let { i -> recipientList.add(i) } }

        recipientList.remove("<EMAIL>")

        val template = CASE_STATUS_CHANGE_EMAIL_TEMPLATE
        val subject = "Case ID $id Status Update"


        val ctx = getContextStatusUpdate(country, case.initiatedFor!!, case.category?.subCategoryId?:"DYNAMIC_CASE",
            getCaseSubCategoryName(case), id, case.statusUpdate!!)
        recipientList.removeAll(listOf( updatedByEmail))
        recipientList.forEach{
            mailSendingService.sendEmail(
                MailTemplate(
                    templateName = template,
                    subject = subject,
                    context = ctx,
                    recipient = it
                )
            )
        }
    }

    @Async
    fun caseEmailStatusUpdateForApplicant( country: String,applicantName: String,categoryId:String, categoryName: String,
                                           id: Long?, statusUpdateText: String, emails: List<String>) {

        val template = CASE_STATUS_CHANGE_EMAIL_APPLICANT_TEMPLATE
        val subject = "Case ID $id Status Update"

        val ctx = getContextStatusUpdate(country,applicantName,categoryId, categoryName, id, statusUpdateText)

        emails.forEach{
            mailSendingService.sendEmail(
                MailTemplate(
                    templateName = template,
                    subject = subject,
                    context = ctx,
                    recipient = it
                )
            )
        }
    }






    @Async
    fun feesApprovalRequiredEmail(accountManager: LoginAccountEntity?, id: Long?, categoryId: String,
                                  uuid: String,feeToken: String, emails: MutableSet<String>) {

        val adminEmail = accountManager?.let { getActiveUserEmail(it) }

        val template = CASE_FEES_APPROVAL_EMAIL_TEMPLATE
        val subject = "Case ID $id Approval Required"

        val ctx = getContextFeesApproval(id,categoryId,uuid,feeToken)
        val ccList = mutableListOf<String>()
        adminEmail?.let { emails.add(it) }

        emails.forEach{
            mailSendingService.sendEmail(
                MailTemplate(
                    templateName = template,
                    subject = subject,
                    context = ctx,
                    recipient = it
                )
            )
        }
    }

    @Async
    fun caseFeesApprovedEmail( accountManager: LoginAccountEntity?, targetEmails: MutableList<String>, id: Long, categoryId: String) {

        val adminEmail = accountManager?.let{getActiveUserEmail(it)}
        val template = CASE_FEES_APPROVED_EMAIL_TEMPLATE
        val subject = "Case ID $id Fees Approved"

        val ctx = getContextFeesApproval(id,categoryId,"","")
        adminEmail?.let { targetEmails.add(it) }

        targetEmails.forEach {
            mailSendingService.sendEmail(
                MailTemplate(
                    templateName = template,
                    subject = subject,
                    context = ctx,
                    recipient = it
                )
            )
        }
    }




    private fun getContextFeesApproval(id: Long?,categoryId:String, uuid: String,feeToken: String): Context {
        return CaseFeesApprovalEmailContext.ModelMapper.toContext(
            CaseFeesApprovalEmailContext(
                id,
                caseUrl(webUrl, id, categoryId),
                caseUrlForFeesPublic(webUrl,uuid,feeToken),
                awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
            )
        )
    }

    private fun getContext(categoryId: String, categoryName: String, caseId: Long?, assignedTo: String, token:String): Context {
        return CaseSubmitContext.ModelMapper.toContext(
            CaseSubmitContext(
                assignedTo,
                categoryName,
                caseId,
                caseUrl(webUrl, caseId, categoryId),
                awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                caseUrlForPublic(webUrl, caseId, token)
            )
        )
    }

    private fun getContextStatusUpdate(country: String,applicantName: String,categoryId:String, categoryName: String,
                                       id: Long?, statusUpdateText: String): Context {
        return CaseStatusEmailContext.ModelMapper.toContext(
            CaseStatusEmailContext(
                country,
                applicantName,
                statusUpdateText,
                categoryName,
                id,
                caseUrl(webUrl, id, categoryId),
                awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
            )
        )
    }


    private fun getActiveUserEmail(user: LoginAccountEntity, notificationType: NotificationType? = null): String? {
        return if(notificationType == null){
            if(isActive(user.status)) user.email else null
        }
        else {
            if(isNotificationEnabled(user, notificationType)){
                user.email
            }
            else{
                null
            }
        }
    }

    private fun isNotificationEnabled(user: LoginAccountEntity, notificationType: NotificationType): Boolean {

        if(user is CorporateUserEntity){
            return isActive(user.status) && (user.notificationPreferences.any { it.notificationKey == notificationType && it.value })
        }
        else {
            return isActive(user.status)
        }
    }

    private fun isActive(status: AccountStatus): Boolean {
        return status== AccountStatus.ACTIVE
    }

    private fun getActiveUserEmail(user: ClientView, notificationType: NotificationType?): String? {
        return if(notificationType == null){
            if(isActive(user.status)) user.email else null
        }
        else {
            if(isNotificationEnabled(loginAccountRepository.findById(user.userId).get(), notificationType)){
                user.email
            }
            else{
                null
            }
        }
    }



    private fun fetchEmailRecipients(case: CaseEntity): MutableList<String> {
        val recipientList: MutableList<String> = mutableListOf()
        if (!case.notifyCaseOwner) {
            return recipientList
        }
        case.createdBy?.let { getActiveUserEmail(it, NotificationType.CASE_UPDATE_EMAIL)?.let { i -> recipientList.add(i) } }
        val caseOwner = corporateUserRepository.findById(case.createdBy!!.userId).get()
        // Send email to case owner's managers
        if (caseOwner.managers.isEmpty()) {
            return recipientList
        }
        val managers = loginAccountRepository.findAllByIdIn(caseOwner.managers.map { it.managerId })
        managers.forEach {
            it as CorporateUserEntity
            if(it.band?.name !="Super Admin") {
                getActiveUserEmail(it, NotificationType.CASE_UPDATE_EMAIL)?.let { i ->
                    if (!recipientList.contains(i)) recipientList.add(i)
                }
            }
        }
        return recipientList
    }

    private fun caseEmail( categoryId: String?, categoryName: String?, id: Long?, assignedTo: String, email: String,
                           template: String, subject: String, ccEmail: List<String>, isPublicCase: Boolean=false) {

        val loginAccountEntity=  loginAccountRepository.findByEmail(email)
        val token = if (isPublicCase) {
            val tokenRepo= validationTokenRepository.findByUserId(loginAccountEntity?.id!!)
            tokenRepo!!.code
        } else ""

        val ctx = getContext(categoryId?:"DYNAMIC_CASE", categoryName?:"Dynamic Case", id, assignedTo, token)

        partnerEmailUtil.updateContext(ctx, loginAccountEntity!!)

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = template,
                subject = subject,
                context = ctx,
                ccRecipients = ccEmail,
                recipient = email
            )
        )
    }

    private fun getActiveUser(user: UserView, notificationType: NotificationType? = null): Boolean {
        return if(notificationType == null ) {
            isActive(user.status)
        } else{
            isNotificationEnabled(loginAccountRepository.findById(user.id!!).get(), notificationType)
        }
    }


    private fun getContextFeesApprovalReminder(id: Long?,categoryId:String, uuid: String,feeToken: String,requestedDate: String): Context {
        return CaseFeesApprovalReminderEmailContext.ModelMapper.toContext(
            CaseFeesApprovalReminderEmailContext(
                id,
                requestedDate,
                caseUrl(webUrl, id, categoryId),
                caseUrlForFeesPublic(webUrl,uuid,feeToken),
                awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
            )
        )
    }



    fun caseDocumentRequestedMail(case: CaseEntity) {

        val subject = "Case ID ${case.id}: Document Requested"

        val ctx = Context()
        val caseOwner = case.createdBy
        if (caseOwner != null) {
            ctx.setVariable("CASE_OWNER_FIRST_NAME", case.createdBy!!.fullName.split(" ")[0])
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        }
        else {
            return
        }

        val email = filterDraftUser(caseOwner.email, caseOwner.status)
        if (email!=null) {
            mailSendingService.sendEmail(
                MailTemplate(
                    templateName = CASE_DOCUMENT_REQUESTED_EMAIL_TEMPLATE,
                    subject = subject,
                    context = ctx,
                    recipient = email
                )
            )
        }
    }

    fun caseDocumentUploadMail(case: CaseEntity, loggedInUserName: String) {

        val subject = "Case ID ${case.id}: Documents Received"

        val ctx = Context()
        if (case.createdBy != null) {
            ctx.setVariable("CASE_ID", case.id)
            ctx.setVariable("USER_NAME", loggedInUserName)
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        }

        val recipientSet = mutableSetOf<String>()
        recipientSet.addAll(case.assignee.filter{ getActiveUser(it, NotificationType.DOC_UPLOAD_EMAIL) }.map { it.email }.toMutableList())

        recipientSet.addAll(case.managers.filter{ getActiveUser(it, NotificationType.DOC_UPLOAD_EMAIL) }.map { it.email }.toMutableList())

        case.accountManager?.let { getActiveUserEmail(it, NotificationType.DOC_UPLOAD_EMAIL)?.let { i -> recipientSet.add(i) } }

        if(recipientSet.isEmpty()){
            log.warn("Recipients list is empty no emails will be sent for Received documents.")
            return
        }

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = CASE_DOCUMENT_RECEIVED_EMAIL_TEMPLATE,
                subject = subject,
                context = ctx,
                recipient = recipientSet.joinToString(separator = ", ")
            )
        )
    }

    @Async
    fun caseCgRequestMail(case: CaseEntity, partnerAdmin: LoginAccountEntity) {

        val subject = "Partner Case Request for Support"

        val ctx = Context()
        if (case.createdBy != null) {
            ctx.setVariable("CASE_ID", case.id)
            ctx.setVariable("CASE_CATEGORY", getCaseSubCategoryName(case))
            ctx.setVariable("CASE_COUNTRY", case.country)
            ctx.setVariable("PARTNER_ADMIN_NAME", "${partnerAdmin.firstName} ${partnerAdmin.lastName}")
            ctx.setVariable("PARTNER_NAME", partnerAdmin.partner!!.name)
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        }

        val recipientSet = mutableSetOf<String>()

        recipientSet.addAll(cgSupportEmail)

        if(recipientSet.isEmpty()){
            log.warn("Recipients list is empty no emails will be sent for case support requests.")
            return
        }

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = CASE_REQUEST_SUPPORT_EMAIL_TEMPLATE,
                subject = subject,
                context = ctx,
                recipient = recipientSet.joinToString(separator = ", ")
            )
        )
    }

    @Async
    fun caseCgRequestAcceptedMail(case: CaseEntity, caseManager: CaseAssigneeEntity) {

        val subject = "Case Assigned: ${case.id}"

        val ctx = Context()
        if (case.createdBy != null) {
            ctx.setVariable("CASE_ID", case.id)
            ctx.setVariable("CASE_CATEGORY", getCaseSubCategoryName(case))
            ctx.setVariable("CASE_COUNTRY", case.country)
            ctx.setVariable("CASE_MANAGER_NAME", "${caseManager.expert.firstName} ${caseManager.expert.lastName}")
            ctx.setVariable("CASE_CORPORATE_NAME", case.createdBy!!.fullName)
            ctx.setVariable("CASE_URL", caseOverviewUrl(webUrl, case.id, case.category?.subCategoryId))
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        }

        val recipientSet = mutableSetOf<String>()

        if(case.createdBy!=null) {
            filterDraftUser(case.createdBy!!.email, case.createdBy!!.status)?.let { recipientSet.add(it) }
        }

        recipientSet.add(caseManager.expert.email)

        if(recipientSet.isEmpty()){
            log.warn("Recipients list is empty no emails will be sent for case support accept.")
            return
        }

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = CASE_REQUEST_SUPPORT_ACCEPT_EMAIL_TEMPLATE,
                subject = subject,
                context = ctx,
                recipient = recipientSet.joinToString(separator = ", ")
            )
        )

    }

    private fun filterDraftUser(email: String, status: AccountStatus): String? {
        if (status == AccountStatus.DRAFT) {
            return null
        }
        return email
    }

    fun caseTypeChangeEmail(case: CaseEntity, oldType: String, oldCaseId: Long, updatedBy: String, emails: List<String>?) {

        val ctx = Context()
        if (case.createdBy != null) {
            ctx.setVariable("CASE_TYPE", oldType)
            ctx.setVariable("NEW_CASE_TYPE", getCaseSubCategoryName(case))
            ctx.setVariable("CASE_OWNER", case.createdBy!!.fullName)
            ctx.setVariable("CASE_COMPANY", case.createdBy!!.company)
            ctx.setVariable("USER_NAME", updatedBy)
            ctx.setVariable("CASE_URL", caseOverviewUrl(webUrl, oldCaseId, case.category?.subCategoryId))
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))

            val user = loginAccountRepository.findById(case.createdBy!!.userId).getOrNull()
            user?.let { partnerEmailUtil.updateContext(ctx, it) }
        }

        val recipientSet = mutableSetOf<String>()

        val (newCcEmail, toEmail) = caseSubmissionEmailRecipients(case, emails)

        if (toEmail != null) {
            recipientSet.addAll(toEmail.split(","))
        }

        if (emails!=null) {
            recipientSet.addAll(emails)
        }
        else if (case.partnerId!=null) {
            val partner = partnerRepository.findById(case.partnerId!!).get()
            recipientSet.addAll(partner.partnerUsers.map { it.email })
            if (case.notifyCaseOwner) {
                filterDraftUser(case.createdBy!!.email, case.createdBy!!.status)?.let { recipientSet.add(it) }
            }
        }

        if(recipientSet.isEmpty()){
            log.warn("Recipients list is empty no emails will be sent for case type change.")
            return
        }

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = CASE_TYPE_CHANGE_TEMPLATE_NAME,
                subject = "Case Converted to ${getCaseSubCategoryName(case)}",
                context = ctx,
                recipient = recipientSet.joinToString(separator = ", ")
            )
        )
    }

    private fun getCaseSubCategoryName(case: CaseEntity): String =
        case.caseForm?.name ?: case.category?.subCategoryName?:"Dynamic Case"

    fun caseCancelledEmail(case: CaseEntity) {
        val subject = "Case Cancelled"

        val ctx = Context()
        if (case.createdBy != null) {
            ctx.setVariable("CASE_ID", case.id)
            ctx.setVariable("INITIATED_FOR", case.initiatedFor)
            ctx.setVariable("CASE_CATEGORY", getCaseSubCategoryName(case))
            ctx.setVariable("CASE_COUNTRY", case.country)
            ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        }

        loginAccountRepository.findById(case.createdBy!!.userId).getOrNull()?.let {
            partnerEmailUtil.updateContext(ctx, it)
        }


        mailSendingService.sendEmail(
            MailTemplate(
                templateName = CASE_CANCELLED_EMAIL_TEMPLATE,
                subject = subject,
                context = ctx,
                recipient = case.createdBy!!.email
            )
        )
    }

}


