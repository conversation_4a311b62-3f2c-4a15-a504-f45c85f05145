package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.view.CircleView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.aws.AwsS3FileMetadata
import com.centuroglobal.shared.data.pojo.circle.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.CircleViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.StringUtil
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.text.SimpleDateFormat
import java.util.*

private val log = KotlinLogging.logger {}

private const val CIRCLE_BANNER_IMG_URL = "https://cg-goglobal-prod.s3.eu-west-2.amazonaws.com/circle_banner/circle-default-banner.png"

@Service
class CircleService(
    @Value("\${app.aws.s3.circle-banner-folder}")
    private val circleBannerFolder: String,
    private val circleRepository: CircleRepository,
    private val expertiseService: ExpertiseService,
    private val circleMemberRepository: CircleMemberRepository,
    private val circleRequestRepository: CircleRequestRepository,
    private val circleResponseTrailRepository: CircleResponseTrailRepository,
    private val circleViewRepository: CircleViewRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val expertUserService: ExpertUserService,
    private val expertUserRepository: ExpertUserRepository,
    val awsS3Service: AwsS3Service
) {
    @Transactional
    fun createCircle(
        createUpdateCircleRequest: CreateUpdateCircleRequest,
        authenticatedUser: AuthenticatedUser
    ): Circle {
        val circleEntity = CircleEntity(
            name = createUpdateCircleRequest.name,
            about = createUpdateCircleRequest.about,
            circleType = createUpdateCircleRequest.circleAccessType,
            status = createUpdateCircleRequest.status,
            lastUpdatedBy = authenticatedUser.userId,
            createdBy = authenticatedUser.userId,
            countryCodes = createUpdateCircleRequest.countryCode
        )
        val circleMembers: MutableList<CircleMemberEntity> = mutableListOf()
        if (circleEntity.circleType == CircleType.PUBLIC) {
            circleMembers.addAll(
                getAllPublicMember(
                    circleEntity,
                    circleEntity.countryCodes,
                    createUpdateCircleRequest.expertiseIds,
                    authenticatedUser
                )
            )
        } else {
            createUpdateCircleRequest.members?.forEach {
                circleMembers.add(
                    CircleMemberEntity(
                        circle = circleEntity,
                        userId = it,
                        lastUpdatedBy = authenticatedUser.userId
                    )
                )
            }
        }
        circleEntity.members = circleMembers
        createUpdateCircleRequest.expertiseIds?.let {
            circleEntity.expertises = expertiseService.retrieveExpertiseByIds(it)
        }
        circleRepository.save(circleEntity)
        return circleEntity.id?.let { circleDetails(circleId = it, authenticatedUser = authenticatedUser) }
            ?: throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
    }

    private fun getAllPublicMember(
        circleEntity: CircleEntity,
        countryCodes: List<String>?,
        expertiseIds: List<Int>?,
        authenticatedUser: AuthenticatedUser
    ): List<CircleMemberEntity> {
        val filter = ExpertSearchFilter()
        filter.countryCodes = countryCodes
        filter.expertiseIds = expertiseIds
        return expertUserRepository.searchByCriteriaAndIdInForCircleOrderByProfilePhotoUrlDesc(
            filter,
            AccountStatus.ACTIVE,
            Pageable.unpaged()
        )
            .filter { it.id != null }
            .map {
                if (circleEntity.id != null) {
                    it.id?.let { it1 -> circleMemberRepository.findTopByCircleAndUserId(circleEntity, it1) }
                        ?: it.id?.let { it1 ->
                            CircleMemberEntity(
                                id = null,
                                circle = circleEntity,
                                circleMemberStatus = CircleMemberStatus.INACTIVE,
                                userId = it1,
                                lastUpdatedBy = authenticatedUser.userId
                            )
                        }
                        ?: throw ApplicationException(ErrorCode.NOT_FOUND)
                } else it.id?.let { it1 ->
                    CircleMemberEntity(
                        id = null,
                        circle = circleEntity,
                        circleMemberStatus = CircleMemberStatus.INACTIVE,
                        userId = it1,
                        lastUpdatedBy = authenticatedUser.userId
                    )
                } ?: throw ApplicationException(ErrorCode.NOT_FOUND)
            }.toList()
    }

    @Transactional
    fun updateCircle(
        circleId: Long,
        createUpdateCircleRequest: CreateUpdateCircleRequest,
        authenticatedUser: AuthenticatedUser
    ): Circle {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        circleEntity.name = createUpdateCircleRequest.name
        circleEntity.about = createUpdateCircleRequest.about
        circleEntity.circleType = createUpdateCircleRequest.circleAccessType
        circleEntity.countryCodes = createUpdateCircleRequest.countryCode
        circleEntity.status = createUpdateCircleRequest.status
        val circleMembers: MutableList<CircleMemberEntity> = mutableListOf()
        if (circleEntity.circleType == CircleType.PUBLIC) {
            circleMembers.addAll(
                getAllPublicMember(
                    circleEntity,
                    circleEntity.countryCodes,
                    createUpdateCircleRequest.expertiseIds,
                    authenticatedUser
                )
            )
        } else {
            createUpdateCircleRequest.members?.forEach {
                circleMembers.add(
                    circleMemberRepository.findTopByCircleAndUserId(circleEntity, it)
                        ?: CircleMemberEntity(
                            circle = circleEntity,
                            userId = it,
                            lastUpdatedBy = authenticatedUser.userId
                        )
                )
            }
        }
        val deleted: MutableList<CircleMemberEntity> = mutableListOf()
        circleEntity.members.forEach { circleMemberEntity ->
            run {
                val exist = circleMemberEntity.userId in circleMembers.map { it.userId }
                if (!exist) deleted.add(circleMemberEntity)
            }
        }
        circleEntity.members = circleMembers
        circleRepository.save(circleEntity)
        circleMemberRepository.deleteAll(deleted)
        createUpdateCircleRequest.expertiseIds?.let {
            circleEntity.expertises = expertiseService.retrieveExpertiseByIds(it)
        }
        return circleDetails(circleId = circleId, authenticatedUser = authenticatedUser) ?: throw ApplicationException(
            ErrorCode.INTERNAL_SERVER_ERROR
        )
    }

    @Transactional
    fun deleteCircle(circleId: Long, authenticatedUser: AuthenticatedUser) {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        circleEntity.lastUpdatedBy = authenticatedUser.userId
        circleRepository.saveAndFlush(circleEntity)
        circleRepository.delete(circleEntity)
    }

    @Transactional
    fun circleStatusUpdate(circleId: Long, circleStatus: CircleStatus, authenticatedUser: AuthenticatedUser) {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        circleEntity.status = circleStatus
        circleRepository.saveAndFlush(circleEntity)
    }

    @Transactional(readOnly = true)
    fun listCircle(
        filter: CircleSearchFilter,
        pageRequest: Pageable,
        authenticatedUser: AuthenticatedUser
    ): CircleListing {
        val pagedClientView: Page<CircleView> = circleViewRepository.searchByCriteria(filter, pageRequest)
        pagedClientView.get().forEach {
            it.bannerPhotoKey =
                it.bannerPhotoKey?.let { it1 -> awsS3Service.getS3Url(it1) }
                    ?: CIRCLE_BANNER_IMG_URL
        }
        val stats: CircleStats = retrieveStats(filter)
        val circleListing = CircleListing.ModelMapper.from(pagedClientView, stats)
        circleListing.data.rows.forEach {
            if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT) {
                addDetailsForExpert(it, authenticatedUser)
            }
        }
        return circleListing
    }

    private fun addDetailsForExpert(it: Circle, authenticatedUser: AuthenticatedUser) {
        val circleMemberEntity: CircleMemberEntity? =
            circleMemberRepository.findTopByCircle_IdAndUserId(it.id, authenticatedUser.userId)
        if (circleMemberEntity != null) {
            it.memberAction = if (circleMemberEntity.circleMemberStatus == CircleMemberStatus.ACTIVE)
                CircleMemberAction.JOINED
            else CircleMemberAction.ACCEPT_AND_JOIN
        } else {
            val circleRequestEntity =
                circleRequestRepository.findTopByCircle_IdAndUserIdOrderByIdDesc(it.id, authenticatedUser.userId)
            if (circleRequestEntity != null) {
                it.memberAction = CircleMemberAction.REQUESTED
            } else it.memberAction = CircleMemberAction.REQUEST_TO_JOIN
        }

        it.membersThumbnail = circleMemberRepository
            .findTop5ByCircle_IdAndCircleMemberStatus(it.id, CircleMemberStatus.ACTIVE)
            .map {
                expertUserService.retrieveProfileSummary(it.userId)
                    .profilePictureFullUrl
            }
    }

    @Transactional
    fun circleDetails(circleId: Long, authenticatedUser: AuthenticatedUser): Circle? {
        val circle: Circle = getCircle(circleId)
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT) {
            circleMemberRepository.findTopByCircleAndUserId(circleEntity, authenticatedUser.userId)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        }
        circle.members = if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT) {
            expertUserService.retrieveActiveExpertsSummaryFromIdIn(ExpertSearchFilter(),
                PageRequest.of(
                    0,
                    Int.MAX_VALUE
                ),
                circleEntity.members.filter { it.circleMemberStatus == CircleMemberStatus.ACTIVE }
                    .map { it.userId }).rows
        } else expertUserService.retrieveActiveExpertsSummaryFromIdIn(ExpertSearchFilter(), PageRequest.of(
            0,
            Int.MAX_VALUE
        ), circleEntity.members.map { it.userId }).rows
        circle.members?.forEach {
            val circleMemberEntity = circleMemberRepository.findTopByCircleAndUserId(circleEntity, it.id)
            if (circleMemberEntity != null)
                it.isCurrentCircleJoined = circleMemberEntity.circleMemberStatus == CircleMemberStatus.ACTIVE
        }
        /*circle.requests = expertUserService.retrieveActiveExpertsSummaryFromIdIn(ExpertSearchFilter(),PageRequest.of(0,
            Int.MAX_VALUE),circleEntity.requests.map { it.userId }).rows*/
        if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT) {
            addDetailsForExpert(circle, authenticatedUser)
        }
        return circle
    }

    private fun getCircle(circleId: Long): Circle {
        val circleView = circleViewRepository.findFirstById(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val circle = Circle.ModelMapper.from(circleView)
        circle.bannerImageFullUrl = circleView.bannerPhotoKey?.let { awsS3Service.getS3Url(it) }
            ?: CIRCLE_BANNER_IMG_URL
        return circle
    }

    @Transactional(readOnly = true)
    fun retrieveStats(filter: CircleSearchFilter): CircleStats {
        val result = circleViewRepository.findStatsByCriteria(filter)
        return CircleStats(
            circles = result.sumOf { "${it[0]}".toInt() },
            active = result.filter { it[1].toString() == CircleStatus.ACTIVE.name }.sumOf { "${it[0]}".toInt() },
            inactive = result.filter { it[1].toString() == CircleStatus.INACTIVE.name }.sumOf { "${it[0]}".toInt() },
            public = result.filter { it[2].toString() == CircleType.PUBLIC.name }.sumOf { "${it[0]}".toInt() },
            private = result.filter { it[2].toString() == CircleType.PRIVATE.name }.sumOf { "${it[0]}".toInt() }
        )
    }

    @Transactional
    fun approveMember(approve: Boolean, circleId: Long, memberId: Long, authenticatedUser: AuthenticatedUser) {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        if (approve) {
            //Add member in circle.
            val circleMemberEntity: CircleMemberEntity =
                circleMemberRepository.findTopByCircleAndUserId(circleEntity, memberId)
                    ?: CircleMemberEntity(
                        circle = circleEntity,
                        userId = memberId,
                        lastUpdatedBy = authenticatedUser.userId
                    )
            circleMemberEntity.circleMemberStatus = CircleMemberStatus.ACTIVE
            circleMemberRepository.saveAndFlush(circleMemberEntity)
        }

        //Delete request.
        val circleRequestEntity: CircleRequestEntity? =
            circleRequestRepository.findTopByCircleAndUserIdOrderByIdDesc(circleEntity, memberId)
        circleRequestEntity?.let {
            it.isActive = false
            circleRequestRepository.save(it)
        }
    }

    @Transactional
    fun circleMemberAction(
        circleId: Long,
        circleMemberAction: CircleMemberAction,
        memberId: Long,
        authenticatedUser: AuthenticatedUser
    ) {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        when (circleMemberAction) {
            CircleMemberAction.JOINED -> {
                val circleMemberEntity: CircleMemberEntity =
                    circleMemberRepository.findTopByCircleAndUserId(circleEntity, memberId)
                        ?: CircleMemberEntity(
                            circle = circleEntity,
                            userId = memberId,
                            lastUpdatedBy = authenticatedUser.userId
                        )
                circleMemberEntity.circleMemberStatus = CircleMemberStatus.ACTIVE
                circleMemberRepository.saveAndFlush(circleMemberEntity)
            }
            CircleMemberAction.REQUESTED -> requestToJoin(circleId, authenticatedUser)
            CircleMemberAction.LEAVE -> {
                val circleMemberEntity: CircleMemberEntity =
                    circleMemberRepository.findTopByCircleAndUserId(circleEntity, memberId)
                        ?: CircleMemberEntity(
                            circle = circleEntity,
                            userId = memberId,
                            lastUpdatedBy = authenticatedUser.userId
                        )
                circleMemberEntity.isActive = false
                circleMemberRepository.save(circleMemberEntity)
            }
            else -> {
                return
            }
        }
    }

    @Transactional
    fun requestToJoin(circleId: Long, authenticatedUser: AuthenticatedUser) {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val circleRequestEntity: CircleRequestEntity =
            circleRequestRepository.findTopByCircleAndUserIdOrderByIdDesc(circleEntity, authenticatedUser.userId)
                ?: CircleRequestEntity(
                    circle = circleEntity,
                    userId = authenticatedUser.userId,
                    lastUpdatedBy = authenticatedUser.userId
                )
        circleRequestRepository.saveAndFlush(circleRequestEntity)
    }

    @Transactional
    fun getCircleResponseTrail(
        circleId: Long,
        userId: Long,
        lastMessageDateTime: Long?,
        seenBy: UserType
    ): List<List<CircleResponseTrail>> {

        val circleEntity = circleRepository.findByIdOrNull(circleId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        circleEntity.circleResponseTrails.forEach {
            if (seenBy != UserType.BACKOFFICE && it.rootUserId != userId) {
                it.isSeen = true
                circleResponseTrailRepository.save(it)
            }
        }
        val leadResponseTrails: List<CircleResponseTrail> =
            circleEntity.circleResponseTrails.sortedBy { it.createdDate }.map {
                CircleResponseTrail(
                    id = it.id,
                    rootUserId = it.rootUserId,
                    message = it.message,
                    dateTime = TimeUtil.toEpochMillis(it.createdDate),
                    asset = it.assetUrl,
                    isAsset = it.assetUrl != null,
                    seen = it.isSeen,
                    respondedBy = it.userType
                )
            }
        val map: TreeMap<String, MutableList<CircleResponseTrail>> = TreeMap()
        leadResponseTrails.filter {
            if (lastMessageDateTime != null) {
                it.dateTime > lastMessageDateTime
            } else true
        }.forEach {
            val loginAccount: LoginAccountEntity = loginAccountRepository.getReferenceById(it.rootUserId)
            it.shortAdminName = StringUtil.getShortName(loginAccount.firstName, loginAccount.lastName)

            if (it.respondedBy == UserType.EXPERT) {
                it.profilePictureFullUrl = expertUserService.retrieveProfileSummary(it.rootUserId).profilePictureFullUrl
            }

            val date: String =
                SimpleDateFormat("yyyy-MM-dd").format(Date(it.dateTime))
            SimpleDateFormat("yyyy-MM-dd").format(Date(it.dateTime))
            if (map.containsKey(date)) {
                map[date]?.add(it)
            } else {
                map[date] = mutableListOf(it)
            }
        }
//        val circle = getCircle(circleId)
//        circle.messages = map.values.map { it }
        return map.values.map { it }
    }

    @Transactional
    fun saveMessageTrail(
        userId: Long,
        circleId: Long,
        circleMessageTrailRequest: CircleMessageTrailRequest,
        authenticatedUser: AuthenticatedUser
    ):
            List<List<CircleResponseTrail>> {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT) {
            circleMemberRepository.findTopByCircleAndUserId(circleEntity, authenticatedUser.userId)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        }

        val circleResponseTrailEntity = CircleResponseTrailEntity(
            message = circleMessageTrailRequest.message,
            userType = UserType.valueOf(authenticatedUser.userType),
            rootUserId = userId,
            lastUpdatedBy = userId,
            circle = circleEntity
        )
        circleResponseTrailRepository.save(circleResponseTrailEntity)
        return getCircleResponseTrail(
            circleId,
            userId,
            circleMessageTrailRequest.lastMessageDateTime,
            UserType.valueOf(authenticatedUser.userType)
        )
    }

    @Transactional(readOnly = true)
    fun circleMemberSearch(
        circleId: Long, filter: ExpertSearchFilter,
        pageable: Pageable,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<ExpertProfileSummary> {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val userIds = if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT)
            circleEntity.members.filter { it.circleMemberStatus == CircleMemberStatus.ACTIVE }.map { it.userId }
        else circleEntity.members.map { it.userId }
        val result = expertUserService.retrieveActiveExpertsSummaryFromIdIn(filter, pageable, userIds)
        result.rows.forEach {
            val circleMemberEntity = circleMemberRepository.findTopByCircleAndUserId(circleEntity, it.id)
            if (circleMemberEntity != null)
                it.isCurrentCircleJoined = circleMemberEntity.circleMemberStatus == CircleMemberStatus.ACTIVE
        }
        return result
    }

    @Transactional
    fun uploadCoverPicture(circleId: Long, requestedById: Long, photo: MultipartFile): CircleBannerUploadResponse {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val existingProfileKey = circleEntity.bannerPhotoKey.orEmpty()

        if (existingProfileKey.isNotBlank()) {
            awsS3Service.deleteFileByS3Key(existingProfileKey)
        }

        log.info { "File content length: ${photo.size}" }
        val key = uploadFileToS3(photo, circleBannerFolder, false)
        circleEntity.bannerPhotoKey = key
        circleEntity.lastUpdatedBy = requestedById

        circleRepository.save(circleEntity)

        return CircleBannerUploadResponse(key, awsS3Service.getS3Url(key!!))
    }

    private fun uploadFileToS3(
        file: MultipartFile?,
        folder: String,
        isPublicAccess: Boolean = false
    ): String? {
        return if (file != null) {
            var fileName: String? = null
            val time = System.currentTimeMillis()
            file.originalFilename?.let { fileName = "${time}_${it}" }
            val awsFileData: AwsS3FileMetadata? = awsS3Service.uploadFile(file, folder, isPublicAccess, fileName)
            awsFileData?.key
        } else {
            throw ApplicationException(ErrorCode.FILE_UPLOAD_FAIL)
        }
    }

    @Transactional
    fun circleRequestSearch(
        circleId: Long,
        filter: ExpertSearchFilter,
        pageable: PageRequest
    ): PagedResult<ExpertProfileSummary> {
        val circleEntity = circleRepository.findByIdOrNull(circleId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        return expertUserService.retrieveActiveExpertsSummaryFromIdIn(
            filter,
            pageable,
            circleEntity.requests.map { it.userId })
    }

    @Transactional(readOnly = true)
    fun listCircleExpert(
        authenticatedUser: AuthenticatedUser,
        filter: CircleSearchFilter,
        pageRequest: Pageable
    ): CircleListing {
//        val expertProfile = expertUserService.retrieveProfile(authenticatedUser.userId)
//        filter.countryCode = mutableListOf(expertProfile.countryCode)
//        filter.expertise = expertProfile.expertiseId?.toMutableList()
        filter.status = CircleStatus.ACTIVE
        val joinedCircleId: MutableSet<Long> = mutableSetOf()
        circleMemberRepository.findAllByUserIdAndCircle_CircleType(authenticatedUser.userId, CircleType.PRIVATE)
            .forEach {
                it.circle.id?.let { it1 ->
                    circleRepository.findById(it1).ifPresent { it2 ->
                        it2.id?.let { it3 ->
                            joinedCircleId.add(
                                it3
                            )
                        }
                    }
                }
            }

        val pagedClientView: Page<CircleView> = circleViewRepository.searchForExpertByCriteria(
            filter,
            joinedCircleId,
            CircleStatus.ACTIVE,
            CircleType.PUBLIC,
            pageRequest
        )
        pagedClientView.get().forEach {
            it.bannerPhotoKey =
                it.bannerPhotoKey?.let { it1 -> awsS3Service.getS3Url(it1) }
                    ?: CIRCLE_BANNER_IMG_URL
        }
        //val stats: CircleStats = retrieveStats(filter)
        val circleListing = CircleListing.ModelMapper.from(pagedClientView, null)
        circleListing.data.rows.forEach {
            if (UserType.valueOf(authenticatedUser.userType) == UserType.EXPERT) {
                addDetailsForExpert(it, authenticatedUser)
            }
        }
        return circleListing
    }
}