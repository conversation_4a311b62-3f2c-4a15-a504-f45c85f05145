package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.ValidationType
import com.centuroglobal.shared.data.pojo.client.ClientListing
import com.centuroglobal.shared.data.pojo.client.ClientSearchFilter
import com.centuroglobal.shared.data.pojo.client.ClientStats
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.util.AdminAccessUtil
import mu.KotlinLogging
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

private val log = KotlinLogging.logger {}
private val expertValidSubscriberStatuses = listOf(AccountStatus.ACTIVE.name, AccountStatus.SUSPENDED.name)

@Service
class ClientService(
    private val clientViewRepository: ClientViewRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val tokenVerificationService: TokenVerificationService,
    private val countryService: CountryService,
    private val expertiseService: ExpertiseService
) {

    @Transactional(readOnly = true)
    fun listClients(
        filter: ClientSearchFilter,
        pageRequest: Pageable
    ): ClientListing {
        val pagedClientView: Page<ClientView> = clientViewRepository.searchByCriteria(filter, pageRequest)
        val stats: ClientStats = retrieveStats(filter)
        val clientListing = ClientListing.ModelMapper.from(pagedClientView, stats)
        clientListing.data.rows.forEach {
            try {
                it.country = it.countryCode?.let { it1 -> countryService.retrieveByCountryCode(it1).name }
            } catch (ignored: Exception) {
                log.debug(ignored.message)
            }
        }
        return clientListing
    }

    fun exportClientList(
        filter: ClientSearchFilter
    ): ClientListing {
        val pagedClientView: Page<ClientView> = clientViewRepository.searchByCriteria(
            filter, PageRequest.of(
                0, Integer.MAX_VALUE,
                Sort.by(
                    Sort.Order(Sort.Direction.DESC, "createdDate"),
                    Sort.Order(Sort.Direction.ASC, "fullName", Sort.NullHandling.NULLS_LAST)
                )
            )
        )

        val clientListing = ClientListing.ModelMapper.from(pagedClientView, ClientStats(0, 0, 0, 0, 0))
        clientListing.data.rows.forEach {
            try {
                it.country = it.countryCode?.let { it1 -> countryService.retrieveByCountryCode(it1).name }
                it.expertise = if (it.expertiseIds.isNullOrEmpty()) {
                    null
                } else {
                    expertiseService.retrieveExpertiseByIds(it.expertiseIds!!.split(",").map { it1 -> it1.toInt() })
                        .joinToString("; ") { it2 -> it2.name }
                }
            } catch (ignored: Exception) {
                log.debug(ignored.message)
            }
        }
        return clientListing
    }

    /*private fun generateClientCsv(pagedClientView: Page<ClientView>, out: OutputStream) {
        val writer = OutputStreamWriter(out)
        val csvWriter = CSVWriter(writer)
        csvWriter.writeNext(arrayOf<String>("Name","Email","role","Subscription","status","Joined","Company","Referred by","Referrals","Country","Expertise"))
        pagedClientView.forEach {
            val country: String = it.countryCode?.let { it1 -> countryService.retrieveByCountryCode(it1).name } ?: ""
            val expertise: String = if (it.expertiseIds.isNullOrEmpty()) {
                ""
            } else {
                expertiseService.retrieveExpertiseByIds(it.expertiseIds.split(",").map { it1 -> it1.toInt() })
                    .joinToString("; ") { it2 -> it2.name }
            }
            csvWriter.writeNext(arrayOf<String>(it.fullName,it.email,it.userType.name,it.subscription.toString(),it.status.toString(),it.createdDate.format(
                DateTimeFormatter.ISO_DATE),it.company?:"",it.referredBy?:"",
                it.referral.toString(),country,expertise))
        }
        csvWriter.close()
        csvWriter.flush()
    }*/

    @Transactional(readOnly = true)
    fun retrieveStats(filter: ClientSearchFilter): ClientStats {
        val result = clientViewRepository.findStatsByCriteria(filter)
        return ClientStats(
            users = result.sumOf { "${it[0]}".toInt() },
            active = result.filter { it[1].toString() == AccountStatus.ACTIVE.name }.sumOf { "${it[0]}".toInt() },
            corporate = result.filter { it[2].toString() == UserType.CORPORATE.name }.sumOf { "${it[0]}".toInt() },
            expert = result.filter { it[2].toString() == UserType.EXPERT.name }.sumOf { "${it[0]}".toInt() },
            subscribed = result.filter {
                // Corporate with status = PENDING_VERIFICATION will not have subscription status = true
                it[3] as Boolean && it[2].toString() == UserType.CORPORATE.name ||
                        // Expert with status = PENDING_VERIFICATION will not be counted as a subscribed user
                        it[3] as Boolean && it[2].toString() == UserType.EXPERT.name && expertValidSubscriberStatuses.contains(
                    it[1].toString()
                )
            }.sumOf { "${it[0]}".toInt() }
        )
    }

    @Transactional(readOnly = true)
    fun listClientsReferredBy(
        referredById: Long,
        pageRequest: Pageable
    ): ClientListing {
        val pagedClientView: Page<ClientView> = clientViewRepository.findByReferredById(referredById, pageRequest)
        val stats: ClientStats = retrieveStatsReferredBy(referredById)

        val clientListing = ClientListing.ModelMapper.from(pagedClientView, stats)
        clientListing.data.rows.forEach {
            try {
                it.country = it.countryCode?.let { it1 -> countryService.retrieveByCountryCode(it1).name }
            } catch (ignored: Exception) {
                log.debug(ignored.message)
            }
        }
        return clientListing
    }

    @Transactional(readOnly = true)
    fun retrieveStatsReferredBy(referredById: Long): ClientStats {
        val result = clientViewRepository.findStatsByReferredById(referredById)
        return ClientStats(
            users = result.sumOf { "${it[0]}".toInt() },
            active = result.filter { it[1].toString() == AccountStatus.ACTIVE.name }.sumOf { "${it[0]}".toInt() },
            corporate = result.filter { it[2].toString() == UserType.CORPORATE.name }.sumOf { "${it[0]}".toInt() },
            expert = result.filter { it[2].toString() == UserType.EXPERT.name }.sumOf { "${it[0]}".toInt() },
            subscribed = result.filter {
                // Corporate with status = PENDING_VERIFICATION will not have subscription status = true
                it[3] as Boolean && it[2].toString() == UserType.CORPORATE.name ||
                        // Expert with status = PENDING_VERIFICATION will not be counted as a subscribed user
                        it[3] as Boolean && it[2].toString() == UserType.EXPERT.name && expertValidSubscriberStatuses.contains(
                    it[1].toString()
                )
            }.sumOf { "${it[0]}".toInt() }
        )
    }

    fun resendVerificationCode(userId: Long) {
        val loginAccountEntity: LoginAccountEntity =
            loginAccountRepository.findById(userId).orElseThrow{ throw ApplicationException(ErrorCode.BAD_REQUEST)}

        when(AdminAccessUtil.getUserType(loginAccountEntity)) {
            UserType.BACKOFFICE -> {
                tokenVerificationService.resend(userId, ValidationType.INVITE_BACKOFFICE)
            }
            UserType.EXPERT -> {
                val type = if((loginAccountEntity as ExpertUserEntity).companyProfile?.companyType == ExpertCompanyType.SUPPLIER){
                    ValidationType.INVITE_PARTNER_EXPERT
                }
                else{
                    ValidationType.INVITE_EXPERT
                }
                tokenVerificationService.resend(userId, type)
            }
            UserType.CORPORATE -> {
                val type = if((loginAccountEntity as CorporateUserEntity).corporate.partner!=null){
                    ValidationType.INVITE_PARTNER_CORPORATE
                }
                else{
                    ValidationType.CORPORATE_SIGNUP
                }
                tokenVerificationService.resend(userId, type)
            }
            UserType.PARTNER -> {
                tokenVerificationService.resend(userId, ValidationType.PARTNER_SIGNUP)
            }
        }
    }

    fun updateUserStatus(userId: Long, accountStatus: AccountStatus) {
        val loginAccountEntity: LoginAccountEntity = loginAccountRepository.findById(userId).orElseThrow {
            ApplicationException(ErrorCode.BAD_REQUEST)
        }
        loginAccountEntity.status = accountStatus
        loginAccountRepository.save(loginAccountEntity)
    }
}