package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.subscription.SubscriptionPlanEntity
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.shared.data.payload.account.*
import com.centuroglobal.shared.data.payload.account.signup.OnBoardingProceedRequest
import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.payload.corporate.CorporateUserInfoRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.subscription.SharedSubscriptionService
import com.centuroglobal.shared.util.EditUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.JsonNode
import mu.KotlinLogging
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.client.RestTemplate
import java.time.LocalDate
import java.util.stream.Collectors
import kotlin.jvm.optionals.getOrNull

private val log = KotlinLogging.logger {}

@Service
class CorporateService(
    @Value("\${app.aws.s3.user-profile-folder}")
    private val userProfileFolder: String,
    @Value("\${app.aws.s3.onboarding-doc-folder}")
    private val onboardingDocsFolder: String,
    @Value("\${app.aws.s3.corporate-doc-folder}")
    private val corporateDocsFolder: String,
    private val corporateRepository: CorporateRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val corporateUserService: CorporateUserService,
    private val countryService: CountryService,
    private val tokenVerificationService: TokenVerificationService,
    private val restTemplate: RestTemplate,
    private val loginAccountRepository: LoginAccountRepository,
    private val onBoardingRepository: OnBoardingRepository,
    private val accountEntityRepository: AccountEntityRepository,
    private val bandsRepository: BandsRepository,
    private val partnerRepository: PartnerRepository,
    private val awsS3Service: AwsS3Service,
    private val caseRepository: CaseRepository,
    private val queryRepository: QueryRepository,
    private val rfpRepository: RfpRepository,
    private val clientViewRepository: ClientViewRepository,
    private val validationTokenRepository: ValidationTokenRepository,
    private val clientDocRepository: ClientDocRepository,
    private val subscriptionService: SubscriptionService,
    private val bandsDetailsRepository: BandsDetailsRepository,
    private val sharedSubscriptionService: SharedSubscriptionService,
    private val userProfileUtil: UserProfileUtil,
    private val excelService: ExcelService,
    private val visaFeesRepository: VisaFeesRepository
) {

    @Transactional
    fun createCorporateAndReturnData(request: SignUpRequest, sendVerifyMail: Boolean = true): Pair<Long, Long> {
        val corporateId = createCorporate(request, sendVerifyMail)
        log.info("corporate created with id $corporateId")
        Thread.sleep(1500)
        val userId = corporateRepository.findByIdOrNull(corporateId)!!.rootUserId

        val accountId = corporateUserRepository.findById(userId).get().accounts!!.iterator().next().id!!
        log.info("corporate user id $userId")
        return Pair(userId, accountId)
    }

    fun createCorporateWithPartner(request: SignUpRequest, partnerId: Long): Long {
        return createCorporate(request, true)
    }

    @Transactional
    fun createCorporate(request: SignUpRequest, sendVerifyMail: Boolean = true): Long {
        try {

            val domainCount = loginAccountRepository.countEmailDomain(request.email, Role.ROLE_CORPORATE)
            if(domainCount>0L) {
                throw ApplicationException(ErrorCode.CORPORATE_CREATE_DUPLICATE_DOMAIN)
            }

            val status = if (request.isDraft) CorporateStatus.DRAFT else CorporateStatus.PENDING_VERIFICATION

            val companyLogo = getProfileUrl(request.companyLogoId)
            val corporateEntity = corporateRepository.save(
                CorporateEntity(
                    name = request.corporateName.trim(),
                    countryCode = countryService.retrieveByCountryCode(request.countryCode).code,
                    status = status,
                    subscriptionActive = request.partnerId != null,
                    lastUpdatedBy = 0L,
                    rootUserId = 0L,
                    partner = request.partnerId?.let { partnerRepository.findById(it).getOrNull() },
                    companyLogoId = companyLogo,
                    primaryColor = request.primaryColor,
                    secondaryColor = request.secondaryColor,
                    questionsQuota = request.aiMessageCount,
                    subscriptionStartDate = request.subscriptionStartDate?.let { TimeUtil.fromInstantMillis(it) },
                    subscriptionEndDate = request.subscriptionEndDate?.let { TimeUtil.fromInstantMillis(it) },
                    isTeamEmail = request.isTeamEmail && request.assignedTeam?.isNotEmpty() == true,
                    visaSignAuthName = request.visaSignAuthName,
                    visaSignAuthDesignation = request.visaSignAuthDesignation,
                    modeOfPayment = request.modeOfPayment,
                    signatureKey = request.signatureKey,
                    businessLetterKey = request.businessLetterKey
                )
            )

            uploadVisaDocs(request, corporateEntity)

            request.assignedTeam?.let { teamList ->
                corporateEntity.team = teamList.map { CorporateTeamEntity(corporate = corporateEntity,
                    designation = it.designation,
                    user = loginAccountRepository.findById(it.userId).get()) }.toMutableList()
            }

            val docPath = "$onboardingDocsFolder/${corporateEntity.id}"

            request.onboardingDocs?.forEach {
                awsS3Service.uploadFromTmp(it.s3Key, "$docPath/${it.s3Key}")
            }

            val savedCorporate = corporateRepository.save(corporateEntity)

            //create subscription
            if(request.partnerId == null) {
                createSubscription(request, savedCorporate.id!!)
                sharedSubscriptionService.createEmptyUsageForCurrentMonth(corporateEntity, LocalDate.now().withDayOfMonth(1))
            }

            val docEntities = request.onboardingDocs?.map {
                ClientDocEntity(

                    docName = it.docName,
                    docKey = "$docPath/${it.s3Key}",
                    fileType = it.type,
                    fileName = it.fileName,
                    fileSize = it.size,
                    docType = ClientDocType.ON_BOARD,
                    referenceType = UserDocType.CORPORATE,
                    referenceId = savedCorporate.id!!
                )
            }?.toMutableList()

            docEntities?.let { clientDocRepository.saveAll(it) }

            //upload company logo
            companyLogo?.let { awsS3Service.uploadFromTmp(request.companyLogoId!!, it) }

            val newUser = corporateUserService.createCorporateRootUser(
                CreateCorporateUserRequest(
                    corporateId = corporateEntity.id,
                    email = request.email,
                    firstName = request.firstName,
                    lastName = request.lastName,
                    jobTitle = request.jobTitle,
                    referralCode = request.referralCode,
                    keepMeInformed = request.keepMeInformed,
                    countryCode = request.countryCode
                ), sendVerifyMail
            )

            val corporateRootUser = corporateUserRepository.findById(newUser.id!!).get()

            val bandName: String
            val defaultBands = if (request.partnerId != null){
                bandName = "Super Admin"
                bandsRepository.findByCorporateId(-2)
            } else{
                bandName = "Super Admin (free)"
                bandsRepository.findByCorporateId(-1)
            }
            defaultBands!!.forEach {
                val b = BandsEntity(
                    name = it.name,
                    description = it.description,
                    status = it.status,
                    color = it.color,
                    corporate = corporateEntity
                )
                b.createdBy=newUser.id
                b.updatedBy=newUser.id

                val accesses = getBandAccesses(request.features, request.partnerId, it, b)
                b.bandAccesses = accesses
                bandsRepository.save(b)
            }

            corporateEntity.lastUpdatedBy = newUser.id!!
            corporateEntity.rootUserId = newUser.id!!

            var account = AccountEntity(
                name = request.corporateName,
                status = AccountStatus.ACTIVE,
                description = null,
                companyName = null,
                corporate = corporateEntity
            )

            account.createdBy = newUser.id
            account.updatedBy = newUser.id
            account = accountEntityRepository.save(account)
            val band = bandsRepository.findByNameAndCorporate(bandName, corporateEntity)
            val newAccount = accountEntityRepository.findById(account.id!!).get()
            corporateRootUser.accounts = mutableSetOf(newAccount)
            corporateRootUser.band = band
            corporateRootUser.questionsQuota = corporateEntity.questionsQuota?:0
            corporateUserRepository.save(corporateRootUser)
            val updatedEntity = corporateRepository.saveAndFlush(corporateEntity)
            return updatedEntity.id!!

        } catch (ex: Exception) {
            when (ex) {
                is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.CORPORATE_ALREADY_EXISTS)
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.CORPORATE_CREATE_FAIL)
                }
            }
        }
    }

    private fun uploadVisaDocs(
        genericRequest: Any,
        corporateEntity: CorporateEntity
    ) {
        val signatureKey: String?
        val businessLetterKey: String?
        val visaFeesKey: String?

        when (genericRequest) {
            is SignUpRequest -> {
                signatureKey = genericRequest.signatureKey
                businessLetterKey = genericRequest.businessLetterKey
                visaFeesKey = genericRequest.visaFeesKey
            }

            is UpdateCorporateRequest -> {
                signatureKey = genericRequest.signatureKey
                businessLetterKey = genericRequest.businessLetterKey
                visaFeesKey = genericRequest.visaFeesKey
            }

            else -> {
                return
            }
        }

        if(signatureKey!=null){
            corporateEntity.signatureKey = uploadVisaDoc(signatureKey, "signature", corporateEntity)
        }
        if(businessLetterKey!=null){
            corporateEntity.businessLetterKey = uploadVisaDoc(businessLetterKey, "business-letter", corporateEntity)
        }
        if(visaFeesKey!=null){
            populateVisaFees(visaFeesKey, corporateEntity)
            corporateEntity.visaFeesKey = uploadVisaDoc(visaFeesKey, "visa-fees", corporateEntity)
        }
        visaFeesKey?.let { populateVisaFees(it, corporateEntity) }
    }

    private fun populateVisaFees(key: String, corporateEntity: CorporateEntity) {
        log.info("populating visa fees for corporate ${corporateEntity.id}")
        corporateEntity.visaFees?.forEach { visaFeesRepository.delete(it) }

        try {
            val fileInputStream = awsS3Service.downLoadFile(key="temp/$key")
            XSSFWorkbook(fileInputStream).use { workbook ->
                val sheet = workbook.getSheetAt(0)
                val visaFees = mutableListOf<VisaFeesEntity>()
                for (rowNo in 1..sheet.lastRowNum) {
                    val visaFeesEntity = VisaFeesEntity(
                        corporate = corporateEntity,
                        countryCode = excelService.setValue(sheet, rowNo, 0)!!,
                        eVisaFees = excelService.setValue(sheet, rowNo, 1)!!.toDouble(),
                        businessVisaFees = excelService.setValue(sheet, rowNo, 2)!!.toDouble()
                    )
                    visaFees.add(visaFeesEntity)
                }
                log.info("Total ${visaFees.size} visa fees populated for corporate ${corporateEntity.id}")
                corporateEntity.visaFees = visaFees
                corporateRepository.save(corporateEntity)
            }
        }
        catch (e: Exception) {
            log.error("Error in populating visa fees", e)
            throw ApplicationException(ErrorCode.UPLOAD_FILE_FAIL("Error in populating visa fees"))
        }
    }

    private fun uploadVisaDoc(
        s3Key: String,
        docName: String,
        corporateEntity: CorporateEntity
    ): String {
        val docPath = "$corporateDocsFolder/${corporateEntity.id}"
        awsS3Service.uploadFromTmp(s3Key, "$docPath/$docName")
        return "$docPath/$docName"
    }

    private fun getBandAccesses(
        features: List<String>?,
        partnerId: Long?,
        defaultBand: BandsEntity,
        copyOfDefaultBand: BandsEntity
    ): MutableList<BandDetailsEntity>? {

        val filteredFeatures = mutableListOf<String>()
        features?.let { filteredFeatures.addAll(it) }

        val defaultAccesses = defaultBand.bandAccesses
        if (partnerId!=null) {
            //remove features that are requested but not present for a partner corporate accesses
            val partnerFeatures = partnerRepository.findByIdOrNull(partnerId)?.corporateAccess?.split(",")?:emptyList()
            filteredFeatures.retainAll(partnerFeatures)
        }

        return defaultAccesses?.filter { access ->
            filteredFeatures.contains(access.access?.featureKey) || filteredFeatures.contains(
                access.visibility?.featureKey
            )
        }
            ?.map { i ->
                BandDetailsEntity(
                    band = copyOfDefaultBand,
                    access = i.access,
                    visibility = i.visibility
                )
            }?.toMutableList()

    }

    private fun createSubscription(request: SignUpRequest, id: Long): SubscriptionPlanEntity {
        return if(request.subscriptionPlan == "CUSTOM"){
            subscriptionService.createCustomSubscription(
                request.customSubscription!!,
                id,
                request.subscriptionStartDate,
                request.subscriptionEndDate
            )
        } else {
            subscriptionService.createDefaultSubscription(
                request.subscriptionPlan!!.toLong(), id,
                request.subscriptionStartDate,
                request.subscriptionEndDate
            )
        }
    }

    private fun getProfileUrl(key: String?): String? {
        return if (key.isNullOrBlank()) null else "$userProfileFolder/${key}"
    }

    @Transactional(readOnly = true)
    fun retrieveCorporate(userId: Long): Corporate {

        val corporateUser = corporateUserRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        val profilePhotoUrl = corporateUser.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url)}
        val corporate = Corporate.ModelMapper.fromRootUser(corporateUser, profilePhotoUrl)

        val onBoardingEntity: OnBoardingEntity? = onBoardingRepository.findFirstByRootUserId(corporateUser.corporate.rootUserId)
        onBoardingEntity?.let {
            corporate.onBoardingInfo = OnBoardingProceedRequest.ModelMapper.fromEntity(onBoardingEntity)
        }
        return corporate
    }

    //@Transactional
    fun updateCorporate(
        corporateUserId: Long,
        requesterUserId: Long,
        requesterUserType: String,
        request: UpdateCorporateProfileRequest
    ): Corporate {
        try {
            if (request.corporateInfo == null && request.status == null)
                throw ApplicationException(ErrorCode.BAD_REQUEST)

            val corporateUser = corporateUserRepository.findById(corporateUserId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

            if (request.corporateInfo != null)
                updateCorporateInfo(corporateUser, request.corporateInfo!!)

            if (request.status != null && UserType.valueOf(requesterUserType) == UserType.BACKOFFICE)
                updateCorporateStatus(corporateUser, request.status!!, request.corporateInfo?.corporateId)

            if(request.userInfo != null)
                updateCorporateUserInfo(corporateUser, request.userInfo!!)

            corporateUser.lastUpdatedBy = requesterUserId
            corporateUser.corporate.lastUpdatedBy = requesterUserId

            // Backwards compatibility... If corporate doesn't have a stripe account, let's create it
            /*val stripeAccount = stripeAccountRepository.findByUserId(corporateUser.id!!)
            if (stripeAccount == null)
                stripeService.createCustomer(corporateUser.id!!, corporateUser.email)*/


            if(request.corporateInfo?.isPrimary == true)
                corporateUser.userType = "PRIMARY"
            else
                corporateUser.userType = "SECONDARY"

            val profilePhotoUrl = corporateUser.profilePhotoUrl?.let { url -> awsS3Service.getS3Url(url)}

            return Corporate.ModelMapper.fromRootUser(
                // force save and flush to ensure that DataIntegrityViolationException is thrown here
                // instead of facade level

                corporateUserRepository.saveAndFlush(corporateUser),
                profilePhotoUrl
            )
        } catch (ex: Exception) {
            when (ex) {
                is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.CORPORATE_ALREADY_EXISTS)
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.CORPORATE_UPDATE_FAIL)
                }
            }
        }
    }

    fun updatePartnerCorporate(
        corporateId: Long,
        requesterUserId: Long,
        requesterUserType: String,
        partnerId: Long,
        request: UpdatePartnerCorporateRequest
    ): Long {
        try {
            val corporate = corporateRepository.findByIdAndPartnerId(corporateId, partnerId)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)

            corporate.name = request.corporateName
            corporate.countryCode = request.countryCode
            corporate.lastUpdatedBy = requesterUserId
            request.companyLogoId?.let {
                val companyLogo = getProfileUrl(request.companyLogoId)
                if (companyLogo != null) {
                    awsS3Service.updateProfilePicture(request.companyLogoId, companyLogo, corporate.companyLogoId)
                    corporate.companyLogoId = request.companyLogoId

                }
            }
            request.primaryColor?.let { corporate.primaryColor = request.primaryColor }
            request.secondaryColor?.let { corporate.secondaryColor = request.secondaryColor }


            val updatedEntity = corporateRepository.save(
                corporate
            )
            return updatedEntity.id!!
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error("Unexpected Exception", ex)
                    throw ApplicationException(ErrorCode.CORPORATE_UPDATE_FAIL)
                }
            }
        }
    }


    private fun updateCorporateUserInfo(corporateUser: CorporateUserEntity, userInfo: CorporateUserInfoRequest) {

        val band = bandsRepository.findById(userInfo.bandId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        corporateUser.band = band

        val accounts = userInfo.accounts.map {
            accountEntityRepository.findById(it).orElseThrow{ApplicationException(ErrorCode.BAD_REQUEST)}
        }.toSet()
        corporateUser.accounts = accounts

        if(userInfo.managerUserIds != null) {
            corporateUser.managers = userInfo.managerUserIds!!.stream().map {
                    id->CorporateUserManagerEntity(
                managerId = id,
                corporateUser = corporateUser
            )
            }.collect(Collectors.toList())
        }
    }

    @Transactional
    fun resend(corporateUserId: Long): String? {
        val userId = corporateUserRepository.findById(corporateUserId)
        if(userId.isPresent && userId.get().status == AccountStatus.PENDING_VERIFICATION) {
            return tokenVerificationService.resend(userId.get().id!!, ValidationType.CORPORATE_SIGNUP)
        } else{
            throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_NOT_FOUND)
        }
    }

    private fun updateCorporateInfo(
        rootUser: CorporateUserEntity,
        profileRequest: UpdateCorporateInfoRequest
    ) {
        rootUser.firstName = profileRequest.firstName.trim()
        rootUser.lastName = profileRequest.lastName.trim()
        rootUser.jobTitle = profileRequest.jobTitle.trim()
        rootUser.keepMeInformed = profileRequest.keepMeInformed ?: false
        rootUser.contactNo = profileRequest.contactNo
        rootUser.dialCode = profileRequest.dialCode
        rootUser.questionsQuota = profileRequest.aiMessageCount
        rootUser.corporate.name = profileRequest.corporateName.trim()
        rootUser.userType = if (profileRequest.isPrimary) "PRIMARY" else "SECONDARY"
        rootUser.countryCode = countryService.retrieveByCountryCode(profileRequest.countryCode.trim()).code


        val profilePicKey = getProfileUrl(profileRequest.profilePicS3Key)
        if(profileRequest.profilePicS3Key?.isNotBlank() == true) {
            rootUser.profilePhotoUrl = awsS3Service.updateProfilePicture(profileRequest.profilePicS3Key, profilePicKey, rootUser.profilePhotoUrl)
        }

    }

    private fun updateCorporateStatus(
        rootUser: CorporateUserEntity,
        statusRequest: UpdateCorporateStatusRequest,
        corporateId: Long?
    ) {
        rootUser.status = AccountStatus.valueOf(statusRequest.status)
        rootUser.corporate.status = CorporateStatus.valueOf(statusRequest.status)
        corporateId?.let {
            if(it != rootUser.corporate.id) {
                rootUser.corporate = corporateRepository.findById(it).get()
                rootUser.userType = "SECONDARY"
            }
        }
    }


    fun linkedInSignup(accessHeader: String, email: String?): LoginAccountEntity {
        val headers = HttpHeaders()
        headers.set("Authorization", "Bearer $accessHeader")
        val entity: HttpEntity<Any> = HttpEntity(headers)
        val responseEntity: ResponseEntity<JsonNode> = restTemplate.exchange(
            "https://api.linkedin.com/v2/me", HttpMethod.GET, entity,
            JsonNode::class.java
        )
        val jsonNode = responseEntity.body
        val firstName: String = jsonNode?.get("localizedFirstName")?.asText() ?: ""
        val lastName: String = jsonNode?.get("localizedLastName")?.asText() ?: ""
        val userEmail: String = email ?: throw ApplicationException(ErrorCode.LOGIN_FAIL)
        val countryCode: String = (jsonNode?.get("localizedLastName")?.get("country")?.asText()) ?: "US"
        val corporateId: Long = createCorporate(SignUpRequest(userEmail, firstName, lastName, null, "",
            countryCode, null), false)
        var loginAccountEntity = corporateUserRepository.findFirstByCorporateId(corporateId)
            ?: throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
        loginAccountEntity.isLinkedin = true
        loginAccountEntity = loginAccountRepository.save(loginAccountEntity)
        return loginAccountEntity
    }

    fun retrieveAllCorporateUsers(partnerId: Long?): List<CorporateUsers> {
        val corporateUserList = if (partnerId!=null){
            corporateUserRepository.findUsersByCorporatePartnerId(partnerId)
        } else {
            corporateUserRepository.findAllProjectedBy()
        }
        return corporateUserList
    }

    fun checkUserExists(email: String): Boolean {
        val user = loginAccountRepository.findByEmail(email)
        return user != null
    }

    @Transactional
    fun addAccount(request: AddAccountRequest, corporateId: Long, authenticatedUser: AuthenticatedUser): Long {

        val corporate = corporateRepository.findById(corporateId)
        val accountEntity = accountEntityRepository.save(
            AccountEntity(
                name = request.name,
                status = request.status,
                description = request.description,
                companyName = request.companyName,
                corporate = corporate.get()
            )
        )
        return accountEntity.id!!
    }

    @Transactional(readOnly = true)
    fun retrieveAccountsForAdmin(filter: AccountSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser,
                         companyId: Long): PagedResult<AccountListing> {

        val accountList =
                accountEntityRepository.searchByCriteriaForAdmin(filter, companyId, pageRequest)

        val accListResponse = accountList.map { AccountListing.ModelMapper.from(
            it,
            it.createdBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) },
            it.updatedBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) }
        ) }.toList()

        return PagedResult.ModelMapper.from(accountList, accListResponse)
    }

    @Transactional(readOnly = true)
    fun retrieveAccounts(filter: AccountSearchFilter, pageRequest: PageRequest, authenticatedUser: AuthenticatedUser,
                         companyId: Long): PagedResult<AccountListing> {

        val accesses = authenticatedUser.visibilities.filter { it.feature == "USER" }
        if(accesses.isEmpty()){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val access = accesses[0]

        val accountList = when {
            access.accesses.contains("FULL") -> {
                accountEntityRepository.searchByCriteriaForAdmin(filter, companyId, pageRequest)
            }
            access.accesses.contains("REPORTEES") -> {
                //first find reportees
                val reportees = corporateUserRepository.findIdByManagersManagerId(authenticatedUser.userId)
                val createdByUserIds = reportees.map { it.id }.plus(authenticatedUser.userId)
                // Get all accounts created by reportees or current user
                accountEntityRepository.searchByCriteria(filter, companyId, createdByUserIds, pageRequest)
            }
            access.accesses.contains("OWN") -> {
                //OWN account. Get accounts created by current user.
                accountEntityRepository.searchByCriteria(filter, companyId, listOf(authenticatedUser.userId), pageRequest)
            }
            else -> {
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }

        val accListResponse = accountList.map { AccountListing.ModelMapper.from(
            it,
            it.createdBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) },
            it.updatedBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) }
        ) }.toList()

        return PagedResult.ModelMapper.from(accountList, accListResponse)
    }

    @Transactional
    fun updateAccount(
        accountId: Long,
        authenticatedUser: AuthenticatedUser,
        request: AddAccountRequest
    ): AccountEntity {

        val accountOg = accountEntityRepository.findById(accountId).get()

        accountOg.name = request.name
        accountOg.status = request.status
        accountOg.description = request.description
        accountOg.companyName = request.companyName

        return accountEntityRepository.saveAndFlush(accountOg)
    }


    @Transactional(readOnly = true)
    fun retrieveCorporateAccountUsers(authenticatedUser: AuthenticatedUser, corporateId: Long): MutableList<Account> {
        val corporate = corporateRepository.getReferenceById(corporateId)
        val accounts = accountEntityRepository.findAllByCorporate(corporate) as MutableList<AccountEntity>
        return toAccountUsers(accounts)
    }


    @Transactional(readOnly = true)
    fun retrieveForAdmins(authenticatedUser: AuthenticatedUser, companyId: Long): MutableList<Account> {

        val corporate = corporateRepository.findById(companyId)

        val accountList = accountEntityRepository.findAllByCorporate(corporate.get()) as MutableList<AccountEntity>

        return toAccountUsers(accountList)
    }

    @Transactional(readOnly = true)
    fun retrieveForCorporateAccountUsers(authenticatedUser: AuthenticatedUser, companyId: Long): MutableList<Account> {

        val accesses = authenticatedUser.visibilities.filter { it.feature == "USER" }
        if(accesses.isEmpty()){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val access = accesses[0]

        val accountList = when {
            access.accesses.contains("FULL") -> {
                val corporate = corporateRepository.findById(companyId)
                accountEntityRepository.findAllByCorporate(corporate.get()) as MutableList<AccountEntity>
            }
            access.accesses.contains("REPORTEES") -> {
                //first find reportees and then accounts for each reportees and then
                val reportees = corporateUserRepository.findAllByManagersManagerId(authenticatedUser.userId)
                reportees.add(corporateUserRepository.findById(authenticatedUser.userId).get())
                val accountUserMap = mutableMapOf<Long, Account>()

                reportees.filter { user->user.status!=AccountStatus.SUSPENDED }.forEach { corporateUserEntity ->
                    corporateUserEntity.accounts?.forEach {

                        if(accountUserMap.containsKey(it.id)){
                            accountUserMap[it.id!!]!!.users = accountUserMap[it.id!!]!!.users!!.plus(AccountUser(corporateUserEntity.id!!, corporateUserEntity.firstName+" "+corporateUserEntity.lastName))
                        }
                        else{
                            accountUserMap[it.id!!] = Account(it.name, it.id!!,
                                listOf(AccountUser(corporateUserEntity.id!!, corporateUserEntity.firstName+" "+corporateUserEntity.lastName)))
                        }
                    }
                }
                return accountUserMap.values.toMutableList()
            }
            access.accesses.contains("OWN") -> {
                //OWN account
                corporateUserRepository.findById(authenticatedUser.userId).get().accounts!!
            }
            else -> {
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }

        return toAccountUsers(accountList)
    }

    @Transactional(readOnly = true)
    private fun toAccountUsers(accountList: Collection<AccountEntity>): MutableList<Account> {
        return accountList.map {
            Account(it.name,it.id!!, it.corporateUsers?.filter {user -> user.status!=AccountStatus.SUSPENDED }?.map { a->
                AccountUser(
                    a.id!!,
                    a.firstName+" "+a.lastName
                )
            }?.toList()
            )
        }.toMutableList()
    }

    fun canUpdateUser(userId: Long, requestedByUser: AuthenticatedUser): Boolean {
        val corporateUser = corporateUserRepository.findById(userId).orElseThrow { ApplicationException(ErrorCode.BAD_REQUEST) }
        if (corporateUser.corporate.id == requestedByUser.companyId) {
            val accesses = requestedByUser.visibilities.filter { it.feature == "USER" }
            if(accesses.isNotEmpty()) {
                val access = accesses[0]
                when {
                    access.accesses.contains("FULL") -> {
                        // User can update if he has FULL access
                        return true
                    }
                    access.accesses.contains("REPORTEES") -> {
                        // Check if the user being updated reports to requestedByUser
                        return corporateUserRepository.findAllByManagersManagerIdAndManagersCorporateUser(
                            requestedByUser.userId, corporateUser).isPresent || requestedByUser.userId == userId
                    }
                    access.accesses.contains("OWN") -> {
                        //OWN account
                        return userId == requestedByUser.userId
                    }
                }
            }
        }
        return false
    }

    fun getAccountDetails(accountId: Long): AccountEntity {
        return accountEntityRepository.findById(accountId).orElseThrow {  ApplicationException(ErrorCode.NOT_FOUND)}
    }

    @Transactional(readOnly = true)
    fun retrieveSecondaryCorporateUsers(corporateId: Long): List<CorporateUserDetail>? {
        val corporateUsers = corporateUserRepository.findByCorporateId(corporateId)
        return corporateUsers.map { CorporateUserDetail.ModelMapper.from(it)}
    }

    @Transactional(readOnly = true)
    fun retrieveCorporates(isPartnerCompany: Boolean?, partnerId: Long?): List<ReferenceData> {
        val corporates =
            if(partnerId!=null) {
                corporateRepository.findAllByPartnerId(partnerId)
            } else if(isPartnerCompany == true) {
                corporateRepository.findByPartnerIdNotNull()
            } else {
                corporateRepository.findByPartnerIsNull()
            }
        return corporates.filter { it.id!! > 0 }
    }

    fun retrieveCorporateUsers(corporateId: Long): List<CorporateUsers> {
        return corporateUserRepository.findUsersByCorporateId(corporateId)
    }

    fun retrieveCorporateUserAccounts(userId: Long): List<ReferenceData> {
        return accountEntityRepository.findByCorporateUsersId(userId)
    }

    fun retrieveCorporateAccounts(corporateId: Long): List<ReferenceData> {
        return accountEntityRepository.findByCorporateId(corporateId)
    }

    @Transactional(readOnly = true)
    fun retrieveByCorporateId(corporateId: Long): CorporateResponse {

        val corporateEntity =
            corporateRepository.findById(corporateId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        val corporateUser = corporateEntity.users.filter { it.id == corporateEntity.rootUserId } [0]

        val onboardingDocs = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(
            corporateId,
            UserDocType.CORPORATE,
            ClientDocType.ON_BOARD
        )

        return CorporateResponse(
            id = corporateEntity.id,
            email = corporateUser.email,
            firstName = corporateUser.firstName,
            lastName = corporateUser.lastName,
            jobTitle = corporateUser.jobTitle,
            corporateName = corporateEntity.name,
            countryCode = corporateEntity.countryCode,
            primaryColor = corporateEntity.primaryColor,
            secondaryColor = corporateEntity.secondaryColor,
            companyLogoId = corporateEntity.companyLogoId?.let { awsS3Service.getS3Url(corporateEntity.companyLogoId!!) },
            referralCode = "",
            aiMessageCount = corporateEntity.questionsQuota,
            associatedPartner = corporateEntity.partner?.id,
            assignedTeam = corporateEntity.team.map {
                CorporateTeam(
                    it.designation,
                    it.user.id!!
                )
            },
            onboardingDocs = onboardingDocs.map {
                OnboardingDocs(
                    it.id,
                    it.docName,
                    it.docKey,
                    awsS3Service.getS3Url(it.docKey),
                    it.fileSize,
                    it.fileType,
                    it.fileName,
                    TimeUtil.toEpochMillis(it.lastUpdatedDate)
                )
            },
            subscriptions = subscriptionService.getPlans(corporateId),
            features = getDistinctFeatureAccesses(corporateUser.band),
            isTeamEmail = corporateEntity.isTeamEmail,
            visaSignAuthName = corporateEntity.visaSignAuthName,
            visaSignAuthDesignation = corporateEntity.visaSignAuthDesignation,
            modeOfPayment = corporateEntity.modeOfPayment,
            signature = corporateEntity.signatureKey?.let { awsS3Service.getS3Url(it) },
            businessLetter = corporateEntity.businessLetterKey?.let { awsS3Service.getS3Url(it) },
            visaFees = corporateEntity.visaFeesKey?.let { awsS3Service.getS3Url(it) }
        )

    }

    private fun getDistinctFeatureAccesses(band: BandsEntity?) =
        band?.bandAccesses?.mapNotNull { it.access?.featureKey ?: it.visibility?.featureKey }?.distinct()?: emptyList()

    @Transactional
    fun disassociateCorporate(corporateId: Long, authenticatedUser: AuthenticatedUser): Boolean {

        val corporate: CorporateEntity = corporateRepository.findById(corporateId).orElseThrow{ throw ApplicationException(ErrorCode.NOT_FOUND)}

        log.info("Disassociate the Corporate "+corporateId+" from partner "+corporate.partner?.id)
        // check active cases
        val activeCases = caseRepository.countByCreatedByIdAndStatusNotIn(corporateId, listOf("CASE_COMPLETE", "CANCELLED"))
        val corporateUsers = corporate.users.map { it.id!! }
        val assignedCases = caseRepository.countByAssigneeIdIn(corporateUsers)

        if(activeCases>0 || assignedCases>0) {
            log.error("Can not disassociate a corporate:[$corporateId] because there are active cases for it")
            throw ApplicationException(ErrorCode.CASE_NOT_COMPLETE)
        }

        //check active queries
        val activeQueries = queryRepository.countByCreatedByCorporateIdAndStatusNotIn(corporateId, listOf(QueryStatus.CANCELLED, QueryStatus.RESOLVED))

        val assignedQueries = queryRepository.countByAssignedToIdIn(corporateUsers)

        if(activeQueries>0 || assignedQueries>0) {
            log.error("Can not disassociate a corporate:[$corporateId] because there are active queries for it")
            throw ApplicationException(ErrorCode.QUERY_NOT_COMPLETE)
        }

        // check active rfp
        val activeRfp = rfpRepository.countByCreatedByCorporateIdAndStatusNotIn(corporateId, listOf(RfpStatus.CANCELLED, RfpStatus.RESOLVED, RfpStatus.DRAFT))
        val assignedRfp = rfpRepository.countByAssignedToIdIn(corporateUsers)

        if(activeRfp>0 || assignedRfp>0) {
            log.error("Can not disassociate a corporate:[$corporateId] because there are active proposals for it")
            throw ApplicationException(ErrorCode.RFP_NOT_COMPLETE)
        }

        corporate.partner = null
        corporate.users.forEach {
            it.status = AccountStatus.SUSPENDED
        }
        corporateRepository.save(corporate)
        return true
    }

    @Transactional
    fun delete(corporateId: Long): Boolean {
        val corporate = corporateRepository.findById(corporateId)
            .orElseThrow { ApplicationException(ErrorCode.CORPORATE_NOT_FOUND) }

        corporate.users.forEach {
            hasActiveCases(it.id!!)
            hasActiveQueries(it.id!!)
            hasActiveRfp(it.id!!)
            onBoardingRepository.deleteAllByRootUserId(it.id!!)
            validationTokenRepository.deleteAllByUserId(it.id!!)
            onBoardingRepository.deleteAllByRootUserId(it.id!!)
        }
        bandsRepository.deleteByCorporateRootUserId(corporate.rootUserId)
        corporateRepository.delete(corporate)
        return true
    }

    @Transactional
    fun deleteCorporateUser(corporateUserId: Long): Boolean {

        hasActiveCases(corporateUserId)
        hasActiveQueries(corporateUserId)
        hasActiveRfp(corporateUserId)

        onBoardingRepository.deleteAllByRootUserId(corporateUserId)
        validationTokenRepository.deleteAllByUserId(corporateUserId)
        corporateUserRepository.deleteById(corporateUserId)

        return true
    }

    private fun hasActiveQueries(corporateUserId: Long) {

        val activeQueries = queryRepository.countByCreatedById(corporateUserId)
        val assignedQueries = queryRepository.countByAssignedToIdIn(listOf(corporateUserId))
        if(activeQueries>0 || assignedQueries>0) {
            log.error("Can not delete a corporate user: [$corporateUserId] because there are active queries for it")
            throw ApplicationException(ErrorCode.QUERY_NOT_COMPLETE)
        }
    }

    private fun hasActiveRfp(corporateUserId: Long) {

        val activeRfp = rfpRepository.countByCreatedById(corporateUserId)
        val assignedRfp = rfpRepository.countByAssignedToIdIn(listOf(corporateUserId))
        if(activeRfp>0 || assignedRfp>0) {
            log.error("Can not delete a corporate user: [$corporateUserId] because there are active proposals for it")
            throw ApplicationException(ErrorCode.RFP_NOT_COMPLETE)
        }
    }

    private fun hasActiveCases(corporateUserId: Long) {

        val clientView = clientViewRepository.getReferenceByUserId(corporateUserId)
        val activeCases = caseRepository.countByCreatedBy(clientView!!)
        val assignedCases = caseRepository.countByAssigneeIdIn(listOf(corporateUserId))
        if(activeCases>0 || assignedCases>0) {
            log.error("Can not delete a corporate user:[$corporateUserId] because there are active cases for it")
            throw ApplicationException(ErrorCode.CASE_NOT_COMPLETE)
        }
    }

    fun retrieveCorporateUserDetails(corporateUserId: Long): CorporateUserDetailsResponse {
        val corporateUser=  corporateUserRepository.findById(corporateUserId)
        return CorporateUserDetailsResponse.ModelMapper.from(corporateUser.get(),awsS3Service)
    }

    @Transactional
    fun update(corporateId: Long, request: UpdateCorporateRequest): Boolean {

        val corporate = corporateRepository.findById(corporateId)
            .orElseThrow { ApplicationException(ErrorCode.CORPORATE_NOT_FOUND) }

        corporate.name = request.corporateName
        corporate.countryCode = request.countryCode
        corporate.primaryColor = request.primaryColor
        corporate.secondaryColor = request.secondaryColor
        corporate.visaSignAuthName = request.visaSignAuthName
        corporate.visaSignAuthDesignation = request.visaSignAuthDesignation
        corporate.modeOfPayment = request.modeOfPayment

        val companyLogo = getProfileUrl(request.companyLogoId)
        awsS3Service.updateProfilePicture(request.companyLogoId, companyLogo, corporate.companyLogoId)
        corporate.companyLogoId = companyLogo

        if(corporate.questionsQuota != request.aiMessageCount) {
            corporate.questionsQuota = request.aiMessageCount
            corporate.users.forEach { it.questionsQuota = request.aiMessageCount }
        }

        //update subscription
        if(corporate.partner == null) {
            updateSubscription(request, corporateId)
        }

        updateBandAccess(corporate, request.features)

        if(request.assignedTeam != null) {
            val newTeam = EditUtil.updateCorporateTeam(corporate.team, request.assignedTeam!!.map {
                CorporateTeamEntity(
                    user = loginAccountRepository.findById(it.userId).get(),
                    corporate = corporate,
                    designation = it.designation
                )
            }.toMutableList())
            corporate.team.addAll(newTeam)
            corporate.isTeamEmail = request.isTeamEmail && corporate.team.isNotEmpty()
        }

        val docPath = "$onboardingDocsFolder/${corporate.id}"
        if(request.onboardingDocs != null) {

            val onboardingDocs = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(
                corporateId,
                UserDocType.CORPORATE,
                ClientDocType.ON_BOARD
            )

            val pair =
                EditUtil.updateOnboardingDocs(onboardingDocs, request.onboardingDocs!!.map {
                    ClientDocEntity(
                        docName = it.docName,
                        docKey = if (!it.s3Key.startsWith(docPath)) "$docPath/${it.s3Key}" else it.s3Key,
                        fileType = it.type,
                        fileName = it.fileName,
                        fileSize = it.size,
                        docType = ClientDocType.ON_BOARD,
                        referenceId = corporateId,
                        referenceType = UserDocType.CORPORATE
                    )
                }.toMutableList())

            onboardingDocs.addAll(pair.first)

            //upload newly added/updated docs
            pair.first.forEach {
                awsS3Service.uploadFromTmp(it.docKey, "$docPath/${it.docKey}")
            }

            //delete removed docs from s3
            pair.second.forEach {
                awsS3Service.deleteFileByS3Key(it.docKey)
            }

            clientDocRepository.deleteAll(pair.second)
            clientDocRepository.saveAll(onboardingDocs)
        }

        uploadVisaDocs(request, corporate)

        corporateRepository.save(corporate)
        return true
    }

    @Transactional
    fun updateBandAccess(corporate: CorporateEntity, features: List<String>?) {

        val bands = bandsRepository.findByCorporateId(corporate.id!!)!!

        val existingAccess = bands.map { getDistinctFeatureAccesses(it).toSet() }.flatten()

        val newAccessToAdd = features?.toMutableSet()?: mutableSetOf()
        val accessesToRemove = existingAccess.toMutableSet()

        accessesToRemove.removeAll(newAccessToAdd)
        newAccessToAdd.removeAll(existingAccess.toSet())

        //remove existing accesses that are not in current requested features list.
        bandsDetailsRepository.deleteAllByBandCorporateAndAccessFeatureKeyIn(corporate, accessesToRemove.toList())

        //insert new accesses that were not present before but make sure they are present in default bands
        if (newAccessToAdd.isNotEmpty()) {
            val defaultBandCorporateId = if(corporate.partner!=null) -2L else -1L
            val defaultBands = bandsRepository.findByCorporateId(defaultBandCorporateId)
            val newBandDetails = mutableListOf<BandDetailsEntity>()
            defaultBands?.forEach { defaultBand ->

                val band = bands.firstOrNull { it.name == defaultBand.name }
                if (band!=null) {
                    getBandAccesses(
                        newAccessToAdd.toList(),
                        corporate.partner?.id,
                        defaultBand,
                        band
                        )?.let {
                        newBandDetails.addAll(it)
                    }
                }
            }
            bandsDetailsRepository.saveAll(newBandDetails)
        }
    }

    @Transactional
    private fun updateSubscription(request: UpdateCorporateRequest, corporateId: Long): SubscriptionPlanEntity? {

        //delete future plan if exists
        subscriptionService.deleteInActivePlan(corporateId)

        return if(request.subscriptionPlan == "CUSTOM") {
            //make upcoming plan as inactive
            request.customSubscription?.isActive = false

            request.customSubscription?.let {
                subscriptionService.createCustomSubscription(
                    it,
                    corporateId,
                    request.subscriptionStartDate,
                    request.subscriptionEndDate
                )
            }
        } else {
            request.subscriptionPlan?.let {
                subscriptionService.createDefaultSubscription(
                    it.toLong(),
                    corporateId,
                    request.subscriptionStartDate,
                    request.subscriptionEndDate,
                    false
                )
            }
        }
    }

    fun getEligibleClientList(
        user: AuthenticatedUser,
        userId: Long?,
        accountId: Long?
    ): Pair<Boolean, List<ClientView>> {

        val caseAccesses = user.visibilities.filter { it.feature == "USER" }
        if(caseAccesses.isEmpty()){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val caseAccess = caseAccesses[0]

        val corporateUsers = when {
            caseAccess.accesses.contains("FULL") -> {
                corporateUserRepository.findByCorporateId(user.companyId!!)
            }
            caseAccess.accesses.contains("REPORTEES") -> {
                corporateUserRepository.findAllByManagersManagerId(user.userId)
            }
            caseAccess.accesses.contains("OWN") -> {
                listOf(corporateUserRepository.findById(user.userId).get())
            }
            else -> {
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }
        var userIdList = mutableListOf<Long>()
        var accountFilterSearch = false

        if (userId == null && accountId == null) {
            userIdList = corporateUsers.map { it.id!! }.toMutableList()
            userIdList.add(user.userId)
        } else if (userId != null && accountId == null) {
            userIdList.add(userId)
        } else if(userId==null && accountId !=null){
            userIdList = corporateUsers.map { it.id!! }.toMutableList()
            userIdList.add(user.userId)
            accountFilterSearch = true
        } else {
            userIdList.add(userId!!)
            accountFilterSearch = true
        }

        val userList = clientViewRepository.findAllByUserIdIn(userIdList)
        return Pair(accountFilterSearch, userList)
    }

}