package com.centuroglobal.service

import com.centuroglobal.client.WorldBankClient
import com.centuroglobal.costofliving.client.NumbeoClient
import com.centuroglobal.data.payload.dashboard.CountryGdp
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.view.CountryView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs
import com.centuroglobal.shared.data.payload.dashboard.KeyValuePair
import com.centuroglobal.shared.data.pojo.CompanyTeam
import com.centuroglobal.shared.data.pojo.CorporateUserCounts
import com.centuroglobal.shared.data.pojo.DashboardCompanyInfo
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.repository.view.CountryViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.stream.Collectors


@Service
class DashboardService(
    private val blueprintRepository: BlueprintRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val leadRepository: LeadRepository,
    private val eventRepository: EventRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val countryGdpRepository: CountryGdpRepository,
    private val countryIndicesRepository: CountryIndicesRepository,
    private val countryViewRepository: CountryViewRepository,
    private val countryIndicesCategoryRepository: CountryIndicesCategoryRepository,
    private val countryHighlightsRepository: CountryHighlightsRepository,
    private val caseRepository: CaseRepository,
    private val clientViewRepository: ClientViewRepository,
    @Value("\${app.costOfLiving.expiry.city_day}")
    private val expiryDay: Long,
    @Value("\${app.indices.expiry.days}")
    private val indicesExpiryDay: Long,
    @Value("\${app.costOfLiving.apiKey}")
    private val apiKey: String,
    private val userProfileUtil: UserProfileUtil,
    private val awsS3Service: AwsS3Service,
    private val clientDocRepository: ClientDocRepository
) {
    @Autowired
    lateinit var worldBankClient: WorldBankClient

    @Autowired
    lateinit var numbeoClient: NumbeoClient

    fun generateAggregateData(user: AuthenticatedUser): List<KeyValuePair> {

        val blueprint = blueprintRepository.countByStatus(BlueprintStatus.ACTIVE)
        val expertUserCount = expertUserRepository.count()
        val leadCreated = leadRepository.countByCreatedBy(user.userId)
        val eventCount = eventRepository.countByEndDateGreaterThan(
            Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant())
        )
        val referralCount = loginAccountRepository.countByReferredBy(user.userId)
        val clientView = clientViewRepository.findById(user.userId)
        val caseCount = caseRepository.countByCreatedBy(clientView.get())
        val list = mutableListOf<KeyValuePair>()
        list.add(KeyValuePair("blueprint", blueprint.toString()))
        list.add(KeyValuePair("experts", expertUserCount.toString()))
        list.add(KeyValuePair("leadsSubmitted", leadCreated.toString()))
        list.add(KeyValuePair("upcomingEvents", eventCount.toString()))
        list.add(KeyValuePair("referralBonus", referralCount.toString()))
        list.add(KeyValuePair("casesInitiated", caseCount.toString()))
        return list
    }

    fun getTopCountryDataByFilter(filter: String?): List<CountryGdp> {
        val date = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
        val countriesList = countryViewRepository.findAll()
        val countryGdpList = getCountryGdpList(countriesList, date)
        val countryIndicesList = getCountryIndicesList(date, countriesList)
        val topCountryList = getTopCountriesByFilter(filter, countryIndicesList)
        val countryGdpTopList: MutableList<CountryGdp> = ArrayList()

        for (entry in topCountryList.entries) {
            val gdp = countryGdpList.find { country -> country.countryCode == entry.key }
            val c = CountryGdp(
                countryCode = entry.key,
                gdp = gdp?.gdp,
                population = gdp?.population,
                gdpPerCapita = gdp?.gdpPerCapita,
                indices = entry.value
            )
            countryGdpTopList.add(c)
        }
        return countryGdpTopList.sortedByDescending { it.indices }
    }

    private fun getTopCountriesByFilter(
        filter: String?,
        countryIndicesList: MutableList<CountryIndicesEntity>
    ): Map<String?, Double?> {
        var countriesTopList: Map<String?, Double?> = HashMap()
        when (filter) {
            "costOfLivingIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.costOfLivingIndex }.take(DashboardEnum.NOOFRECORD.key)
                    .stream().collect(
                        Collectors.toMap(CountryIndicesEntity::countryCode, CountryIndicesEntity::costOfLivingIndex)
                    )
            "qualityOfLifeIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.qualityOfLifeIndex }.take(DashboardEnum.NOOFRECORD.key)
                    .stream().collect(
                        Collectors.toMap(CountryIndicesEntity::countryCode, CountryIndicesEntity::qualityOfLifeIndex)
                    )
            "crimeIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.crimeIndex }.take(DashboardEnum.NOOFRECORD.key).stream()
                    .collect(
                        Collectors.toMap(CountryIndicesEntity::countryCode, CountryIndicesEntity::crimeIndex)
                    )
            "healthCareIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.healthCareIndex }.take(DashboardEnum.NOOFRECORD.key).stream()
                    .collect(
                        Collectors.toMap(CountryIndicesEntity::countryCode, CountryIndicesEntity::healthCareIndex)
                    )
            "purchasingPowerInclRentIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.purchasingPowerInclRentIndex }
                    .take(DashboardEnum.NOOFRECORD.key).stream().collect(
                        Collectors.toMap(
                            CountryIndicesEntity::countryCode,
                            CountryIndicesEntity::purchasingPowerInclRentIndex
                        )
                    )
            "pollutionIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.pollutionIndex }.take(DashboardEnum.NOOFRECORD.key).stream()
                    .collect(
                        Collectors.toMap(CountryIndicesEntity::countryCode, CountryIndicesEntity::pollutionIndex)
                    )
            "safetyIndex" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.safetyIndex }.take(DashboardEnum.NOOFRECORD.key).stream()
                    .collect(
                        Collectors.toMap(CountryIndicesEntity::countryCode, CountryIndicesEntity::safetyIndex)
                    )
            "propertyPriceToIncomeRatio" -> countriesTopList =
                countryIndicesList.sortedByDescending { it.propertyPriceToIncomeRatio }
                    .take(DashboardEnum.NOOFRECORD.key).stream().collect(
                        Collectors.toMap(
                            CountryIndicesEntity::countryCode,
                            CountryIndicesEntity::propertyPriceToIncomeRatio
                        )
                    )
        }
        return countriesTopList
    }

    private fun getCountryIndicesList(
        date: LocalDateTime,
        countriesList: List<CountryView>
    ): MutableList<CountryIndicesEntity> {
        val expiryDate = date.minusDays(indicesExpiryDay)
        val countryIndicesList = countryIndicesRepository.findAllByCreatedDateGreaterThan(expiryDate)
        if (countryIndicesList.isEmpty()) {
            for (country in countriesList) {
                val data = numbeoClient.findAllCountryIndices(apiKey, country.countryCode)
                val indicesData = CountryIndicesEntity(
                    countryCode = country.countryCode,
                    costOfLivingIndex = data.cpi_index,
                    qualityOfLifeIndex = data.quality_of_life_index,
                    crimeIndex = data.crime_index,
                    pollutionIndex = data.pollution_index,
                    healthCareIndex = data.health_care_index,
                    purchasingPowerInclRentIndex = data.purchasing_power_incl_rent_index,
                    safetyIndex = data.safety_index,
                    propertyPriceToIncomeRatio = data.property_price_to_income_ratio,
                    createdDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
                )
                countryIndicesList.add(indicesData)
            }
            countryIndicesRepository.saveAll(countryIndicesList)
        }
        return countryIndicesList
    }

    private fun getCountryGdpList(
        countriesList: List<CountryView>,
        date: LocalDateTime
    ): MutableList<CountryGdpEntity> {
        val expiryDate = date.minusDays(expiryDay)
        var countryGdpList = countryGdpRepository.findAllByCreatedDateGreaterThan(expiryDate)
        if (countryGdpList.isEmpty()) {
            val dataMap: MutableMap<String, CountryGdpEntity> = HashMap()
            for (i in 3 downTo 1) {
                val year = date.year.minus(i)

                val gdpPerCapitaData = worldBankClient.findGdpPerCapita("json", 500, year)
                val populationData = worldBankClient.findPopulation("json", 500, year)
                val gdpData = worldBankClient.findGDP("json", 500, year)



                if (gdpPerCapitaData[1] == null || populationData[1] == null || gdpData[1] == null)
                    continue

                val gdpPerCapitaMap = getDecimalData(gdpPerCapitaData)
                val populationMap = getIntegerData(populationData)
                val gdpMap = getDecimalData(gdpData)

                gdpPerCapitaMap.entries.forEach { gdpPerCapita->
                    val countryDataList = countriesList.filter { it.isoCoutryCode == gdpPerCapita.key }
                    if (countryDataList.isNotEmpty()) {
                        val countryData = countryDataList[0]
                        val countryGdp = CountryGdpEntity(
                            id = countryData.id,
                            gdpPerCapita = gdpPerCapita.value ?: dataMap[countryData.countryCode]?.gdpPerCapita,
                            population = populationMap[gdpPerCapita.key] ?: dataMap[countryData.countryCode]?.population,
                            gdp = gdpMap[gdpPerCapita.key] ?: dataMap[countryData.countryCode]?.gdp,
                            createdDate = date,
                            countryCode = countryData.countryCode
                        )
                        dataMap[countryData.countryCode] = countryGdp
                    }
                }
            }
            countryGdpList = countryGdpRepository.saveAll(dataMap.toList().map { it.second })
        }
        return countryGdpList
    }


    private fun getDecimalData(dataList: List<Object>): MutableMap<String, String?> {
        val data = ArrayList(dataList[1] as Collection<LinkedHashMap<String, Any?>>)
        val dataMap: MutableMap<String, String?> = HashMap()
        var i = 0
        while (i < data.size) {
            if (data[i] != null) {
                val iso3code = data[i]["countryiso3code"].toString()
                if (iso3code.isNotEmpty()) {
                    if (data[i]["value"] is Double) {
                        val value: Double? = data[i]["value"] as? Double
                        dataMap[iso3code] = toBigDecimalStringOrNull(value)
                    } else {
                        val value: Long? = data[i]["value"] as? Long
                        dataMap[iso3code] =
                            toBigDecimalStringOrNull(value)
                    }
                }
            }
            i++
        }
        return dataMap
    }

    private fun toBigDecimalStringOrNull(value: Any?): String? {
        if(value == null) return null

        var bigDecimalAsString: String? = when (value) {
            is Double -> {
                val doubleToBigDecimal = BigDecimal.valueOf(value)
                doubleToBigDecimal.setScale(2, RoundingMode.HALF_UP)
                    .toPlainString()
            }

            is Long -> {
                BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP)
                    .toPlainString()
            }

            else -> {
                null
            }
        }
        return bigDecimalAsString
    }


    private fun getIntegerData(dataList: List<Object>): MutableMap<String, String?> {
        val data = ArrayList(dataList[1] as Collection<LinkedHashMap<String, Any?>>)
        val dataMap: MutableMap<String, String?> = HashMap()
        var i = 0
        while (i < data.size) {
            if (data[i] != null) {
                val iso3code = data[i]["countryiso3code"].toString()
                if (iso3code.isNotEmpty()) {
                    dataMap[iso3code] = data[i]["value"].toString()
                }
            }
            i++
        }
        return dataMap
    }

    fun getIndicesCategory(): List<CountryIndicesCategoryEntity> {
        return countryIndicesCategoryRepository.findAll()
    }

    fun getCountryHighlights(): List<CountryHighlightsEntity>? {
        return countryHighlightsRepository.findAll()
    }

    @Transactional(readOnly = true)
    fun getTeamAndDocs(authenticatedUser: AuthenticatedUser): DashboardCompanyInfo {

        if (authenticatedUser.userType != UserType.CORPORATE.name) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val canAccessDocs =
            authenticatedUser.accesses.firstOrNull { it.feature == "COMPANY_INFO" }?.accesses?.contains("VIEW_DOCUMENTS")?:false

        val user = loginAccountRepository.findById(authenticatedUser.userId).get() as CorporateUserEntity

        val corporate = user.corporate

        val team = corporate.team.map {
            CompanyTeam(it.designation, userProfileUtil.retrieveProfile(it.user, null))
        }
        val counts = getCorporateUserCounts(corporate)
        val response = DashboardCompanyInfo(team, null, counts, corporate.countryCode, corporate.name)

        if (canAccessDocs) {
            val onBoardingDocs = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(
                corporate.id!!,
                UserDocType.CORPORATE,
                ClientDocType.ON_BOARD
            )
            response.docs = onBoardingDocs.map {
                OnboardingDocs(
                    it.id,
                    it.docName,
                    it.docKey,
                    awsS3Service.getS3Url(it.docKey),
                    it.fileSize,
                    it.fileType,
                    it.fileName,
                    TimeUtil.toEpochMillis(it.lastUpdatedDate)
                )
            }
        }
        return response
    }

    private fun getCorporateUserCounts(corporate: CorporateEntity): CorporateUserCounts {
        val accounts = corporate.accountList?.filter { it.status == AccountStatus.ACTIVE }?.size ?: 0
        val userCountMap =
            corporate.users.filter { it.status == AccountStatus.ACTIVE }.groupingBy { it.band!!.name }.eachCount()

        return CorporateUserCounts(
            accounts,
            userCountMap.getOrDefault("Super Admin", 0),
            userCountMap.getOrDefault("Account Manager", 0),
            userCountMap.getOrDefault("Applicant", 0)
        )
    }
}