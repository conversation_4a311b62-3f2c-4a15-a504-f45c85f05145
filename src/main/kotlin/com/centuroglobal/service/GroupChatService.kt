package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.chat.GroupChatDetails
import com.centuroglobal.shared.data.pojo.chat.GroupChatMessageRequest
import com.centuroglobal.shared.data.pojo.chat.GroupChatMessageResponse
import com.centuroglobal.shared.data.pojo.chat.GroupChatParticipantResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.GroupChatUtil
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class GroupChatService(

    val awsS3Service: AwsS3Service,
    val groupChatRepository: GroupChatRepository,
    val groupChatMessageRepository: GroupChatMessageRepository,
    val groupChatParticipantRepository: GroupChatParticipantRepository,
    val loginAccountRepository: LoginAccountRepository,
    val caseRepository: CaseRepository,
    val queryRepository: QueryRepository,
    val groupChatUtil: GroupChatUtil


) {
    @Transactional
    fun createMessage(messageRequest: GroupChatMessageRequest, authenticatedUser: AuthenticatedUser) : GroupChatMessageResponse {

        val groupChatEntity = groupChatRepository.findByChatTypeAndReferenceId(messageRequest.chatType,
            messageRequest.referenceId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        if(groupChatEntity.participants.firstOrNull { it.user.id == authenticatedUser.userId } == null) {
            throw ApplicationException(ErrorCode.NOT_FOUND)
        }

        val message = GroupChatMessageEntity(
            groupChat = groupChatEntity,
            message = messageRequest.message,
            userId = authenticatedUser.userId,
            assetUrl = "",
            isSeen = false
        )
        val savedMsg = groupChatMessageRepository.save(message)

        // update last seen for user
        updateLastSeen(groupChatEntity, authenticatedUser.userId)

        groupChatUtil.touchReferenceEntity(messageRequest.referenceId, messageRequest.chatType)

        return GroupChatMessageResponse(
            savedMsg.id,
            TimeUtil.toEpochMillis(savedMsg.createdDate),
            savedMsg.message,
            savedMsg.userId,
            savedMsg.isSeen,
            savedMsg.assetUrl,
            savedMsg.isHidden
        )
    }

    private fun updateLastSeen(groupChat: GroupChatEntity, participantId: Long) {
        val participantEntity = groupChatParticipantRepository.findByGroupChatAndUserId(groupChat, participantId)
        participantEntity.ifPresent {
            it.lastSeen = LocalDateTime.now()
            groupChatParticipantRepository.save(it)
        }
    }

    fun getMessages(
        chatType: ChatType,
        referenceId: Long,
        lastMessageDateTime: Long,
        authenticatedUser: AuthenticatedUser
    ): List<GroupChatMessageResponse>? {

        val groupChatEntity = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        // if role is admin/super admin return all messages
        val messages = if(isAdminAndChatParticipant(authenticatedUser, groupChatEntity)){
            groupChatMessageRepository.findByGroupChatAndCreatedDateGreaterThanOrderByCreatedDate(
                groupChatEntity, TimeUtil.fromInstantMillis(lastMessageDateTime))
        }
        else{
            groupChatMessageRepository.findByGroupChatAndIsHiddenAndCreatedDateGreaterThanOrderByCreatedDate(
                groupChatEntity, false, TimeUtil.fromInstantMillis(lastMessageDateTime))
        }

        return messages?.map{
            GroupChatMessageResponse(
                it.id,
                TimeUtil.toEpochMillis(it.createdDate),
                it.message,
                it.userId,
                it.isSeen,
                it.assetUrl,
                it.isHidden
            )
        }
    }

    fun getParticipants(chatType: ChatType, referenceId: Long, user: AuthenticatedUser): GroupChatDetails {

        val groupChat = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        val participants = groupChatParticipantRepository.findAllByGroupChat(groupChat)

            return GroupChatDetails(participants?.filter { it.isReal }?.map {
                when (it.user) {
                    is CorporateUserEntity -> {
                        GroupChatParticipantResponse.ModelMapper.from(it, it.user as CorporateUserEntity, awsS3Service)
                    }

                    is ExpertUserEntity -> {
                        GroupChatParticipantResponse.ModelMapper.from(it, it.user as ExpertUserEntity, awsS3Service)
                    }

                    else -> {
                        GroupChatParticipantResponse.ModelMapper.from(it, awsS3Service)
                    }
                }
            }, participants?.any { it.user.id==user.userId && it.isReal })

    }

    fun deleteMessage(messageId: Long, hidden: Boolean, authenticatedUser: AuthenticatedUser) {

        val messageEntity = groupChatMessageRepository.findById(messageId).orElseThrow {
            ApplicationException(ErrorCode.NOT_FOUND) }

        val groupChat = messageEntity.groupChat
        if(!isMessageDeleteAllowed(groupChat)){
            return
        }

        if (isAdminOrPartnerAndChatParticipant(authenticatedUser, groupChat)){
            messageEntity.isHidden = hidden
            groupChatMessageRepository.save(messageEntity)
        }
    }

    private fun isMessageDeleteAllowed(groupChat: GroupChatEntity): Boolean {
        return when(groupChat.chatType){
            ChatType.CASE, ChatType.APPLICANT_CASE, ChatType.CLIENT_CASE -> {
                // for CASE type chats - Once a Case is completed, Cancelled or Archived, assigned CG Admin will
                // not be able to hide/un-hide a message.
                val case = caseRepository.findById(groupChat.referenceId).get()
                !(case.archive || case.status in listOf("CANCELLED", "CASE_COMPLETE"))
            }

            ChatType.QUERY -> {
                true
            }

            ChatType.RFP -> {
                true
            }

            else -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
        }
    }

    private fun isAdminAndChatParticipant(authenticatedUser: AuthenticatedUser, groupChatEntity: GroupChatEntity): Boolean{

        val participant = groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, authenticatedUser.userId)

        return participant.isPresent &&
                (Role.ROLE_SUPER_ADMIN.name == authenticatedUser.role || Role.ROLE_ADMIN.name == authenticatedUser.role)
    }

    private fun isAdminOrPartnerAndChatParticipant(authenticatedUser: AuthenticatedUser, groupChatEntity: GroupChatEntity): Boolean{

        val participant = groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, authenticatedUser.userId)

        return participant.isPresent && (AdminAccessUtil.isAdmin(authenticatedUser) || AdminAccessUtil.isPartner(participant.get().user))
    }


    fun createGroupChat(chatType: ChatType, referenceId: Long, participants: List<Long>?, virtualParticipants: List<LoginAccountEntity>?): GroupChatEntity {
        return createGroupChatWithOwner(chatType, referenceId, participants, virtualParticipants)
    }
    fun createGroupChatWithOwner(chatType: ChatType, referenceId: Long, participants: List<Long>?,
                                 virtualParticipants: List<LoginAccountEntity>?, chatOwner: Long? = null): GroupChatEntity {
        val groupChatEntity = GroupChatEntity(chatType = chatType, referenceId = referenceId)
        //Set createdBy/updatedBy manually since these details are not available because of anonymous authentication
        chatOwner?.let {
            groupChatEntity.createdBy = it
            groupChatEntity.updatedBy = it
        }
        val groupChat = groupChatRepository.save(groupChatEntity)

        // Add chat participants
        participants?.distinct()?.forEach {
            groupChatParticipantRepository.save(
                GroupChatParticipantEntity(
                    groupChat = groupChat,
                    user = loginAccountRepository.findById(it).get()
                )
            )
        }

        if(!virtualParticipants.isNullOrEmpty()){
            virtualParticipants?.distinct()?.filter { participants.isNullOrEmpty() || !participants.contains(it.id) }?.forEach {
                groupChatParticipantRepository.save(
                    GroupChatParticipantEntity(
                        groupChat = groupChat,
                        user = it,
                        isReal = false
                    )
                )
            }
        }

        return groupChat
    }

    fun getMessagesCount(chatType: ChatType, referenceId: Long): Long {

        val groupChatEntity = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        return groupChatMessageRepository.countByGroupChat(groupChatEntity)
    }

    @Transactional
    fun updateChatParticipants(
        referenceId: Long,
        chatType: ChatType,
        updatedParticipants: Set<Long>,
        virtualParticipants: List<Long>? = null
    ) {

        val groupChat = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) ?: return
        val participants = groupChatParticipantRepository.findAllByGroupChat(groupChat)
        val newParticipants = mutableListOf<Long>()

        // Add case owners managers as participants by default
        newParticipants.addAll(updatedParticipants)

        val existingParticipants = participants?.map { it.user.id!! }?.toMutableList()
        existingParticipants?.retainAll(newParticipants)

        if (existingParticipants != null) {
            newParticipants.removeAll(existingParticipants)
        }

        // Add new participants to group chat
        newParticipants.forEach {
            groupChatParticipantRepository.save(GroupChatParticipantEntity(
                groupChat = groupChat,
                user = loginAccountRepository.findById(it).get()
            ))
        }
        // make existing participants active that are inactive and present in existingParticipants list and
        // make other participants inactive.
        participants?.forEach {
            it.isActive = existingParticipants?.contains(it.user.id) == true || virtualParticipants?.contains(it.user.id) == true

            //if virtual participant is added as real participant then change isReal flag to true
            it.isReal = updatedParticipants.contains(it.user.id)
        }
        if (participants != null) {
            groupChatParticipantRepository.saveAll(participants.toMutableList())
        }
    }

    /**
     * Sets last seen timestamp to current timestamp for a chat participant
     */
    fun updateLastSeen(chatType: ChatType, referenceId: Long, userId: Long) {
        val groupChat = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) ?: return
        updateLastSeen(groupChat, userId)
    }

    /**
     * @return Unread message count for a chat and for a participant based on last seen
     */
    fun getUnreadMessagesCount(chatType: ChatType, referenceId: Long, userId: Long): Long {

        val groupChat = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) ?: return 0
        val participantEntity = groupChatParticipantRepository.findByGroupChatAndUserId(groupChat, userId)

        return  if(participantEntity.isPresent){
            groupChatMessageRepository.countByGroupChatAndCreatedDateGreaterThan(
                groupChat,
                participantEntity.get().lastSeen ?: TimeUtil.fromInstantMillis(0)
            )
        }
        else{
            0
        }
    }
    fun getUserChatMap(chatType: ChatType, updatedInHours: Long, notificationType: NotificationType): MutableMap<Long, MutableSet<Map<String, Any>>>? {
        val updatedAt = LocalDateTime.now().minusHours(updatedInHours)
        val messages = groupChatMessageRepository.findByCreatedDateGreaterThanAndGroupChatChatType(updatedAt, chatType) ?: return null
        val userChatReferenceMap = mutableMapOf<Long, MutableSet<Map<String, Any>>>()

        messages.groupBy { it.groupChat.id }.forEach {
            val groupChatEntity = it.value[0].groupChat
            //fetch all participants for current group chat
            val participants = groupChatParticipantRepository.findAllByGroupChatAndIsActive(groupChatEntity,
                true)
            //sort chat messages to get last chat message
            val lastGroupChatMessage = it.value.sortedByDescending { it1->it1.createdDate }[0]
            val participantsUserIds = participants?.filter { participant ->
                // Filter corporate users who enabled notifications
                if (participant.user is CorporateUserEntity) {
                    (participant.user as CorporateUserEntity).notificationPreferences.any {
                            notification -> notification.notificationKey == notificationType && notification.value
                    }
                } else {
                    true
                }
            }?.map { participant -> participant.user.id }?.toMutableList()

            //Do not send notification to user who sent last message
            participantsUserIds?.remove(lastGroupChatMessage.createdBy)

            val referenceMap = frameReferenceMap(groupChatEntity)

            participantsUserIds?.forEach { notificationUserId->
                userChatReferenceMap.getOrPut(notificationUserId!!) { mutableSetOf() }.add(referenceMap)
            }
        }
        return userChatReferenceMap
    }
    private fun frameReferenceMap(groupChat: GroupChatEntity): Map<String, Any> {
        return when (groupChat.chatType) {
            ChatType.CASE -> {
                val case = caseRepository.findById(groupChat.referenceId).get()
                mapOf("id" to case.id!!, "title" to (case.category?.subCategoryName?:""))
            }
            ChatType.QUERY -> {
                val query = queryRepository.findById(groupChat.referenceId).get()
                mapOf("id" to query.id!!, "title" to query.heading)
            }
            else -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
        }
    }
    fun delete(referenceId: Long, chatType: ChatType) {
        val chat = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId)
        if (chat != null) {
            groupChatRepository.delete(chat)
        }
    }

    fun getVirtualParticipants(partner: PartnerEntity?): List<LoginAccountEntity> {

        val users = mutableListOf<LoginAccountEntity>()
        loginAccountRepository.findAllByRoleIn(listOf(Role.ROLE_SUPER_ADMIN.toString()))?.let { users.addAll(it) }

        //if chat entity is created by partner then add all partner admins as virtual participants
        if(partner!=null) {
            users.addAll(partner.partnerUsers)
        }
        return users
    }

    fun updateReferenceId(referenceId: Long, chatType: ChatType, newReferenceId: Long) {
        val chat = groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId)
        if (chat!=null) {
            chat.referenceId = newReferenceId
            groupChatRepository.save(chat)
        }
    }
}