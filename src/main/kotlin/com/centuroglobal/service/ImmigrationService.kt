package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.ImmigrationCategoryEntity
import com.centuroglobal.shared.data.enums.Common
import com.centuroglobal.shared.data.enums.ImmigrationCategory
import com.centuroglobal.data.payload.ImmigrationDetails
import com.centuroglobal.data.payload.ImmigrationRequirement
import com.centuroglobal.shared.data.entity.playbook.PlaybookSessionEntity
import com.centuroglobal.shared.repository.ImmigrationCategoryRepository
import com.centuroglobal.shared.repository.ImmigrationRequirementRepository
import com.centuroglobal.shared.repository.playbook.PlaybookSessionRepository
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.*

@Service
class ImmigrationService(
    private val immigrationCategoryRepository: ImmigrationCategoryRepository,
    private val immigrationRequirementRepository: ImmigrationRequirementRepository,
    private val playbookSessionRepository: PlaybookSessionRepository
) {
    fun getVisaDetails(sourceCountry: String, destinationCountry: String): ImmigrationRequirement {
        val data = immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry)
        return ImmigrationRequirement(
            businessVisa = data.businessVisa.equals(Common.REQUIRED.name),
            workVisa = data.workVisa.equals(Common.REQUIRED.name),
            eVisa = data.eVisa.equals(Common.REQUIRED.name),
            validDays = 0
        )
    }

    fun getVisaDetails(sourceCountry: String, destinationCountry: String, visaType: String): ImmigrationDetails {
        val data = immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry)
        val visa = immigrationCategoryRepository.findByVisaCode(visaType)

        val isVisaRequired = when (visa.category) {
            ImmigrationCategory.BUSINESS_VISA.name -> data.businessVisa.equals(Common.REQUIRED.name)
            ImmigrationCategory.WORK_VISA.name -> data.workVisa.equals(Common.REQUIRED.name)
            ImmigrationCategory.E_VISA.name -> data.eVisa.equals(Common.REQUIRED.name)
            else -> false
        }
        val sessionEntity = PlaybookSessionEntity(
            playbook = null,
            questionCount = 0,
            startTime = LocalDateTime.now(),
            sessionId = UUID.randomUUID().toString(),
            type = "VISA"
        )
         playbookSessionRepository.save(sessionEntity)

        return ImmigrationDetails(
            isVisaRequired = isVisaRequired,
            noOfDays = 0,
            sessionId = sessionEntity.sessionId
        )
    }

    fun getVisaCategory(visaType: String?): List<ImmigrationCategoryEntity> {
        return if (visaType != null) {
            immigrationCategoryRepository.findAllByVisaCode(visaType)
        } else {
            immigrationCategoryRepository.findAll()
        }
    }

}
