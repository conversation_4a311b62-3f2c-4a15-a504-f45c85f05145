package com.centuroglobal.service

import com.centuroglobal.data.payload.lead.CreateLeadRequest
import com.centuroglobal.data.payload.lead.LeadResponseRequest
import com.centuroglobal.data.payload.lead.UpdateLeadRequest
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.LeadEntity
import com.centuroglobal.shared.data.entity.LeadResponseEntity
import com.centuroglobal.shared.data.entity.LeadResponseTrailEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.view.CustomerView
import com.centuroglobal.shared.data.enums.LeadNotificationType
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.LeadStatus.*
import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LeadRepository
import com.centuroglobal.shared.repository.LeadResponseRepository
import com.centuroglobal.shared.repository.LeadResponseTrailRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.view.AdminLeadSummaryViewRepository
import com.centuroglobal.shared.repository.view.CustomerViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.StringUtil
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

private val log = KotlinLogging.logger {}
private const val UNEXPECTED_EXCEPTION = "Unexpected Exception"

@Service
class LeadService(
    @Value("\${app.lead.recent-preview-count}")
    private val maxRecentPreview: Int,
    private val leadRepository: LeadRepository,
    private val leadResponseRepository: LeadResponseRepository,
    private val leadResponseTrailRepository: LeadResponseTrailRepository,
    private val adminLeadSummaryViewRepository: AdminLeadSummaryViewRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val expertUserService: ExpertUserService,
    private val countryService: CountryService,
    private val expertiseService: ExpertiseService,
    private val leadNotificationService: LeadNotificationService,
    private val customerViewRepository: CustomerViewRepository,
    private val awsS3Service: AwsS3Service
) {

    @Transactional
    fun create(request: CreateLeadRequest, user: AuthenticatedUser): LeadDetail {
        try {
            val expertises = expertiseService.retrieveExpertiseByIds(request.expertiseId)
            val country = countryService.retrieveByCountryCode(request.countryCode)
            val region = if (request.regionId == null) null else
                countryService.validateRegionWithinCountry(request.regionId, country.code)
            val leadType = leadTypeFromUserType(user.userType)
            val leadTypeId = extractLeadTypeId(user, leadType)

            val lead = leadRepository.save(
                LeadEntity(
                    title = request.title,
                    description = request.description,
                    expertises = expertises,
                    countryCode = country.code,
                    regionId = region?.regionId,
                    status = ACTIVE,
                    leadType = leadType,
                    leadTypeId = leadTypeId,
                    createdBy = user.userId,
                    lastUpdatedBy = user.userId
                )
            )

            leadNotificationService.createNotification(
                lead,
                LeadNotificationType.NEW_LEAD
            )
            return LeadDetail.ModelMapper.fromOwnLead(
                lead, expertises, country, region, expertUserService::retrieveProfileSummary, getCreator(lead.createdBy)
            )

        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error(UNEXPECTED_EXCEPTION, ex)
                    throw ApplicationException(ErrorCode.LEAD_SAVE_FAIL)
                }
            }
        }
    }

    private fun getCreator(createdBy: Long): Creator? {
        val customerView: CustomerView = customerViewRepository.findByIdOrNull(createdBy) ?: return null
        return Creator(
            createdBy,
            customerView.displayName,
            customerView.profilePhotoUrl?.let { awsS3Service.getS3Url(it) },
            customerView.jobTitle,
            customerView.companyName,
            countryService.retrieveByCountryCode(customerView.countryCode).name,
            customerView.countryRegion
        )
    }

    @Transactional
    fun update(leadId: String, request: UpdateLeadRequest, userId: Long): LeadDetail {
        val leadInfo = LeadInfo.Mapper.from(leadId)

        try {
            val lead = leadRepository.findByIdAndLeadTypeId(leadInfo.leadId, leadInfo.leadTypeId)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)

            val expertises = expertiseService.retrieveExpertiseByIds(request.expertiseId)
            val country = countryService.retrieveByCountryCode(request.countryCode)
            val region = countryService.validateRegionWithinCountry(request.regionId, country.code)

            lead.title = request.title
            lead.description = request.description
            if (request.expertiseId.isNotEmpty()) {
                lead.expertises = expertises
            }
            lead.countryCode = country.code
            lead.regionId = region?.regionId
            lead.status = request.leadStatus
            lead.lastUpdatedBy = userId

            val updatedLead = leadRepository.save(lead)

            updateUnReadMessageCountAndDate(lead.responses, userId)
            return LeadDetail.ModelMapper.fromOwnLead(
                updatedLead,
                expertises,
                country,
                region,
                expertUserService::retrieveProfileSummary,
                getCreator(lead.createdBy)
            )

        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error(UNEXPECTED_EXCEPTION, ex)
                    throw ApplicationException(ErrorCode.LEAD_UPDATE_FAIL)
                }
            }
        }
    }

    @Transactional(readOnly = true)
    fun search(
        leadType: String,
        leadTypeId: Long,
        leadSearchFilter: LeadSearchFilter,
        pageRequest: PageRequest
    ): PagedResult<LeadSummary> {
        if (leadSearchFilter.countryCode != null) countryService.retrieveByCountryCode(leadSearchFilter.countryCode!!)
        if (leadSearchFilter.expertiseId != null) expertiseService.retrieveExpertiseById(leadSearchFilter.expertiseId!!)
        if (leadType == "BACKOFFICE") throw ApplicationException(ErrorCode.BAD_REQUEST)

        val leadsPageResult =
            leadRepository.searchByCriteria(
                leadSearchFilter,
                LeadType.valueOf(leadType),
                leadTypeId,
                arrayOf(ACTIVE, RESOLVED, UNRESOLVED),
                pageRequest
            )

        val mappedLeads = leadsPageResult.map {
            LeadSummary.ModelMapper.from(
                it,
                it.expertises.map { it1 -> it1.name },
                countryService.retrieveByCountryCode(it.countryCode).name,
                regionCheck(it.regionId, it.countryCode)
            )
        }.toList()

        return PagedResult.ModelMapper.from(leadsPageResult, mappedLeads)
    }

    @Transactional(readOnly = true)
    fun retrieve(leadId: String, userId: Long): LeadDetail {
        val leadInfo = LeadInfo.Mapper.from(leadId)
        val lead = leadRepository.findByIdAndLeadTypeId(leadInfo.leadId, leadInfo.leadTypeId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val country = countryService.retrieveByCountryCode(lead.countryCode)
        val region = countryService.validateRegionWithinCountry(lead.regionId, country.code)

        updateUnReadMessageCountAndDate(lead.responses, userId)
        return LeadDetail.ModelMapper.fromOwnLead(
            lead,
            lead.expertises,
            country,
            region,
            expertUserService::retrieveProfileSummary,
            getCreator(lead.createdBy)
        )
    }

    @Transactional(readOnly = true)
    fun searchLeadsForExpert(
        leadSearchFilter: LeadSearchFilter,
        expertUserId: Long,
        pageRequest: PageRequest
    ): PagedResult<ExpertLeadSummary>? {
        if (leadSearchFilter.countryCode != null) countryService.retrieveByCountryCode(leadSearchFilter.countryCode!!)
        if (leadSearchFilter.expertiseId != null) expertiseService.retrieveExpertiseById(leadSearchFilter.expertiseId!!)

        val leadsPageResult =
            leadRepository.searchByCriteriaForExpert(leadSearchFilter, expertUserId, arrayOf(ACTIVE), pageRequest)

        val mappedLeads = leadsPageResult.map {
            ExpertLeadSummary.ModelMapper.from(
                it,
                it.expertises.map { it -> it.name },
                countryService.retrieveByCountryCode(it.countryCode).name,
                regionCheck(it.regionId, it.countryCode),
                expertUserId
            )
        }.toList()

        return PagedResult.ModelMapper.from(leadsPageResult, mappedLeads)
    }

    @Transactional(readOnly = true)
    fun retrieveLeadDetailForExpert(expertId: Long, leadId: String): LeadDetail {
        val leadInfo = LeadInfo.Mapper.from(leadId)
        val lead = leadRepository.findByIdOrNull(leadInfo.leadId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val country = countryService.retrieveByCountryCode(lead.countryCode)
        val region = countryService.validateRegionWithinCountry(lead.regionId, country.code)

        updateUnReadMessageCountAndDate(lead.responses, expertId)
        return LeadDetail.ModelMapper.fromLeadForExpert(
            lead,
            lead.expertises,
            country,
            region,
            expertId,
            getCreator(lead.createdBy)
        )
    }

    private fun updateUnReadMessageCountAndDate(responses: List<LeadResponseEntity>, userId: Long) {
        responses.forEach {
            it.id?.let { it1 ->
                it.unReadMessageCount = leadResponseTrailRepository.countAllByLeadResponse_IdAndRootUserIdNotAndIsSeen(
                    it1,
                    userId,
                    false
                )
                if (it.unReadMessageCount > 0) {
                    it.lastMessageDateTime = TimeUtil.toEpochMillis(
                        leadResponseTrailRepository.findTopByLeadResponse_IdAndRootUserIdNotAndIsSeenOrderByCreatedDate(
                            it1,
                            userId,
                            false
                        ).createdDate
                    )
                } else it.lastMessageDateTime = System.currentTimeMillis()
            }
        }
    }

    @Transactional
    fun delete(leadId: String, userId: Long): String {
        val leadInfo = LeadInfo.Mapper.from(leadId)

        try {
            val lead = leadRepository.findByIdAndLeadTypeId(leadInfo.leadId, leadInfo.leadTypeId)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)

            lead.status = DELETED
            lead.lastUpdatedBy = userId

            leadRepository.save(lead)
            return AppConstant.SUCCESS_RESPONSE_STRING

        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error(UNEXPECTED_EXCEPTION, ex)
                    throw ApplicationException(ErrorCode.LEAD_DELETE_FAIL)
                }
            }
        }
    }

    @Transactional
    fun respond(leadId: String, userId: Long, responseRequest: LeadResponseRequest): String {
        val leadInfo = LeadInfo.Mapper.from(leadId)

        try {
            val lead = leadRepository.findByIdOrNull(leadInfo.leadId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

            val leadResponseEntity = LeadResponseEntity(
                lead = lead,
                expertId = userId,
                description = responseRequest.description,
                lastUpdatedBy = userId
            )
            leadResponseEntity.leadResponseTrails = listOf(
                LeadResponseTrailEntity(
                    leadResponse = leadResponseEntity,
                    message = responseRequest.description,
                    respondedBy = LeadType.EXPERT,
                    rootUserId = userId,
                    assetUrl = null,
                    lastUpdatedBy = userId
                )
            )
            lead.addResponse(
                leadResponseEntity
            )
            leadRepository.save(lead)
            leadNotificationService.createNotification(lead, LeadNotificationType.RESPOND)

            return AppConstant.SUCCESS_RESPONSE_STRING

        } catch (ex: Exception) {
            when (ex) {
                is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.LEAD_RESPONSE_MULTIPLE)
                is ApplicationException -> throw ex
                else -> {
                    log.error(UNEXPECTED_EXCEPTION, ex)
                    throw ApplicationException(ErrorCode.LEAD_RESPONSE_FAIL)
                }
            }
        }
    }

    @Transactional
    fun updateResponse(leadId: String, userId: Long, responseRequest: LeadResponseRequest): String {
        val leadInfo = LeadInfo.Mapper.from(leadId)

        try {
            val lead = leadRepository.findByIdOrNull(leadInfo.leadId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
            lead.updateResponse(userId, responseRequest.description, userId)

            return AppConstant.SUCCESS_RESPONSE_STRING

        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> {
                    log.error(UNEXPECTED_EXCEPTION, ex)
                    throw ApplicationException(ErrorCode.LEAD_RESPONSE_UPDATE_FAIL)
                }
            }
        }
    }

    @Transactional(readOnly = true)
    fun searchLeadsForAdmin(
        leadSearchFilter: LeadSearchFilter,
        pageRequest: PageRequest
    ): PagedResult<AdminLeadSummary>? {
        if (leadSearchFilter.countryCode != null) countryService.retrieveByCountryCode(leadSearchFilter.countryCode!!)
        if (leadSearchFilter.expertiseId != null) expertiseService.retrieveExpertiseById(leadSearchFilter.expertiseId!!)
        val statusFilter =
            if (leadSearchFilter.status != null) arrayOf(leadSearchFilter.status) else arrayOf(
                ACTIVE,
                RESOLVED,
                UNRESOLVED
            )

        val leadsPageResult =
            adminLeadSummaryViewRepository.searchByCriteria(leadSearchFilter,
                statusFilter as Array<LeadStatus>, pageRequest)

        val mappedLeads = leadsPageResult.map {
            AdminLeadSummary.ModelMapper.from(
                it,
                countryService.retrieveByCountryCode(it.countryCode).name,
                if (it.regionId == null) null else countryService.validateRegionWithinCountry(
                    it.regionId, it.countryCode
                )?.name
            )
        }.toList()

        return PagedResult.ModelMapper.from(leadsPageResult, mappedLeads)
    }

    fun recent(): List<RecentLeadSummary>? {
        val leadsPageResult: Page<LeadEntity> =
            leadRepository.searchByCriteria(
                filter = LeadSearchFilter.Builder.build(LeadsSearchCriteria()),
                status = arrayOf(ACTIVE),
                pageable = PageRequest.of(0, maxRecentPreview, SearchConstant.SORT_ORDER("createdDate", "DESC"))
            )

        return leadsPageResult.map {
            RecentLeadSummary.ModelMapper.from(
                it,
                it.expertises.map { it -> it.name },
                countryService.retrieveByCountryCode(it.countryCode).name,
                regionCheck(it.regionId, it.countryCode)
            )
        }.toList()
    }

    fun recentForCorporate(userId: Long): List<RecentLeadSummary>? {
        val leadsPageResult: Page<LeadEntity> =
            leadRepository.findAllByCreatedBy(
                userId,
                PageRequest.of(0, maxRecentPreview, SearchConstant.SORT_ORDER("createdDate", "DESC"))
            )

        return leadsPageResult.map {
            RecentLeadSummary.ModelMapper.from(
                it,
                it.expertises.map { it1 -> it1.name },
                countryService.retrieveByCountryCode(it.countryCode).name,
                regionCheck(it.regionId, it.countryCode)
            )
        }.toList()
    }

    private val leadTypeFromUserType: (String) -> LeadType = {
        when (UserType.valueOf(it)) {
            UserType.CORPORATE -> LeadType.CORPORATE
            UserType.EXPERT -> LeadType.EXPERT
            else -> throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
        }
    }

    private val extractLeadTypeId: (AuthenticatedUser, LeadType) -> Long = { user, leadType ->
        when (leadType) {
            LeadType.CORPORATE -> user.companyId!!
            LeadType.EXPERT -> user.userId
            LeadType.BACKOFFICE -> user.userId
        }
    }

    private val regionCheck: (Int?, String) -> String? = { regionId, countryCode ->
        if (regionId == null) null else countryService.validateRegionWithinCountry(
            regionId, countryCode
        )?.name
    }

    @Transactional
    fun getLeadResponseTrail(
        leadId: String,
        expertId: Long,
        userId: Long,
        lastMessageDateTime: Long?,
        seenBy: LeadType
    ): List<List<LeadResponseTrail>> {
        val leadInfo = LeadInfo.Mapper.from(leadId)
        val leadResponseEntity: LeadResponseEntity =
            leadResponseRepository.findByLead_IdAndExpertId(leadInfo.leadId, expertId)
                ?: return mutableListOf()
        leadResponseEntity.leadResponseTrails.forEach {
            if (seenBy != LeadType.BACKOFFICE && it.rootUserId != userId) {
                it.isSeen = true
                leadResponseTrailRepository.save(it)
            }
        }
        val leadResponseTrails: List<LeadResponseTrail> =
            leadResponseEntity.leadResponseTrails.sortedBy { it.createdDate }.map {
                LeadResponseTrail(
                    id = it.id,
                    rootUserId = it.rootUserId,
                    message = it.message,
                    dateTime = TimeUtil.toEpochMillis(it.createdDate),
                    asset = it.assetUrl,
                    isAsset = it.assetUrl != null,
                    seen = it.isSeen,
                    respondedBy = it.respondedBy
                )
            }
        val map: TreeMap<String, MutableList<LeadResponseTrail>> = TreeMap()
        leadResponseTrails.filter {
            if (lastMessageDateTime != null) {
                it.dateTime > lastMessageDateTime
            } else true
        }.forEach {
            if (it.respondedBy == LeadType.CORPORATE) {
                val loginAccount: LoginAccountEntity = loginAccountRepository.getById(it.rootUserId)
                it.shortCorpName = StringUtil.getShortName(loginAccount.firstName, loginAccount.lastName)
            }

            val date: String = TimeUtil.fromInstant(it.dateTime).year.toString() +
                    TimeUtil.fromInstant(it.dateTime).month.toString() + TimeUtil.fromInstant(it.dateTime).dayOfMonth.toString()
            log.debug("date of the message {} and id {}", date, it.id)
            if (map.containsKey(date)) {
                map[date]?.add(it)
            } else {
                map[date] = mutableListOf(it)
            }
        }
        return map.values.map { it }
    }

    @Transactional
    fun saveMessageTrail(
        userId: Long,
        leadId: String,
        respondedBy: LeadType,
        leadMessageTrailRequest: LeadMessageTrailRequest
    ):
            List<List<LeadResponseTrail>> {
        val leadInfo = LeadInfo.Mapper.from(leadId)
        val leadResponseEntity: LeadResponseEntity? =
            leadResponseRepository.findByLead_IdAndExpertId(leadInfo.leadId, leadMessageTrailRequest.expertId)
        if (leadResponseEntity == null) {
            leadMessageTrailRequest.message?.let { LeadResponseRequest(it) }?.let { respond(leadId, userId, it) }
            return getLeadResponseTrail(
                leadId,
                leadMessageTrailRequest.expertId,
                userId,
                leadMessageTrailRequest.lastMessageDateTime,
                respondedBy
            )
        }
        val leadResponseTrailEntity: LeadResponseTrailEntity
        if (leadMessageTrailRequest.id != null) {
            leadResponseTrailEntity =
                leadMessageTrailRequest.id?.let { leadResponseTrailRepository.findByIdOrNull(it) } ?: throw ApplicationException(
                    ErrorCode.LEAD_INVALID_ID
                )
            leadResponseTrailEntity.message = leadMessageTrailRequest.message
        } else {
            leadResponseTrailEntity = LeadResponseTrailEntity(
                message = leadMessageTrailRequest.message,
                respondedBy = respondedBy,
                rootUserId = userId,
                lastUpdatedBy = userId,
                leadResponse = leadResponseEntity
            )
        }
        leadResponseTrailRepository.save(leadResponseTrailEntity)
        return getLeadResponseTrail(
            leadId,
            leadMessageTrailRequest.expertId,
            userId,
            leadMessageTrailRequest.lastMessageDateTime,
            respondedBy
        )
    }
}

