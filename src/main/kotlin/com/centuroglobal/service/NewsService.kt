package com.centuroglobal.service

import com.centuroglobal.client.NewsApiClient
import com.centuroglobal.shared.data.entity.NewsCategoryEntity
import com.centuroglobal.shared.data.entity.NewsEntity
import com.centuroglobal.shared.repository.NewsCategoryRepository
import com.centuroglobal.shared.repository.NewsRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit


@Service
class NewsService(
    private val newsCategoryRepository: NewsCategoryRepository,
    private val newsRepository: NewsRepository,
    @Value("\${app.news.expiry.minutes}")
    private val newsExpiryMinutes: Long,
    @Value("\${app.news.apiToken}")
    private val apiToken: String
) {

    @Autowired
    lateinit var newsApiClient: NewsApiClient

    @Transactional
    fun getNewsCategory(): List<NewsCategoryEntity> {
        return newsCategoryRepository.findAll()
    }

    @Transactional
    fun getNews(industryCode: String): List<NewsEntity> {
        val newsExpiryDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS).minusMinutes(newsExpiryMinutes)
        var newsList = newsRepository.findTop5ByIndustryCodeAndCreatedDateGreaterThanOrderByPublishedDateDesc(
            industryCode,
            newsExpiryDate
        )

        if (newsList.isEmpty()) {
            val newCatData = newsCategoryRepository.findAllByIndustryCode(industryCode)
            newsRepository.deleteByIndustryCode(industryCode)
            val newsData = newsApiClient.findAllNews(apiToken, newCatData.searchQuery, 5)
            newsList = newsData.data.map { it ->
                val cityData = NewsEntity(
                    url = it.url,
                    title = it.title,
                    locale = it.locale,
                    source = it.source,
                    language = it.language,
                    imageUrl = it.image_url,
                    description = it.description,
                    publishedDate = it.published_at,
                    relevanceScore = it.relevance_score,
                    industryCode = newCatData.industryCode,
                    category = if (newCatData.category != null) newCatData.category else it.categories?.get(0)
                )
                newsRepository.save(cityData)
            }.toList()
        }
        return newsList
    }
}