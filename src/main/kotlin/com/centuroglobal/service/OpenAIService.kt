package com.centuroglobal.service

import com.centuroglobal.shared.client.OpenAIApiClient
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.payload.openai.*
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.AskAIRequest
import com.centuroglobal.shared.exception.ApplicationException
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.concurrent.TimeUnit

private val log = KotlinLogging.logger {}

@Service
class OpenAIService(
    @Value("\${app.openapi.api-key}")
    private val apiKey: String,

    @Value("\${app.openapi.prompt-message}")
    private val promptMessageText: String,

    @Value("\${app.openapi.assistant-id}")
    private val assistantId: String,

    @Value("\${app.openapi.file-assistant-id}")
    private val fileAssistantId: String,

    @Value("\${app.openapi.message-read-timeout}")
    private val messageReadTimeout: Long,

    private val adminMasterDataService: AdminMasterDataService,

    private val openAIApiClient: OpenAIApiClient,

    private val pythonApiClient: PythonApiClient

) {

    fun createThread(threadId: String?=null): ThreadResponse {
        val token = getBearerToken()
        return if (threadId != null) {
            openAIApiClient.getThread(token, threadId)
        } else {
            openAIApiClient.createThread(token)
        }
    }

    private fun getBearerToken(): String {
        return "Bearer $apiKey"
    }

    fun getAnswerFromThread(threadId: String, message: String): MessageData{
        val messageRequest = CreateMessageRequest("user", message)
        return getAnswerFromThread(threadId, messageRequest, assistantId)
    }

    fun getAnswerFromThreadWithAttachments(threadId: String, message: String, fileIds: List<String>): MessageData{
        val attachments = fileIds.map { Attachment(it, listOf(Tool("code_interpreter"))) }
        val messageRequest = CreateMessageRequest("user", message, attachments)
        return getAnswerFromThread(threadId, messageRequest, fileAssistantId, "gpt-4-turbo")
    }


    private fun getAnswerFromThread(threadId: String, message: CreateMessageRequest, assistantId: String, model: String="gpt-4o"): MessageData {

        val token = getBearerToken()

        openAIApiClient.createMessage(token, threadId, message)

        val run = openAIApiClient.createRun(token, threadId, CreateRunRequest(assistantId, model))

        val delayInMillis: Long = 1500
        var readAttempts = TimeUnit.SECONDS.toMillis(messageReadTimeout) / delayInMillis
        do {
            Thread.sleep(delayInMillis)
            val retrievedRun = openAIApiClient.getRun(token, run.threadId, run.id)
            readAttempts -= 1
            if (readAttempts < 0) {
                throw ApplicationException(ErrorCode.MESSAGE_READ_TIMEOUT)
            }
        } while (retrievedRun.status != "completed")

        return openAIApiClient.getMessages(token, run.threadId, 1).data.first()
    }

    fun readMessageText(messages: MessageData): String {
        val answer = messages.content.first().text
        var answerText = answer.value

        //replace file annotations from answer
        answer.annotations.forEach {
            answerText = answerText.replace(it.text, "")
        }
        return answerText
    }

    fun uploadFile(file: MultipartFile, purpose: String): ThreadResponse {
        return openAIApiClient.uploadFile(getBearerToken(), file, purpose)
    }

    fun createThreadWithContext(context: String): ThreadResponse {

        val thread = createThread()
        val token = getBearerToken()

        val message = CreateMessageRequest("assistant", context)

        openAIApiClient.createMessage(token, thread.id, message)
        return thread

    }

    fun getAnswerFromLangChain(messageRequest: AskAIRequest): LangchainResponse {
        return pythonApiClient.askAI(messageRequest)
    }

}