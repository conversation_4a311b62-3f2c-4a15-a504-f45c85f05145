package com.centuroglobal.service

import com.centuroglobal.data.pojo.PartnerCreateRequest
import com.centuroglobal.data.pojo.PartnerUserCreateRequest
import com.centuroglobal.data.pojo.PartnerUserDetails
import com.centuroglobal.data.pojo.UpdatePartnerRequest
import com.centuroglobal.facade.AdminClientFacade
import com.centuroglobal.shared.data.AppConstant.STATIC_PUBLIC_BUCKET
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.account.CompanyRequest
import com.centuroglobal.shared.data.payload.account.CreatePrimaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.EditUtil
import com.centuroglobal.shared.util.PartnerEmailUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context
import java.util.concurrent.CompletableFuture

private val log = KotlinLogging.logger {}
private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

private val signUpTokenUrl: (webUrl: String, code: String) -> String =
    { webUrl, code -> "${webUrl}/auth/email-confirmation?code=${code}" }

private const val PARTNER_CORPORATE_INVITATION_EMAIL_TEMPLATE_NAME = "email/partner_invite_corporate_upgrade"
private const val PARTNER_EXPERT_INVITATION_EMAIL_TEMPLATE_NAME = "email/partner_invite_expert_upgrade"
private const val UNEXPECTED_EXCEPTION = "Unexpected Exception"

@Service
class PartnerService(
    @Value("\${app.aws.s3.user-profile-folder}")
    private val userProfileFolder: String,
    @Value("\${app.aws.s3.partner-onboarding-doc-folder}")
    private val onboardingDocsFolder: String,
    private val partnerRepository:PartnerRepository,
    private val corporateUserRepository:CorporateUserRepository,
    private val corporateRepository:CorporateRepository,
    private val expertUserRepository:ExpertUserRepository,
    private val expertCompanyProfileRepository:ExpertCompanyProfileRepository,
    private val bandsRepository:BandsRepository,
    private val tokenVerificationService: TokenVerificationService,
    private val microAccessMasterRepository:MicroAccessMasterRepository,
    private val awsS3Service: AwsS3Service,
    private val loginAccountRepository: LoginAccountRepository,
    private val mailSendingService: MailSendingService,
    private val userRoleRepository: UserRoleRepository,
    private val expertUserService: ExpertUserService,
    private val validationTokenRepository: ValidationTokenRepository,
    private val adminClientFacade: AdminClientFacade,
    private val userProfileUtil: UserProfileUtil,
    private val clientDocRepository: ClientDocRepository,
    private val visibilityMasterRepository: VisibilityMasterRepository,
    private val partnerEmailUtil: PartnerEmailUtil,
    private val corporateService: CorporateService

    ) {
    @Transactional
    fun createPartner(request: PartnerCreateRequest, authenticatedUser: AuthenticatedUser): Long {

        val savedBand = createPartnerBand(request.features)
        log.info("partner band created and stored")

        val companyLogo = getProfileUrl(request.companyLogo)
        val accesses = request.corporateFeatures.joinToString(",")
        log.info("saving partner info")
        val partnerEntity = partnerRepository.save(
            PartnerEntity(
                name = request.name!!,
                createdFrom = request.createFrom,
                contractFromDate = TimeUtil.fromInstant(request.startDate/ 1000),
                contractToDate = TimeUtil.fromInstant(request.endDate/ 1000),
                casesManaged = request.casesManagedBy,
                queriesManaged = request.queryManagedBy,
                status = if(request.createFrom == PartnerType.NEW) CorporateStatus.PENDING_VERIFICATION else CorporateStatus.ACTIVE,
                companyLogo = companyLogo,
                themePrimaryColor = request.primaryColor,
                themeSecondaryColor = request.secondaryColor,
                corporateAccess = accesses,
                country = request.country,
                band = savedBand,
                referenceId = request.createReferenceId
            )
        )
        log.info("partner created initially")

        //upload company logo
        companyLogo?.let { awsS3Service.uploadFromTmp(request.companyLogo!!, it) }

        //make copy of logo in static bucket
        copyLogoToStaticBucket(request.companyLogo, partnerEntity.id!!)

        val partnerUserDetails = request.rootUserDetails

        val partnerRootUserId = createPartnerRootUser(
            authenticatedUser.userId,
            request.createFrom, request.createReferenceId,
            partnerUserDetails, partnerEntity, savedBand,
            request.aiMessageCount
        )
        changeValidationType(partnerRootUserId, ValidationType.PARTNER_SIGNUP, request.createFrom)

        partnerEntity.rootUserId = partnerRootUserId

        //create onboard docs
        val docPath = "$onboardingDocsFolder/${partnerEntity.id}"
        val docEntities = request.onboardingDocs?.map {
            ClientDocEntity(
                docName = it.docName,
                docKey = "$docPath/${it.s3Key}",
                fileType = it.type,
                fileName = it.fileName,
                fileSize = it.size,
                docType = ClientDocType.ON_BOARD,
                referenceType = UserDocType.PARTNER,
                referenceId = partnerEntity.id!!
            )
        }?.toMutableList()

        request.onboardingDocs?.forEach {
            awsS3Service.uploadFromTmp(it.s3Key, "$docPath/${it.s3Key}")
        }

        docEntities?.let { clientDocRepository.saveAll(it) }

        return partnerRepository.save(partnerEntity).id!!
    }

    private fun createPartnerBand(features: List<String>?): BandsEntity {
        val bandEntity = BandsEntity(
            name = "Partner Admin",
            description = "",
            status = BandStatus.ACTIVE
        )

       val bandDetails = getAllAccessesByFeatureKeys(features, bandEntity)

        bandEntity.bandAccesses = bandDetails
        return bandsRepository.save(bandEntity)
    }

    private fun getAllAccessesByFeatureKeys(features: List<String>?, bandEntity: BandsEntity): MutableList<BandDetailsEntity> {

        val accesses = mutableListOf<MicroAccessMasterEntity>()
        val visibilities = mutableListOf<VisibilityMasterEntity>()
        features?.forEach {
            accesses.addAll(microAccessMasterRepository.findAllByFeatureKey(it))
        }
        features?.forEach {
            visibilities.addAll(visibilityMasterRepository.findAllByFeatureKey(it))
        }
        val bandDetails = accesses.map {
            BandDetailsEntity(
                band = bandEntity,
                access = it
            )
        }.toMutableList()

        bandDetails.addAll(visibilities.map {
            BandDetailsEntity(
                band = bandEntity,
                visibility = it
            )
        })

        return bandDetails

    }


    @Transactional(readOnly = true)
    fun listPartner(
        filter: PartnerSearchFilter, pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<PartnerList?> {

        val userList = partnerRepository.searchByCriteria(filter, pageRequest)

        val partnerDetails = userList.map {
            PartnerList.ModelMapper.from(it)
        }
        return PagedResult.ModelMapper.from(userList, partnerDetails.toList())
    }

    @Transactional(readOnly = true)
    fun listPartnerUser(filter: PartnerUserSearchFilter, partnerId: Long, pageRequest: PageRequest): PagedResult<PartnerUserList> {
        val partnerUserList = loginAccountRepository.searchByCriteria(partnerId, filter, pageRequest)

        val partnerUserListDetails = partnerUserList.map {
            PartnerUserList.ModelMapper.from(it)
        }
        return PagedResult.ModelMapper.from(partnerUserList, partnerUserListDetails.toList())
    }

    @Transactional(readOnly = true)
    fun listAllPartnerUser(filter: PartnerUserSearchFilter, partnerId: Long?, pageRequest: PageRequest): PagedResult<PartnerUserList> {
        val partnerUserList = loginAccountRepository.searchPartnerUsersByCriteria(filter, partnerId, pageRequest)

        val partnerUserListDetails = partnerUserList.map {
            PartnerUserList.ModelMapper.from(it)
        }
        return PagedResult.ModelMapper.from(partnerUserList, partnerUserListDetails.toList())
    }


    @Transactional
    fun createPartnerRootUser(
        authenticatedUserId: Long,
        createdFrom: PartnerType,
        createReferenceId: Long?,
        partnerUserDetails: PartnerUserDetails?,
        partnerEntity: PartnerEntity,
        bandsEntity: BandsEntity,
        aiMessageCount: Long
    ): Long {

        when (createdFrom) {
            PartnerType.NEW -> {

                //creation of expert company and user
                try {
                    val request = CreatePrimaryExpertUserRequest(
                        email = partnerUserDetails!!.email,
                        countryCode = partnerUserDetails.country,
                        jobTitle = partnerUserDetails.jobTitle,
                        firstName = partnerUserDetails.firstName,
                        lastName = partnerUserDetails.lastName,
                        profileImage = partnerUserDetails.profilePicS3Key,
                        aiMessageCount = aiMessageCount,
                        companyProfile = CompanyRequest(
                            aboutBusiness = "Expert created automatically by Centuro System",
                            companyNumber = partnerUserDetails.contactNumber!!,
                            companyAddress = partnerUserDetails.country,
                            name = partnerEntity.name,
                            services = null.toString(),
                            effectiveDate = TimeUtil.toEpochMillis(partnerEntity.contractFromDate),
                            effectiveEndDate = TimeUtil.toEpochMillis(partnerEntity.contractToDate),
                            contractAcceptedDate = TimeUtil.toEpochMillis(partnerEntity.contractFromDate),
                            size = null,
                            sizeName = null,
                            summary = null,
                            territory = partnerUserDetails.country,
                            logoFullUrl = partnerUserDetails.profilePicS3Key?:"",
                            feesCurrency = "\$",
                            feesAmount = "0",
                            specialTerms = null.toString(),
                            membershipStatus = "EXCLUSIVE",
                            contract = "Not selected"
                        )
                    )

                    val primaryExpertId = expertUserService.createPrimaryExpertUser(authenticatedUserId, request)

                    val primaryExpert =
                        expertUserRepository.findById(primaryExpertId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

                    createPartnerExpert(primaryExpert, partnerEntity, bandsEntity)
                    partnerEntity.createdFrom = PartnerType.EXPERT
                    partnerEntity.referenceId = primaryExpert.companyProfile!!.id
                    partnerEntity.associatedCompanies = mutableListOf(primaryExpert.companyProfile!!)
                    //mail code

                    return primaryExpertId

                } catch (ex: Exception) {
                    when (ex) {
                        is DataIntegrityViolationException -> throw ApplicationException(ErrorCode.EMAIL_ALREADY_EXISTS)
                        is ApplicationException -> throw ex
                        else -> {
                            log.error(UNEXPECTED_EXCEPTION, ex)
                            throw ApplicationException(ErrorCode.EXPERT_USER_CREATE_FAIL)
                        }
                    }
                }

            }

            PartnerType.CORPORATE -> {

                val corporateEntity = corporateRepository.findById(createReferenceId!!)
                    .orElseThrow { ApplicationException(ErrorCode.CORPORATE_NOT_FOUND) }
                if (corporateEntity.status!=CorporateStatus.ACTIVE) {
                    log.error("Corporate is not Active to create partner.")
                    throw ApplicationException(ErrorCode.CORPORATE_NOT_ACTIVE)
                }
                val rootUserId = corporateEntity.rootUserId
                val corporateUserEntity = corporateEntity.users.filter { it.id == rootUserId }[0]

                try {

                    userRoleRepository.save(UserRoleEntity(
                        user = corporateUserEntity,
                        role= Role.ROLE_PARTNER
                    ))
                    corporateUserEntity.partnerJobTitle = corporateUserEntity.jobTitle
                    corporateUserEntity.partner = partnerEntity
                    corporateUserEntity.partnerBand = bandsEntity
                    corporateUserRepository.save(corporateUserEntity)

                    sendMail(corporateUserEntity.firstName,
                        corporateUserEntity.email,
                        PARTNER_CORPORATE_INVITATION_EMAIL_TEMPLATE_NAME,
                        "Partner Created Successfully from Corporate"
                    )
                }
                catch (ex: Exception) {
                    if (ex is DataIntegrityViolationException){
                        log.error("User Email Id already exists", ex)
                        throw ApplicationException(ErrorCode.USER_EMAIL_ALREADY_EXISTS)
                    }
                    else {
                        log.error(ex.message, ex)
                        throw ex
                    }
                }
                return corporateUserEntity.id!!
            }

            PartnerType.EXPERT -> {

                val expertCompany = expertCompanyProfileRepository.findById(createReferenceId!!)
                    .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
                val primaryExperts =
                    expertUserRepository.findAllByCompanyProfileAndExpertType(expertCompany, ExpertType.PRIMARY.name)

                if (primaryExperts.isEmpty()) {
                    log.error("No primary experts found for expert company: $createReferenceId")
                    throw ApplicationException(ErrorCode.NOT_FOUND)
                }
                if (primaryExperts[0].status!=AccountStatus.ACTIVE) {
                    log.error("Primary expert is not Active to create partner.")
                    throw ApplicationException(ErrorCode.EXPERT_NOT_ACTIVE)
                }

                createPartnerExpert(primaryExperts[0], partnerEntity, bandsEntity)
                partnerEntity.associatedCompanies = mutableListOf(expertCompany)

                try {
                    val partnerUser = loginAccountRepository.findById(primaryExperts[0].id!!).get()

                    sendMail(
                        partnerUser.firstName,
                        partnerUser.email,
                        PARTNER_EXPERT_INVITATION_EMAIL_TEMPLATE_NAME,
                        "Partner Created Successfully from Expert"
                    )
                }
                catch (ex: Exception) {
                    if (ex is DataIntegrityViolationException){
                        log.error("User Email Id already exists", ex)
                        throw ApplicationException(ErrorCode.USER_EMAIL_ALREADY_EXISTS)
                    }
                    else {
                        log.error(ex.message, ex)
                        throw ex
                    }
                }
                return primaryExperts[0].id!!
            }
        }
    }

    private fun changeValidationType(id: Long, type: ValidationType, createFrom: PartnerType) {
        val userType =
            when (createFrom) {
                PartnerType.EXPERT, PartnerType.NEW -> ValidationType.INVITE_EXPERT
                PartnerType.CORPORATE -> ValidationType.CORPORATE_SIGNUP
            }
        val validationTokenEntity = validationTokenRepository.findByUserIdAndType(id, userType)

        validationTokenEntity?.let {
            it.type = type
            validationTokenRepository.save(it)
        }
    }

    fun createPartnerExpert(primaryExpert:  ExpertUserEntity, partnerEntity: PartnerEntity, bandsEntity: BandsEntity){
        try {
            userRoleRepository.save( UserRoleEntity(
                user = primaryExpert,
                role= Role.ROLE_PARTNER
            ))

            primaryExpert.partnerJobTitle =  primaryExpert.jobTitle
            primaryExpert.partner = partnerEntity
            primaryExpert.partnerBand = bandsEntity
            expertUserRepository.save(primaryExpert)
        }
        catch (ex: Exception) {
            log.error(ex.message, ex)
            throw ex
        }

    }


    @Async
    private fun sendMail(firstName: String, emailTo: String, templateName: String, subject: String, emailToBcc: List<String> = emptyList()) {
        val ctx = Context()
        ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        ctx.setVariable("FIRST_NAME", firstName)

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = templateName,
                subject = subject,
                context = ctx,
                recipient = emailTo,
                bccRecipients = emailToBcc
            )
        )
    }

    private fun createPartnerAdminUser(request: PartnerUserDetails, partnerEntity: PartnerEntity): LoginAccountEntity {

        val profilePhotoUrl = getProfileUrl(request.profilePicS3Key)

        val partnerUserEntity = if (partnerEntity.createdFrom == PartnerType.CORPORATE) CorporateUserEntity()
        else ExpertUserEntity(contactEmail = "", contactNumber = "", contactWebsite = "", displayName = "", expertType = null, jobTitle = "")

        partnerUserEntity.firstName = request.firstName
        partnerUserEntity.lastName = request.lastName
        partnerUserEntity.partnerJobTitle = request.jobTitle
        partnerUserEntity.partnerBand = partnerEntity.band
        partnerUserEntity.countryCode = request.country
        partnerUserEntity.email = request.email
        partnerUserEntity.contactNo = request.contactNumber
        partnerUserEntity.partner = partnerEntity
        partnerUserEntity.profilePhotoUrl = profilePhotoUrl

        partnerUserEntity.dialCode = request.dialCode
        partnerUserEntity.status = AccountStatus.PENDING_VERIFICATION

        val savedUser = try {
            when (partnerUserEntity) {
                is CorporateUserEntity -> {
                    partnerUserEntity.role = Role.ROLE_CORPORATE
                    partnerUserEntity.jobTitle = request.jobTitle
                    partnerUserEntity.corporate = corporateRepository.findById(partnerEntity.referenceId!!).get()
                    partnerUserEntity.band = partnerEntity.band
                    corporateUserRepository.save(partnerUserEntity)
                }

                is ExpertUserEntity -> {
                    partnerUserEntity.role = Role.ROLE_EXPERT
                    partnerUserEntity.expertType = ExpertType.SECONDARY.name
                    partnerUserEntity.contactEmail = request.email
                    partnerUserEntity.jobTitle = request.jobTitle
                    partnerUserEntity.countryCode = request.country
                    partnerUserEntity.displayName = "${request.firstName} ${request.lastName}"
                    partnerUserEntity.companyProfile = expertCompanyProfileRepository.findById(partnerEntity.referenceId!!).get()
                    expertUserRepository.save(partnerUserEntity)
                }

                else -> {
                    null
                }
            }
        }

        catch (ex: Exception) {
            if (ex is DataIntegrityViolationException){
                log.error("User Email Id already exists", ex)
                throw ApplicationException(ErrorCode.USER_EMAIL_ALREADY_EXISTS)
            }
            else {
                log.error(ex.message, ex)
                throw ex
            }
        }

        val userRoles = listOf(
            UserRoleEntity(
                user = savedUser as LoginAccountEntity,
                role= savedUser.role
            ),
            UserRoleEntity(
                user = savedUser,
                role= Role.ROLE_PARTNER
            )
        )
        userRoleRepository.saveAll(userRoles)

        //upload profile pic
        profilePhotoUrl?.let { awsS3Service.uploadFromTmp(request.profilePicS3Key!!, it) }
        tokenVerificationService.createToken(savedUser, ValidationType.PARTNER_SIGNUP)
        return savedUser
    }

    @Transactional
    fun createPartnerUser(request: PartnerUserCreateRequest): Long? {

        val partnerEntity = partnerRepository.findById(request.partnerId)
            .orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }

        return createPartnerAdminUser(request.userDetails, partnerEntity).id
    }

    private fun getProfileUrl(key: String?): String? {
        return if (key.isNullOrBlank()) null else "$userProfileFolder/${key}"
    }

    private fun updatePartnerBand(features: List<String>?, partner: PartnerEntity): BandsEntity {

        val bandsEntity = bandsRepository.findById(partner.band?.id!!).get()
        val itr = bandsEntity.bandAccesses?.iterator()
        while (itr?.hasNext() == true) {
            val access = itr.next()
            access.band=null
            itr.remove()
        }

        val bandDetails = getAllAccessesByFeatureKeys(features, bandsEntity)

        bandsEntity.bandAccesses = bandDetails
        return bandsRepository.save(bandsEntity)
    }

    private fun updatePartnerInfo(
        partner: PartnerEntity,
        partnerRequest: UpdatePartnerRequest
    ) {

        val companyLogo = getProfileUrl(partnerRequest.companyLogo)

        partner.name = partnerRequest.name!!
        partner.contractFromDate = TimeUtil.fromInstant(partnerRequest.startDate/1000)
        partner.contractToDate = TimeUtil.fromInstant(partnerRequest.endDate/1000)
        partner.country = partnerRequest.country
        partner.casesManaged = partnerRequest.casesManagedBy
        partner.queriesManaged = partnerRequest.queryManagedBy
        partner.themePrimaryColor = partnerRequest.primaryColor
        partner.themeSecondaryColor = partnerRequest.secondaryColor
        partner.corporateAccess = partnerRequest.corporateFeatures.joinToString(",")


        partner.companyLogo = awsS3Service.updateProfilePicture(partnerRequest.companyLogo, companyLogo, partner.companyLogo)

        copyLogoToStaticBucket(partnerRequest.companyLogo, partner.id!!)

        val updateBand = updatePartnerBand(partnerRequest.features, partner)
        partner.band = updateBand

        val partnerUser = loginAccountRepository.findById(partner.rootUserId!!).get()
        if (partner.createdFrom == PartnerType.NEW) {
            val partnerUserDetails = partnerRequest.rootUserDetails!!
            partnerUser.firstName = partnerUserDetails.firstName
            partnerUser.lastName = partnerUserDetails.lastName
            //partnerUser.jobTitle = partnerUserDetails.jobTitle
            partnerUser.countryCode = partnerUserDetails.country
            partnerUser.contactNo = partnerUserDetails.contactNumber
            partnerUser.dialCode = partnerUserDetails.dialCode

            val profilePicture = getProfileUrl(partnerUserDetails.profilePicS3Key)

            partnerUser.profilePhotoUrl = awsS3Service.updateProfilePicture(partnerUserDetails.profilePicS3Key, profilePicture, partnerUser.profilePhotoUrl)
        }

        val docPath = "$onboardingDocsFolder/${partner.id}"
        if(partnerRequest.onboardingDocs != null) {

            val docType = ClientDocType.ON_BOARD
            val referenceType = UserDocType.PARTNER
            val onboardingDocs = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(
                partner.id!!,
                referenceType,
                docType
            )

            val pair =
                EditUtil.updateOnboardingDocs(onboardingDocs, partnerRequest.onboardingDocs.map {
                    ClientDocEntity(
                        docName = it.docName,
                        docKey = if (!it.s3Key.startsWith(docPath)) "$docPath/${it.s3Key}" else it.s3Key,
                        fileType = it.type,
                        fileName = it.fileName,
                        fileSize = it.size,
                        docType = docType,
                        referenceId = partner.id!!,
                        referenceType = referenceType
                    )
                }.toMutableList())

            onboardingDocs.addAll(pair.first)

            //upload newly added/updated docs
            pair.first.forEach {
                awsS3Service.uploadFromTmp(it.docKey, "$docPath/${it.docKey}")
            }

            //delete removed docs from s3
            pair.second.forEach {
                awsS3Service.deleteFileByS3Key(it.docKey)
            }

            clientDocRepository.deleteAll(pair.second)
            clientDocRepository.saveAll(onboardingDocs)
        }

        loginAccountRepository.save(partnerUser)
        val savedPartner = partnerRepository.saveAndFlush(partner)
        updatePartnerCorporateBands(savedPartner.corporates, partnerRequest.corporateFeatures)
    }

    @Transactional
    private fun updatePartnerCorporateBands(corporates: List<CorporateEntity>, accesses: List<String>) {
        corporates.forEach {
            corporateService.updateBandAccess(it, accesses)
        }
    }

    private fun copyLogoToStaticBucket(companyLogo: String?, partnerId: Long) {
        companyLogo?.let { awsS3Service.uploadFromTmp(it,
            partnerEmailUtil.getPartnerLogoURI(partnerId), STATIC_PUBLIC_BUCKET) }
    }

    @Transactional
    fun updatePartner(
        partnerId: Long,
        request: UpdatePartnerRequest,
        authenticatedUser: AuthenticatedUser
    ): PartnerEntity? {

        val partner = partnerRepository.findById(partnerId).get()

        if (partner.createdFrom == PartnerType.NEW) {
            if (request.rootUserDetails == null)
                throw ApplicationException(ErrorCode.BAD_REQUEST)
        }
        updatePartnerInfo(partner, request)

        return partner
    }

    @Transactional(readOnly = true)
    fun retrieveCorporates(partnerId: Long): List<ReferenceData> {
        val corporates = corporateRepository.findAllByPartnerId(partnerId)
        return corporates.filter { it.id!! > 0 }
    }

    @Transactional(readOnly = true)
    fun retrieveExpertCompany(partnerId: Long): List<ReferenceData> {
        return expertUserService.retrieveExpertCompany(partnerId, null)
    }

    @Transactional(readOnly = true)
    fun retrieveBaseUsersList(partnerId: Long): List<Map<String, String>>? {

        val partner =
            partnerRepository.findById(partnerId).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }

        return when (partner.createdFrom) {
            PartnerType.CORPORATE -> {
                val corporateUsers = partner.referenceId?.let { corporateUserRepository.findAllByCorporateIdAndBandNameIn(it, listOf("Super Admin (free)", "Super Admin") ) }
                    ?: throw ApplicationException(ErrorCode.NOT_FOUND)

                corporateUsers.filter { it.status == AccountStatus.ACTIVE }.map {
                    mapOf(
                        "id" to it.id.toString(),
                        "name" to it.firstName + " " + it.lastName,
                        "type" to it.getUserType().name
                    )
                }
            }

            PartnerType.EXPERT -> {
                val companyProfile = partner.referenceId?.let { expertCompanyProfileRepository.findById(it)
                    .orElseThrow { throw ApplicationException(ErrorCode.NOT_FOUND) }
                }

                companyProfile?.users?.filter{it.status == AccountStatus.ACTIVE}?.map {
                    mapOf(
                        "id" to it.id.toString(),
                        "name" to it.firstName + " " + it.lastName,
                        "type" to it.getUserType().name
                    )
                }
            }

            else -> {
                return emptyList()
            }
        }
    }

    fun retrieveRootUserDetails(companyType: String, companyId: Long): Map<String, Any?> {

        when (PartnerType.valueOf(companyType)) {
            PartnerType.CORPORATE -> {

                val corporate = corporateRepository.findById(companyId)
                    .orElseThrow { ApplicationException(ErrorCode.CORPORATE_NOT_FOUND) }
                val rootUser = corporateUserRepository.findById(corporate.rootUserId)
                    .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

                return mapOf(
                    "firstName" to rootUser.firstName, "lastName" to rootUser.lastName,
                    "jobTitle" to rootUser.jobTitle, "email" to rootUser.email, "country" to rootUser.countryCode,
                    "dialCode" to rootUser.dialCode,
                    "contactNumber" to rootUser.contactNo,
                    "profileImageUrl" to rootUser.profilePhotoUrl?.let { url -> awsS3Service.getProfilePicUrl(url) },
                    "aiMessageCount" to rootUser.questionsQuota
                )
            }

            PartnerType.EXPERT -> {

                val expertCompany = expertCompanyProfileRepository.findById(companyId)
                    .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
                val primaryExpert =
                    expertUserRepository.findAllByCompanyProfileAndExpertType(expertCompany, ExpertType.PRIMARY.name)[0]
                return mapOf(
                    "firstName" to primaryExpert.firstName,
                    "lastName" to primaryExpert.lastName,
                    "jobTitle" to primaryExpert.jobTitle,
                    "email" to primaryExpert.email,
                    "country" to primaryExpert.countryCode,
                    "dialCode" to primaryExpert.dialCode,
                    "contactNumber" to primaryExpert.contactNo,
                    "profileImageUrl" to primaryExpert.profilePhotoUrl?.let {awsS3Service.getProfilePicUrl(it) },
                    "aiMessageCount" to primaryExpert.questionsQuota
                )
            }

            else -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
        }
    }

    fun retrievePartnerUserDetails(partnerId: Long, userId: Long): UserDetails {

        val partner =
            partnerRepository.findById(partnerId).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }

        val partnerUser = loginAccountRepository.findByPartnerAndId(partner, userId)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        return UserDetails.ModelMapper.from(
            partnerUser,
            partnerUser.partnerJobTitle,
            partnerUser.profilePhotoUrl?.let { awsS3Service.getProfilePicUrl(it) }
        )
    }

    fun updatePartnerUser(
        partnerId: Long, userId: Long, request: UpdatePartnerUserRequest,
        authenticatedUser: AuthenticatedUser
    ): UserDetails {

        val partner = partnerRepository.findById(partnerId).get()

        val partnerUser = loginAccountRepository.findByPartnerAndId(partner, userId)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        updatePartnerUserInfo(partnerUser, request)

        return toUserDetails(partnerUser)
    }

    private fun updatePartnerUserInfo(
        partnerUser: LoginAccountEntity,
        partnerRequest: UpdatePartnerUserRequest
    ) {

        val profilePhotoUrl = getProfileUrl(partnerRequest.profilePicS3Key)

        partnerUser.firstName = partnerRequest.firstName
        partnerUser.lastName = partnerRequest.lastName
        partnerUser.partnerJobTitle = partnerRequest.jobTitle
        partnerUser.countryCode = partnerRequest.country
        partnerUser.contactNo = partnerRequest.contactNumber
        partnerUser.dialCode = partnerRequest.dialCode

        partnerUser.profilePhotoUrl = awsS3Service.updateProfilePicture(partnerRequest.profilePicS3Key, profilePhotoUrl, partnerUser.profilePhotoUrl)

        loginAccountRepository.save(partnerUser)
    }

    @Transactional(readOnly = true)
    fun retrieveAllPartnerDetails(partnerId: Long): AllPartnerList? {
        val partnerEntity = partnerRepository.findById(partnerId).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }

        val onboardingDocsEntities = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(
            partnerId,
            UserDocType.PARTNER,
            ClientDocType.ON_BOARD
        )

        val onboardingDocs = onboardingDocsEntities.map {
            OnboardingDocs(
                it.id,
                it.docName,
                it.docKey,
                awsS3Service.getS3Url(it.docKey),
                it.fileSize,
                it.fileType,
                it.fileName,
                TimeUtil.toEpochMillis(it.lastUpdatedDate)
            )
        }

        return AllPartnerList.ModelMapper.from(partnerEntity, awsS3Service, onboardingDocs)
    }

    @Transactional(readOnly = true)
    fun getPartnerId(userId: Long): Long? {
        val partnerEntity = partnerRepository.findByPartnerUsersId(userId)
        return partnerEntity?.id?:corporateUserRepository.findByIdOrNull(userId)?.let { it.corporate.partner?.id }
    }

    @Transactional
    fun resend(partnerId: Long, userId: Long): String {
        val partnerEntity = partnerRepository.findById(partnerId).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND)}
        val partnerUser = loginAccountRepository.findByPartnerAndId(partnerEntity, userId)
        if(partnerUser.isPresent && partnerUser.get().status == AccountStatus.PENDING_VERIFICATION) {
            return tokenVerificationService.resend(partnerUser.get().id!!, ValidationType.PARTNER_SIGNUP)
        } else{
            throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_NOT_FOUND)
        }
    }


    @Transactional(readOnly = true)
    fun listUserDetails(userId: Long):  UserDetails{
        val user = loginAccountRepository.findById(userId).get()
        return toUserDetails(user)
    }

    private fun toUserDetails(
        user: LoginAccountEntity
    ): UserDetails {
        val jobTitle: String?
        when (AdminAccessUtil.getUserType(user)) {
            UserType.CORPORATE -> {
                val corporateUsers = user as CorporateUserEntity
                jobTitle = corporateUsers.jobTitle
            }
            UserType.PARTNER -> {
                jobTitle = user.partnerJobTitle
            }
            UserType.EXPERT -> {
                val expertUser = user as ExpertUserEntity
                jobTitle = expertUser.jobTitle
            }
            else -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
        }
        val profilePictureUrl = user.profilePhotoUrl?.let { awsS3Service.getProfilePicUrl(it) }
        return (UserDetails.ModelMapper.from(user, jobTitle, profilePictureUrl))
    }

    @Transactional(readOnly = true)
    fun listExpertStats(
        partnerId: Long?,
        filter: PartnerExpertSearchFilter, pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): ListingWithStatsDetails<ExpertList>? {

        val expertData = listPartnerExpert(partnerId, filter,pageRequest, authenticatedUser)

        val statsByCriteria = expertCompanyProfileRepository.findStatsByCriteria(partnerId,filter)

        val expertStatsMap = statsByCriteria.map { mapOf(it[0] to it[1]) }.flatMap { it1->it1.entries }.associate { it2->it2.key to it2.value }

        val activeUsers =expertStatsMap.getOrDefault(AccountStatus.ACTIVE, 0L) as Long
        val suspendedUsers = expertStatsMap.getOrDefault(AccountStatus.SUSPENDED, 0L) as Long
        val pendingUsers = expertStatsMap.getOrDefault(AccountStatus.PENDING_VERIFICATION, 0L) as Long

        val totalUsers = expertStatsMap.values.sumOf { it as Long }
        val stats = ListingStats(totalUsers, activeUsers, suspendedUsers, pendingUsers)

        return ListingWithStatsDetails(expertData, stats)

    }



    @Transactional(readOnly = true)
    fun listPartnerExpert(
        partnerId: Long?,
        filter: PartnerExpertSearchFilter, pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<ExpertList?> {

        val expertList = expertCompanyProfileRepository.searchByCriteria(partnerId, filter, pageRequest)

        return toPartnerExpertList(expertList)
    }

    private fun toPartnerExpertList(expertList: Page<ExpertCompanyProfileEntity>): PagedResult<ExpertList?> {
        val partnerExpertDetails = expertList.map {
            ExpertList.ModelMapper.from(
                it
            )
        }
        return PagedResult.ModelMapper.from(expertList, partnerExpertDetails.toList())
    }

    @Transactional(readOnly = true)
    fun listCorporateStats(
        partnerId: Long?,
        filter: PartnerCorporateSearchFilter, pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): ListingWithStatsDetails<CorporateList>? {

        val corporateData = listPartnerCorporate(partnerId, filter,pageRequest, authenticatedUser)

        val statsByCriteria =  corporateRepository.findStatsByCriteria(partnerId,filter)

        val corporateStatsMap = statsByCriteria.map { mapOf(it[0] to it[1]) }.flatMap { it1->it1.entries }.associate { it2->it2.key to it2.value }

        val activeUsers = corporateStatsMap.getOrDefault(CorporateStatus.ACTIVE, 0L) as Long
        val suspendedUsers = corporateStatsMap.getOrDefault(CorporateStatus.SUSPENDED, 0L) as Long
        val pendingUsers = corporateStatsMap.getOrDefault(CorporateStatus.PENDING_VERIFICATION, 0L) as Long

        val totalUsers = corporateStatsMap.values.sumOf { it as Long }

        val stats = ListingStats(totalUsers, activeUsers, suspendedUsers, pendingUsers)
        return ListingWithStatsDetails(corporateData, stats)

    }


    @Transactional(readOnly = true)
    fun listPartnerCorporate(
        partnerId: Long?,
        filter: PartnerCorporateSearchFilter, pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<CorporateList?> {
        val corporateList = corporateRepository.searchByPartnerCriteria(partnerId, filter, pageRequest)
        return toCorporateList(corporateList)
    }

    private fun toCorporateList(corporateList: Page<CorporateEntity>): PagedResult<CorporateList?> {
        val partnerCorporateDetails = corporateList.map {
            CorporateList.ModelMapper.from(
                it
            )
        }
        return PagedResult.ModelMapper.from(corporateList, partnerCorporateDetails.toList())
    }

    @Transactional
    fun updateCompanyStatus(
        request: UpdateCompanyStatusRequest,
        authenticatedUser: AuthenticatedUser
    ): Boolean {

        when(request.type){
            UserType.PARTNER->{
                val partner = request.id?.let { partnerRepository.findById(it).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }}!!

                if(partner.status == CorporateStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE) {
                    throw ApplicationException(ErrorCode.PENDING_ACCOUNT_UPDATE_FAIL)
                }

                partner.partnerUsers.forEach {
                    if (it.status == AccountStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE){
                        // do nothing
                    }
                    else {
                        it.status = request.status!!
                        loginAccountRepository.save(it)
                    }
                }

                partner.status = CorporateStatus.valueOf(request.status.toString())
                partnerRepository.save(partner)
            }

            UserType.EXPERT -> {
                val expert = request.id?.let { expertCompanyProfileRepository.findById(it).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) } }

                expert?.users?.forEach {
                    if (it.status == AccountStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE){
                        //do nothing
                    }
                    else {
                        it.status = request.status!!
                        expertUserRepository.save(it)
                    }
                }
            }

            UserType.CORPORATE->{
                val corporate = request.id?.let { corporateRepository.findById(it).orElseThrow { ApplicationException(ErrorCode.CORPORATE_NOT_FOUND) } }!!

                if(corporate.status == CorporateStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE) {
                    throw ApplicationException(ErrorCode.PENDING_ACCOUNT_UPDATE_FAIL)
                }

                corporate.users.forEach {
                    if (it.status == AccountStatus.PENDING_VERIFICATION && request.status == AccountStatus.ACTIVE){
                        // do nothing
                    }
                    else{
                        it.status = request.status!!
                        corporateUserRepository.save(it)
                    }
                }
                corporate.status = CorporateStatus.valueOf(request.status.toString())
                corporateRepository.save(corporate)
            }
            else ->{
                return false
            }
        }
        return true


    }
    @Transactional
    fun updatePartnerCompanyStatus(
        partnerId: Long,
        request: UpdateCompanyStatusRequest,
        authenticatedUser: AuthenticatedUser
    ): Boolean {

        when(request.type){
            UserType.EXPERT -> {

                val partner = authenticatedUser.partnerId?.let { partnerRepository.findById(it).get() }
                val expert = request.id?.let { expertCompanyProfileRepository.findById(it).get() }

                if(expert!!.associatedPartners.contains(partner)) {
                        updateCompanyStatus(request, authenticatedUser)
                }
            }
            UserType.CORPORATE -> {
                val partner = authenticatedUser.partnerId?.let { partnerRepository.findById(it).get() }
                val corporate = request.id?.let { corporateRepository.findById(it).get() }

                if (corporate!!.partner == partner) {
                    updateCompanyStatus(request, authenticatedUser)
                }
            }
            else ->{
                return false
            }
        }
        return true
    }

    fun retrievePartnerAssociatedUsers(partnerId: Long): List<ClientUser> {
        return partnerRepository.findPartnerAssociatedUsers(partnerId)
    }

    fun generateVerificationLink(userId: Long,
                                 partnerId: Long,
                                 authenticatedUser: AuthenticatedUser):String{
        val user = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if (validateUserBelongsToPartner(user, partnerId)) {
            return tokenVerificationService.generateVerificationLink(userId)
        } else {
            throw ApplicationException(ErrorCode.PARTNER_USER_NOT_FOUND)
        }
    }

//    fun temporaryPassword(
////        userId: Long,
////        partnerId: Long,
////        authenticatedUser: AuthenticatedUser
////    ): CompletableFuture<TemporaryPassword> {
////        val user = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
////        if (validateUserBelongsToPartner(user, partnerId)) {
////            return adminUserService.temporaryPassword(userId, authenticatedUser)
////        } else {
////            throw ApplicationException(ErrorCode.PARTNER_USER_NOT_FOUND)
////        }
////    }

    fun updateUserStatus(userId: Long ,partnerId: Long,accountStatus: AccountStatus): CompletableFuture<Any> {
        val user = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if ( user.getUserType()===UserType.BACKOFFICE || validateUserBelongsToPartner(user,partnerId)  ) {
          return adminClientFacade.updateUserStatus(userId, accountStatus)
        } else {
            throw ApplicationException(ErrorCode.PARTNER_USER_NOT_FOUND)
        }
    }

     fun validateUserBelongsToPartner(user: LoginAccountEntity, partnerId: Long): Boolean {
        return when (AdminAccessUtil.getUserType(user)) {
            UserType.EXPERT -> {
                (user as ExpertUserEntity).companyProfile?.associatedPartners!!.any { it.id == partnerId }
            }

            UserType.BACKOFFICE -> { false }
            UserType.CORPORATE -> {
                (user as CorporateUserEntity).corporate.partner?.id == partnerId
            }

            UserType.PARTNER -> {
                user.partner?.id == partnerId
            }

        }
    }
    fun retrievePartnerUsers(partnerId: Long): List<ClientUser> {
        return partnerRepository.findPartnerUsers(partnerId)
    }

    @Transactional
    fun deletePartner(partnerId: Long): Boolean {
        val partnerEntity = partnerRepository.findById(partnerId)

        partnerEntity.ifPresent { partner ->
            if (partner.corporates.isEmpty() && partner.associatedCompanies.isEmpty()) {
                val partnerUsers = partner.partnerUsers
                if (partnerUsers.size == 1) {
                    partnerUsers.map {
                        partnerRepository.delete(partnerEntity.get())
                        loginAccountRepository.deleteById(it.id!!)
                    }
                } else {
                    throw ApplicationException(ErrorCode.DELETE_PARTNER_FAIL)
                }

            } else {
                throw ApplicationException(ErrorCode.PARTNER_USER_EXIST)
            }
        }
        return true
    }

    fun retrieveExpertUsers(partnerId: Long, companyId: Long): List<ExpertUserReferenceData> {
        return retrieveExpertUsersByCompanyIds(partnerId, companyId.toString())
    }

    fun retrieveExpertUsersByCompanyIds(partnerId: Long, companyIds: String): List<ExpertUserReferenceData> {
        return expertUserService.retrieveExpertUsersByCompanyIds(partnerId, companyIds.split(",").map { it.toLong() })
    }

    fun getUserProfiles(partnerId: Long, userIds: List<Long>): List<UserProfile> {
        return userIds.map { userProfileUtil.retrieveProfile(it) }
    }

    fun retrievePartners(): List<ReferenceData> {
        return partnerRepository.findAllByIdGreaterThanEqual(0L)
    }

    @Transactional(readOnly = true)
    fun getDashboardInfo(authenticatedUser: AuthenticatedUser): PartnerDashboardCompanyInfo {

        if (authenticatedUser.userType != UserType.PARTNER.name) {
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }

        val partnerEntity = partnerRepository.findById(authenticatedUser.partnerId!!)
            .orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }

        val docEntities = clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(partnerEntity.id!!, UserDocType.PARTNER, ClientDocType.ON_BOARD)

        val docs = docEntities.map {
            OnboardingDocs(
                it.id,
                it.docName,
                it.docKey,
                awsS3Service.getS3Url(it.docKey),
                it.fileSize,
                it.fileType,
                it.fileName,
                TimeUtil.toEpochMillis(it.lastUpdatedDate)
            )
        }

        return PartnerDashboardCompanyInfo(
            docs = docs,
            stats = PartnerUserCounts(
                partnerEntity.partnerUsers.size,
                partnerEntity.corporates.size,
                partnerEntity.associatedCompanies.filter { it.companyType == ExpertCompanyType.SUPPLIER }.size
            ),
            companyName = partnerEntity.name,
            countryCode = partnerEntity.country ?: ""
        )
    }

    fun retrievePartnerBandAccesses(partnerId: Long): List<String?>? {
        val partner =
            partnerRepository.findById(partnerId).orElseThrow { ApplicationException(ErrorCode.PARTNER_NOT_FOUND) }
        return partner.corporateAccess.split(",").distinct()
    }
}