package com.centuroglobal.service

import com.centuroglobal.data.payload.auth.ChangePasswordRequest
import com.centuroglobal.data.payload.auth.RecoverPasswordRequest
import com.centuroglobal.data.payload.auth.ResetPasswordRequest
import com.centuroglobal.data.properties.PasswordPolicyProperties
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ValidationState
import com.centuroglobal.shared.data.enums.ValidationType
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.ValidationTokenRepository
import org.passay.*
import org.springframework.data.repository.findByIdOrNull
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.concurrent.ThreadLocalRandom

@Service
class PasswordService(
    private val policyProperties: PasswordPolicyProperties,
    private val passwordEncoder: PasswordEncoder,
    private val loginAccountRepository: LoginAccountRepository,
    private val validationTokenRepository: ValidationTokenRepository,
    private val tokenVerificationService: TokenVerificationService
) {
    private lateinit var validator: PasswordValidator
    private var charRuleList = mutableListOf<CharacterRule>()

    init {
        // Construct charRuleList
        if (policyProperties.requireLowercase) {
            charRuleList.add(CharacterRule(EnglishCharacterData.LowerCase, 1))
        }
        if (policyProperties.requireUppercase) {
            charRuleList.add(CharacterRule(EnglishCharacterData.UpperCase, 1))
        }
        if (policyProperties.requireDigit) {
            charRuleList.add(CharacterRule(EnglishCharacterData.Digit, 1))
        }
        if (policyProperties.requireSpecialChar) {
            charRuleList.add(CharacterRule(EnglishCharacterData.Special, 1))
        }

        // Create validator
        val rules = mutableListOf<Rule>()
        rules.addAll(charRuleList)
        rules.add(WhitespaceRule())
        rules.add(LengthRule(policyProperties.minLength, policyProperties.maxLength))
        validator = PasswordValidator(rules)
    }

    @Transactional
    fun changePassword(userId: Long, request: ChangePasswordRequest): String {
        val entity = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if (!passwordEncoder.matches(request.oldPassword, entity.password)) {
            throw ApplicationException(ErrorCode.OLD_PASSWORD_IS_WRONG)
        } else if (request.newPassword == request.oldPassword) {
            throw ApplicationException(ErrorCode.NEW_PASSWORD_SAME_AS_OLD_PASSWORD)
        }

        try {
            return savePassword(entity, request.newPassword)
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> throw ApplicationException(ErrorCode.CHANGE_PASSWORD_FAIL)
            }
        }
    }

    @Transactional
    fun recoverPassword(request: RecoverPasswordRequest): String {
        val entity = loginAccountRepository.findByEmailAndStatusIn(
            request.email,
            listOf(AccountStatus.ACTIVE)
        )

        if (entity != null) {
            tokenVerificationService.invalidateUnusedToken(entity.id!!, ValidationType.RECOVER_PASSWORD)
            tokenVerificationService.createToken(entity, ValidationType.RECOVER_PASSWORD)
        }

        return AppConstant.SUCCESS_RESPONSE_STRING
    }

    @Transactional
    fun resetPassword(request: ResetPasswordRequest): TokenResult? {
        val tokenEntity = tokenVerificationService.getValidTokenEntity(request.code)
        val entity =
            loginAccountRepository.findByIdOrNull(tokenEntity.userId)
                ?: throw ApplicationException(ErrorCode.RESET_PASSWORD_FAIL)

        tokenEntity.state = ValidationState.VERIFIED
        validationTokenRepository.save(tokenEntity)

        try {
            entity.status = AccountStatus.ACTIVE
            savePassword(entity, request.password)
        } catch (ex: Exception) {
            when (ex) {
                is ApplicationException -> throw ex
                else -> throw ApplicationException(ErrorCode.RESET_PASSWORD_FAIL)
            }
        }
        return tokenVerificationService.generateLoginToken(entity)
    }

    fun generatePassword(): String {
        val randomLength =
            ThreadLocalRandom.current().nextInt(policyProperties.minLength, policyProperties.maxLength + 1)
        return PasswordGenerator().generatePassword(randomLength, charRuleList)
    }

    fun validateAndEncodePassword(password: String): String {
        validate(password)
        return passwordEncoder.encode(password)
    }

    private fun savePassword(entity: LoginAccountEntity, newPassword: String): String {
        validate(newPassword)

        entity.password = passwordEncoder.encode(newPassword)
        entity.passwordCreationDate = LocalDateTime.now()
        entity.lastUpdatedBy = entity.id!!
        loginAccountRepository.save(entity)

        return AppConstant.SUCCESS_RESPONSE_STRING
    }

    private fun validate(password: String?) {
        if (!password.isNullOrBlank()) {
            val result: RuleResult = validator.validate(PasswordData(password))
            if (!result.isValid) {
                throw ApplicationException(ErrorCode.PASSWORD_INVALID(policyProperties.validationErrorMessage))
            }
        }
    }

    fun loginAccountUuidApproval(uuid: String): Boolean {
        val loginAccount =
            loginAccountRepository.findByLoginToken(uuid) ?: return false
        if (loginAccount.loginTokenExpire!! < LocalDateTime.now()) {
            return false
        }
        return true
    }

    fun logout(userId: Long): Boolean {
        val loginAccount =loginAccountRepository.findById(userId).get()
        loginAccount.loginToken=null
        loginAccountRepository.save(loginAccount)
        return true
    }
}