package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.AIChatEntity
import com.centuroglobal.shared.data.entity.playbook.*
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.AIChatResponse
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.playbook.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AIChatRepository
import com.centuroglobal.shared.repository.ContentLibraryRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.playbook.PlaybookChatRepository
import com.centuroglobal.shared.repository.playbook.PlaybookRepository
import com.centuroglobal.shared.repository.playbook.PlaybookSessionRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.core.JacksonException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import mu.KotlinLogging
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

private val log = KotlinLogging.logger {}

@Service
class PlaybookService(

    private val playbookRepository: PlaybookRepository,
    private val playbookSessionRepository: PlaybookSessionRepository,
    private val userProfileUtil: UserProfileUtil,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val contentLibraryRepository: ContentLibraryRepository,
    private val playbookChatRepository: PlaybookChatRepository,
    private val travelAssessmentRepository: TravelAssessmentRepository,
    private val aiChatRepository: AIChatRepository,
    private val openAIService: OpenAIService

) {

    @Transactional
    fun create(playbookRequest: PlaybookRequest, requestId: String?): PlaybookResponse {
        log.debug("creating playbook for country: ${playbookRequest.country}")

        val playbookEntity = PlaybookEntity(
            country = playbookRequest.country,
            about = playbookRequest.about,
            industry = playbookRequest.industry,
            industryName = playbookRequest.industryName
        )

        val details = mutableListOf<PlaybookDetailsEntity>()

        playbookRequest.details.forEach {
            details.add(
                PlaybookDetailsEntity(
                    category = it.category,
                    subCategory = "",
                    isSelected = it.isSelected,
                    playbook = playbookEntity
                )
            )
            details.addAll(it.children.map { child ->
                PlaybookDetailsEntity(
                    category = it.category,
                    subCategory = child.subCategory,
                    isSelected = child.isSelected,
                    playbook = playbookEntity
                )
            })
        }

        playbookEntity.details = details
        playbookEntity.playbookContent = PlaybookContentEntity(playbook = playbookEntity)

        val savedEntity = playbookRepository.save(playbookEntity)

        //create playbook session
        val sessionEntity = createPlaybookSession(savedEntity, "CREATE", requestId)

        val json = framePlaybookJson(savedEntity)

        val createdBy = getCreatedBy(savedEntity.createdBy!!)

        return PlaybookResponse.ModelMapper.from(savedEntity, sessionEntity.sessionId, json, createdBy)
    }
    private fun getCreatedBy(id: Long): String {
        return loginAccountRepository.getFirstNameLastNameById(id)
    }

    private fun framePlaybookJson(playbook: PlaybookEntity): JsonNode {
        val template =
            contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", playbook.country) ?: throw ApplicationException(
                ErrorCode.NOT_FOUND
            )

        val mapper = ObjectMapper()

        // return data if already present and up to date
        val playbookContent = playbook.playbookContent!!
        if (playbookContent.data!=null && playbookContent.lastUpdatedDate >= template.lastUpdatedDate) {
            log.debug("No changes in playbook content so not generating again.")
            return mapper.readTree(playbookContent.data)
        }

        val templateNode = try {
            mapper.readTree(template.data)
        } catch (ex: JacksonException) {
            throw ApplicationException(ErrorCode.PLAYBOOK_INVALID_TEMPLATE)
        }

        for (detail in playbook.details) {
            val elements = templateNode.get("uiElements").elements()
            while (elements.hasNext()) {
                val element = elements.next()
                val category = element.get("category")

                if (category!=null && category.textValue() == detail.category) {

                    if(!detail.isSelected && detail.subCategory.isEmpty()) {
                        //remove parent
                        log.debug("Removing Category element: ${category.textValue()}")
                        elements.remove()
                    }
                    else {
                        val pages = element.get("pages").elements()
                        while (pages.hasNext()) {
                            val page = pages.next()
                            val subcategory = page.get("subCategory")

                            if(subcategory!=null && subcategory.textValue() == detail.subCategory && !detail.isSelected) {
                                //remove page
                                log.debug("Removing subcategory element: ${category.textValue()}")
                                pages.remove()
                            }
                        }
                    }
                }
            }
        }
        //update playbook data if outdated
        log.debug("Updating newly generated data in playbook content")
        playbookContent.data = templateNode.toString()
        playbookRepository.save(playbook)

        return templateNode
    }

    @Transactional
    fun get(playbookId: Long, authenticatedUser: AuthenticatedUser, createSession: Boolean, requestId: String?): PlaybookResponse {
        val playbook = playbookRepository.findById(playbookId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val json = framePlaybookJson(playbook)

        val skipSession = if (AdminAccessUtil.isAdminOrPartner(authenticatedUser)) {
            !createSession
        }
        else {
            false
        }

        //create playbook session
        val sessionEntity =
            if (!skipSession) {
                createPlaybookSession(playbook, "VIEW", requestId)
            } else {
                null
            }
        val createdBy = getCreatedBy(playbook.createdBy!!)

        return PlaybookResponse.ModelMapper.from(playbook, sessionEntity?.sessionId, json, createdBy)
    }

    private fun createPlaybookSession(playbook: PlaybookEntity, type: String, requestId: String?): PlaybookSessionEntity {

        val sessionEntity = PlaybookSessionEntity(
            playbook = playbook,
            questionCount = 0,
            startTime = LocalDateTime.now(),
            sessionId = requestId ?: UUID.randomUUID().toString(),
            type = type
        )
        return playbookSessionRepository.save(sessionEntity)
    }

    @Transactional(readOnly = true)
    fun list(filter: PlaybookSearchFilter, pageRequest: PageRequest): PagedResult<PlaybookListResponse> {

        val page = playbookRepository.searchByCriteria(filter, pageRequest)

        return PagedResult.ModelMapper.from(page, page.map {
            PlaybookListResponse.ModelMapper.from(it,
                it.createdBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) })
        }.toList())

    }

    fun delete(playbookId: Long): Boolean {
        playbookRepository.deleteById(playbookId)
        return true
    }

    @Transactional
    fun share(playbookId: Long, users: List<Long>): Boolean {

        val playbook = playbookRepository.findById(playbookId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        log.debug("Sharing playbook id: {} with users: {}", playbookId, users)

        val usersSet = users.map {
            loginAccountRepository.getReferenceById(it)
        }.toSet()

        playbook.sharedWith.clear()
        playbook.sharedWith.addAll(usersSet)

        playbookRepository.save(playbook)
        log.debug("Playbook $playbookId shared with ${usersSet.size} users.")
        return true
    }

    fun shareUsersList(authenticatedUser: AuthenticatedUser): List<PlaybookShareUserReferenceData> {

        // if user is partner admin return all partner admins
        if (authenticatedUser.role == Role.ROLE_PARTNER.name) {
            return loginAccountRepository.findPlaybookUsersByPartnerIdAndStatusIn(
                authenticatedUser.partnerId!!,
                listOf(AccountStatus.ACTIVE)
            )
        }

        val accesses = authenticatedUser.visibilities.filter { it.feature == "USER" }
        if(accesses.isEmpty()){
            throw ApplicationException(ErrorCode.FORBIDDEN)
        }
        val access = accesses[0]

        return when {
            access.accesses.contains("FULL") -> {
                corporateUserRepository.findPlaybookUsersByCorporateIdAndStatusIn(
                    authenticatedUser.companyId!!,
                    listOf(AccountStatus.ACTIVE)
                )
            }
            access.accesses.contains("REPORTEES") -> {
                corporateUserRepository.findPlaybookUsersByManagersManagerIdAndStatusIn(
                    authenticatedUser.userId,
                    listOf(AccountStatus.ACTIVE)
                )
            }
            else -> {
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }
    }

    fun closeSession(sessionId: String, authenticatedUser: AuthenticatedUser): Boolean {

        val sessionEntity = getPlaybookSession(sessionId, authenticatedUser)

        return if (sessionEntity.endTime == null) {
            sessionEntity.endTime = LocalDateTime.now()
            playbookSessionRepository.save(sessionEntity)
            true
        } else {
            false
        }
    }

    fun aiMessage(sessionId: String, request: AIMessageRequest, authenticatedUser: AuthenticatedUser): String {
        var sessionEntity: PlaybookSessionEntity? = null
        val threadId = if(sessionId == "visa-assessment") {
            null
        }
        else {
            sessionEntity = getPlaybookSession(sessionId, authenticatedUser)

            if(sessionEntity.endTime!=null) {
                throw ApplicationException(ErrorCode.PLAYBOOK_SESSION_CLOSED)
            }
            sessionEntity.threadId
        }

        return createMessage(sessionEntity, threadId, request)
    }

    private fun createMessage(sessionEntity: PlaybookSessionEntity?, threadId: String?, request: AIMessageRequest): String {
        val thread = openAIService.createThread(threadId)

        sessionEntity?.threadId = thread.id
        sessionEntity?.let { playbookSessionRepository.save(it) }

        val messages = openAIService.getAnswerFromThread(thread.id, request.question)

        val answerText = openAIService.readMessageText(messages)

        //store question, answer in table
        val playbookChatEntity = sessionEntity?.let {
            PlaybookChatEntity(
                question = request.question,
                answer = answerText,
                playbookSession = it
            )
        }

        //also store chats in ai_chat table
        aiChatRepository.save(
            AIChatEntity(
                question = request.question,
                answer = answerText
            )
        )

        // suspend function doesn't populate @CreatedBy and @LastModifiedBy
        /*playbookChatEntity.createdBy = authenticatedUser.userId
        playbookChatEntity.updatedBy = authenticatedUser.userId*/

        playbookChatEntity?.let { playbookChatRepository.save(it) }
        return answerText
    }

    @Transactional(readOnly = true)
    fun logs(filter: PlaybookLogsSearchFilter, pageRequest: PageRequest): PagedResult<PlaybookLogsResponse> {

        val page = playbookSessionRepository.searchByCriteria(filter, pageRequest)

        return PagedResult.ModelMapper.from(
            page,
            page.map { PlaybookLogsResponse.ModelMapper.from(it) }.toList()
        )
    }

    @Transactional(readOnly = true)
    fun getChatMessages(sessionId: String, authenticatedUser: AuthenticatedUser): List<AIChatResponse> {

        val sessionEntity = getPlaybookSession(sessionId, authenticatedUser)

        return sessionEntity.playbookChats.sortedByDescending { it.createdDate }.map {
            AIChatResponse(
                it.id,
                it.question,
                it.answer,
                userProfileUtil.retrieveProfile(it.createdBy!!),
                TimeUtil.toEpochMillis(it.createdDate),
                0,
                emptyList()
            )
        }
    }

    private fun getPlaybookSession(sessionId: String, authenticatedUser: AuthenticatedUser): PlaybookSessionEntity {

        return when(authenticatedUser.role) {

            Role.ROLE_ADMIN.name, Role.ROLE_SUPER_ADMIN.name -> {
                playbookSessionRepository.findBySessionId(sessionId)
            }
            Role.ROLE_PARTNER.name -> {
                playbookSessionRepository.findBySessionId(
                    sessionId
                )
            }

            Role.ROLE_CORPORATE.name -> {
                playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(
                    sessionId,
                    authenticatedUser.companyId!!
                )
            }
            else -> {
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        } ?: throw ApplicationException(ErrorCode.NOT_FOUND)
    }

    fun listCountries(prefix: String?): List<String> {
        return contentLibraryRepository.findByIdentifierAndActive("TEMPLATE")
    }

    fun assessmentAiMessage(sessionId: String, request: AIMessageRequest, authenticatedUser: AuthenticatedUser): String? {

        val assessment = travelAssessmentRepository.findBySessionId(sessionId)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val sessionEntity = getPlaybookSession(sessionId, authenticatedUser)

        val threadId = if(sessionEntity.threadId == null) {
            openAIService.createThreadWithContext(assessment.data).id
        }
        else {
            sessionEntity.threadId
        }
        return createMessage(sessionEntity, threadId, request)
    }

}