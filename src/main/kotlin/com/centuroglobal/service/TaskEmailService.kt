package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.TaskEntity
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.data.pojo.email.TaskContext
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private const val NEW_TASK_EMAIL_TEMPLATE = "email/task/new_task"
private const val UPDATE_TASK_EMAIL_TEMPLATE = "email/task/update_task"
private const val TASK_REMINDER_EMAIL_TEMPLATE = "email/task/task_reminder"

private const val TASK_CREATED_EMAIL_SUBJECT = "New Task - "
private const val TASK_UPDATED_EMAIL_SUBJECT = "Task status updated - "
private const val TASK_REMINDER_EMAIL_SUBJECT = "Task Reminder - "

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

private val taskUrl: (webUrl: String) -> String = { webUrl -> "${webUrl}/task-management/my-tasks" }

@Service
class TaskEmailService(
    @Value("\${app.web-url}")
    private val webUrl: String,

    private val mailSendingService: MailSendingService,
    private val awsS3Service: AwsS3Service
) {
    @Async
    @Transactional(readOnly = true)
    fun taskEmail(task: TaskEntity, mailToUserName:String, performerFullName:String, recipient: String,
                  taskEvent: String) {

        var template = ""
        var subject = ""

        when (taskEvent) {
            AppConstant.CONST_NEW_TASK -> {
                template = NEW_TASK_EMAIL_TEMPLATE
                subject = TASK_CREATED_EMAIL_SUBJECT + task.name
            }
            AppConstant.CONST_UPDATE_TASK -> {
                template = UPDATE_TASK_EMAIL_TEMPLATE
                subject = TASK_UPDATED_EMAIL_SUBJECT + task.name
            }
            AppConstant.CONST_TASK_REMINDER -> {
                template = TASK_REMINDER_EMAIL_TEMPLATE
                subject = TASK_REMINDER_EMAIL_SUBJECT + task.name
            }
        }

        taskEmail(task, recipient, mailToUserName, performerFullName, template, subject, emptyList(),taskEvent)
    }

    private fun taskEmail( task:TaskEntity?, email: String, mailToUsername: String, performerFullName:String
                           , template: String, subject: String, ccEmail: List<String>, taskEvent: String) {

        val ctx = getContext(task,mailToUsername,performerFullName,taskEvent)

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = template,
                subject = subject,
                context = ctx,
                ccRecipients = ccEmail,
                recipient = email
            )
        )
    }

    private fun getContext(task: TaskEntity?,mailToUsername: String, performerFullName: String,taskEvent: String): Context {

        var context = Context()

        var status = ""
        if(task != null){
            status = task.status?.label ?: ""
        }

        when (taskEvent) {
            AppConstant.CONST_UPDATE_TASK -> {

                context = TaskContext.ModelMapper.toContext( TaskContext(
                    awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                    taskUrl(webUrl),
                    performerFullName,
                    mailToUsername,
                    task!!.name,
                    formatDueDate(task.dueDate),
                    task.referenceType.toString(),
                    if(task.referenceType.toString() == "OPEN") "NA" else task.referenceId.toString(),
                    status)
                )
            }
            AppConstant.CONST_NEW_TASK -> {
                context = TaskContext.ModelMapper.toContext(
                    TaskContext(
                        awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                        taskUrl(webUrl),
                        mailToUsername,
                        performerFullName,
                        task!!.name,
                        formatDueDate(task.dueDate),
                        task.referenceType.toString(),
                        if(task.referenceType.toString() == "OPEN") "NA" else task.referenceId.toString(),
                        null)
                )
            }
            AppConstant.CONST_TASK_REMINDER -> {
                context = TaskContext.ModelMapper.toContext(
                    TaskContext(
                        awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER),
                        taskUrl(webUrl),
                        mailToUsername,
                        null,
                        task!!.name,
                        formatDueDate(task.dueDate),
                        task.referenceType.toString(),
                        if(task.referenceType.toString() == "OPEN") "NA" else task.referenceId.toString(),
                        status)
                )
            }
        }

        return context
    }
    private fun formatDueDate(localDateTime: LocalDateTime?): String? {
        val dateFormat = DateTimeFormatter.ofPattern("dd-MMM-yyyy")
        return localDateTime?.format(dateFormat)
    }
}