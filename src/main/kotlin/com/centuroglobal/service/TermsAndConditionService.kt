package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.MasterDataEntity
import com.centuroglobal.shared.data.enums.ExpertType
import com.centuroglobal.shared.data.enums.MasterDataDocType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.TermsAndCondition
import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.data.pojo.email.RenewalContractAcceptanceContext
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.AdminAccessUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit


private val log = KotlinLogging.logger {}
private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

@Service
class TermsAndConditionService(
    @Value("\${spring.mail.renewal-contract-acceptance.to}")
    private val adminEmail: String,
    private val awsS3Service: AwsS3Service,
    private val mailSendingService: MailSendingService,
    private val masterContentRepository: MasterContentRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val masterDataRepository: MasterDataRepository,
    private val expertUserRepository: ExpertUserRepository,
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository,
    private val tokenVerificationService: TokenVerificationService
) {

    @Transactional(readOnly = true)
    fun retrieveTermsAndCondition(authenticatedUser: AuthenticatedUser): Map<String, Any?> {
        val loginAccount = loginAccountRepository.findByEmail(authenticatedUser.email) ?: return emptyMap()

        var showContract = false
        var blockAccess = false
        var renewContract = false
        var userType: String? = null
        var expertType: String? = null
        var masterData: MasterDataEntity? = null
        when (UserType.valueOf(authenticatedUser.userType)) {
            UserType.CORPORATE -> {
                masterData = masterDataRepository.findByDocType(MasterDataDocType.CORPORATE_TC.name)
                userType = loginAccount.getUserType().name
                // as per CG-1189 corporate users will not be asked to accept t&c so set lastTermsViewDate to current date
                loginAccount.lastTermsViewDate = LocalDateTime.now()
            }
            UserType.PARTNER -> {
                masterData = masterDataRepository.findByDocType(MasterDataDocType.PARTNER_TC.name)
                userType = loginAccount.getUserType().name
            }
            UserType.EXPERT -> {
                if (loginAccount is ExpertUserEntity) {
                    masterData = masterDataRepository.findByDocType(MasterDataDocType.PARTNER_TC.name)
                    val company = loginAccount.companyProfile!!
                        if (company.effectiveEndDate < LocalDateTime.now()) {
                            blockAccess = true
                        }
//                        else if (company.lastUpdatedDate > company.contractAcceptedDate) {
//                            if (loginAccount.expertType == ExpertType.PRIMARY.name) {
//                                showContract = true
//                            } else {
//                                blockAccess = true
//                            }
//                        }
                }
                userType = loginAccount.getUserType().name
                val expertUser = loginAccount.id?.let { expertUserRepository.findByIdOrNull(it) }
                if (expertUser != null) {
                    expertType = expertUser.expertType
                    renewContract = expertUser.companyProfile!!.renewContract
                }
            }
            UserType.BACKOFFICE-> {
                log.debug("backoffice user")
            }
            else -> { throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR) }
        }
        val showTnC = !(masterData != null && loginAccount.lastTermsViewDate != null &&
                masterData.lastUploadDate!! < loginAccount.lastTermsViewDate)
        return mapOf(
            "showTNC" to showTnC,
            "showContract" to showContract,
            "blockAccess" to blockAccess,
            "renewContract" to renewContract,
            "userType" to userType,
            "expertType" to expertType,
        )
    }

    @Transactional
    fun update(email: String, tncAccepted: Boolean, renewAccepted: Boolean, contractAccepted: Boolean): Unit {
        val loginAccount = loginAccountRepository.findByEmail(email) ?: return

        val expertUser = expertUserRepository.findByIdOrNull(loginAccount.id!!)
        if (expertUser != null) {
            val company = expertUser.companyProfile
            if (company != null) {

                if (company.renewContract) {
                    company.renewContract = false
                    sendEmail(expertUser.displayName, company.name)
                }
                if (contractAccepted) company.contractAcceptedDate = LocalDateTime.now().plusMinutes(2)
                    .truncatedTo(ChronoUnit.MILLIS)
                expertCompanyProfileRepository.saveAndFlush(company)
            }
        }
        if (tncAccepted) {
            loginAccount.tncView = true
            loginAccount.lastTermsViewDate = LocalDateTime.now().truncatedTo(ChronoUnit.MILLIS)
        }
        loginAccountRepository.save(loginAccount)
    }

    @Transactional(readOnly = true)
    fun retrieveTermsAndConditionData(authenticatedUser: AuthenticatedUser): TermsAndCondition? {
        val loginAccount = loginAccountRepository.findByEmail(authenticatedUser.email)
        return retrieveTnCData(loginAccount!!)
    }

    @Transactional(readOnly = true)
    fun retrieveTermsAndConditionData(code: String?): TermsAndCondition? {
        val loginAccount = tokenVerificationService.getUserFromToken(code)
        return retrieveTnCData(loginAccount)
    }

    @Transactional(readOnly = true)
    fun retrieveTnCData(
        loginAccount: LoginAccountEntity
    ): TermsAndCondition? {
        return when (loginAccount.getUserType()) {
            UserType.CORPORATE -> {
                TermsAndCondition(
                    termsAndCondition = masterContentRepository.findByDocType(MasterDataDocType.CORPORATE_TC.name).content
                )
            }
            UserType.EXPERT -> {
                if (loginAccount is ExpertUserEntity) {
                    TermsAndCondition(
                        termsAndCondition = masterContentRepository.findByDocType(MasterDataDocType.PARTNER_TC.name).content,
                        contractDetails = if (loginAccount.viewContract) ExpertCompanyProfile.ModelMapper.from(
                            loginAccount.companyProfile!!,
                            null
                        ) else null
                    )
                } else {
                    log.warn("Entity is not of userType CorporateUser!!")
                    null
                }
            }
            UserType.BACKOFFICE -> {
                log.warn("Entity is not of userType BackofficeUser!!")
                null
            }
            else -> { throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR) }
        }
    }

    private fun sendEmail(expertName: String, companyName: String) {
        log.info("contract renew email triggered for expert", expertName)
        val subject = "Contract Renewed for $expertName"

        val template = "email/renewal_contract_acceptance"
        val formatter = DateTimeFormatter.ofPattern("MM-dd-YYYY HH:mm")
        val formattedDate = LocalDateTime.now().format(formatter)

        val ctx = RenewalContractAcceptanceContext.ModelMapper.toContext(
            RenewalContractAcceptanceContext(
                expertName,
                companyName,
                formattedDate,
                awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER)
            )
        )

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = template,
                subject = subject,
                context = ctx,
                recipient = adminEmail
            )
        )
    }
}