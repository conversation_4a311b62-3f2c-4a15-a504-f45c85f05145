package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.VerifiedUser
import com.centuroglobal.shared.data.pojo.email.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.security.JwtHelper
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.SecurityUtil
import com.centuroglobal.shared.util.TimeUtil
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.stream.Collectors


private val signUpTokenUrl: (webUrl: String, code: String) -> String =
    { webUrl, code -> "${webUrl}/auth/email-confirmation?code=${code}" }

@Service
class TokenVerificationService(
    @Value("\${app.web-url}")
    private val webUrl: String,
    private val validationTokenRepository: ValidationTokenRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val masterContentRepository: MasterContentRepository,
    private val adminAuthoritiesRepository: AdminAuthoritiesRepository,
    private val jwtHelper: JwtHelper,
    private val authService: AuthService,
    private val corporateUserRepository: CorporateUserRepository,
    private val expertUserRepository: ExpertUserRepository
) {
    private val tokenExpiryInSeconds: Int = 24 * 60 * 60

    @Transactional
    fun createToken(loginAccount: LoginAccountEntity, type: ValidationType) {
        createToken(type, loginAccount, ValidationState.CREATED)
    }

    fun createToken(type: ValidationType, loginAccount: LoginAccountEntity, state: ValidationState): ValidationTokenEntity {
        // 1. generate code
        val code = SecurityUtil.generateMd5Hash("${type}:${loginAccount.id!!}")

        // 2. compute expiry
        val expiryDate = TimeUtil.fromInstant(
            System.currentTimeMillis() / 1000 + tokenExpiryInSeconds
        )

        return validationTokenRepository.save(
            ValidationTokenEntity(
                code = code, userId = loginAccount.id!!,
                type = type, state = state,
                expiryDate = expiryDate
            )
        )
    }

    @Transactional(readOnly = true)
    fun validateCode(code: String?): VerifiedUser? {
        val loginAccount = getUserFromToken(code)
        return VerifiedUser.ModelMapper.fromUserEntity(loginAccount, masterContentRepository.findAll())
    }

    fun getUserFromToken(code: String?): LoginAccountEntity {
        val validationToken = getValidTokenEntity(code)
        return loginAccountRepository.findByIdOrNull(validationToken.userId)
            ?: throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_VERIFICATION_FAIL)
    }

    @Transactional
    fun generateLoginToken(loginAccount: LoginAccountEntity, role: Role? = null): TokenResult? {
        var corporateUser: CorporateUserEntity? = null
        val expertUser: ExpertUserEntity?
        var partnerUser: LoginAccountEntity? = null

        val displayName = (loginAccount.firstName + ' ' + loginAccount.lastName).trim()

        val userRole = role ?: AdminAccessUtil.getUserRole(loginAccount)

        var userType = loginAccount.getUserType()

        var companyId: Long? = null

        val country = when (userRole) {
            Role.ROLE_CORPORATE -> {
                corporateUser = corporateUserRepository.findByIdOrNull(loginAccount.id!!)
                userType = UserType.CORPORATE
                companyId = corporateUser?.corporate?.id
                corporateUser?.corporate?.countryCode
            }
            Role.ROLE_EXPERT, Role.ROLE_SUPPLIER -> {
                expertUser = expertUserRepository.findByIdOrNull(loginAccount.id!!)
                userType = UserType.EXPERT
                companyId = expertUser?.companyProfile?.id
                expertUser?.countryCode

            }
            Role.ROLE_PARTNER -> {
                partnerUser = loginAccountRepository.findByIdOrNull(loginAccount.id!!)
                userType = UserType.PARTNER
                companyId = partnerUser?.partner?.id
                partnerUser?.partner?.country
            }
            else -> {
                null
            }
        }
        val corporateId = corporateUser?.corporate?.id
        val partnerId = partnerUser?.partner?.id

        val adminAuthoritiesList = adminAuthoritiesRepository.findAllByUserId(loginAccount.id!!)
        val adminAuthorities = adminAuthoritiesList.stream().filter(AdminAuthoritiesEntity::hasAccess)
            .map(AdminAuthoritiesEntity::accessName).collect(Collectors.joining(",")) ?: ""

        val claims =
            jwtHelper.generateClaims(loginAccount, displayName, companyId, country ?: "", adminAuthorities, partnerId)

        // overriding user role and type in claims
        claims[JwtClaim.USER_TYPE.key] = userType.name
        claims[JwtClaim.ROLE.key] = userRole.name


        val refreshToken = SecurityUtil.generateMd5Hash("${userType}:${loginAccount.id}")
        try {
            loginAccount.refreshToken = refreshToken
            loginAccountRepository.saveAndFlush(loginAccount)
        } catch (ex: DataIntegrityViolationException) {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        return authService.generateTokenResult(
            claims,
            refreshToken,
            loginAccount.lastLoginDate,
            loginAccount.id,
            userRole.name,
            null,
            loginAccount
        )
    }

    @Transactional
    fun invalidateUnusedToken(userId: Long, type: ValidationType) {

        val validationTokens = validationTokenRepository.findAllByUserIdAndStateInAndType(
            userId,
            listOf(ValidationState.CREATED, ValidationState.EMAIL_SENT),
            type
        )

        if (validationTokens.isNotEmpty()) {
            validationTokens.forEach { token -> token.state = ValidationState.INVALID }
        }
    }

    @Transactional(readOnly = true)
    fun getValidTokenEntity(code: String?): ValidationTokenEntity {
        val validationToken = getValidationTokenEntity(code)
        if (isTokenExpired(validationToken)) {
            throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_EXPIRED_TOKEN)
        }

        return validationToken
    }

    @Transactional
    fun resend(userId: Long, type: ValidationType): String {
        val token = validationTokenRepository.findByUserIdAndStateAndType(userId, ValidationState.EMAIL_SENT, type)

        if (token == null) {
            val userEntity = loginAccountRepository.findByIdAndStatus(userId, AccountStatus.PENDING_VERIFICATION)
                ?: throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_NOT_FOUND)
            invalidateUnusedToken(userEntity.id!!, type)
            createToken(userEntity, type)
        } else {
            token.expiryDate = TimeUtil.fromInstant(System.currentTimeMillis() / 1000 + tokenExpiryInSeconds)
            token.state = ValidationState.CREATED
        }

        return AppConstant.SUCCESS_RESPONSE_STRING
    }
    @Transactional
    fun generateVerificationLink(userId: Long): String {

        val userEntity = loginAccountRepository.findByIdAndStatus(userId, AccountStatus.PENDING_VERIFICATION)
            ?: throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_NOT_FOUND)

        if(!(UserType.CORPORATE == userEntity.getUserType() || UserType.EXPERT == userEntity.getUserType())){
            throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
        }

        var token = validationTokenRepository.findByUserId(userId)

        if (token == null) {
            token = createToken(ValidationType.CORPORATE_SIGNUP, userEntity, ValidationState.EMAIL_SENT)
        } else {
            token.expiryDate = TimeUtil.fromInstant(System.currentTimeMillis() / 1000 + tokenExpiryInSeconds)
            validationTokenRepository.save(token)
        }
        return signUpTokenUrl(webUrl, token.code)
    }

    fun generateLoginTokenForRole(authenticatedUser: AuthenticatedUser, role: Role): TokenResult? {

        val loginAccountEntity = loginAccountRepository.findById(authenticatedUser.userId)
            .orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }

        val hasValidRole = loginAccountEntity.userRoles.map { it.role }.contains(role)
        if(!hasValidRole) {
            throw ApplicationException(ErrorCode.NOT_FOUND)
        }

        return generateLoginToken(loginAccountEntity, role)

    }

    private fun isTokenExpired(validationToken: ValidationTokenEntity): Boolean {
        return validationToken.expiryDate < LocalDateTime.now()
    }

    private fun getValidationTokenEntity(code: String?): ValidationTokenEntity {

        if (code.isNullOrBlank()) {
            throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_INVALID_TOKEN)
        }

        return validationTokenRepository.findByCodeAndState(
            code,
            ValidationState.EMAIL_SENT
        ) ?: throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_INVALID_TOKEN)

    }

    fun extendTokenValidity(code: String): Boolean {

        val validationToken = getValidationTokenEntity(code)
        if (isTokenExpired(validationToken)) {
            validationToken.expiryDate = LocalDateTime.now().plusHours(24)
            validationToken.state = ValidationState.CREATED
            validationTokenRepository.save(validationToken)
            return true
        }
        return false
    }
}