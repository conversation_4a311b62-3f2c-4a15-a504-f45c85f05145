package com.centuroglobal.service

import com.centuroglobal.service.travel.TravelHistoryService
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.FeatureSessionEntity
import com.centuroglobal.shared.data.entity.travel.TravelAssessmentEntity
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.travel.TravelAssessmentStatus
import com.centuroglobal.shared.data.enums.travel.TravelStayUnit
import com.centuroglobal.shared.data.payload.travel.AssessmentCaseGenerateRequest
import com.centuroglobal.shared.data.payload.travel.AssessmentDossierGenerateRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.CaseFormSearchFilter
import com.centuroglobal.shared.data.pojo.travel.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.BaseRepository
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.FeatureSessionRepository
import com.centuroglobal.shared.repository.PassportVisaRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentLogViewRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentTrackingRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

private val log = mu.KotlinLogging.logger {}

@Service
class TravelAssessmentService(
    private val travelAssessmentRepository: BaseRepository<TravelAssessmentEntity, Long>,
    private val travelAssessmentTrackingRepository: TravelAssessmentTrackingRepository,
    private val userProfileUtil: UserProfileUtil,
    private val objectMapper: ObjectMapper,
    private val pyApiClient: PythonApiClient,
    private val travelHistoryService: TravelHistoryService,
    private val passportVisaRepository: PassportVisaRepository,
    private val caseRepository: CaseRepository,
    private val travelAssessmentLogViewRepository: TravelAssessmentLogViewRepository,
    private val featureSessionRepository: FeatureSessionRepository,
    private val caseFormService: CaseFormService

) : BaseService<TravelAssessmentEntity, Long>() {

    private val caseToAssessmentMap = mapOf(
        "BUSINESS_VISA" to "BUSINESS_VISA",
        "ENTRY_VISA" to "ENTRY_VISA",
        "POSTED_WORKER_NOTIFICATION" to "PWN_CHECK",
        "SINGLE_VISA" to "WORK_PERMIT",
        "RIGHT_TO_WORK_CHECK" to "RTW_CHECK"
    )

    @Transactional(readOnly = true)
    fun listTravelAssessmentTracking(
        assessmentId: Long
    ): List<TravelAssessmentTrackingDetails> {

        val tracking = travelAssessmentTrackingRepository.findByAssessmentId(assessmentId)

        return tracking.map { TravelAssessmentTrackingDetails(
            id = it.id,
            step = it.step,
            request = it.request,
            response = it.response,
            createdDate = TimeUtil.toEpochMillis(it.createdDate),
            updatedDate = TimeUtil.toEpochMillis(it.lastUpdatedDate),
            createdBy = it.createdBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) },
            updatedBy = it.updatedBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) }
        ) }
    }

    @Transactional
    @Suppress("UNCHECKED_CAST")
    fun get(assessmentId: Long, authenticatedUser: AuthenticatedUser, sessionId: String?): Map<String, Any?> {
        val assessment = getAssessment(assessmentId, authenticatedUser)

        val response = parseData(assessment.data).toMutableMap()

        val companyName = assessment.user.corporate.name

        response["caseId"] = caseRepository.findByAssessmentId(assessmentId)?.id
        val metadata = (response["message"] as Map<String, Any?>)["data"] as MutableMap<String, Any?>
        (metadata["metadata"] as MutableMap<String, Any>)["companyName"] = companyName

        // create log
        val session = featureSessionRepository.save(
            FeatureSessionEntity(
                referenceId = assessmentId.toString(),
                startTime = LocalDateTime.now(),
                referenceType = "TRAVEL_ASSESSMENT",
                sessionId = sessionId ?: UUID.randomUUID().toString(),
                type = "VIEW"
            )
        )
        response["viewSessionId"] = session.sessionId

        return response
    }

    private fun parseData(data: String): Map<String, Any?> {
        val reader = objectMapper.readerFor(Map::class.java)
        return reader.readValue(data)
    }

    @Transactional(readOnly = true)
    fun listTravelAssessments(
        searchFilter: TravelAssessmentSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<TravelAssessmentDetails> {

        searchFilter.loggedInUserCorporateId = authenticatedUser.companyId
        val assessments = super.list(searchFilter, authenticatedUser, pageRequest, travelAssessmentRepository, "USER")

        //val assessments = travelAssessmentRepository.searchByCriteriaForFullAccess(searchFilter, pageRequest)

        return toTravelAssessmentDetails(assessments)
    }

    private fun toTravelAssessmentDetails(assessments: Page<TravelAssessmentEntity>): PagedResult<TravelAssessmentDetails> {

        val travelAssessmentsList = getTravelAssessmentList(assessments.toList())

        return PagedResult.ModelMapper.from(assessments, travelAssessmentsList)
    }

    private fun getTravelAssessmentList(assessments: List<TravelAssessmentEntity>) : List<TravelAssessmentDetails>{

        val assessmentDetails = assessments.map {

            TravelAssessmentDetails(
                id = it.id,
                originCountry = it.originCountry,
                destinationCountry = it.destinationCountry,
                periodOfStay = it.periodOfStay,
                stayUnit = TravelStayUnit.valueOf(it.stayUnit.toString()).toString(),
                purpose = it.purpose.split(","),
                createdOn = TimeUtil.toEpochMillis(it.createdDate),
                applicant = it.user.id?.let { it1 -> userProfileUtil.retrieveProfile(it1, it.user.corporate.name) },
                createdBy = "${it.createdByUser!!.firstName} ${it.createdByUser!!.lastName}"
            )
        }
        return assessmentDetails.toList()
    }

    fun getAssessment(assessmentId: Long, authenticatedUser: AuthenticatedUser): TravelAssessmentEntity {
        val assessment = super.get(assessmentId, authenticatedUser, travelAssessmentRepository, "USER")
        if(assessment.status == TravelAssessmentStatus.IN_PROGRESS) {
            log.error("Assessment with id: $assessmentId is not completed")
            throw ApplicationException(ErrorCode.NOT_FOUND)
        }
        return assessment
    }

    fun delete(assessmentId: Long, authenticatedUser: AuthenticatedUser): Boolean {
        val assessment = getAssessment(assessmentId, authenticatedUser)
        travelAssessmentRepository.delete(assessment)
        return true
    }

    @Suppress("UNCHECKED_CAST")
    fun generateCase(assessmentId: Long, authenticatedUser: AuthenticatedUser, caseType: String?=null, caseFormId: Long?=null): Map<String, Any?> {

        val assessment = getAssessment(assessmentId, authenticatedUser)
        val data = parseData(assessment.data)

        val managerId = assessment.user.managers.firstOrNull()?.managerId

        val metadata = (data["message"] as Map<String, Any?>)["data"] as MutableMap<String, Any?>

        val filter = TravelHistorySearchFilter(
            destinationCountry = assessment.destinationCountry,
            from = LocalDateTime.now().minusMonths(12)
        )

        val lastYearHistory = travelHistoryService.list(
            filter, PageRequest.of(
                0,
                Int.MAX_VALUE,
                SearchConstant.SORT_ORDER("createdDate", "DESC")
            ), assessment.user.id!!, authenticatedUser
        )

        // add travel history
        (metadata["metadata"] as MutableMap<String, Any?>)["travel_history"] = lastYearHistory.rows.map {
            mapOf(
                "destination_country" to it.destinationCountry,
                "origin_country" to it.originCountry,
                "period_of_stay" to it.periodOfStay,
                "stay_unit" to it.stayUnit.name,
            )
        }

        // add passport details
        val passports = passportVisaRepository.getDocByUserAndDocType(assessment.user, "passport")

        val passportDetails = passports.map {
            mapOf(
                "nationality" to it.nationality,
                "expiry_date" to it.expiryDate
            )
        }
        (metadata["metadata"] as MutableMap<String, Any?>)["passport_details"] = passportDetails

        // add salary details
        (metadata["metadata"] as MutableMap<String, Any?>)["salary_details"] = assessment.user.salary

        val assessmentType = caseToAssessmentMap.getOrDefault(caseType, data["key"].toString())

        if (caseType!=null && caseType == "DYNAMIC_CASE") {

            val filter = CaseFormSearchFilter.Builder.build(
                null,
                null,
                assessment.destinationCountry,
                CaseFormStatus.ACTIVE,
                null,
                null,
                authenticatedUser.partnerId,
                authenticatedUser.partnerId != null
            )
            val pageRequest = PageRequest.of(0, Int.MAX_VALUE)

            val page = caseFormService.list(filter, pageRequest, authenticatedUser)
            val caseForms = page.rows.filter { !it.isDefault }.map { mapOf("id" to it.id!!, "name" to it.name, "description" to it.description) }

            val request = AssessmentCaseGenerateRequest(
                assessmentData = metadata,
                caseType = assessmentType,
                contactPerson = managerId ?: assessment.user.id!!,
                caseForms = caseForms,
                caseFormId = caseFormId
            )
            return pyApiClient.generateCaseUsingForm(request)
        }
        else {
            val request = AssessmentCaseGenerateRequest(
                assessmentData = metadata,
                caseType = assessmentType,
                contactPerson = managerId ?: assessment.user.id!!,
                caseFormId = null
            )
            return pyApiClient.generateCase(request)
        }

    }

    fun logs(searchFilter: TravelAssessmentLogsSearchFilter, pageRequest: PageRequest): PagedResult<TravelAssessmentLogsResponse>? {

        val page = travelAssessmentLogViewRepository.searchByCriteria(searchFilter, pageRequest)

        return PagedResult.ModelMapper.from(
            page,
            page.map { TravelAssessmentLogsResponse.ModelMapper.from(it) }.toList()
        )
    }

    @Suppress("UNCHECKED_CAST")
    fun generateDossier(assessmentId: Long, authenticatedUser: AuthenticatedUser): String {

        val assessment = getAssessment(assessmentId, authenticatedUser)

        val dossierData = assessment.dossierData
        if(!dossierData.isNullOrBlank()) {
            log.debug("Dossier data already exists for assessment id: $assessmentId hence returning it")
            return dossierData
        }

        log.debug("Generating dossier for assessment id: $assessmentId")

        val data = parseData(assessment.data)

        val metadata = (data["message"] as Map<String, Any?>)["data"] as MutableMap<String, Any?>

        val filter = TravelHistorySearchFilter(
            destinationCountry = assessment.destinationCountry,
            from = LocalDateTime.now().minusMonths(12)
        )

        val lastYearHistory = travelHistoryService.list(
            filter, PageRequest.of(
                0,
                Int.MAX_VALUE,
                SearchConstant.SORT_ORDER("createdDate", "DESC")
            ), assessment.user.id!!, authenticatedUser
        )

        // add travel history
        (metadata["metadata"] as MutableMap<String, Any?>)["travel_history"] = lastYearHistory.rows.map {
            mapOf(
                "destination_country" to it.destinationCountry,
                "origin_country" to it.originCountry,
                "period_of_stay" to it.periodOfStay,
                "stay_unit" to it.stayUnit.name,
            )
        }

        // add passport details
        val passports = passportVisaRepository.getDocByUserAndDocType(assessment.user, "passport")

        val passportDetails = passports.map {
            mapOf(
                "nationality" to it.nationality,
                "expiry_date" to it.expiryDate
            )
        }
        (metadata["metadata"] as MutableMap<String, Any?>)["passport_details"] = passportDetails

        // add salary details
        (metadata["metadata"] as MutableMap<String, Any?>)["job_title"] = assessment.user.jobTitle

        val visaType = when(data["key"].toString()) {
            "PWN_CHECK" -> "Posted worker notification"
            "BUSINESS_VISA" -> "Business Visa"
            "WORK_PERMIT" -> "Work Permit"
            else -> null
        }

        (metadata["metadata"] as MutableMap<String, Any?>)["recommended_visa"] = visaType

        val request = AssessmentDossierGenerateRequest(
            assessmentData = metadata
        )
        val dossier = pyApiClient.generateDossier(request)
        assessment.dossierData = dossier
        travelAssessmentRepository.save(assessment)
        return dossier
    }

}