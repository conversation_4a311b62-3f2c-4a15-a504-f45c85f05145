package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.data.payload.UserActionRequest
import com.centuroglobal.shared.data.pojo.UserActionResult
import com.centuroglobal.shared.repository.UserActionRepository
import com.centuroglobal.shared.util.TimeUtil
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service
class UserActionService(
    private val userActionRepository: UserActionRepository
) {

    @Transactional
    fun addList(request: List<UserActionRequest>, userId: Long): Bo<PERSON>an {
        try {
            val actionList: MutableList<UserActionEntity> = mutableListOf()
            request.forEach {
                val endTime = if (it.endTime != null) TimeUtil.fromInstantMillis(it.endTime) else null
                actionList.add(
                    UserActionEntity(
                        location = it.location,
                        browser = it.browser,
                        startTime = TimeUtil.fromInstantMillis(it.startTime!!),
                        endTime = endTime,
                        os = it.os,
                        additionalData = it.additionalData,
                        action = it.action,
                        source = it.source,
                        userId = userId,
                        createdBy = userId,
                        sessionId = it.sessionId
                    )
                )
            }
            userActionRepository.saveAll(actionList)
            return true
        } catch (ex: Exception) {
            throw ex
        }
    }

    @Transactional
    fun create(request: UserActionResult): UserActionEntity {
        try {
            val action = UserActionEntity(
                location = request.location,
                browser = request.browser,
                startTime = request.startTime,
                endTime = request.endTime,
                os = request.os,
                additionalData = request.additionalData,
                action = request.action,
                source = request.source,
                userId = request.userId,
                createdBy = request.createdBy,
                sessionId = request.sessionId
            )
            return userActionRepository.save(action)
        } catch (ex: Exception) {
            throw ex
        }
    }

    fun retrieve(): MutableList<UserActionEntity> {
        return userActionRepository.findAll()
    }
}
