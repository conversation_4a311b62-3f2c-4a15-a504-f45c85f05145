package com.centuroglobal.service.auth

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.LoginHistoryEntity
import com.centuroglobal.shared.data.entity.auth.MfaTokenEntity
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginHistoryRepository
import com.centuroglobal.shared.repository.auth.MfaTokenRepository
import com.centuroglobal.shared.service.GenericEmailService
import com.centuroglobal.shared.util.PartnerEmailUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}
private const val MFA_OTP_MAIL_TEMPLATE = "email/mfa_otp_email"


@Service
class MfaService(
    @Value("\${app.authentication.mfa.token-expiry-in-secs}")
    private val tokenExpiryInSeconds: Long,

    @Value("\${app.authentication.mfa.static-token:}")
    private val staticToken: String,

    private val mfaTokenRepository: MfaTokenRepository,
    private val loginHistoryRepository: LoginHistoryRepository,
    private val genericEmailService: GenericEmailService,
    private val partnerEmailUtil: PartnerEmailUtil
) {

    fun validateMfa(mfaCode: String?, loginAccountEntity: LoginAccountEntity, loginHistoryEntity: LoginHistoryEntity) {
        val userId = loginAccountEntity.id!!
        if(mfaCode.isNullOrBlank()) {
            if(isMfaRequired(userId, loginHistoryEntity)) {

                val tokens = mfaTokenRepository.findByUserIdAndIsValidated(userId, false)
                val currentTime = LocalDateTime.now()
                var tokenEntity = tokens.firstOrNull { it.expiryDate > currentTime }

                if (tokenEntity == null) {
                    tokenEntity = MfaTokenEntity(
                        userId = userId,
                        code = generateCode(),
                        expiryDate = LocalDateTime.now().plusSeconds(tokenExpiryInSeconds)
                    )
                }
                else {
                    tokenEntity.expiryDate = LocalDateTime.now().plusSeconds(tokenExpiryInSeconds)
                }
                mfaTokenRepository.save(tokenEntity)

                //send email
                sendEmail(loginAccountEntity, tokenEntity.code)

                throw ApplicationException(ErrorCode.MFA_REQUIRED)
            }
        }
        else {
            // validate mfaCode
            val tokenEntity =
                mfaTokenRepository.findByUserIdAndCodeAndIsValidated(loginAccountEntity.id!!, mfaCode, false)

            //constant otp for qat/uat
            if (tokenEntity == null && mfaCode == staticToken) {
                return
            }
            else if (tokenEntity == null) {
                throw ApplicationException(ErrorCode.TOKEN_INVALID)
            }

            if (tokenEntity.expiryDate < LocalDateTime.now()) {
                throw ApplicationException(ErrorCode.EMAIL_VERIFICATION_EXPIRED_TOKEN)
            }

            //token matched
            tokenEntity.isValidated = true
            mfaTokenRepository.save(tokenEntity)
        }
    }

    @Transactional
    private fun sendEmail(loginAccountEntity: LoginAccountEntity, code: String) {
        try {

            val variableMap = mutableMapOf(
                "DISPLAY_NAME" to "${loginAccountEntity.firstName} ${loginAccountEntity.lastName}",
                "OTP" to code
            )

            partnerEmailUtil.updateMap(variableMap, loginAccountEntity)

            genericEmailService.sendEmail(
                subject = "Centuro Login OTP",
                templateName = MFA_OTP_MAIL_TEMPLATE,
                variableMap = variableMap,
                recipient = listOf(loginAccountEntity.email)
            )
        }
        catch (ex: Exception) {
            log.error("Error occurred while sending otp mail", ex)
        }
    }

    private fun generateCode(): String {
        return (100000 until 999999).random().toString()
    }

    private fun isMfaRequired(userId: Long, currentLoginHistory: LoginHistoryEntity): Boolean {

        val sort = SearchConstant.SORT_ORDER("createdDate", "DESC")

        val loginHistoryList = loginHistoryRepository.findByUserIdAndLoginSuccess(userId, true, PageRequest.of(0, 3, sort))

        for (loginHistory in loginHistoryList) {
            if (loginHistory.browser != currentLoginHistory.browser) {
                return true
            }
            if(loginHistory.os != currentLoginHistory.os) {
                return true
            }
            if (loginHistory.city != currentLoginHistory.city) {
                return true
            }
            if(loginHistory.country != currentLoginHistory.country) {
                return true
            }
        }
        return false
    }

}