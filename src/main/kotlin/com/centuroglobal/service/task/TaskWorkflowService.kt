package com.centuroglobal.service.task

import com.centuroglobal.service.BaseService
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.CaseMilestonesEntity
import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.entity.task.TaskWorkflowEntity
import com.centuroglobal.shared.data.enums.CaseStatus
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.TaskAssigneeType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.task.*
import com.centuroglobal.shared.data.pojo.CaseMilestoneResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.CaseMilestoneChart
import com.centuroglobal.shared.data.pojo.task.dto.TaskWorkflowSearchFilter
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskTemplateRequest
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskWorkflowRequest
import com.centuroglobal.shared.data.pojo.task.request.ReorderTaskRequest
import com.centuroglobal.shared.data.pojo.task.response.TaskTemplateDetails
import com.centuroglobal.shared.data.pojo.task.response.TaskWorkflowDetails
import com.centuroglobal.shared.data.pojo.task.response.TaskWorkflowReport
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.task.TaskTemplateRepository
import com.centuroglobal.shared.repository.task.TaskWorkflowRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import mu.KotlinLogging
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalUnit
import kotlin.jvm.optionals.getOrNull

private val log = KotlinLogging.logger {}

@Service
class TaskWorkflowService(
    private val taskWorkflowRepository: TaskWorkflowRepository,
    private val taskTemplateRepository: TaskTemplateRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateUserRepository: CorporateUserRepository,
    private val taskAssigneeRepository: TaskAssigneeRepository,
    private val caseRepository: CaseRepository,
    private val taskRepository: TaskRepository,
    private val partnerRepository: PartnerRepository,
    private val pyApiClient: PythonApiClient,
    private val userProfileUtil: UserProfileUtil,
    private val caseMilestonesRepository: CaseMilestonesRepository,
    private val caseStatusHistoryRepository: CaseStatusHistoryRepository

) : BaseService<TaskWorkflowEntity, Long>() {

    fun create(request: CreateTaskWorkflowRequest, authenticatedUser: AuthenticatedUser): Long {

        val partner = authenticatedUser.partnerId?.let { partnerRepository.getReferenceById(it) }
        val workflowEntity = TaskWorkflowEntity(
            name = request.name,
            description = request.description,
            country = request.country,
            status = request.status,
            category = request.category,
            partner = partner,
            showToPartner = request.showToPartner,
            taskGenerationStatus = TaskGenerationStatus.NOT_STARTED
        )
        val savedEntity = taskWorkflowRepository.save(workflowEntity)
        return savedEntity.id!!
    }


    @Transactional
    fun updateTaskWorkflow(
        taskWorkFLowId: Long,
        request: CreateTaskWorkflowRequest, authenticatedUser: AuthenticatedUser
    ): Long {

        val taskWorkflowEntity = fetchTaskWorkflowEntity(taskWorkFLowId, authenticatedUser)

        taskWorkflowEntity.name = request.name
        taskWorkflowEntity.description = request.description
        taskWorkflowEntity.country = request.country
        taskWorkflowEntity.status = request.status
        taskWorkflowEntity.category = request.category
        taskWorkflowEntity.showToPartner = request.showToPartner

        taskWorkflowRepository.save(taskWorkflowEntity)

        return taskWorkflowEntity.id?.let { taskWorkflowEntity.id }
            ?: throw ApplicationException(ErrorCode.INTERNAL_SERVER_ERROR)
    }

    @Transactional
    fun createTask(
        authenticatedUser: AuthenticatedUser,
        taskWorkFlowId: Long,
        caseId: Long,
        startDate: Long
    ): List<Long> {
        val taskWorkflowEntity = fetchTaskWorkflowEntity(taskWorkFlowId, authenticatedUser)

        if (!(taskWorkflowEntity.referenceId==caseId && taskWorkflowEntity.referenceType == ReferenceType.CASE.name)) {
            log.error("The workflow id $taskWorkFlowId is not linked with case: $caseId")
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        log.debug("Creating tasks for workflow id: $taskWorkFlowId")

        val listIds: MutableList<Long> = emptyList<Long>().toMutableList()

        val taskTemplates = taskWorkflowEntity.taskTemplates

        val caseEntity = caseRepository.findByIdOrNull(caseId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        var firstTask = true

        // if start date is weekend then take next working day as start date
        val startDateInLocalDateTime = TimeUtil.fromInstantMillis(startDate)
        val workingStartDate =  if (TimeUtil.isWorkingDay(startDateInLocalDateTime)) {
            startDate
        }
        else {
            TimeUtil.toEpochMillis(TimeUtil.getNthWorkingDay(startDateInLocalDateTime, 1))
        }

        var nextTaskStartDate: Long = 0

        taskTemplates.sortedBy { it.displayOrder }. forEach {

            val taskEntity = saveTask(caseEntity, it, authenticatedUser,
                if(firstTask) workingStartDate else nextTaskStartDate, firstTask)

            firstTask = false;

            nextTaskStartDate = taskEntity.dueDate?.let { it1 -> TimeUtil.toEpochMillis(TimeUtil.getNthWorkingDay(it1, 1)) }!!

            taskEntity.id?.let { it1 -> listIds.add(it1) }
            it.task = taskEntity
        }
        caseEntity.startDate = TimeUtil.fromInstantMillis(workingStartDate)
        caseRepository.save(caseEntity)
        taskWorkflowRepository.save(taskWorkflowEntity)
        populateTaskTimelines(taskTemplates, workingStartDate)
        return listIds
    }

    private fun populateTaskTimelines(taskTemplates: MutableList<TaskTemplateEntity>, startDate: Long) {

        taskTemplates.sortBy { it.displayOrder }
        var plannedStartDate = TimeUtil.fromInstantMillis(startDate)
        var isFirstTask = true
        val tasks = mutableListOf<TaskEntity>()

        val filteredTemplates = if (taskTemplates.any { it.task!!.startDate!=null }) {

            for (template in taskTemplates) {
                if (TaskStatus.IN_PROGRESS == template.task!!.status) {
                    plannedStartDate = template.task!!.dueDate!!
                    isFirstTask = false
                }
                else if (TaskStatus.COMPLETED == template.task!!.status) {
                    plannedStartDate = template.task!!.dueDate!!
                    isFirstTask = true
                }
            }
            plannedStartDate = TimeUtil.getNthWorkingDay(plannedStartDate, 1)

            // timelines are present already and needs to be recalculated
            taskTemplates.filter { it.task!!.status == TaskStatus.NOT_STARTED }
        }
        else {
            // timelines are not populated.
            taskTemplates
        }

        filteredTemplates.forEach {

            it.task!!.plannedStartDate = plannedStartDate
            it.task!!.expectedDueDate = TimeUtil.getNthWorkingDay(plannedStartDate, it.expectedTimeline-1)
            it.task!!.dueDate = it.task!!.expectedDueDate
            if (isFirstTask) {
                if (it.task!!.startDate==null) {
                    it.task!!.startDate = plannedStartDate
                }
            } else {
                it.task!!.startDate = null
            }
            plannedStartDate = TimeUtil.getNthWorkingDay(plannedStartDate, it.expectedTimeline)
            isFirstTask = false
            tasks.add(it.task!!)
        }
        taskRepository.saveAll(tasks)
    }

    private fun saveTask(
        caseEntity: CaseEntity,
        taskTemplateEntity: TaskTemplateEntity,
        authenticatedUser: AuthenticatedUser,
        startDate: Long,
        firstTask: Boolean
    ): TaskEntity {

        val companyId = corporateUserRepository.findByIdOrNull(caseEntity.createdBy!!.userId)?.corporate!!.id ?: 0


        var taskEntity = taskRepository.save(
            TaskEntity(
                name = taskTemplateEntity.name,
                description = taskTemplateEntity.description ?: "",
                visibility = taskTemplateEntity.visibility?.let { it1 -> TaskVisibility.valueOf(it1.uppercase()) },
                referenceType = ReferenceType.WORKFLOW,
                referenceId = caseEntity.id!!,
                caseMilestone = taskTemplateEntity.caseMilestone,
                progress = taskTemplateEntity.progress,
                caseStatus = taskTemplateEntity.caseStatus,
                usefulLinks = taskTemplateEntity.usefulLinks,
                instruction = taskTemplateEntity.instruction,
                dueDate = calculateDueDate(
                    TimeUtil.fromInstantMillis(startDate), taskTemplateEntity.expectedTimeline-1, taskTemplateEntity.expectedTimelineUnit
                ),
                priority = taskTemplateEntity.priority,
                //TODO: task status
                status = if(firstTask) TaskStatus.IN_PROGRESS else TaskStatus.NOT_STARTED,
                companyId = companyId,
                companyType = TaskCompanyType.CORPORATE,
                partner = authenticatedUser.partnerId?.let { partnerRepository.getReferenceById(it) },
                plannedStartDate = null,
                startDate = null,
                completedDate = null,
                inProgressDate = if(firstTask) LocalDateTime.now() else null
            )
        )
        val userId = saveTaskAssignee(taskEntity, caseEntity, taskTemplateEntity, companyId)
        val idAndType = getCompanyIdAndType(userId!!)
        taskEntity.companyId = idAndType.first
        taskEntity.companyType = idAndType.second
        taskEntity.createdBy = caseEntity.managers[0].id
        taskEntity.updatedBy = caseEntity.managers[0].id
        taskEntity = taskRepository.save(taskEntity)
        return taskEntity
    }

    public fun getCompanyIdAndType(userId: Long): Pair<Long, TaskCompanyType> {
        val user = loginAccountRepository.findById(userId).get()
        return if (AdminAccessUtil.isPartner(user) && user.partner!=null) {
            Pair(user.partner!!.id!!, TaskCompanyType.PARTNER)
        } else if (user.role == Role.ROLE_SUPER_ADMIN || user.role == Role.ROLE_ADMIN) {
            Pair(0, TaskCompanyType.CG)
        } else if (user.role == Role.ROLE_EXPERT ||  user.role == Role.ROLE_SUPPLIER) {
            Pair((user as ExpertUserEntity).companyProfile!!.id!!, TaskCompanyType.EXPERT)
        } else if (user.getUserType() == UserType.CORPORATE) {
            Pair((user as CorporateUserEntity).corporate.id!!, TaskCompanyType.CORPORATE)
        } else {
            throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
        }

    }

    private fun saveTaskAssignee(
        task: TaskEntity, case: CaseEntity, taskTemplate: TaskTemplateEntity,
        companyId: Long
    ): Long? {
        var assigneeUserId: Long? = null

        when (taskTemplate.assignedTo.uppercase()) {

            "CASE_MANAGER" -> {
                if (case.managers.isEmpty()) {
                    log.error("No managers assigned for case: ${case.id}")
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                }
                assigneeUserId = case.managers[0].id!!
                case.managers.forEach {
                    val assignee = loginAccountRepository.findById(it.id!!).get()
                    log.info("Assignee: ${assignee.id}")
                    log.info("Task: ${task.id}")
                    log.info("corporatePartnerId: ${getCorporatePartnerId(assignee)}")
                    taskAssigneeRepository.save(
                        TaskAssigneeEntity(
                            task = task,
                            assigneeId = assignee,
                            type = TaskAssigneeType.TEAM,
                            companyId = null,
                            corporatePartnerId = getCorporatePartnerId(assignee)
                        )
                    )
                }
            }

            "APPLICANT" -> {

                val caseOwner = loginAccountRepository.findById(case.createdBy!!.userId).get()
                assigneeUserId = caseOwner.id
                taskAssigneeRepository.save(
                    TaskAssigneeEntity(
                        task = task,
                        assigneeId = caseOwner,
                        //TODO
                        type = TaskAssigneeType.CORPORATE,
                        companyId = companyId,
                        corporatePartnerId = getCorporatePartnerId(caseOwner)
                    )
                )
            }

            "CLIENT" -> {

                val corporateUser = corporateUserRepository.findById(case.createdBy!!.userId).get()

                val managerIds = corporateUser.managers.map {
                    it.managerId
                }.toMutableList()

                if (managerIds.isEmpty()) {
                    managerIds.add(corporateUser.id!!)
                }
                assigneeUserId = managerIds[0]

                managerIds.map {

                    val manager = loginAccountRepository.findById(it).get()

                    taskAssigneeRepository.save(
                        TaskAssigneeEntity(
                            task = task,
                            assigneeId = manager,
                            //TODO
                            type = TaskAssigneeType.CORPORATE,
                            companyId = companyId,
                            corporatePartnerId = getCorporatePartnerId(manager)
                        )
                    )
                }
            }
        }
        return assigneeUserId
    }

    private fun calculateDueDate(startDate: LocalDateTime, expectedTimeline: Long, expectedTimelineUnit: String):
            LocalDateTime {
        val unit: TemporalUnit = when (expectedTimelineUnit.uppercase()) {
            "DAYS" -> ChronoUnit.DAYS
            "WEEKS" -> ChronoUnit.WEEKS
            "MONTHS" -> ChronoUnit.MONTHS
            "YEARS" -> ChronoUnit.YEARS
            else -> throw IllegalArgumentException("Invalid timeline unit: $expectedTimelineUnit")
        }

        return TimeUtil.getNthWorkingDay(startDate, ChronoUnit.DAYS.between(startDate,
            startDate.plus(expectedTimeline, unit)))
    }


    private fun getCorporatePartnerId(user: LoginAccountEntity): Long {
        return if(user is CorporateUserEntity) {
            user.corporate.partner?.id?:-1
        }
        else {
            -1
        }
    }

    @Transactional
    fun copyTaskWorkflow(
        taskWorkflowId: Long,
        authenticatedUser: AuthenticatedUser
    ): Long {

        val taskWorkflowEntity = fetchTaskWorkflowEntityForView(taskWorkflowId, authenticatedUser)

        val copiedTaskWorkflow = copyTaskWorkflow(taskWorkflowEntity)

        copiedTaskWorkflow.name = "Copy-"+taskWorkflowEntity.name

        copiedTaskWorkflow.partner = authenticatedUser.partnerId?.let { partnerRepository.getReferenceById(it) }

        val savedEntity = taskWorkflowRepository.save(copiedTaskWorkflow)

        return savedEntity.id!!
    }

    @Transactional
    private fun copyTaskWorkflow(taskWorkflowEntity: TaskWorkflowEntity): TaskWorkflowEntity {
        val copiedWorkflowEntity = TaskWorkflowEntity(
            name = taskWorkflowEntity.name,
            description = taskWorkflowEntity.description,
            country = taskWorkflowEntity.country,
            category = taskWorkflowEntity.category,
            status = taskWorkflowEntity.status,
            taskGenerationStatus = TaskGenerationStatus.NOT_STARTED
        )
        copiedWorkflowEntity.taskTemplates = taskWorkflowEntity.taskTemplates.map {
            TaskTemplateEntity(
                name = it.name,
                description = it.description,
                expectedTimeline = it.expectedTimeline,
                priority = it.priority,
                expectedTimelineUnit = it.expectedTimelineUnit,
                visibility = it.visibility,
                instruction = it.instruction,
                assignedTo = it.assignedTo,
                caseStatus = it.caseStatus,
                caseMilestone = it.caseMilestone,
                usefulLinks = it.usefulLinks,
                progress = it.progress,
                displayOrder = it.displayOrder,
                workflow = copiedWorkflowEntity
            )
        }.toMutableList()

        return copiedWorkflowEntity
    }

    @Transactional(readOnly = true)
    fun list(
        searchFilter: TaskWorkflowSearchFilter,
        pageRequest: PageRequest,
        authenticatedUser: AuthenticatedUser
    ): PagedResult<TaskWorkflowDetails> {

        if (searchFilter.isPartner == false && AdminAccessUtil.isPartner(authenticatedUser)) {
            searchFilter.showToPartner = true
        }

        val workflows = super.list(searchFilter, authenticatedUser, pageRequest, taskWorkflowRepository, "TASK")

        val workflowDetailsList = workflows.toList().map {
            TaskWorkflowDetails.ModelMapper.from(it)
        }
        return PagedResult.ModelMapper.from(workflows, workflowDetailsList)
    }


    fun getGeneratedTasks(taskWorkflowEntity: TaskWorkflowEntity,
                                    authenticatedUser: AuthenticatedUser): List<TaskTemplateDetails> {

        val listTaskTemplates = taskTemplateRepository.findByWorkflow(taskWorkflowEntity)

        val templateDetails = listTaskTemplates?.map { TaskTemplateDetails.ModelMapper.from(it) }

        return if(templateDetails.isNullOrEmpty())
            emptyList()
        else templateDetails
    }


    fun resetTaskGenerationStatus(taskWorkflowId: Long, authenticatedUser: AuthenticatedUser) {

        val taskWorkflowEntity = fetchTaskWorkflowEntity(taskWorkflowId, authenticatedUser)

        updateTaskWorkflowStatus(taskWorkflowEntity, TaskGenerationStatus.NOT_STARTED, authenticatedUser)
    }

    @Async
    fun createTaskTemplate(taskWorkflowId: Long, authenticatedUser: AuthenticatedUser) {

        val taskWorkflowEntity = fetchTaskWorkflowEntity(taskWorkflowId, authenticatedUser)

        val request = CreateTaskWorkflowRequest(
            name = taskWorkflowEntity.name,
            description = taskWorkflowEntity.description,
            status = taskWorkflowEntity.status,
            category = taskWorkflowEntity.category,
            country = taskWorkflowEntity.country
        )

        updateTaskWorkflowStatus(taskWorkflowEntity, TaskGenerationStatus.IN_PROGRESS, authenticatedUser)

        val listTasksRequests: List<CreateTaskTemplateRequest>

        try {

            listTasksRequests = pyApiClient.createTaskTemplate(request)

            listTasksRequests.map { it.expectedTimelineUnit = "DAYS" }

        } catch (exeption: Exception) {

            updateTaskWorkflowStatus(taskWorkflowEntity, TaskGenerationStatus.FAILED, authenticatedUser)

            log.info { "Exception while generating tasks $exeption" }

            throw ApplicationException(ErrorCode.TASK_GENERATION_EXCEPTION)
        }

        if (!listTasksRequests.isNullOrEmpty()) {

            var firstTask = true

            var dispalyOrderVar: Long = 0

            listTasksRequests.forEach {
                it.displayOrder = dispalyOrderVar++
                it.visibility = TaskVisibility.PUBLIC.toString()

                createTaskTemplate(taskWorkflowId, it, firstTask, authenticatedUser)

                firstTask = false
            }

            updateTaskWorkflowStatus(taskWorkflowEntity, TaskGenerationStatus.COMPLETED, authenticatedUser)
        }
    }

    @Transactional
    fun removeTaskTemplatesForWorkflow(taskWorkflowEntity: TaskWorkflowEntity){

        taskWorkflowEntity.taskTemplates.clear()

        taskWorkflowRepository.saveAndFlush(taskWorkflowEntity)

        taskTemplateRepository.deleteByWorkflow(taskWorkflowEntity)
    }

    @Async
    @Transactional
    fun updateTaskWorkflowStatus(taskWorkflowEntity: TaskWorkflowEntity,
                                 taskGenerationStatus: TaskGenerationStatus, authenticatedUser: AuthenticatedUser) {

        taskWorkflowEntity.taskGenerationStatus = taskGenerationStatus

        taskWorkflowRepository.saveAndFlush(taskWorkflowEntity)

    }

    @Transactional
    fun createTaskTemplate(id: Long, request: CreateTaskTemplateRequest,
                           firstTask: Boolean, authenticatedUser: AuthenticatedUser): Long? {

        var isFirstTask = firstTask

        val workflow = fetchTaskWorkflowEntity(id, authenticatedUser)
        val canAdd =  canAddTask(workflow, request)
        if (!canAdd) {
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }

        val taskTemplateEntity = TaskTemplateEntity(
            name = request.taskName,
            description = request.description,
            caseStatus = request.caseStatus,
            caseMilestone = request.caseMilestone,
            assignedTo = request.assignedTo,
            expectedTimeline = request.expectedTimeline,
            expectedTimelineUnit = request.expectedTimelineUnit ?: "DAYS",
            instruction = request.instruction?:"",
            progress = request.progress,
            usefulLinks = request.usefulLinks,
            visibility = request.visibility?:"",
            displayOrder = request.displayOrder,
            workflow = workflow,
            priority = request.priority
        )

        //this is required since audit config fields won't be injected with interceptor being a new thread + mandatory db field
        taskTemplateEntity.createdBy = authenticatedUser.userId

        val templates = mutableListOf<TaskTemplateEntity>()
        templates.addAll(workflow.taskTemplates)

        // reorder tasks if tasks is not added in the end.
        val needReordering = templates.firstOrNull { it.displayOrder == request.displayOrder }
        if (needReordering!=null) {
            templates.filter { it.displayOrder!! >= request.displayOrder!! }
                .forEach { it.displayOrder = it.displayOrder!! + 1 }
        }
        templates.add(taskTemplateEntity)

        if(taskTemplateEntity.displayOrder?.toInt() == 0) {
            isFirstTask = true
        }

        templates.sortByDescending { it.displayOrder }

        if (workflow.referenceId!=null && workflow.referenceType == "CASE") {
            val caseEntity = caseRepository.findByIdOrNull(workflow.referenceId!!) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

            if (hasTasks(workflow)) {
                val startDateLong = 0L
                taskTemplateEntity.task = saveTask(
                    caseEntity, taskTemplateEntity, authenticatedUser, startDateLong,
                    isFirstTask
                )
                if (needReordering!=null) {
                    taskTemplateEntity.task?.startDate = needReordering.task?.startDate
                }
                populateTaskTimelines(templates, startDateLong)
                val prevTask = templates.firstOrNull { it.displayOrder == taskTemplateEntity.displayOrder!!.minus(1) }?.task
                val nextTask = templates.firstOrNull { it.displayOrder == taskTemplateEntity.displayOrder!!.plus(1) }?.task

                if (prevTask?.status == TaskStatus.COMPLETED) {
                    val tasks = listOf(
                        prevTask,
                        taskTemplateEntity.task,
                        nextTask
                    )
                    populateMilestone(tasks, workflow.referenceId!!, false)
                }
            }
        }
        taskTemplateRepository.saveAll(templates)

        return id
    }

    private fun canAddTask(workflow: TaskWorkflowEntity, request: CreateTaskTemplateRequest): Boolean {

        val templates = workflow.taskTemplates
        val tasksPresent = templates.any { it.task!=null }
        if (!tasksPresent) {
            return true
        }

        // check if new task is getting added before or at position of completed or in_progress task
        val conflictingTask = templates.firstOrNull { it.displayOrder == request.displayOrder } ?: return true

        return conflictingTask.task?.status == TaskStatus.NOT_STARTED
    }

    @Transactional(readOnly = true)
    fun getWorkflow(id: Long, authenticatedUser: AuthenticatedUser): TaskWorkflowDetails {
        val workflow = fetchTaskWorkflowEntityForView(id, authenticatedUser)
        return TaskWorkflowDetails.ModelMapper.toView(workflow)
    }

    @Transactional
    fun deleteTaskTemplate(id: Long, taskId: Long, authenticatedUser: AuthenticatedUser): Boolean {
        val workflow = fetchTaskWorkflowEntity(id, authenticatedUser)
        val deletedTask = workflow.taskTemplates.firstOrNull { it.id == taskId } ?:throw ApplicationException(ErrorCode.NOT_FOUND)

        //in progress or completed tasks can not be deleted
        if (deletedTask.task!=null && deletedTask.task!!.status != TaskStatus.NOT_STARTED) {
            throw ApplicationException(ErrorCode.TASK_TEMPLATE_DELETE_EXCEPTION)
        }

        // set start date to next task
        deletedTask.task?.startDate?.let {
            workflow.taskTemplates.firstOrNull { it.displayOrder == deletedTask.displayOrder!! + 1 }?.task?.startDate =
                deletedTask.task?.startDate
        }

        taskTemplateRepository.deleteByIdAndWorkflow(taskId, workflow)

        deletedTask.task?.let { taskRepository.deleteById(it.id!!) }

        workflow.taskTemplates.removeIf { it.id == taskId }

        // reorder tasks if task is not deleted from the end.
        val updatedTasks = workflow.taskTemplates.filter { it.displayOrder!! > deletedTask.displayOrder!! }
        if (updatedTasks.isNotEmpty()) {
            updatedTasks.forEach { it.displayOrder = it.displayOrder!! - 1 }
            taskTemplateRepository.saveAll(updatedTasks)
        }
        // if tasks are present then recalculate timelines
        if (hasTasks(workflow)) {
            populateTaskTimelines(workflow.taskTemplates, 0)

            val prev = workflow.taskTemplates.firstOrNull { it.displayOrder == deletedTask.displayOrder?.minus(1) }
            val next = workflow.taskTemplates.firstOrNull { it.displayOrder == prev?.displayOrder?.plus(1) }

            val tasks = listOf(prev?.task, deletedTask.task, next?.task)

            if (prev?.task?.status == TaskStatus.COMPLETED) {
                populateMilestone(tasks, workflow.referenceId!!, true)
            }
        }
        return true
    }

    private fun hasTasks(workflow: TaskWorkflowEntity): Boolean {
        return workflow.taskTemplates.any {it.task?.startDate!=null}
    }

    @Transactional
    fun deleteWorkflow(id: Long, authenticatedUser: AuthenticatedUser): Boolean {
        val workflow = fetchTaskWorkflowEntity(id, authenticatedUser)

        if (workflow.referenceType == "CASE") {
            resetCaseProgress(workflow.referenceId!!)
        }

        taskWorkflowRepository.deleteById(workflow.id!!)
        return true
    }

    private fun resetCaseProgress(caseId: Long) {

        val case = caseRepository.findById(caseId).orElseThrow { ApplicationException(ErrorCode.NOT_FOUND) }
        caseStatusHistoryRepository.deleteAllByCase(case)
        caseMilestonesRepository.deleteAllByCase(case)

        case.status = CaseStatus.PENDING_VERIFICATION.name
        case.statusUpdate = null
        case.startDate = null
        case.percentCompletion = 0

        caseRepository.save(case)

    }


    fun updateTaskStatus(id: Long, status: TaskWorkflowStatus, authenticatedUser: AuthenticatedUser): Boolean{

        val workflowEntity = fetchTaskWorkflowEntity(id, authenticatedUser)

        if(status == workflowEntity.status)
            throw ApplicationException(ErrorCode.BAD_REQUEST)

        workflowEntity.status = status
        taskWorkflowRepository.save(workflowEntity)
        return true
    }

    @Transactional
    fun updateTaskTemplate(id: Long, taskId:Long, request: CreateTaskTemplateRequest, authenticatedUser: AuthenticatedUser): Boolean {

        val workflow = fetchTaskWorkflowEntity(id, authenticatedUser)
        val task =
            taskTemplateRepository.findByIdAndWorkflow(taskId, workflow) ?: throw ApplicationException(ErrorCode.NOT_FOUND)

        val timelineUpdated = task.expectedTimeline != request.expectedTimeline

        task.displayOrder = request.displayOrder
        task.assignedTo = request.assignedTo
        task.description = request.description
        task.caseMilestone = request.caseMilestone
        task.expectedTimeline = request.expectedTimeline
        task.expectedTimelineUnit = request.expectedTimelineUnit ?: "DAYS"
        task.instruction = request.instruction?:""
        task.name = request.taskName
        task.progress = request.progress
        task.priority = request.priority
        task.usefulLinks = request.usefulLinks
        task.visibility = request.visibility?:""
        task.caseStatus = request.caseStatus

        task.task?.let {
            if (it.status!=TaskStatus.NOT_STARTED && timelineUpdated) {
                log.error("Task template Timeline can not be updated when task is not in NOT_STARTED state")
                throw ApplicationException(ErrorCode.TASK_TEMPLATE_DELETE_EXCEPTION)
            }
            val caseEntity = workflow.referenceId?.let { it1 -> caseRepository.findByIdOrNull(it1) } ?: throw ApplicationException(ErrorCode.NOT_FOUND)
            val companyId = corporateUserRepository.findByIdOrNull(caseEntity.createdBy!!.userId)?.corporate!!.id ?: 0
            it.name = task.name
            it.caseMilestone = task.caseMilestone
            it.caseStatus = task.caseStatus
            it.instruction = task.instruction
            it.priority = task.priority
            it.usefulLinks = task.usefulLinks
            it.visibility = task.visibility.let { it1 -> TaskVisibility.valueOf(it1.uppercase()) }
            it.progress = task.progress

            task.description?.let { desc -> it.description=desc }
            // delete existing assignees
            taskAssigneeRepository.deleteByTaskId(it.id!!)
            saveTaskAssignee(it, caseEntity, task, companyId)
            it.updatedBy = authenticatedUser.userId
            taskRepository.save(it)

            if (timelineUpdated) {
                val templates = mutableListOf<TaskTemplateEntity>()
                templates.addAll(workflow.taskTemplates)
                templates.removeIf { it1 -> it1.id == taskId }
                templates.add(task)
                populateTaskTimelines(templates, 0)
            }
        }
        taskTemplateRepository.save(task)
        return true
    }

    fun fetchTaskWorkflowEntity(
        id: Long,
        authenticatedUser: AuthenticatedUser
    ): TaskWorkflowEntity {
        return super.get(id, authenticatedUser, taskWorkflowRepository, "TASK")
    }

    private fun fetchTaskWorkflowEntityForView(
        id: Long,
        authenticatedUser: AuthenticatedUser
    ): TaskWorkflowEntity {
        return taskWorkflowRepository.getWorkflowForView(id, authenticatedUser.partnerId)?:
            throw ApplicationException(ErrorCode.NOT_FOUND)
    }

    @Transactional(readOnly = true)
    fun getTask(id: Long, taskId: Long, authenticatedUser: AuthenticatedUser): TaskTemplateDetails {
        val workflow = fetchTaskWorkflowEntity(id, authenticatedUser)
        val task =
            taskTemplateRepository.findByIdAndWorkflow(taskId, workflow) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        return TaskTemplateDetails.ModelMapper.from(task)
    }

    @Transactional
    fun reorderTasks(id: Long, request: List<ReorderTaskRequest>, authenticatedUser: AuthenticatedUser): Boolean {

        val workflowEntity = fetchTaskWorkflowEntity(id, authenticatedUser)
        val orderMap = request.associate { it.id to it.displayOrder }

        workflowEntity.taskTemplates.forEach {

            //if template has task and is not in not_started state then throw error
            if (it.task!=null) {
                if (it.task!!.status!=TaskStatus.NOT_STARTED && it.displayOrder!=orderMap[it.id!!]) {
                    throw ApplicationException(ErrorCode.BAD_REQUEST)
                }
            }

            it.displayOrder = orderMap[it.id]!!
        }

        taskWorkflowRepository.save(workflowEntity)

        if(hasTasks(workflowEntity)) {
            populateTaskTimelines(workflowEntity.taskTemplates, 0)
        }
        return true
    }

    @Transactional
    fun link(id: Long, referenceId: Long, referenceType: String, authenticatedUser: AuthenticatedUser): Long? {

        //check if there exists a workflow
        log.info("Linking workflow id: $id with case id: $referenceId")
        val existingWorkflow = taskWorkflowRepository.findByReferenceIdAndReferenceType(referenceId, referenceType)
        if (existingWorkflow!=null) {
            log.info("Case has already linked with workflow id: ${existingWorkflow.id}")
            val tasksCreated = existingWorkflow.taskTemplates.any { it.task != null }
            // do not allow to link another workflow when tasks are already created.
            if (tasksCreated) {
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
            taskWorkflowRepository.delete(existingWorkflow)
        }

        val taskWorkflowEntity = fetchTaskWorkflowEntityForView(id, authenticatedUser)

        val copiedTaskWorkflow = copyTaskWorkflow(taskWorkflowEntity)

        val partner = getPartnerForWorkflow(referenceId, referenceType)

        copiedTaskWorkflow.partner = partner
        copiedTaskWorkflow.referenceId = referenceId
        copiedTaskWorkflow.referenceType = referenceType

        val savedEntity = taskWorkflowRepository.save(copiedTaskWorkflow)

        return savedEntity.id
    }

    private fun getPartnerForWorkflow(referenceId: Long, referenceType: String): PartnerEntity? {
        return when (referenceType) {
            "CASE" -> {
                val partnerId = caseRepository.findById(referenceId).getOrNull()?.partnerId
                partnerId?.let { partnerRepository.getReferenceById(partnerId) }
            }
            "QUERY" -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
            "RFP" -> {
                throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
            }
            else -> {
                throw ApplicationException(ErrorCode.BAD_REQUEST)
            }
        }
    }

    @Transactional(readOnly = true)
    fun getReport(referenceId: Long, referenceType: String, authenticatedUser: AuthenticatedUser): List<TaskWorkflowReport> {

        val workflowEntity = taskWorkflowRepository.findByReferenceIdAndReferenceType(referenceId, referenceType)?:throw ApplicationException(ErrorCode.NOT_FOUND)

        val templates = workflowEntity.taskTemplates
        templates.sortBy { it.displayOrder }

        return templates.filter { it.task!=null }.map {
            val updatedBy = it.task!!.updatedBy?.let { it1 -> loginAccountRepository.getFirstNameLastNameById(it1) }
            val deviation = calculateDeviation(it.task!!)
            TaskWorkflowReport.ModelMapper.from(it, updatedBy, deviation)
        }
    }

    private fun calculateDeviation(task: TaskEntity): Long? {

        if (task.status == TaskStatus.COMPLETED) {
            return TimeUtil.getWorkingDaysBetween(task.completedDate!!, task.expectedDueDate!!)
        }
        else if (task.status == TaskStatus.IN_PROGRESS) {
            if (task.expectedDueDate!=null && task.expectedDueDate!!.isBefore(LocalDateTime.now())) {
                return TimeUtil.getWorkingDaysBetween(LocalDateTime.now(), task.expectedDueDate!!)
            }
        }
        return null
    }

    fun getMilestoneChart(referenceId: Long, referenceType: String): CaseMilestoneChart? {

        val workflow =
            taskWorkflowRepository.findByReferenceIdAndReferenceType(referenceId, referenceType) ?: return null

        if (!hasTasks(workflow)) {
            return null
        }

        val templates = workflow.taskTemplates
        templates.sortByDescending { it.displayOrder }


        val lastTask = templates.first { it.task != null }.task ?: return null

        val plannedDueDate = lastTask.dueDate?.let { TimeUtil.toEpochMillis(it) }
        val actualCompletionDate =
            (lastTask.completedDate ?: lastTask.expectedDueDate ?: lastTask.dueDate)?.let { TimeUtil.toEpochMillis(it) }

        val deviation = if (lastTask.completedDate != null) {
            TimeUtil.getWorkingDaysBetween(lastTask.completedDate!!, lastTask.dueDate!!)
        } else if (lastTask.expectedDueDate == null) {
            0
        } else if (lastTask.expectedDueDate!!.toLocalDate() == lastTask.dueDate!!.toLocalDate()) {
            0
        } else {
            TimeUtil.getWorkingDaysBetween(lastTask.expectedDueDate!!, lastTask.dueDate!!)
        }

        return CaseMilestoneChart(plannedDueDate, actualCompletionDate, deviation, lastTask.status==TaskStatus.COMPLETED)

    }

    fun getWorkflowMilestones(referenceId: Long, referenceType: String): MutableList<CaseMilestoneResponse> {
        val workflow =
            taskWorkflowRepository.findByReferenceIdAndReferenceType(referenceId, "CASE") ?: return mutableListOf()

        if (!hasTasks(workflow)) {
            return mutableListOf()
        }

        val templates = workflow.taskTemplates.sortedBy { it.displayOrder }

        val milestones = mutableListOf<CaseMilestoneResponse>()
        // get consecutive unique milestones
        for (i in templates.indices) {
            val template = templates[i]
            val milestone = CaseMilestoneResponse(
                sequence = template.displayOrder!!,
                milestoneKey = template.caseMilestone,
                lastUpdatedBy = template.task?.updatedBy?.let { it1 -> userProfileUtil.retrieveProfile(it1) },
                createdDate = template.task?.completedDate?.let { it1 -> TimeUtil.toEpochMillis(it1) },
                isCompleted = template.task?.status == TaskStatus.COMPLETED
            )

            if (i == 0 || templates[i].caseMilestone!=templates[i-1].caseMilestone) {
                milestones.add(milestone)
            }
            else {
                // use last duplicate
                milestones[milestones.size-1] = milestone
            }
        }
        return milestones
    }

    private fun createMilestone(task: TaskEntity?, case: CaseEntity) {
        val milestone = CaseMilestonesEntity(
            case = case,
            milestoneKey = task?.caseMilestone!!,
            lastUpdatedBy = task.updatedBy!!
        )
        caseMilestonesRepository.save(milestone)
    }

    private fun deleteMilestone(task: TaskEntity?, case: CaseEntity) {
        val milestone = caseMilestonesRepository.findTopByCaseAndMilestoneKeyOrderByCreatedDateDesc(
            case,
            task?.caseMilestone!!
        )
        milestone?.let { caseMilestonesRepository.delete(it) }
    }

    fun populateMilestone(tasks: List<TaskEntity?>, caseId: Long, isDelete: Boolean) {

        if (tasks.size == 3) {
            //create scenario
            //prev and next milestones are not same means milestone is already present for prev
            if (tasks[0]?.caseMilestone != tasks[2]?.caseMilestone) {
                // if prev and current milestones are same then
                // delete prev milestone as it would get created when current task is completed
                if (tasks[0]?.caseMilestone == tasks[1]?.caseMilestone) {
                    val case = caseRepository.getReferenceById(caseId)
                    if (isDelete) {
                        createMilestone(tasks[0], case)
                    }
                    else {
                        deleteMilestone(tasks[0], case)
                    }
                }
            }

            // if prev  and next task's milestones are same
            if (tasks[0]?.caseMilestone == tasks[2]?.caseMilestone) {

                // Check if prev and current milestones are different then create milestone for prev
                if (tasks[0]?.caseMilestone != tasks[1]?.caseMilestone) {

                    val case = caseRepository.getReferenceById(caseId)
                    if (isDelete) {
                        deleteMilestone(tasks[0], case)
                    }
                    else {
                        // add milestone for prev
                        createMilestone(tasks[0], case)
                    }
                }
            }
        }
        else if (tasks.size == 2) {
            if (tasks[0]?.caseMilestone != tasks[1]?.caseMilestone) {
                if (tasks[0]?.status == TaskStatus.COMPLETED) {
                    val case = caseRepository.getReferenceById(caseId)
                    createMilestone(tasks[0], case)
                }
            }
        }
    }

    /**
     * Delete tasks for a workflow.
     * @param referenceId id of the reference entity.
     * @param referenceType type of the reference entity. CASE, QUERY, RFP
     * @param statuses list of task statuses to be deleted. If null, all tasks are deleted.
     */
    fun deleteWorkflowTasks(referenceId: Long, referenceType: String, statuses: List<TaskStatus>? = null) {
        val workflow = taskWorkflowRepository.findByReferenceIdAndReferenceType(referenceId, referenceType)
        if (workflow!=null) {
            var tasks = workflow.taskTemplates.map { it.task }
            if (statuses!=null) {
               tasks = tasks.filter { it?.status in statuses }
            }
            taskRepository.deleteAll(tasks)
        }
    }
}