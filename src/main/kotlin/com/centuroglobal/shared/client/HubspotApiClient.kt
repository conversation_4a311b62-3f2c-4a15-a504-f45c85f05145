package com.centuroglobal.shared.client

import com.centuroglobal.shared.data.payload.HubspotCompanyRequest
import com.centuroglobal.shared.data.payload.HubspotDealRequest
import com.centuroglobal.shared.data.payload.HubspotRequest
import com.centuroglobal.shared.data.payload.HubspotResponse
import com.centuroglobal.shared.data.payload.hubspot.Hubspot
import com.centuroglobal.shared.data.payload.hubspot.HubspotFilter
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.*

@FeignClient(name = "HubspotApiClient", url = "https://api.hubapi.com/crm/v3/objects")
interface HubspotApiClient {

    @RequestMapping(value = ["/contacts/search"], method = [RequestMethod.POST])
    fun findAllUser(@RequestHeader(value = "Authorization") apiKey: String, @RequestBody filter: HubspotFilter): Hubspot

    @RequestMapping(value = ["/contacts"], method = [RequestMethod.POST])
    fun saveUser(@RequestHeader(value = "Authorization") apiKey: String, @RequestBody filter: HubspotRequest): HubspotResponse

    @RequestMapping(value = ["/contacts/{id}"], method = [RequestMethod.PATCH])
    fun updateUser(@PathVariable("id") id: Long, @RequestHeader(value = "Authorization") apiKey: String, @RequestBody filter: HubspotRequest): HubspotResponse

    @RequestMapping(value = ["/deals"], method = [RequestMethod.POST])
    fun saveDeal(@RequestHeader(value = "Authorization") apiKey: String, @RequestBody dealRequest: HubspotDealRequest): HubspotResponse

    @RequestMapping(value = ["/deals/{id}"], method = [RequestMethod.PATCH])
    fun updateDeal(@PathVariable("id") id: Long, @RequestHeader(value = "Authorization") apiKey: String, @RequestBody dealRequest: HubspotDealRequest): HubspotResponse

    @RequestMapping(value = ["/companies/search"], method = [RequestMethod.POST])
    fun findAllCompany(@RequestHeader(value = "Authorization") apiKey: String, @RequestBody filter: HubspotFilter): Hubspot

    @RequestMapping(value = ["/companies"], method = [RequestMethod.POST])
    fun saveCompany(@RequestHeader(value = "Authorization") apiKey: String, @RequestBody filter: HubspotCompanyRequest): HubspotResponse


}