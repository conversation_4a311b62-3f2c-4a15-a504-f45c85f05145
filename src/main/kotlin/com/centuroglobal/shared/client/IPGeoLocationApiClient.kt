package com.centuroglobal.shared.client

import com.centuroglobal.shared.data.payload.ip.IPGeoLocationResponse
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.*

@FeignClient(name = "IPGeoLocationApiClient", url = "http://ip-api.com/json")
interface IPGeoLocationApiClient {

    @RequestMapping(value = ["/{ip}"], method = [RequestMethod.GET])
    fun findByIp(@PathVariable("ip") ip: String): IPGeoLocationResponse

}