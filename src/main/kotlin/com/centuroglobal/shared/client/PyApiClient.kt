package com.centuroglobal.shared.client

import com.centuroglobal.shared.data.payload.ExtractDocDetailsRequest
import com.centuroglobal.shared.data.payload.openai.LangchainResponse
import com.centuroglobal.shared.data.payload.travel.AssessmentCaseGenerateRequest
import com.centuroglobal.shared.data.payload.travel.AssessmentDossierGenerateRequest
import com.centuroglobal.shared.data.pojo.AICaseReporterRequest
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.AskAIRequest
import com.centuroglobal.shared.data.pojo.passportvisa.DocumentMetadataGeneric
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskTemplateRequest
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskWorkflowRequest
import com.fasterxml.jackson.databind.JsonNode
import feign.Headers
import feign.Response
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod

@FeignClient(name = "PythonApiClient", url = "\${app.api.python.base-url}")
interface PythonApiClient {


    //@RequestLine("POST /extract-doc-details")
    @RequestMapping(method = [RequestMethod.POST], value = ["/extract-doc-details"])
    @Headers("Content-Type: application/json")
    fun extractDocDetails(
        @RequestBody request: ExtractDocDetailsRequest
    ): Response

    @RequestMapping(method = [RequestMethod.POST], value = ["/extract-doc-details"])
    @Headers("Content-Type: application/json")
    fun extractDocDetailsResponse(
        @RequestBody request: ExtractDocDetailsRequest
    ): DocumentMetadataGeneric

    @RequestMapping(method = [RequestMethod.POST], value = ["/generate-case"])
    @Headers("Content-Type: application/json")
    fun generateCase(
        @RequestBody request: AssessmentCaseGenerateRequest
    ): Map<String, Any?>

    @RequestMapping(method = [RequestMethod.POST], value = ["/generate-case-form"])
    @Headers("Content-Type: application/json")
    fun generateCaseUsingForm(
        @RequestBody request: AssessmentCaseGenerateRequest
    ): Map<String, Any?>


    @RequestMapping(method = [RequestMethod.POST], value = ["/create-task"])
    @Headers("Content-Type: application/json")
    fun createTaskTemplate(
        @RequestBody request: CreateTaskWorkflowRequest
    ): List<CreateTaskTemplateRequest>

    @RequestMapping(method = [RequestMethod.POST], value = ["/generate-dossier"])
    fun generateDossier(
        @RequestBody request: AssessmentDossierGenerateRequest
    ): String

    @RequestMapping(method = [RequestMethod.POST], value = ["/reporter"])
    fun caseReporter(chatRequest: AICaseReporterRequest): JsonNode

    @RequestMapping(method = [RequestMethod.POST], value = ["/ask-ai"])
    fun askAI(chatRequest: AskAIRequest): LangchainResponse

}
