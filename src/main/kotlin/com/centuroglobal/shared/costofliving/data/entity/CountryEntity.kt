package com.centuroglobal.shared.costofliving.data.entity

import jakarta.persistence.*

@Entity(name = "countries")
data class CountryEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var countryCode: String,

    @Column(nullable = false)
    var country: String,

    @Column(nullable = false)
    var isoCoutryCode: String,

    @Column(nullable = false)
    var overallRating: Long?,

    @Column(nullable = false)
    var entrepreneurshipRating: Long?,

    @Column(nullable = false)
    var openForBusinessRating: Long?,

    @Column(nullable = false)
    var qualityOfLifeRating: Long?,

    @Column(nullable = false)
    var socialPurposeRating: Long?,

    @Column(nullable = false)
    var globalTravelRiskRating: Long?,

    @Column(nullable = false)
    var gdpRating: Long?,

    @Column(nullable = false)
    var economicFreedomRating: Long?,

    @Column(nullable = false)
    var highestInflationRateRating: Long?,

    @Column(nullable = false)
    var lowestInflationRateRating: Long?,

    @Column(nullable = false)
    var dialCode: String?
)