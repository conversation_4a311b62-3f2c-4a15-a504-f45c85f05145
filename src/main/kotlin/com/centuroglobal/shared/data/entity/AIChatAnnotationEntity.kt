package com.centuroglobal.shared.data.entity

import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne

@Entity(name = "ai_chat_annotation")
data class AIChatAnnotationEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var title: String,

    var type: String,

    var url: String,

    var startIndex: Int,

    var endIndex: Int,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ai_chat_id")
    var aiChat: AIChatEntity? = null
)