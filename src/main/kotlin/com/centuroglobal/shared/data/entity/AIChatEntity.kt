package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "ai_chat")
data class AIChatEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var question: String,

    var answer: String?,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chat_thread_id")
    var chatThread: ChatThreadEntity? = null,

    @OneToMany(mappedBy = "aiChat", cascade = [CascadeType.ALL], orphanRemoval = true)
    var annotations: List<AIChatAnnotationEntity>? = null

): AuditUserBaseEntity()