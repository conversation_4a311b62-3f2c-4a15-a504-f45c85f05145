package com.centuroglobal.shared.data.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "access_log")
data class AccessLogEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    val method: String,

    val uri: String,

    val controller: String,

    val action: String,

    val xid: String,

    val userId: Long?,

    @Column(columnDefinition = "text")
    val userAgent: String,

    val clientId: String,

    val clientIp: String,

    val receivedAt: LocalDateTime,

    val completedAt: LocalDateTime? = null,

    val responseTime: Int? = null,

    var responseStatus: Int? = null
)