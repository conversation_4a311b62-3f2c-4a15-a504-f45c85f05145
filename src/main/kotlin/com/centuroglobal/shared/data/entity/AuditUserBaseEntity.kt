package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import jakarta.persistence.Column
import jakarta.persistence.EntityListeners
import jakarta.persistence.MappedSuperclass

@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class AuditUserBaseEntity @JvmOverloads constructor(

    @Column(updatable = false)
    @CreatedBy
    var createdBy: Long? = null,

    @Column
    @LastModifiedBy
    var updatedBy: Long? = null
        
): AuditBaseEntity()