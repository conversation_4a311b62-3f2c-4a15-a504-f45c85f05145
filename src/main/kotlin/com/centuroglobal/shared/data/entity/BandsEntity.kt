package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.BandStatus
import jakarta.persistence.*

@Entity(name = "bands")
data class BandsEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var name: String,

    var description: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: BandStatus,

    var color: String? = null,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "corporate_id")
    var corporate: CorporateEntity? = null,

    @OneToMany(mappedBy = "band",fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    var corporateUsers: List<CorporateUserEntity>? = null,

    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL], orphanRemoval = false, mappedBy = "band")
    var bandAccesses: MutableList<BandDetailsEntity>? = mutableListOf()

): AuditUserBaseEntity()