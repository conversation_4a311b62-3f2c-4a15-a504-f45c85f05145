package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.BlueprintActionStatus
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import jakarta.persistence.*

@Entity(name = "blueprint_audit")
data class BlueprintAuditEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(nullable = false)
    val countryCode: String,

    @Column(nullable = false, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    val action: BlueprintActionStatus,

    @Column(nullable = false)
    override var lastUpdatedBy: Long

) : AuditByBaseEntity(lastUpdatedBy)
