package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.payload.case.BusinessIncorporationRequest
import com.centuroglobal.shared.data.entity.CaseEntity
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Entity

@Entity(name = "business_incorporation")
@DiscriminatorValue("bi")
data class BusinessIncorporationEntity(

    @Column(nullable = false)
    var firstName: String,

    @Column(nullable = false)
    var lastName: String,

    var companyName: String? = null,

    @Column(nullable = false)
    var companyHqCountry: String,

    @Column(nullable = false)
    var companyHqAddress: String,

    @Column(nullable = false)
    @JsonProperty("newCompanyBranchCountry")
    var newBranchCountry: String,

    @JsonProperty("newCompanyBranchAddress")
    @Column(nullable = true)
    var newBranchAddress: String? = null,

    @Column(nullable = false)
    var typeOfBusiness: String,

    @Column(nullable = true)
    var otherDetails: String? = null
) : CaseEntity() {
    object ModelMapper {
        fun from(request: BusinessIncorporationRequest): BusinessIncorporationEntity {
            return BusinessIncorporationEntity(
                firstName = request.firstName,
                lastName = request.lastName,
                companyName = request.companyName,
                companyHqCountry = request.companyHqCountry,
                companyHqAddress = request.companyHqAddress,
                newBranchCountry = request.newCompanyBranchCountry,
                newBranchAddress = request.newCompanyBranchAddress,
                typeOfBusiness = request.typeOfBusiness,
                otherDetails = request.otherDetails
            )
        }
    }
}