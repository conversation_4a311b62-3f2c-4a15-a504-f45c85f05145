package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.enums.PartnerCaseType
import jakarta.persistence.*


@Entity(name = "case_assignee")
@Table(name = "case_assignee")
data class CaseAssigneeEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "case_id")
    var case: CaseEntity,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "expert_id")
    var expert: ExpertUserEntity,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var userType: PartnerCaseType?

) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CaseAssigneeEntity

        return expert == other.expert
    }

    override fun hashCode(): Int {
        return expert.hashCode()
    }
}
