package com.centuroglobal.shared.data.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "case_document_file")
data class CaseDocumentFileEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    var caseDocument: CaseDocumentsEntity?,

    var fileName: String? = null,

    var fileType: String? = null,

    var fileSize: Long? = null,

    var isLocked: Boolean? = null,

    var fileKey: String? = null,

    var createdDate: LocalDateTime = LocalDateTime.now()
)