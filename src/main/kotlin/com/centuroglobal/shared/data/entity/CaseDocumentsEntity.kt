package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.CaseEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "case_documents")
data class CaseDocumentsEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "case_id", nullable = false)
    var case: CaseEntity?,

    @Column(nullable = false)
    var documentCode: String,

    @Column(nullable = false)
    var status: String,

    var displayOrder: Long? = null,

    var requestedDate: LocalDateTime?,

    var receivedDate: LocalDateTime?,

    var verifiedDate: LocalDateTime?,

    var apostilledDate: LocalDateTime?,

    var expiryDate: LocalDateTime?,

    @JsonIgnore
    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL])
    @JoinColumn(name = "document_id")
    var caseDocumentFiles: List<CaseDocumentFileEntity>? = listOf(),

    var otherDocumentName: String?=null

)