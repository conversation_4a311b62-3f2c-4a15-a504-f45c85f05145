package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.CaseDocumentAction
import com.fasterxml.jackson.annotation.JsonIgnore
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "case_documents_audit")
data class CaseDoucmentsAuditEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var caseId: Long,

    var documentId: Long,

    var createdDate: LocalDateTime?,

    var createdBy: Long,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var action: CaseDocumentAction,

    var ipAddress: String,

    var fileId: Long,

    var docType: String?,

    var fileName: String?,

    var otherDocumentName: String?
)