package com.centuroglobal.shared.data.entity

import java.time.LocalDateTime
import jakarta.persistence.*

@Entity(name = "case_documents_status_logs")
data class CaseDoucmentsStatusLogsEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var caseId: Long,

    var documentId: Long,

    var status: String,

    var ipAddress: String,

    var createdDate: LocalDateTime?,

    var createdBy: Long,

    var docType: String?,

    var otherDocumentName: String?

)