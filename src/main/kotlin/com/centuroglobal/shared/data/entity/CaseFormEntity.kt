package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.case.CaseFormMapping
import jakarta.persistence.*

@Entity(name = "case_form")
data class CaseFormEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var name: String,

    var description: String,

    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL], mappedBy = "form", orphanRemoval = true)
    var countries: MutableList<CaseFormCountryEntity> = mutableListOf(),

    var category: String,

    var fields: String?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var visibility: TaskVisibility,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: CaseFormStatus,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "partner_id")
    var partner: PartnerEntity? = null,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "firstName", column = Column(name = "case_applicant_first_name_field")),
        AttributeOverride(name = "lastName", column = Column(name = "case_applicant_last_name_field")),
        AttributeOverride(name = "email", column = Column(name = "case_email_field")),
        AttributeOverride(name = "company", column = Column(name = "case_company_field")),
    )
    var mappingFields: CaseFormMapping? = null,

    var fieldCount: Long? = 0,

    var defaultDocuments: String? = null,

    var isDefault: Boolean

) : AuditUserBaseEntity()