package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.PartnerCaseType
import jakarta.persistence.*


@Entity(name = "case_managers")
@Table(name = "case_managers")
data class CaseManagerEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "case_id")
    var case: CaseEntity,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var userType: PartnerCaseType?

) {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CaseManagerEntity

        return user == other.user
    }

    override fun hashCode(): Int {
        return user.hashCode()
    }
}
