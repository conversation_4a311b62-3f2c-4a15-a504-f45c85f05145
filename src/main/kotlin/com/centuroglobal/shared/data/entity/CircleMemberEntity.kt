package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.CircleMemberStatus
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import com.centuroglobal.shared.data.entity.CircleEntity
import org.hibernate.annotations.SQLDelete
import org.hibernate.annotations.Where
import jakarta.persistence.*

@Entity(name = "circle_member")
@SQLDelete(sql = "UPDATE circle_member SET is_active = false WHERE id = ?")
@Where(clause = "is_active = true")
data class CircleMemberEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    val userId: Long,

    var circleMemberStatus: CircleMemberStatus = CircleMemberStatus.INACTIVE,

    @ManyToOne(fetch = FetchType.EAGER, cascade = [CascadeType.REFRESH])
    @JoinColumn(name = "circle_id")
    val circle: CircleEntity,

    override var lastUpdatedBy: Long,

    var isActive: Boolean = true

) : AuditByBaseEntity(lastUpdatedBy)
