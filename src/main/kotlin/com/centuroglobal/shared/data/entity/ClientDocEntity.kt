package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.ClientDocType
import com.centuroglobal.shared.data.enums.UserDocType
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "client_docs")
data class ClientDocEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var docName: String,

    var docKey: String,

    var fileName: String,

    var fileSize: Long,

    var fileType: String,

    var country: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var docType: ClientDocType,

    var createdBy: Long? = null,

    var expiryDate: LocalDateTime? = null,

    var referenceId: Long,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var referenceType: UserDocType,

    @Column(columnDefinition = "varchar")
    var docSubType: String? = null,

    var metadata: String? = null,

    var issueCountry: String?=null,

    @OneToMany(mappedBy = "clientDoc", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    var clientDocFile: MutableList<ClientDocFileEntity>? = null

) : AuditBaseEntity() {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ClientDocEntity

        return docKey == other.docKey  && docType == other.docType
    }

    override fun hashCode(): Int {
        var result = docName.hashCode()
        result = 31 * result + docKey.hashCode()
        return result
    }
}