package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "client_doc_file")
data class ClientDocFileEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_doc_id", nullable = false)
    var clientDoc: ClientDocEntity?,

    var fileName: String,

    var fileSize: Long,

    var fileType: String,

    var docKey: String

)
