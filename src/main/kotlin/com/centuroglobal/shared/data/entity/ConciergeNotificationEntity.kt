package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.ConciergeNotificationState
import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.*

@Entity(name = "concierge_notification")
data class ConciergeNotificationEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(updatable = false, nullable = false)
    val userId: Long,

    @Column(columnDefinition = "mediumtext", updatable = false, nullable = false)
    var requestBody: String,

    @Column(updatable = true, columnDefinition = "varchar")
    @Enumerated(value = EnumType.STRING)
    var state: ConciergeNotificationState

) : AuditBaseEntity()
