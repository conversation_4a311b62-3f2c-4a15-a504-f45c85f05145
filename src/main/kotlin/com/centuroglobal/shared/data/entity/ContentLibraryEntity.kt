package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "content_library")
data class ContentLibraryEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var title: String,

    var countryCode: String,

    var identifier: String,

    var data: String,

    var isActive: Boolean = true,

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "contentLibrary")
    var metadata: MutableList<ContentLibraryMetadataEntity> = mutableListOf()

) : AuditUserBaseEntity()