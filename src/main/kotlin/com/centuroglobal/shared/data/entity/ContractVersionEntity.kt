package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "contract_version")
data class ContractVersionEntity @JvmOverloads constructor(

    @Id
    var awsFileName: String,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "company_profile_id")
    var companyProfile: ExpertCompanyProfileEntity,

    var version: Long = 0,

    override var lastUpdatedBy: Long

) : AuditByBaseEntity(lastUpdatedBy)