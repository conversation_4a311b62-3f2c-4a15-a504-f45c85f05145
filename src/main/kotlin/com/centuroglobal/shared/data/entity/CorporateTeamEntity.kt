package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "corporate_team")
data class CorporateTeamEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var designation: String,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "corporate_id")
    var corporate: CorporateEntity

) : AuditBaseEntity() {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CorporateTeamEntity

        if (designation != other.designation) return false
        return user.id == other.user.id
    }
    override fun hashCode(): Int {
        var result = designation.hashCode()
        result = 31 * result + user.hashCode()
        return result
    }
}