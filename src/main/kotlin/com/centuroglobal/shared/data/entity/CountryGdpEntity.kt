package com.centuroglobal.shared.data.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "country_gdp")
data class CountryGdpEntity(

    @Id
    var id: Long? = null,

    @Column(nullable = false)
    var countryCode: String,

    @Column(nullable = false)
    var gdp: String?,

    @Column(nullable = false)
    var population: String?,

    @Column(nullable = false)
    var gdpPerCapita: String?,

    @Column(nullable = false)
    var createdDate: LocalDateTime? = null

)
