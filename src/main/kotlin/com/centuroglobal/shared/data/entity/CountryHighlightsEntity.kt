package com.centuroglobal.shared.data.entity

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.*

@Entity(name = "country_highlights")
data class CountryHighlightsEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("key")
    var id: Long? = null,

    @JsonProperty("value")
    @Column(nullable = false)
    var highlights: String? = null
)