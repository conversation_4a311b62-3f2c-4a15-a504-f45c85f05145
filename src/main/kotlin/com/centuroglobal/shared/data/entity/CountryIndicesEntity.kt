package com.centuroglobal.shared.data.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "country_indices")
data class CountryIndicesEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var countryCode: String,

    @Column(nullable = false)
    var costOfLivingIndex: Double?,

    @Column(nullable = false)
    var qualityOfLifeIndex: Double?,

    @Column(nullable = false)
    var crimeIndex: Double?,

    @Column(nullable = false)
    var pollutionIndex: Double?,

    @Column(nullable = false)
    var healthCareIndex: Double?,

    @Column(nullable = false)
    var purchasingPowerInclRentIndex: Double?,

    @Column(nullable = false)
    var safetyIndex: Double?,

    @Column(nullable = false)
    var propertyPriceToIncomeRatio: Double?,

    @Column(nullable = false)
    var createdDate: LocalDateTime? = null

)