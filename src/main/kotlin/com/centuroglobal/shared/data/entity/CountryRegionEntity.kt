package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "country_region")
data class CountryRegionEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Int? = null,

    @Column(updatable = false, nullable = false)
    val countryCode: String,

    @Column(nullable = false)
    val name: String,

    @Column(nullable = false)
    val code: String
)