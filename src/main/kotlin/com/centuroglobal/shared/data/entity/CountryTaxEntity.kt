package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "country_tax")
data class CountryTaxEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var country: String,

    @Column(nullable = false)
    var countryCode: String,

    @Column(nullable = false)
    var corporateTaxRate: Long?,

    @Column(nullable = false)
    var employerTaxRate: Long?,

    @Column(nullable = false)
    var employeeTaxRate: Long?,

    @Column(nullable = false)
    var infoText: String?
)