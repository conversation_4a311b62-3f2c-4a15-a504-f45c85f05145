package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.EventInviteeStatus
import jakarta.persistence.*

@Entity(name = "event_invitees")
data class EventInviteeEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH])
    @JoinColumn(name = "client_id")
    var client: ClientView,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "event_id")
    var event: EventEntity,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: EventInviteeStatus = EventInviteeStatus.INVITEE,

    override var lastUpdatedBy: Long
) : AuditByBaseEntity(lastUpdatedBy)