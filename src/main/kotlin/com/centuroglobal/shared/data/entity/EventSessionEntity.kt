package com.centuroglobal.shared.data.entity

import jakarta.persistence.*
import java.util.*

@Entity(name = "event_session")
data class EventSessionEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var name: String,

    @Column(columnDefinition = "text")
    var about: String,

    @Temporal(TemporalType.DATE)
    var date: Date,

    @Temporal(TemporalType.TIME)
    var startTime: Date,

    @Temporal(TemporalType.TIME)
    var endTime: Date,

    var duration: String,

    var timeZone: String,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "event_id")
    var event: EventEntity,

    @ManyToMany
    @JoinTable(
        name = "event_session_speaker",
        joinColumns = [Join<PERSON><PERSON>umn(name = "session_id")],
        inverseJoinColumns = [Jo<PERSON><PERSON><PERSON>um<PERSON>(name = "speaker_id")]
    )
    var speakers: MutableList<EventSpeakerEntity> = mutableListOf(),

    override var lastUpdatedBy: Long
) : AuditByBaseEntity(lastUpdatedBy)
