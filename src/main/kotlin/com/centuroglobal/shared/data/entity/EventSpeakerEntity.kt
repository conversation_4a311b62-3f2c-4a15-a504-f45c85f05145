package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.EventSpeakerType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import com.centuroglobal.shared.data.entity.EventEntity
import com.centuroglobal.shared.data.entity.EventSessionEntity
import jakarta.persistence.*

@Entity(name = "event_speaker")
data class EventSpeakerEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var internalMemberId: Long?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var internalMemberRole: Role?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var type: EventSpeakerType,

    var isHost: Boolean,

    var internalId: String?,

    var firstName: String?,

    var lastName: String?,

    @Column(columnDefinition = "text")
    var about: String?,

    var companyName: String?,

    var jobTitle: String?,

    var countryCode: String?,

    var emailId: String?,

    var profilePictureKey: String? = null,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "event_id")
    val event: EventEntity,

    @ManyToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinTable(
        name = "event_session_speaker",
        joinColumns = [JoinColumn(name = "speaker_id")],
        inverseJoinColumns = [JoinColumn(name = "session_id")]
    )
    val sessions: MutableList<EventSessionEntity> = mutableListOf(),

    override var lastUpdatedBy: Long
) : AuditByBaseEntity(lastUpdatedBy)