package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "expert_company_profile")
data class ExpertCompanyProfileEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var name: String,

    @Column
    var logoKey: String? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var size: CompanySize? = null,

    @Column(columnDefinition = "text")
    var summary: String? = null,

    @JsonIgnore
    @OneToMany(
        fetch = FetchType.LAZY, mappedBy = "companyProfile",
        cascade = [CascadeType.REFRESH, CascadeType.DETACH]
    )
    var users: MutableList<ExpertUserEntity> = mutableListOf(),

    override var lastUpdatedBy: Long,

    var companyNumber: String?,

    var companyAddress: String?,

    var aboutBusiness: String?,

    var effectiveDate: LocalDateTime,

    var effectiveEndDate: LocalDateTime,

    var contractAcceptedDate: LocalDateTime?,

    var feesCurrency: String?,

    var feesAmount: String?,

    var specialTerms: String?,

    var membershipStatus: String?,

    var territory: String?,

    var renewContract: Boolean = false,

    var services: String?,

    @OneToOne(fetch = FetchType.LAZY)
    var account: AccountEntity,

    var profileImage: String? = null,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "expert_contract_id")
    var expertContract: ExpertContractEntity? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: AccountStatus = AccountStatus.PENDING_VERIFICATION,

    var questionsQuota: Long? = null,

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "partner_experts",
        joinColumns = [JoinColumn(name = "ecp_id", referencedColumnName = "id")],
        inverseJoinColumns = [JoinColumn(name = "partner_id", referencedColumnName = "id")]
    )
    @JsonIgnore
    var associatedPartners: MutableList<PartnerEntity> = mutableListOf(),

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var companyType: ExpertCompanyType,

    var invitedBy: Long? = null

) : AuditByBaseEntity(lastUpdatedBy)