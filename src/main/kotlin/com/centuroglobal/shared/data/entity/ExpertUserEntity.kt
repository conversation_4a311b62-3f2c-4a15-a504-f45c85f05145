package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.view.ExpertiseView
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import org.hibernate.annotations.LazyCollection
import org.hibernate.annotations.LazyCollectionOption

@Entity(name = "expert_user")
@DiscriminatorValue("EXPERT")
data class ExpertUserEntity @JvmOverloads constructor(

    override var id: Long? = null,

    @Column(nullable = false)
    var jobTitle: String,

    var countryRegionId: Int? = null,

    var expertiseId: Int? = null,

    @LazyCollection(LazyCollectionOption.FALSE)
    @ManyToMany
    @JoinTable(
        name = "expert_expertise",
        joinColumns = [JoinColumn(name = "expert_id")],
        inverseJoinColumns = [JoinColumn(name = "expertise_id")]
    )
    var expertises: List<ExpertiseView> = mutableListOf(),

    @Column(columnDefinition = "text")
    var bio: String? = null,

    @Column(nullable = false)
    var displayName: String,

    var infoVideoUrl: String? = null,

    @Column(nullable = false)
    var contactNumber: String,

    @Column(nullable = false)
    var contactEmail: String,

    @Column(nullable = false)
    var contactWebsite: String,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "company_profile_id")
    var companyProfile: ExpertCompanyProfileEntity? = null,

    var expertType: String?,

    var viewContract: Boolean = false,

    @ManyToMany(fetch = FetchType.LAZY, cascade  =
    [   CascadeType.DETACH,
        CascadeType.REFRESH,
        CascadeType.PERSIST
    ])
    @JoinTable(
        name = "case_assignee",
        joinColumns = [JoinColumn(name = "expert_id")],
        inverseJoinColumns = [JoinColumn(name = "case_id")]
    )
    @JsonIgnore
    var case: MutableList<CaseEntity> = mutableListOf(),

    var profileImage: String? = null,

) : LoginAccountEntity()