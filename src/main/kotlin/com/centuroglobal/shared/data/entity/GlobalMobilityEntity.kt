package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.payload.case.GlobalMobilityRequest
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Entity

@Entity(name = "global_mobility")
@DiscriminatorValue("global_mobility")
data class GlobalMobilityEntity(

    @Column(nullable = false)
    var firstName: String,

    @Column(nullable = false)
    var lastName: String,

    var companyName: String? = null,

    @Column(nullable = false)
    var globalMobilityCountry: String,

    @Column(nullable = false)
    var relocatingCountry: String,

    var agreeTermOfUse: Boolean

) : CaseEntity() {
    object ModelMapper {
        fun from(request: GlobalMobilityRequest): GlobalMobilityEntity {
            return GlobalMobilityEntity(
                firstName = request.firstName,
                lastName = request.lastName,
                companyName = request.companyName,
                globalMobilityCountry = request.globalMobilityCountry,
                relocatingCountry = request.relocatingCountry,
                agreeTermOfUse = request.agreeTermOfUse
            )
        }
    }
}