package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.payload.case.HrAndPayrollRequest
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Entity

@Entity(name = "hr_and_payroll")
@DiscriminatorValue("hr_and_payroll")
data class HrAndPayrollEntity(

    @Column(nullable = false)
    var firstName: String,

    @Column(nullable = false)
    var lastName: String,

    var companyName: String? = null,

    @Column(nullable = false)
    var hrAndPayrollCountry: String,

    @Column(nullable = false)
    var queryDetails: String

) : CaseEntity() {
    object ModelMapper {
        fun from(request: HrAndPayrollRequest): HrAndPayrollEntity {
            return HrAndPayrollEntity(
                firstName = request.firstName,
                lastName = request.lastName,
                companyName = request.companyName,
                hrAndPayrollCountry = request.hrAndPayrollCountry,
                queryDetails = request.queryDetails
            )
        }
    }
}