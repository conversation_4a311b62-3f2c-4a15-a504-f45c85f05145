package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.payload.case.LegalRequest
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Entity

@Entity(name = "legal")
@DiscriminatorValue("legal")
data class LegalEntity(

    @Column(nullable = false)
    var firstName: String,

    @Column(nullable = false)
    var lastName: String,

    var companyName: String? = null,

    @Column(nullable = false)
    var areaOfSupport: String,

    @Column(nullable = false)
    var legalCountry: String?,

    @Column(nullable = false)
    var queryDetails: String

) : CaseEntity() {
    object ModelMapper {
        fun from(request: LegalRequest): LegalEntity {
            return LegalEntity(
                firstName = request.firstName,
                lastName = request.lastName,
                companyName = request.companyName,
                areaOfSupport = request.areaOfSupport,
                legalCountry = request.legalCountry,
                queryDetails = request.queryDetails
            )
        }
    }
}