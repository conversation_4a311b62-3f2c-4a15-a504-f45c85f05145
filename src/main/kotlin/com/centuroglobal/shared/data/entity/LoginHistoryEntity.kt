package com.centuroglobal.shared.data.entity

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id

@Entity(name = "login_history")
data class LoginHistoryEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var userId: Long,

    var ipAddress: String,

    var city: String?,

    var country: String?,

    var browser: String,

    var os: String,

    var loginSuccess: Boolean

): AuditBaseEntity()