package com.centuroglobal.shared.data.entity

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id

@Entity(name = "master_content")
data class MasterContentEntity(

    @Id
    @Column(nullable = false)
    @JsonProperty("templateType")
    var docType: String,

    @Column(columnDefinition = "TEXT")
    var content: String,

    @JsonProperty("lastUpdatedOn")
    @Column(nullable = false)
    var lastUploadDate: LocalDateTime? = null,

    @Column(nullable = false)
    var lastUpdateBy: Long?
)