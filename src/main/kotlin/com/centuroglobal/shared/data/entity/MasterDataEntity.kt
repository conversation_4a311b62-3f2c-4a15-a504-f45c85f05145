package com.centuroglobal.shared.data.entity

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "master_data")
data class MasterDataEntity(

    @Id
    @Column(nullable = false)
    @JsonProperty("templateType")
    var docType: String,

    @Column(nullable = false)
    @JsonProperty("title")
    var docName: String,

    @JsonProperty("lastUpdatedOn")
    @Column(nullable = false)
    var lastUploadDate: LocalDateTime? = null,

    @Column(nullable = false)
    var lastUpdateBy: Long?,

    @JsonProperty("lastUpdatedBy")
    @Column(nullable = false)
    var lastUpdateByName: String,

    @Column(nullable = false)
    var actionType: String,

    @Column(nullable = false)
    var displayOrder: Long
)