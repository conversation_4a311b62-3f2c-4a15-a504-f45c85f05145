package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "milestones")
@Table(name = "milestones")
data class MilestonesEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    var sequence: Long,

    var milestone: String,

    var milestoneKey: String,

    var status: String,

    var caseSubCategory: String
)