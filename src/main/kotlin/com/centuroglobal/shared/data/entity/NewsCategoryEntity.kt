package com.centuroglobal.shared.data.entity

import jakarta.persistence.*

@Entity(name = "news_category")
data class NewsCategoryEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var category: String?,

    @Column(nullable = false)
    var industryCode: String?,

    @Column(nullable = false)
    var industryDisplayName: String?,

    @Column(nullable = false)
    var searchQuery: String?

)