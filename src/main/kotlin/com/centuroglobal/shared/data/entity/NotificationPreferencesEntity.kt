package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.entity.AuditBaseEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "notification_preferences")
data class NotificationPreferencesEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var notificationKey: NotificationType,

    var value: Boolean,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.MERGE])
    @JoinColumn(name = "user_id")
    @JsonIgnore
    var corporateUser: CorporateUserEntity

) : AuditBaseEntity()