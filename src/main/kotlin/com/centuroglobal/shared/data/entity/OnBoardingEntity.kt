package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.attributeConverter.StringListAttributeConverter
import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import jakarta.persistence.*

@Entity(name = "on_board")
class OnBoardingEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var userType: String?,

    var companySize: String?,

    var organization: String?,

    var expansion: String?,

    @Column(name = "market")
    @Convert(converter = StringListAttributeConverter::class)
    var market: List<String>?,

    @Column(name = "challenge")
    @Convert(converter = StringListAttributeConverter::class)
    var challenge: List<String>?,

    var countryCode: String?,

    var stage: String?,

    var platformUser: String?,

    var rootUserId: Long,

    override var lastUpdatedBy: Long
) : AuditByBaseEntity(lastUpdatedBy)