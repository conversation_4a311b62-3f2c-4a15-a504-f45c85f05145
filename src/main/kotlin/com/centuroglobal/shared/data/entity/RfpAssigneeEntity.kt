package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.enums.PartnerCaseType
import jakarta.persistence.*
import java.time.LocalDateTime


@Entity(name = "rfp_assignee")
@Table(name = "rfp_assignee")
data class RfpAssigneeEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rfp_id")
    var rfp: RfpEntity,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var userType: PartnerCaseType?,

    var assignedDate: LocalDateTime? = null

) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RfpAssigneeEntity

        return user == other.user
    }

    override fun hashCode(): Int {
        return user.hashCode()
    }
}
