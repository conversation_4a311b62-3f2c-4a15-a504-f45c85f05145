package com.centuroglobal.shared.data.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "rfp_document")
data class RfpDocumentEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var fileName: String,

    var fileType: String?,

    var fileSize: Long,

    var fileUploadDate: LocalDateTime = LocalDateTime.now(),

    @ManyToOne(fetch = FetchType.EAGER, cascade = [CascadeType.ALL])
    @JoinColumn(name = "rfp_id")
    var rfp: RfpEntity?
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RfpDocumentEntity

        if (id != other.id) return false
        if (fileName != other.fileName) return false
        if (fileType != other.fileType) return false
        if (fileSize != other.fileSize) return false
        if (fileUploadDate != other.fileUploadDate) return false
        return true
    }

    override fun hashCode(): Int {
        var result = id?.hashCode() ?: 0
        result = 31 * result + fileName.hashCode()
        result = 31 * result + (fileType?.hashCode() ?: 0)
        result = 31 * result + fileSize.hashCode()
        result = 31 * result + fileUploadDate.hashCode()
        return result
    }
}