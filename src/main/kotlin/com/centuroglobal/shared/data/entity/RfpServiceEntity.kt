package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.entity.RfpServiceCountryEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "rfp_service")
data class RfpServiceEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var serviceName: String,

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinColumn(name = "service_id")
    var countries: MutableList<RfpServiceCountryEntity>? = null,
)