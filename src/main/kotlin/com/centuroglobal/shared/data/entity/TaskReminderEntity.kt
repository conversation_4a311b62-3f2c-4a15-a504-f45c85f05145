package com.centuroglobal.shared.data.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "task_reminder")
data class TaskReminderEntity @JvmOverloads constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", nullable = false)
    var task: TaskEntity?,

    var dateTime: LocalDateTime?

) : AuditUserBaseEntity(){
    override fun toString(): String {
        return "TaskReminderEntity(id=$id)"
    }
}
