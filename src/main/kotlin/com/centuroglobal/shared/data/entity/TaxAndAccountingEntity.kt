package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.payload.case.TaxAndAccountingRequest
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Entity

@Entity(name = "tax_and_accounting")
@DiscriminatorValue("tax_and_accounting")
data class TaxAndAccountingEntity(

    @Column(nullable = false)
    var firstName: String,

    @Column(nullable = false)
    var lastName: String,

    var companyName: String? = null,

    @Column(nullable = false)
    var taxAccountingCountry: String?,

    @Column(nullable = false)
    var queryDetails: String

) : CaseEntity() {
    object ModelMapper {
        fun from(request: TaxAndAccountingRequest): TaxAndAccountingEntity {
            return TaxAndAccountingEntity(
                firstName = request.firstName,
                lastName = request.lastName,
                companyName = request.companyName,
                taxAccountingCountry = request.taxAccountingCountry,
                queryDetails = request.queryDetails
            )
        }
    }
}

