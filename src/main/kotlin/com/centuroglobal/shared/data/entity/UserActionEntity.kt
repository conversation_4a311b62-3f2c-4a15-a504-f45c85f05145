package com.centuroglobal.shared.data.entity

import com.centuroglobal.shared.data.pojo.UserActionResult
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "user_action")
data class UserActionEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var location: String?,

    var browser: String?,

    var startTime: LocalDateTime?,

    var endTime: LocalDateTime?,

    var os: String?,

    var additionalData: String?,

    var action: String?,

    var source: String?,

    var userId: Long?,

    var createdBy: Long?,

    var sessionId: String?

) {
    object ModelMapper {
        fun from(request: UserActionResult): UserActionEntity {
            return UserActionEntity(
                location = request.location,
                browser = request.browser,
                startTime = request.startTime,
                endTime = request.endTime,
                os = request.os,
                additionalData = request.additionalData,
                action = request.action,
                source = request.source,
                userId = request.userId,
                createdBy = request.createdBy,
                sessionId = request.sessionId
            )
        }
    }
}