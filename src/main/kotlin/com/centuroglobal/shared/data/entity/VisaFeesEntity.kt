package com.centuroglobal.shared.data.entity

import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne

@Entity(name = "corporate_visa_fees")
data class VisaFeesEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var countryCode: String,

    var eVisaFees: Double,

    var businessVisaFees: Double,

    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.REFRESH, CascadeType.DETACH])
    @JoinColumn(name = "corporate_id")
    var corporate: CorporateEntity? = null

): AuditUserBaseEntity()
