package com.centuroglobal.shared.data.entity.auth

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "mfa_token")
data class MfaTokenEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    val userId: Long,

    val code: String,

    val method: String = "EMAIL",

    var expiryDate: LocalDateTime,

    var isValidated: Boolean = false

) : AuditBaseEntity()