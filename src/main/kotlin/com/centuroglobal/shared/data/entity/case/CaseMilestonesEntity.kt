package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "case_milestones")
data class CaseMilestonesEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "case_id", nullable = false)
    @JsonIgnore
    var case: CaseEntity,

    var milestoneKey: String,

    @Column(nullable = false)
    override var lastUpdatedBy: Long

) : AuditByBaseEntity(lastUpdatedBy)