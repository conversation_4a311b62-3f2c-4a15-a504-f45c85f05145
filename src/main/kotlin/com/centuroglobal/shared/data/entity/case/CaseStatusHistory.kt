package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.AuditByBaseEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.enums.CaseStatus
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "case_history")
data class CaseStatusHistory(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var status: String = CaseStatus.NOT_STARTED.name,

    var statusUpdate: String? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "case_id", nullable = false)
    @JsonIgnore
    var case: CaseEntity,

    @Column(nullable = false)
    override var lastUpdatedBy: Long,

    var actionFor: String? = null,

    var isDeleted: Boolean = false

): AuditByBaseEntity(lastUpdatedBy)