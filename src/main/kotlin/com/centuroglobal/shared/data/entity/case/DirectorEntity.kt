package com.centuroglobal.shared.data.entity.case

import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "director")
data class DirectorEntity(

    var fullName: String,

    var dob: String? = null,

    var occupation: String? = null,

    var nationality: String? = null,

    var residentialAddress: String? = null,

    var serviceAddress: String,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "entity_setup_case_id")
    @JsonIgnore
    var entitySetup: EntitySetupEntity? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null

)