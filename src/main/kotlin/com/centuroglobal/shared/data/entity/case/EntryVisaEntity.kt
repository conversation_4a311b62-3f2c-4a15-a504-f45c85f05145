package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.*
import java.time.LocalDate

@Entity(name = "entry_visa")
data class EntryVisaEntity(

    var destinationCountry:String,

    var tripPurpose:String,

    var travelDate: LocalDate,

    var tripLength: Long,

    var tripLengthDetails: String,

    var destinationCountryDuration: Long,

    var destinationCountryDurationDetails: String,

    var visaEntryType: String,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "firstName", column = Column(name = "first_name")),
        AttributeOverride(name = "middleName", column = Column(name = "middle_name")),
        AttributeOverride(name = "lastName", column = Column(name = "last_name")),
        AttributeOverride(name = "companyName", column = Column(name = "company_name")),
        AttributeOverride(name = "citizenCountry", column = Column(name = "citizen_country")),
        AttributeOverride(name = "residenceCountry", column = Column(name = "residence_country")),
        AttributeOverride(name = "jobTitle", column = Column(name = "job_title")),
        AttributeOverride(name = "shareApplicantInfo", column = Column(name = "share_applicant_info")),
        AttributeOverride(name = "contactNo", column = Column(name = "contact_no")),
        AttributeOverride(name = "emailAddress", column = Column(name = "email_address")),
    )
    var applicant: Applicant ? =null,

    var duties:String?,

    var visaServiceType: String?

) : CaseEntity()