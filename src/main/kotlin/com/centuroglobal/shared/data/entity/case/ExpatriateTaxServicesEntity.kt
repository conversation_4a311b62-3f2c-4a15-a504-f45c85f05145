package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.*

@Entity(name = "expatriate_tax_services")
data class ExpatriateTaxServicesEntity(

    var hostCountry: String,

    var state: String?,

    var services: String?,

    var dateOfHire: Long,

    var assignmentStartDate: Long,

    var endDate: Long?,

    var serviceYears: Long?,

    var permanentTransfer: Boolean?,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "firstName", column = Column(name = "first_name")),
        AttributeOverride(name = "lastName", column = Column(name = "last_name")),
        AttributeOverride(name = "citizenCountry", column = Column(name = "citizen_country")),
        AttributeOverride(name = "residenceCountry", column = Column(name = "residence_country")),
        AttributeOverride(name = "jobTitle", column = Column(name = "job_title")),
        AttributeOverride(name = "applicantState", column = Column(name = "applicant_state")),
        AttributeOverride(name = "contactNo", column = Column(name = "contact_no")),
        AttributeOverride(name = "emailAddress", column = Column(name = "email_address")),
        AttributeOverride(name = "shareApplicantInfo", column = Column(name = "share_applicant_info"))

        )
    var applicant: TaxApplicant

) : CaseEntity()