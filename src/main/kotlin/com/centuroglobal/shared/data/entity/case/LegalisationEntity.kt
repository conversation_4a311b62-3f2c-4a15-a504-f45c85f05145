package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.*

@Entity(name = "legalisation")
data class LegalisationEntity(

    var nationality: String,

    var legalisedCountry: String?,

    var legalisedDocument: String?,

    var otherDocuments: String?,

    var firstName: String,

    var middleName: String?,

    var lastName: String,

    var shareApplicantInfo: Boolean?,

    var accountName: String?,

    var contactNo: String?,

    var emailAddress: String?,

    var applicantCountry: String?=null

): CaseEntity()