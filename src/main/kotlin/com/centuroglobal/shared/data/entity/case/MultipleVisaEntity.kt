package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.OneToMany

@Entity(name = "multiple_immigration_visa")
data class MultipleVisaEntity(

    var caseCountry: String,
    var numberVisaPermit: Long? = null,

    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true, mappedBy = "visaEntity", fetch = FetchType.EAGER)
    var traveller: MutableList<TravellerEntity> = mutableListOf(),

) : CaseEntity()