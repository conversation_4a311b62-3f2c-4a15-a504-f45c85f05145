package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.Entity

@Entity(name = "office_space")
data class OfficeAddressEntity(

    var caseCountry: String,
    var assistanceFindingOffice: Boolean = false,
    var contract: String? = null,
    var spaceType: String? = null,
    var seatingSpace: Long? = null

) : CaseEntity()