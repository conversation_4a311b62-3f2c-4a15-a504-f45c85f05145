package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.*

@Entity(name = "posted_worker_notification")
data class PostedWorkerNotificationEntity(

    var toCountry: String,
    var travelPurpose: String,
    var grossPayCurrency: String,
    var grossPay: Long? = null,
    var weeklyHours: Long? = null,
    var paidCountry: String,
    var sendPersonFirstName: String,
    var sendPersonLastName: String,
    var sendPersonEmail: String,
    var sendPersonShareInfo: Boolean,
    var postingStartDate: Long,
    var postingEndDate: Long,
    var postingWorkAddress: String,
    var receivingCompany: String,
    var receivingCompanyCity: String,
    var receivingCompanyAddress: String,
    var receivingCompanyEmail: String,
    var legalRepr: Boolean,
    var leageReprEmail: String,
    var receivePersonFirstName: String,
    var receivePersonLastName: String,
    var receivePersonEmail: String,
    var receivePersonShareInfo: Boolean,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "firstName", column = Column(name = "first_name")),
        AttributeOverride(name = "lastName", column = Column(name = "last_name")),
        AttributeOverride(name = "nationality", column = Column(name = "nationality")),
        AttributeOverride(name = "residenceCountry", column = Column(name = "residence_country")),
        AttributeOverride(name = "jobTitle", column = Column(name = "job_title")),
        AttributeOverride(name = "highestQualification", column = Column(name = "highest_qualification")),
        AttributeOverride(name = "contactNo", column = Column(name = "contact_no")),
        AttributeOverride(name = "emailAddress", column = Column(name = "email_address")),
        AttributeOverride(name = "shareApplicantInfo", column = Column(name = "share_applicant_info"))

        )
    var applicant: PostedWorkerApplicant

) : CaseEntity()