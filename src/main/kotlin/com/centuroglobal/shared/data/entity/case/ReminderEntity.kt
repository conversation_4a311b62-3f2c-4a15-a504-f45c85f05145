package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.enums.CaseDeadlineType
import com.centuroglobal.shared.data.enums.CaseDeadlineUnit
import com.centuroglobal.shared.data.enums.CaseTrackType
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "reminders")
data class ReminderEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var referenceId: Long,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var trackType: CaseTrackType,

    var deadline: Long? = null,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var deadlineUnit: CaseDeadlineUnit? = null,

    var date: LocalDateTime?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var deadlineAgainst: CaseDeadlineType?,

    var suggestion: String? = null,

    var documentId: Long? = null

) : AuditUserBaseEntity()