package com.centuroglobal.shared.data.entity.case

import com.centuroglobal.shared.data.entity.CaseEntity
import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.OneToMany

@Entity(name = "right_to_work_check")
data class RightToWorkCheckEntity(

    var nationality: String? = null,
    var caseCountry: String,
    var documentType: String? = null,
    var timeToWorkLeft: String? = null,

    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true, mappedBy = "rightToWorkCheckEntity", fetch = FetchType.EAGER)
    var applicantInfo: MutableList<ApplicantInfoEntity> = mutableListOf()

) : CaseEntity()