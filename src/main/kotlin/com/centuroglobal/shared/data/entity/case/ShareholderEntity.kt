package com.centuroglobal.shared.data.entity.case

import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "shareholder")
data class ShareholderEntity(

    var fullName: String? = null,

    var percentageShare: Double? = null,

    var serviceAddress: String? = null,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "entity_setup_case_id")
    @JsonIgnore
    var entitySetup: EntitySetupEntity? = null,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null
)