package com.centuroglobal.shared.data.entity.playbook

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import jakarta.persistence.*

@Entity(name = "playbook_chat")
data class PlaybookChatEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var question: String,

    var answer: String,

    @ManyToOne
    @JoinColumn(name = "playbook_session_id")
    var playbookSession: PlaybookSessionEntity

): AuditUserBaseEntity()