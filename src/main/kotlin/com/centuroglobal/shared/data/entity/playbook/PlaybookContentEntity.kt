package com.centuroglobal.shared.data.entity.playbook

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import jakarta.persistence.*

@Entity(name = "playbook_content")
data class PlaybookContentEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var data: String? = null,

    @OneToOne(cascade = [CascadeType.ALL])
    @JoinColumn(name = "playbook_id")
    var playbook: PlaybookEntity

): AuditUserBaseEntity()