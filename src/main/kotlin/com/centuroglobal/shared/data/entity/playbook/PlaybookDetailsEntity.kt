package com.centuroglobal.shared.data.entity.playbook

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import jakarta.persistence.*

@Entity(name = "playbook_details")
data class PlaybookDetailsEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var category: String,

    var subCategory: String,

    var isSelected: Boolean,

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "playbook_id")
    var playbook: PlaybookEntity

): AuditUserBaseEntity()