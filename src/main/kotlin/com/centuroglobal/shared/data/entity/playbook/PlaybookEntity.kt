package com.centuroglobal.shared.data.entity.playbook

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*

@Entity(name = "playbook")
data class PlaybookEntity (

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var country: String,

    var industry: String,

    var about: String,

    @OneToOne(cascade = [CascadeType.ALL], mappedBy = "playbook")
    var playbookContent: PlaybookContentEntity? = null,

    @OneToMany(cascade = [CascadeType.ALL], mappedBy = "playbook")
    var details: MutableList<PlaybookDetailsEntity> = mutableListOf(),

    @JsonIgnore
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "playbook_share",
        joinColumns = [JoinColumn(name = "playbook_id", referencedColumnName = "id")],
        inverseJoinColumns = [JoinColumn(name = "user_id", referencedColumnName = "id")]
    )
    var sharedWith: MutableSet<LoginAccountEntity> = mutableSetOf(),

    var industryName: String?

): AuditUserBaseEntity()