package com.centuroglobal.shared.data.entity.query

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.enums.ChatType
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "proposal")
data class ProposalEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var fileName: String,

    var fileType: String?,

    var fileSize: Long,

    var fileUploadDate: LocalDateTime = LocalDateTime.now(),

    var isApproved: Boolean = false,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var type: ChatType,

    var referenceId: Long

): AuditUserBaseEntity() {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ProposalEntity

        if (id != other.id) return false
        if (fileName != other.fileName) return false
        if (fileType != other.fileType) return false
        if (fileSize != other.fileSize) return false
        if (fileUploadDate != other.fileUploadDate) return false


        return true
    }

    override fun hashCode(): Int {
        var result = id?.hashCode() ?: 0
        result = 31 * result + fileName.hashCode()
        result = 31 * result + (fileType?.hashCode() ?: 0)
        result = 31 * result + fileSize.hashCode()
        result = 31 * result + fileUploadDate.hashCode()
        return result
    }
}