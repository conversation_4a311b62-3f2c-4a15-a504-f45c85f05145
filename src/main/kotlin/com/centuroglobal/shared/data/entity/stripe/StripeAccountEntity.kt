package com.centuroglobal.shared.data.entity.stripe

import com.centuroglobal.shared.data.entity.AuditBaseEntity
import jakarta.persistence.*

@Entity(name = "stripe_account")
data class StripeAccountEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(updatable = false)
    val userId: Long,

    @Column(updatable = false)
    val customerId: String,

    @Column
    val isActive: Boolean = true

) : AuditBaseEntity()
