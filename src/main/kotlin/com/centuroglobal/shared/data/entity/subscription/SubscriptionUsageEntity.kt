package com.centuroglobal.shared.data.entity.subscription

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.enums.subscription.SubscriptionPaymentStatus
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "subscription_usage")
data class SubscriptionUsageEntity @JvmOverloads constructor(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var name: String,

    var currency: String,

    var companyId: Long,

    var companyType: String,

    var price: Float,

    var overageCharge: Float = 0F,

    var date: LocalDateTime,

    @Enumerated(value = EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar")
    var status: SubscriptionPaymentStatus = SubscriptionPaymentStatus.UNPAID,

    @OneToMany(fetch = FetchType.EAGER, cascade = [CascadeType.ALL],
        orphanRemoval = true, mappedBy = "plan")
    var modules: MutableList<SubscriptionUsageDetailsEntity> = mutableListOf(),

    var startDate: LocalDateTime?,

    var endDate: LocalDateTime?

) : AuditUserBaseEntity()