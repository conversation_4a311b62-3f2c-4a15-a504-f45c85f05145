package com.centuroglobal.shared.data.entity.subscription.usage

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "usage_compliance_calendar")
data class ComplianceCalendarUsageEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Long? = null,

    var country: String,

    var accessedBy: String,

    var userId: Long,

    var bandName: String,

    var charges: Float,

    var createdAt: LocalDateTime?,

    var assetType: String?,

    var assetFor: String?,

    var name: String?,

    var referenceId: Long?,

    var expiry: LocalDateTime?,

    override var subscriptionUsageDetailsId: Long?

): AbstractUsageEntity()