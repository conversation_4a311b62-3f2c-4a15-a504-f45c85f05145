package com.centuroglobal.shared.data.entity.subscription.usage

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "usage_playbook")
data class PlaybookUsageEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Long? = null,

    var country: String,

    var industry: String,

    var accessedBy: String,

    var bandName: String,

    var charges: Float,

    var createdAt: LocalDateTime?,

    var userId: Long,

    var endTimestamp: LocalDateTime? = null,

    var sessionId: String?,

    override var subscriptionUsageDetailsId: Long?

): AbstractUsageEntity()