package com.centuroglobal.shared.data.entity.subscription.usage

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "usage_travel_assessment")
data class TravelAssessmentUsageEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Long? = null,

    var destinationCountry: String,

    var accessedBy: String,

    var charges: Float,

    var createdAt: LocalDateTime?,

    var userId: Long,

    var endTimestamp: LocalDateTime? = null,

    var sessionId: String?,

    var dossierCount: Long = 0,

    var assessmentId: Long =0,

    var type: String,

    override var subscriptionUsageDetailsId: Long?

): AbstractUsageEntity()