package com.centuroglobal.shared.data.entity.subscription.usage

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity(name = "usage_visa_assessment")
data class VisaAssessmentUsageEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Long? = null,

    var issueCountry: String,

    var destinationCountry: String,

    var accessedBy: String,

    var bandName: String,

    var charges: Float,

    var createdAt: LocalDateTime?,

    var userId: Long,

    override var subscriptionUsageDetailsId: Long?

): AbstractUsageEntity()