package com.centuroglobal.shared.data.entity.travel

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import jakarta.persistence.*

@Entity(name = "travel_assessment_feedback")
data class TravelAssessmentFeedbackEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var source: String,

    @Column(nullable = false)
    var destination: String,

    @Column(nullable = false)
    var content: String

) : AuditUserBaseEntity()
