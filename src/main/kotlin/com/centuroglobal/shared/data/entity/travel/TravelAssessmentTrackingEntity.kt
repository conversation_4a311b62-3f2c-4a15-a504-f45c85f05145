package com.centuroglobal.shared.data.entity.travel

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import jakarta.persistence.*

@Entity(name = "travel_assessment_tracking")
data class TravelAssessmentTrackingEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var step: String,

    @Column(nullable = false)
    var request: String,

    @Column(nullable = false)
    var response: String?,

    @ManyToOne(cascade = [CascadeType.ALL])
    @JoinColumn(name = "assessment_id")
    var assessment: TravelAssessmentEntity

):AuditUserBaseEntity()
