package com.centuroglobal.shared.data.entity.travel

import com.centuroglobal.shared.data.entity.AuditUserBaseEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.travel.TravelStayUnit
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity(name = "travel_history")
data class TravelHistoryEntity(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(nullable = false)
    var originCountry: String,

    @Column(nullable = false)
    var destinationCountry: String,

    @Column(nullable = false)
    var arrival: LocalDateTime,

    @Column(nullable = false)
    var departure: LocalDateTime,

    @Column(nullable = false)
    var periodOfStay: Long,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = "varchar")
    var stayUnit: TravelStayUnit,

    @Column(nullable = false)
    var purpose: String,

    @Column(nullable = false)
    var originCity: String?,

    @Column(nullable = false)
    var destinationCity: String?,

    @ManyToOne
    @JoinColumn(name = "user_id")
    var user: LoginAccountEntity

):AuditUserBaseEntity()
