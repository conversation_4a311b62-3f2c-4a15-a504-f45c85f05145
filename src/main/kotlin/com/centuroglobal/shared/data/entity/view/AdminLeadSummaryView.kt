package com.centuroglobal.shared.data.entity.view

import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.LeadType
import jakarta.persistence.*
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime

@Entity
@Subselect(
    value = """
SELECT
    rq.id, rq.title, e.id as expertise_id, rq.country_code, rq.region_id, rq.status,
    response_count, rq.created_date, lead_type, lead_type_id, rq.created_by,
    (select GROUP_CONCAT(e1.name) from lead_expertise le1 LEFT JOIN expertise e1 ON le1.expertise_id = e1.id where le1.lead_id = rq.id) as expertise_name,
    CASE
        WHEN lead_type = 'EXPERT' THEN ecp.name
        WHEN lead_type = 'CORPORATE' THEN c.name
        END AS company_name,
    CASE    
        WHEN lead_type = 'EXPERT' THEN eu.display_name
        ELSE CONCAT(la_cb.first_name, ' ', la_cb.last_name)
        END AS created_by_name
FROM
    lead_request rq
        LEFT JOIN login_account la_ti ON rq.lead_type_id = la_ti.id
        LEFT JOIN login_account la_cb ON rq.created_by = la_cb.id
        LEFT JOIN corporate c ON rq.lead_type_id = c.id
        LEFT JOIN expert_user eu ON la_ti.id = eu.id
        LEFT JOIN expert_company_profile ecp ON eu.company_profile_id = ecp.id
        LEFT JOIN lead_expertise le ON le.lead_id = rq.id
        LEFT JOIN expertise e ON e.id = le.expertise_id
"""
)
data class AdminLeadSummaryView(

    @Id
    val id: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    val leadType: LeadType,

    val leadTypeId: Long,

    val createdByName: String,

    val title: String,

    val expertiseName: String,

    val expertiseId: Int?,

    val countryCode: String,

    val regionId: Int?,

    val companyName: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    val status: LeadStatus,

    val createdDate: LocalDateTime,

    val responseCount: Int
)
