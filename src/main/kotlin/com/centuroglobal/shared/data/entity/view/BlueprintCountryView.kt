package com.centuroglobal.shared.data.entity.view

import com.centuroglobal.shared.data.enums.BlueprintStatus
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity
@Subselect(
    value = """
    select a.* from (
    select
           b.country_code as country_code,
           b.status as status,
           b.created_date as created_date,
           b.last_updated_date as last_updated_at,
           la.email as updated_by,
           if((select coalesce(count(s.blueprint_country_code),0) from step s where s.blueprint_country_code = b.country_code) = 0, 1 ,0) as is_no_data
    from blueprint b
    left outer join login_account la on b.last_updated_by = la.id
                    ) a
    where a.country_code is not null
"""
)
data class BlueprintCountryView(
    @Id
    val countryCode: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: BlueprintStatus,

    var updatedBy: String?,

    var createdDate: LocalDateTime,

    var lastUpdatedAt: LocalDateTime,

    var isNoData: Boolean,

    @Transient
    var countryName: String? = null
) {
    object ModelMapper {
        fun from(view: BlueprintCountryView): BlueprintCountryView = view
    }
}
