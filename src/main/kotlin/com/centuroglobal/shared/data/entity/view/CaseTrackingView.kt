package com.centuroglobal.shared.data.entity.view


import com.centuroglobal.shared.data.enums.CaseDeadlineType
import jakarta.persistence.*
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime

@Entity
@Subselect(
    value = """
        
       SELECT 
                c.initiated_for AS applicant, 
                c.id AS reference_id,
                c.created_by AS case_owner,
                COALESCE(sv.visa_type, mv.visa_type, rwc.visa_type, dv.visa_type, bv.visa_type) AS visa_type,
                COALESCE(sv.visa_issue_date, mv.visa_issue_date, rwc.visa_issue_date, dv.visa_issue_date, bv.visa_issue_date) AS visa_issue_date,
                COALESCE(sv.visa_expiry_date, mv.visa_expiry_date, rwc.visa_expiry_date, dv.visa_expiry_date, bv.visa_expiry_date) AS visa_expiry_date,
                COALESCE(sv.visa_renewal_date, mv.visa_renewal_date, rwc.visa_renewal_date, dv.visa_renewal_date, bv.visa_renewal_date) AS visa_renewal_date,
                COALESCE(sv.case_country, rwc.case_country, bv.destination_country) AS country_of_travel,
                ct.id,
                ct.date,
                ct.suggestion,
                ct.deadline_against AS notification_type,
                cd.document_code AS document_name,
                cd.id AS document_id,
                null AS country,
                null AS company_name
        
        FROM cases c 
        INNER JOIN reminders ct ON ct.reference_id = c.id
        LEFT JOIN single_immigration_visa sv ON sv.id = c.id
        LEFT JOIN multiple_immigration_visa mv ON mv.id = c.id
        LEFT JOIN right_to_work_check rwc ON rwc.id = c.id
        LEFT JOIN dependent_visa dv ON dv.id = c.id
        LEFT JOIN business_visa bv ON bv.id = c.id
        LEFT JOIN case_documents cd ON cd.id = ct.document_id
        
        WHERE ct.deadline_against <> 'COMPANY_DOCUMENT_EXPIRY'
        
        UNION
        
       SELECT 
				CONCAT(la.first_name, " ", la.last_name) AS applicant, 
                ct.reference_id,
                0 AS case_owner,
                null AS visa_type,
                null AS visa_issue_date,
                null AS visa_expiry_date,
                null AS visa_renewal_date,
                null AS country_of_travel,
                ct.id,
                ct.date,
                ct.suggestion,
                ct.deadline_against AS notification_type,
                cld.doc_name AS document_name,
                cld.id AS document_id,
                cld.country AS country,
                co.name AS company_name
        
        FROM corporate co 
        INNER JOIN client_docs cld ON cld.reference_id = co.id 
        INNER JOIN reminders ct ON ct.document_id = cld.id
        LEFT JOIN login_account la ON la.id = cld.created_by
        WHERE ct.deadline_against = 'COMPANY_DOCUMENT_EXPIRY'
     
    """
)
data class CaseTrackingView(
    @Id var id: Long? = null,

    var visaType: String?,

    var visaIssueDate: Long?,

    var visaExpiryDate: Long?,

    var visaRenewalDate: Long?,

    var applicant: String,

    var referenceId: Long,

    var date: LocalDateTime?,

    var suggestion: String?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var notificationType: CaseDeadlineType,

    var documentName: String?,

    var caseOwner: Long,

    var documentId: Long?,

    var countryOfTravel: String?,

    var country: String?,

    var companyName: String?

)