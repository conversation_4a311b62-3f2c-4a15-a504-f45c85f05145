package com.centuroglobal.shared.data.entity.view

import com.centuroglobal.shared.attributeConverter.StringListAttributeConverter
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity
@Subselect(
    value = """
    SELECT a.* FROM
    (
        SELECT c.id,name,about,banner_photo_key,country_codes,country_codes as country_codes_string,status,circle_type,created_date,
           coalesce((select count(circle_member.id) from circle_member where circle_member.circle_id = c.id and circle_member.is_active = true and circle_member.circle_member_status = 0),0) as members,
           coalesce((select count(circle_member.id) from circle_member where circle_member.circle_id = c.id and circle_member.is_active = true),0) as invitee,
           coalesce((select count(circle_request.id) from circle_request where circle_request.circle_id = c.id and circle_request.is_active = true),0) as requests,
           (select concat('0,',group_concat(expertise_id)) from circle_expertise where circle_id = c.id) as expertise_ids,
           ce.expertise_id as expertise_id
        FROM circle c
            LEFT JOIN circle_expertise ce ON ce.circle_id = c.id
        WHERE is_active = true
    ) a
    WHERE a.id IS NOT NULL
"""
)
data class CircleView(
    @Id
    var id: Long,

    var name: String?,

    var about: String?,

    var bannerPhotoKey: String? = null,

    @Convert(converter = StringListAttributeConverter::class)
    var countryCodes: List<String>? = mutableListOf(),

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: CircleStatus,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var circleType: CircleType,

    val expertiseIds: String?,

    var createdDate: LocalDateTime,

    var members: Int,

    var invitee: Int,

    var requests: Int,

    val expertiseId: Int?,

    val countryCodesString: String?
)
