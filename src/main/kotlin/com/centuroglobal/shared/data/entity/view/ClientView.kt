package com.centuroglobal.shared.data.entity.view

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.UserType
import jakarta.persistence.*
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime

@Entity
@Subselect(
    value = """
    SELECT a.* FROM
    (
               SELECT IF(la.user_type = 'EXPERT', ecp.id, c.id) as id,
               TRIM(CONCAT(la.first_name, ' ', la.last_name)) as full_name, 
               la.email as email,
               IF(la.user_type = 'EXPERT', ecp.name, c.name) AS company,
               la.country_code AS country_code,
               IF(la.user_type = 'EXPERT', eu.job_title, cu.job_title) AS job_title,
               IF(la.role = 'ROLE_CORPORATE', 0, 1) AS subscription,
               la.user_type, la.status, la.created_date, rla.id as referred_by_id, rla.email as referred_by,
               coalesce((select count(id) from login_account where referred_by = la.id),0) as referral,
               la.id as user_id,
               (select group_concat(expertise_id) from expert_expertise where expert_id = la.id) as expertise_ids,
               ee.expertise_id as expertise_id, la.is_linkedin as is_linkedin,
               la.subscription_type,
               IF(la.user_type = 'EXPERT', eu.expert_type, '') AS expert_type,
               IF(la.user_type = 'CORPORATE', cu.corporate_user_type, '') AS corporate_type,
               coalesce((select count(id) from corporate_user where corporate_id = c.id),0) as secondary_user_count,
               IF(la.user_type = 'CORPORATE', (select name from bands where id=cu.band_id), '') AS band_name,
               IF(la.user_type = 'EXPERT', pe.partner_id, c.partner_id) AS partner_id,
               la.last_login_date,
               la.last_terms_view_date
        FROM login_account la
                 LEFT JOIN expert_user eu ON eu.id = la.id
                 LEFT JOIN expert_company_profile ecp ON ecp.id = eu.company_profile_id
                 LEFT JOIN partner_experts pe ON pe.ecp_id = ecp.id
                 LEFT JOIN corporate_user cu ON cu.id = la.id
                 LEFT JOIN corporate c ON c.id = cu.corporate_id
                 LEFT JOIN login_account rla ON rla.id = la.referred_by
                 LEFT JOIN expert_expertise ee ON ee.expert_id = la.id
        WHERE la.user_type IN ('EXPERT' , 'CORPORATE')
          AND la.status IN ('ACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION', 'DRAFT')
    ) a
    WHERE a.id IS NOT NULL group by a.user_id
"""
)
data class ClientView(
    var id: Long,

    var email: String,

    var fullName: String,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var status: AccountStatus,

    var company: String?,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    var userType: UserType,

    var subscription: Boolean,

    var createdDate: LocalDateTime,

    var referredById: Long?,

    var referredBy: String?,

    val referral: Int,

    @Id
    val userId: Long,

    val countryCode: String?,

    val expertiseIds: String?,

    val expertiseId: Long?,

    val isLinkedin: Boolean,

    var subscriptionType: String? = null,

    var expertType: String? = null,

    var jobTitle: String?=null,

    var corporateType: String?=null,

    var secondaryUserCount: Long?,

    var bandName: String?=null,

    var partnerId: Long? = null,

    var lastLoginDate: LocalDateTime?=null,

    var lastTermsViewDate: LocalDateTime?=null
)