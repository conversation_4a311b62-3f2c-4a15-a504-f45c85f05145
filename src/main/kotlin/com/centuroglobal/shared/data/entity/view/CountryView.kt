package com.centuroglobal.shared.data.entity.view

import jakarta.persistence.*
import org.hibernate.annotations.Subselect

@Entity
@Subselect(
    value = """
    select c.* from countries c
    """
)
data class CountryView(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    open var id: Long? = null,

    @Column(nullable = false)
    open var countryCode: String,

    @Column(nullable = false)
    open var country: String,

    @Column(nullable = false)
    open var isoCoutryCode: String,

    @Column(nullable = false)
    open var overallRating: Long?,

    @Column(nullable = false)
    open var entrepreneurshipRating: Long?,

    @Column(nullable = false)
    open var openForBusinessRating: Long?,

    @Column(nullable = false)
    open var qualityOfLifeRating: Long?,

    @Column(nullable = false)
    open var socialPurposeRating: Long?,

    @Column(nullable = false)
    open var globalTravelRiskRating: Long?,

    @Column(nullable = false)
    open var gdpRating: Long?,

    @Column(nullable = false)
    open var economicFreedomRating: Long?,

    @Column(nullable = false)
    open var highestInflationRateRating: Long?,

    @Column(nullable = false)
    open var lowestInflationRateRating: Long?,

    @Column(nullable = false)
    open var dialCode: String?
)