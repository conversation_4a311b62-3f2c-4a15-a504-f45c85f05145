package com.centuroglobal.shared.data.entity.view


import jakarta.persistence.Entity
import jakarta.persistence.Id
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime

@Entity
@Subselect(
    value = """
    select cda.*, cdf.file_name from case_documents_audit cda join case_document_file cdf on cda.file_id = cdf.id 
    """
)
data class DocumentAuditView(
    @Id
    var id: Long? = null,

    var caseId: Long,

    var ipAddress: String,

    var fileName: String?,

    var action: String,

    var createdBy: Long,

    var createdDate: LocalDateTime

)