package com.centuroglobal.shared.data.entity.view

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.SubscriptionType
import org.hibernate.annotations.Subselect
import java.time.LocalDateTime
import jakarta.persistence.*

@Entity
@Subselect(
    value = """
    select * from login_account
"""
)
data class UserView(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    open var id: Long? = null,

    @Column(unique = true, updatable = true)
    open var email: String,

    open var firstName: String,

    open var lastName: String,

    open var password: String,

    open var tempPassword: String? = null,

    open var oneTimePassword: String? = null,

    open var passwordCreationDate: LocalDateTime? = null,

    open var refreshToken: String? = null,

    open var loginFailCount: Int? = 0,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    open var status: AccountStatus,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    open var role: Role,

    var lastUpdatedBy: Long,

    open var tour: Boolean,

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar")
    open var subscriptionType: SubscriptionType? = SubscriptionType.FREE,

    open var onboard: Boolean = false,

    open var isLinkedin: Boolean = false,

    open var referredBy: Long? = null,

    open var tncView: Boolean = false,

    open var lastTermsViewDate: LocalDateTime? = null,

    open var lastLoginDate: LocalDateTime? = null)