package com.centuroglobal.shared.data.enums

enum class CompanySize(val displayName: String, val min: Int?, val max: Int?) {
    <PERSON>ze0("Self Employed", null, null),
    <PERSON><PERSON>1("1-10", 1, 10),
    <PERSON><PERSON>2("11-50", 11, 50),
    <PERSON><PERSON><PERSON>("51-200", 51, 200),
    <PERSON><PERSON><PERSON>("210-500", 210, 500),
    <PERSON><PERSON><PERSON>("501-1000", 501, 1000),
    <PERSON><PERSON><PERSON>("1001-5000", 1001, 5000),
    <PERSON><PERSON><PERSON>("5001-10,000", 5001, 10000),
    <PERSON><PERSON>8(">10,000", 10000, Int.MAX_VALUE);
}
