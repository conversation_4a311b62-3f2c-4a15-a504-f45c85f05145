package com.centuroglobal.shared.data.enums

enum class TokenType(val value: String) {
    BEARER("bearer")
}

enum class GrantType(val value: String, val action: String) {
    PASSWORD("password", "login"),
    REFRESH_TOKEN("refresh_token", "refreshToken"),
    LINKEDIN("linkedin", "code");

    companion object {
        private val map = values().associateBy(GrantType::value)
        fun fromValue(type: String) = map[type]
    }
}

enum class Scope(val value: String) {
    APP("app");

    companion object {
        private val map = values().associateBy(Scope::value)
        fun fromValue(type: String?) = map[type]

        fun fromUserType(userType: String?): Scope? {
            return when (userType) {
                UserType.BACKOFFICE.name, UserType.CORPORATE.name, UserType.EXPERT.name, UserType.PARTNER.name -> APP
                else -> null
            }
        }
    }
}