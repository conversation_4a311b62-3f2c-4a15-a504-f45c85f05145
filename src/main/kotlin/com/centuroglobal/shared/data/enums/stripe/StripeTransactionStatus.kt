package com.centuroglobal.shared.data.enums.stripe

import mu.KotlinLogging
import java.util.*

private val log = KotlinLogging.logger {}

enum class StripeTransactionStatus(val key: String) {
    // Payment Intent Statuses
    REQUIRES_PAYMENT_METHOD("requires_payment_method"),
    REQUIRES_CONFIRMATION("requires_confirmation"),
    REQUIRES_ACTION("requires_action"),
    REQUIRES_CAPTURE("requires_capture"),
    PROCESSING("processing"),
    SUCCEEDED("succeeded"),
    CANCELED("canceled"), // Valid status for payment intend and subscription

    // Subscription Statuses
    INCOMPLETE("incomplete"),
    INCOMPLETE_EXPIRED("incomplete_expired"),
    TRIALING("trialing"),
    PAST_DUE("past_due"),
    UNPAID("unpaid"),
    ACTIVE("active"),

    // Invoice Statuses
    DRAFT("draft"),
    OPEN("open"),
    PAID("paid"),
    UNCOLLECTIBLE("uncollectible"),

    // Not sure where these statuses are from, Can't find in documentation...
    REQUIRES_SOURCE_ACTION("requires_source_action"),
    REQUIRES_SOURCE("requires_source"),
    UPDATED("updated"),

    // Internal Statuses
    CREATED("created"), // Used internally, have created transaction, but not yet sent to stripe
    REQUEST_CANCELLATION("request_cancellation"), // Used internally, have requested subscription cancellation, but still active until the end of period
    UNDO_REQUEST_CANCELLATION("undo_request_cancellation"), // Used internally, have requested subscription cancellation, but still active until the end of period

    // Generic
    UNKNOWN("unknown");

    companion object {
        @JvmStatic
        fun fromKey(key: String): StripeTransactionStatus {
            return try {
                valueOf(key.uppercase(Locale.getDefault()))
            } catch(ex: Exception) {
                log.warn("Unable to map [$key] to StripeTransactionStatus. Fallback to [UNKNOWN].")
                UNKNOWN
            }
        }
    }
}
