package com.centuroglobal.shared.data.payload

class HubspotRequest {
    var properties: Properties? = null
}

class HubspotResponse {
    var id: String? = null
    var properties: Any? = null
    var createdAt: String? = null
    var updatedAt: String? = null
    var archived: Boolean? = null
}

class Properties {
    var email: String? = null
    var firstname: String? = null
    var lastname: String? = null

    var cg_platform_status: String? = null
    var last_login: Long? = null
    var subscription_type: String? = null
    var contact_category: String? = null
    var jobtitle: String? = null
    var company: String? = null
    var contract_start_date: Long? = null
    var contract_end_date: Long? = null
    var member_type: String? = null
    var opted_in_to_services_and_offering_marketing: Boolean = false
}