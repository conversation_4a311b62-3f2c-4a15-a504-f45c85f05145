package com.centuroglobal.shared.data.payload.account

import io.swagger.v3.oas.annotations.media.Schema

data class CompanyRequest(

    @Schema()
    val name: String,
    @Schema()
    val companyNumber: String,
    @Schema()
    val companyAddress: String,
    @Schema()
    val aboutBusiness: String,

    @Schema()
    val logoFullUrl: String="",
    @Schema()
    val services: String,
    @Schema()
    val size: String?,
    @Schema()
    val sizeName: String?,
    @Schema()
    val effectiveDate: Long,
    @Schema()
    val effectiveEndDate: Long,
    @Schema()
    val feesCurrency: String = "USD",
    @Schema()
    val feesAmount: String = "0",
    @Schema()
    val specialTerms: String = "",
    @Schema()
    val membershipStatus: String = "",
    @Schema()
    val summary: String?,
    @Schema()
    val territory: String?,
    @Schema()
    val contractAcceptedDate: Long = 0,
    @Schema()
    var partnerId: Long? = null,
    @Schema()
    val contract:String? = null,
    val aiMessageCount: Long  =0

)