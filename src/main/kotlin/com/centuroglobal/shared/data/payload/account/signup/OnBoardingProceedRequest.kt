package com.centuroglobal.shared.data.payload.account.signup

import com.centuroglobal.shared.data.entity.OnBoardingEntity
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class OnBoardingProceedRequest(

    var userType: String?,

    var companySize: String?,

    var organization: String?,

    var expansion: String?,

    var market: List<String>?,

    var challenge: List<String>?,

    var countryCode: String?,

    var platformUser: String?,

    var stage: String?,

    var regJobTitle: String?,

    var regCompanyName: String,

    var regCountryCode: String?
) {
    object ModelMapper {
        fun fromEntity(entity: OnBoardingEntity): OnBoardingProceedRequest = OnBoardingProceedRequest(
            entity.userType,
            entity.companySize,
            entity.organization,
            entity.expansion,
            entity.market,
            entity.challenge,
            entity.countryCode,
            entity.platformUser,
            entity.stage,
            null,
            "",
            null
        )
    }
}
