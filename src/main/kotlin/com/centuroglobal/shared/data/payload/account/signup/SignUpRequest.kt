package com.centuroglobal.shared.data.payload.account.signup

import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansRequest
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Pattern

@JsonIgnoreProperties(ignoreUnknown = true)
data class SignUpRequest(

    @field:Email
    @Schema(required = true)
    val email: String,

    @Schema()
    val firstName: String?,

    @Schema()
    val lastName: String?,

    @Schema()
    val jobTitle: String?,

    @Schema( required = true)
    val corporateName: String,

    @field:Pattern(regexp = "[A-Za-z]{2}")
    @Schema( required = true)
    val countryCode: String,

    @Schema()
    val referralCode: String?,

    @Schema()
    val keepMeInformed: Boolean =false,

    @Schema()
    val recaptchaResponse: String? = null,

    @Schema()
    val primaryColor : String? = null,

    @Schema()
    val secondaryColor:String? = null,

    @Schema()
    val companyLogoId:String? = null,

    @Schema()
    val assignedTeam: List<AssignedTeam>? = null,

    @Schema()
    val onboardingDocs: List<OnboardingDocs>? = null,

    @Schema()
    val aiMessageCount: Long = 0,

    var partnerId: Long? = null,

    var subscriptionPlan: String? = null,

    var customSubscription: SubscriptionPlansRequest? = null,

    var subscriptionStartDate: Long? = null,

    var subscriptionEndDate: Long? = null,

    val features: List<String>? = mutableListOf(),

    val isTeamEmail: Boolean = false,

    val isDraft: Boolean = false,

    val visaSignAuthName: String? = null,

    val visaSignAuthDesignation: String? = null,

    val modeOfPayment: String? = null,

    val signatureKey: String? = null,

    val businessLetterKey: String? = null,

    val visaFeesKey: String? = null

)

data class AssignedTeam (
    val designation: String,
    val userId: Long
)

data class OnboardingDocs (
    val id: Long?,
    val docName: String,
    val s3Key: String,
    val url: String?,
    val size: Long,
    val type: String,
    val fileName: String,
    val uploadDate: Long? = null
)
