package com.centuroglobal.shared.data.payload.account.signup

import com.centuroglobal.shared.data.enums.AccountStatus
import io.swagger.v3.oas.annotations.media.Schema

data class UpdateAccountStatusRequest(
    @Schema(
        required = true
    )
    val userId: Long,
    @Schema(
        required = true,
        description = "Account status to update.",
        allowableValues = ["ACTIVE", "SUSPENDED"]
    )
    val accountStatus: AccountStatus
)
