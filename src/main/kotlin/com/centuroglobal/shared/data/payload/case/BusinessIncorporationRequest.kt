package com.centuroglobal.shared.data.payload.case

data class BusinessIncorporationRequest(

    var id: Long? = null,
    var country: String? = null,
    var formType: String,
    var firstName: String,
    var lastName: String,
    var companyName: String,
    var companyHqCountry: String,
    var companyHqAddress: String,
    var newCompanyBranchCountry: String,
    var newCompanyBranchAddress: String?,
    var typeOfBusiness: String,
    var otherDetails: String? = null,
    val caseOwner: Long
)