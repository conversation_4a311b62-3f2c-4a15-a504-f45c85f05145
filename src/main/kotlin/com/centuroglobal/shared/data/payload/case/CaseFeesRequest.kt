package com.centuroglobal.shared.data.payload.case

import com.centuroglobal.shared.data.entity.case.ApproverDetails
import com.centuroglobal.shared.data.entity.case.CaseFees
import com.centuroglobal.shared.data.entity.case.FeeDetails
import com.centuroglobal.shared.util.TimeUtil

data class CaseFeesRequest(
    val comments: String? = null,
    val approverEmails: String? = null,
    val needApproval: Boolean = false,
    val isApproved: Boolean = false,
    val feesDetails: FeeDetailsRequest?=null,
    val approverDetails: ApproverDetailsRequest?=null

){
    object ModelMapper {
        fun from(fees: CaseFees): CaseFeesRequest {
            return CaseFeesRequest(
                comments = fees.comments,
                approverEmails = fees.approverEmails,
                needApproval = fees.needApproval,
                isApproved = fees.isApproved,
                feesDetails= FeeDetailsRequest.ModelMapper.from(fees.feesDetails),
                approverDetails = ApproverDetailsRequest.ModelMapper.from(fees.approverDetails))
        }
    }
}

data class FeeDetailsRequest(
    val professionalFees: Double? = null,
    val governmentFees: Double? = null,
    val apostilleFees: Double? = null,
    val certificateFees: Double? = null,
    val translationFees: Double? = null,
    val thirdPartyFees: Double? = null,
    val totalFees: Double? = null,
    val incomeRaised: Boolean? = false,
    val incomePaid: Boolean? = false,
    val dependentFees: Double?=null,
    val feesCurrency: String? = null
){
    object ModelMapper {
        fun from(fees: FeeDetails?): FeeDetailsRequest {
            return FeeDetailsRequest(
                professionalFees =fees?.professionalFees,
                governmentFees = fees?.governmentFees,
                apostilleFees = fees?.apostilleFees,
                certificateFees = fees?.certificateFees,
                translationFees = fees?.translationFees,
                thirdPartyFees = fees?.thirdPartyFees,
                totalFees = fees?.totalFees,
                incomeRaised = fees?.incomeRaised,
                incomePaid = fees?.incomePaid,
                dependentFees = fees?.dependentFees,
                feesCurrency = fees?.feesCurrency
            )
        }
    }
}

data class ApproverDetailsRequest(
    val firstName: String? = null,
    val lastName: String? = null,
    val jobTitle: String? = null,
    val email: String? = null,
    val feesAgreedDate: Long? = null,
    val feeToken: String?=null,
){
    object ModelMapper {
    fun from(approverDetails: ApproverDetails?): ApproverDetailsRequest {
        return ApproverDetailsRequest(
            firstName = approverDetails?.firstName,
            lastName = approverDetails?.lastName,
            jobTitle = approverDetails?.jobTitle,
            email = approverDetails?.email,
            feesAgreedDate = if(approverDetails?.feesAgreedDate!=null)
                TimeUtil.toEpochMillis(approverDetails.feesAgreedDate!!) else null)
        }
    }
}