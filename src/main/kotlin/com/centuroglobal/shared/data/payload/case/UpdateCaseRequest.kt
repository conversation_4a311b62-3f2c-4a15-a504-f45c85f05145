package com.centuroglobal.shared.data.payload.case

import io.swagger.v3.oas.annotations.media.Schema

data class UpdateCaseRequest(

    @Schema(required = true)
    val status: String,
    val statusUpdate: String? = null,
    val startDate: Long?,
    val assignCompany: Long?,
    val documents: List<DocumentRequest>? = null,
    val notifyPrimaryExpert: Boolean,
    val isPriorityCase: Boolean,
    var visaIssueDate: Long? = null,
    var visaExpiryDate: Long? = null,
    var estimatedTimeline: String? = null,
    var caseOwner: Long? = null,
    var isStatusChange: Boolean = false,
    var actionFor : String,
//admin only data
    val experts: MutableList<Long>? = null,
    val managers: MutableList<Long>? = null,
    val accountManager: Long? = null,
    val visaType: String? = null,
    var notes: String?=null,
    var notifyCaseOwner: Boolean = true,
    var notifyApplicant: Boolean = false,
    var accountId : Long =0,
    var isVisaDateChanged: Boolean = false,
    var sendUpdateReminder: Boolean? = null
)