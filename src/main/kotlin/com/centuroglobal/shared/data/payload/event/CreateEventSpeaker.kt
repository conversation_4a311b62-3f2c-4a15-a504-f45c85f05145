package com.centuroglobal.shared.data.payload.event

import com.centuroglobal.shared.data.enums.EventSpeakerType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.event.EventClientProfile

data class CreateEventSpeaker(
    var id: Long? = null,
    val type: EventSpeakerType,
    val internalMemberId: Long?,
    val internalMemberRole: Role?,
    val isHost: Boolean = false,

    val profile: EventClientProfile?
)
