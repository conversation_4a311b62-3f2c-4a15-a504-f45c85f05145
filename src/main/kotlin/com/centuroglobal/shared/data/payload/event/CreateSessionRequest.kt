package com.centuroglobal.shared.data.payload.event

import com.fasterxml.jackson.annotation.JsonFormat
import org.springframework.format.annotation.DateTimeFormat
import java.util.*

data class CreateSessionRequest(
    var id: Long? = null,

    var name: String,

    var about: String,

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    var date: Date,

    @DateTimeFormat(pattern = "hh:mm a")
    @JsonFormat(pattern = "hh:mm a")
    var startTime: Date,

    @DateTimeFormat(pattern = "hh:mm a")
    @JsonFormat(pattern = "hh:mm a")
    var endTime: Date,

    var duration: String,

    var timeZone: String,

    var speakers: MutableList<Long> = mutableListOf()
)
