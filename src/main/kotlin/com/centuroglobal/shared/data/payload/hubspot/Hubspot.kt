package com.centuroglobal.shared.data.payload.hubspot

class Hubspot {
    var total: Int? = null
    var results: List<Result> = ArrayList()
    var paging: Paging? = null
}

class Result {
    var id: String? = null
    var properties: Properties? = null
    var createdAt: String? = null
    var updatedAt: String? = null
    var archived: Boolean? = null
}

class Properties {
    var createdate: String? = null
    var email: String? = null
    var firstname: String? = null
    var hs_object_id: String? = null
    var lastmodifieddate: String? = null
    var lastname: String? = null
}

class Paging {
    var next: Next? = null
}

class Next {
    var after: String? = null
}