package com.centuroglobal.shared.data.payload.openai

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateMessageRequest(

    val role: String,

    val content: String,

    val attachments: List<Attachment>? = null
)

data class Attachment(
    @JsonProperty("file_id")
    val fileId: String,

    val tools: List<Tool>
)

data class Tool(
    val type: String
)