package com.centuroglobal.shared.data.payload.openai

import com.centuroglobal.shared.data.entity.AIChatAnnotationEntity
import com.fasterxml.jackson.annotation.JsonProperty


data class LangchainResponse(
    val type: String,
    val text: String,
    val annotations: List<LangChainAnnotation>
)

data class LangChainAnnotation(
    @JsonProperty("end_index")
    val endIndex: Int,
    @JsonProperty("start_index")
    val startIndex: Int,
    val title: String,
    val type: String,
    val url: String
)

data class Annotation(
    val endIndex: Int,
    val startIndex: Int,
    val title: String,
    val type: String,
    val url: String
) {
    object ModelMapper {
        fun from(langChainAnnotation: LangChainAnnotation) =
            Annotation(
                endIndex = langChainAnnotation.endIndex,
                startIndex = langChainAnnotation.startIndex,
                title = langChainAnnotation.title,
                type = langChainAnnotation.type,
                url = langChainAnnotation.url
            )
        fun fromEntity(entity: AIChatAnnotationEntity) =
            Annotation(
                endIndex = entity.endIndex,
                startIndex = entity.startIndex,
                title = entity.title,
                type = entity.type,
                url = entity.url
            )
    }
}