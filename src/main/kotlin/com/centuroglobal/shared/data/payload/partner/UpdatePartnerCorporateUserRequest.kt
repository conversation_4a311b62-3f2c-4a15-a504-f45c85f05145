package com.centuroglobal.shared.data.payload.partner

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Pattern

data class UpdatePartnerCorporateUserRequest(

    @Schema
    val firstName: String,

    @Schema
    val lastName: String,

    @Schema
    val jobTitle: String,

    @Schema
    val corporateName: String,

    @field:Pattern(regexp = "[A-Za-z]{2}")
    @Schema
    val countryCode: String,

    @Schema
    val keepMeInformed: Boolean,

    @Schema
    val primaryColor: String? = null,

    @Schema
    val secondaryColor: String? = null,

    @Schema
    val companyLogoId: String? = null

)