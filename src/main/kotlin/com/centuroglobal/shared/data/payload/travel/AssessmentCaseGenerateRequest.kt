package com.centuroglobal.shared.data.payload.travel

import com.fasterxml.jackson.annotation.JsonProperty

data class AssessmentCaseGenerateRequest(

    @JsonProperty("assessment_data")
    val assessmentData: Map<String, Any?>,

    @JsonProperty("case_type")
    val caseType: String,

    @JsonProperty("contact_person")
    val contactPerson: Long,

    @JsonProperty("case_forms")
    val caseForms: List<Map<String, Any>> = emptyList(),

    @JsonProperty("case_form_id")
    val caseFormId: Long? = null

)
