package com.centuroglobal.shared.data.payload.travel

import com.centuroglobal.shared.data.enums.travel.TravelStayUnit
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank

data class TravelHistoryRequest(

    @Schema(required = true)
    @field:NotBlank
    val originCountry: String,

    @Schema(required = true)
    @field:NotBlank
    val destinationCountry: String,

    val originCity: String?,

    val destinationCity: String?,

    @Schema(required = true)
    val arrival: Long,

    @Schema(required = false)
    val departure: Long?,

    @Schema(required = false)
    val periodOfStay: Long?,

    @Schema(required = true)
    val stayUnit: TravelStayUnit?,

    @Schema(required = true)
    @field:NotBlank
    val purpose: String,

    @Schema(required = false)
    val userId: Long?

    )
