package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.util.TimeUtil

data class AccountListing(

    val id: Long,
    val name: String,
    val status: AccountStatus,
    val createdDate: Long,
    val updatedDate: Long?,
    val activeCases: Long?,
    val accountUsers: Long?,
    val createdBy: UserProfile?,
    val lastUpdatedBy: UserProfile?

) {
    object ModelMapper {
        fun from(accountEntity: AccountEntity, createdBy: UserProfile?, updatedBy: UserProfile?) =
            AccountListing(
                id =  accountEntity.id!!,
                name = accountEntity.name,
                status = accountEntity.status,
                createdDate = TimeUtil.toEpochMillis(accountEntity.createdDate),
                updatedDate = TimeUtil.toEpochMillis(accountEntity.lastUpdatedDate),
                activeCases = if (accountEntity.cases !=null ) accountEntity.cases!!.size.toLong() else null,
                accountUsers = if (accountEntity.corporateUsers !=null) accountEntity.corporateUsers!!.size.toLong() else null,
                createdBy = createdBy,
                lastUpdatedBy = updatedBy
            )
    }
}