package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.BackofficeUserEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class BackofficeUser constructor(

    val id: Long,

    @Schema()
    val email: String,

    @Schema()
    val firstName: String,

    @Schema()
    val lastName: String,

    @Schema(
        allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION","DELETED"]
    )
    val status: AccountStatus,

    @Schema(
        allowableValues = ["ROLE_ADMIN", "ROLE_SUPER_ADMIN"]
    )
    val role: Role,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    val createdDate: Long,

    @Schema()
    var adminAuthorities: List<AdminAuthorities>?,

    var profilePhotoUrl: String? = null,

    val aiMessageCount: Long?
) {
    object ModelMapper {
        fun from(entity: BackofficeUserEntity, list: List<AdminAuthorities>?) =
            BackofficeUser(
                id = entity.id!!,
                email = entity.email,
                firstName = entity.firstName,
                lastName = entity.lastName,
                status = entity.status,
                role = entity.role,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate!!),
                adminAuthorities = list,
                profilePhotoUrl = entity.profilePhotoUrl,
                aiMessageCount = entity.questionsQuota
            )
    }
}