package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.enums.BandStatus
import com.centuroglobal.shared.util.TimeUtil

data class BandCardDetails(

    val id: Long,
    val name: String? =null,
    val description: String? = null,
    val color: String? = null,
    val status: BandStatus,
    val createdDate: Long,
    val updatedDate: Long?,
    val enrolledUsers: Long?,
    val createdBy: UserProfile?,
    val lastUpdatedBy: UserProfile?

) {
    object ModelMapper {
        fun from(bandsEntity: BandsEntity, createdBy: UserProfile?, updatedBy: UserProfile?) =
            BandCardDetails(
                id =  bandsEntity.id!!,
                name = bandsEntity.name,
                description = bandsEntity.description,
                color = bandsEntity.color,
                status = bandsEntity.status,
                createdDate = TimeUtil.toEpochMillis(bandsEntity.createdDate),
                updatedDate = TimeUtil.toEpochMillis(bandsEntity.lastUpdatedDate),
                enrolledUsers = if (bandsEntity.corporateUsers !=null) bandsEntity.corporateUsers!!.size.toLong() else null,
                createdBy = createdBy,
                lastUpdatedBy = updatedBy
            )
    }
}