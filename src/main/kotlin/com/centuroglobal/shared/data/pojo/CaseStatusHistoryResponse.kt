package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.case.CaseStatusHistory
import com.centuroglobal.shared.data.enums.CaseStatus
import com.centuroglobal.shared.util.TimeUtil
import jakarta.persistence.*

class CaseStatusHistoryResponse (
    val id: Long,
    @Enumerated(EnumType.STRING)
    var status: String = CaseStatus.NOT_STARTED.name,

    var statusUpdate: String? = null,

    @Column(nullable = false)
    var lastUpdatedBy: UserProfile,

    var createdDate: Long,

    var isDeleted: Boolean = false,

    var actionFor: String? = null

){
    object ModelMapper {
        fun from(doc: CaseStatusHistory, user: UserProfile): CaseStatusHistoryResponse {
            return CaseStatusHistoryResponse(
                id= doc.id!!,
                status= doc.status,
                statusUpdate = doc.statusUpdate,
                lastUpdatedBy = user,
                createdDate = TimeUtil.toEpochMillis(doc.createdDate),
                isDeleted = doc.isDeleted,
                actionFor = doc.actionFor
            )
        }
    }
}