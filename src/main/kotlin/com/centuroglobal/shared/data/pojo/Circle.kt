package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.view.CircleView
import com.centuroglobal.shared.data.enums.CircleMemberAction
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.centuroglobal.shared.data.pojo.circle.CircleResponseTrail
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema

@JsonIgnoreProperties(ignoreUnknown = true)
data class Circle(

    val id: Long,

    @Schema()
    val name: String?,

    @Schema()
    val about: String?,

    @Schema(
        allowableValues = ["ACTIVE","INACTIVE"]
    )
    val status: CircleStatus,

    var bannerImageFullUrl: String?,

    @Schema()
    val circleAccessType: CircleType,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    val createdDate: Long,

    var countryCodes: List<String>?,

    var expertiseIds: List<Int>?,

    val member: Int,
    val invitee: Int,

    val request: Int,

    var members: List<ExpertProfileSummary>? = null,

    var requests: List<ExpertProfileSummary>? = null,

    var messages: List<List<CircleResponseTrail>>? = null,

    var memberAction: CircleMemberAction? = null,

    var membersThumbnail: List<String>? = null
) {
    object ModelMapper {
        fun from(entity: CircleView) =
            Circle(
                id = entity.id,
                name = entity.name,
                about = entity.about,
                status = entity.status,
                circleAccessType = entity.circleType,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate),
                countryCodes = entity.countryCodes,
                expertiseIds = entity.expertiseIds?.split(",")?.filter { it.isNotBlank() && it != "0" }
                    ?.map { it.toInt() },
                bannerImageFullUrl = entity.bannerPhotoKey,
                member = entity.members,
                invitee = entity.invitee,
                request = entity.requests
            )
    }
}
