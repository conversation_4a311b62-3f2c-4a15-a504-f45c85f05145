package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ContentLibraryEntity
import com.centuroglobal.shared.util.TimeUtil

data class ContentLibraryListResponse (

    val id: Long?,

    val countryCode: String,

    val identifier: String,

    val title: String,

    val updatedByUser: String?,

    val updatedOn: Long?,

    val isActive: Boolean,

    val metadata: List<ContentLibraryMetadata>
){
    object ModelMapper {
        fun from(entity: ContentLibraryEntity, updatedBy: String?): ContentLibraryListResponse {
            return ContentLibraryListResponse(
                entity.id,
                entity.countryCode,
                entity.identifier,
                entity.title,
                updatedBy,
                TimeUtil.toEpochMillis(entity.lastUpdatedDate),
                entity.isActive,
                entity.metadata.map { ContentLibraryMetadata(it.mKey, it.mKey, it.label) }
            )
        }
    }
}
