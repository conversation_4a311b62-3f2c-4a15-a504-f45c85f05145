package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ClientDocEntity
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateDocumentResponse constructor(

    val id: Long?,

    val docName: String,

    val fileName: String,

    val fileSize: Long,

    val fileType: String,

    val expiryDate: Long?,

    val uploadDate: Long,

    val country: String?,

    val createdBy: String,

    val userId: Long?,

    val docSubType: String?,

    val issueCountry: String?,

    val lastUpdated: Long?,

    val filesUploaded: Int,

    val filesData: List<ClientDocFileResponse>

) {
    object ModelMapper {
        fun from(entity: ClientDocEntity, createdBy: String) =
            CorporateDocumentResponse(
                id = entity.id,
                docName = entity.docName,
                fileName = entity.fileName,
                fileSize = entity.fileSize,
                fileType = entity.fileType,
                expiryDate = entity.expiryDate?.let { TimeUtil.toEpochMillis(it) },
                uploadDate = TimeUtil.toEpochMillis(entity.createdDate),
                country = entity.country,
                createdBy = createdBy,
                userId = entity.createdBy,
                docSubType = entity.docSubType,
                issueCountry = entity.issueCountry,
                lastUpdated = TimeUtil.toEpochMillis(entity.lastUpdatedDate),
                filesUploaded = if (entity.clientDocFile.isNullOrEmpty()) 0 else entity.clientDocFile!!.size,
                filesData = if (entity.clientDocFile.isNullOrEmpty()) emptyList() else entity.clientDocFile!!.map { ClientDocFileResponse(
                    id = it.id,
                    fileName = it.fileName,
                    fileSize = it.fileSize,
                    fileType = it.fileType,
                    docKey = it.docKey
                ) }
            )
    }
}