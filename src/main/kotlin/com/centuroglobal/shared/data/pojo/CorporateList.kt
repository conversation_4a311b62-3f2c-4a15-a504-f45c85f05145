package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class CorporateList (
    @Schema()
    val id : Long?,

    @Schema()
    val name: String,

    @Schema()
    val country: String? = null,

    @Schema()
    val createdOn: Long?,

    @Schema(description = "size of corporate users")
    val corporateUsers: Long,

    @Schema(description = "size of accounts")
    val accounts: Long,

    val status: String?,

    val rootUserId : String? = null,

    val activeCases: Long? = null,

    val completedCases: Long?=null,

    val subscription: String,

    val partnerName: String? = null
)
{
    object ModelMapper {
        fun from(entity: CorporateEntity): CorporateList{
            return CorporateList(
                id= entity.id,
                name = entity.name,
                country = entity.countryCode,
                createdOn = TimeUtil.toEpochMillis(entity.createdDate),
                corporateUsers = entity.users.size.toLong(),
                accounts = entity.accountList?.size.let{ it!!.toLong()},
                status = entity.status.toString(),
                rootUserId = entity.rootUserId.toString(),
                activeCases = entity.accountList!!.sumOf { it.cases!!.count { i-> !i.archive } }.toLong(),
                completedCases = entity.accountList!!.sumOf { it.cases!!.count { i-> i.archive } }.toLong(),
                subscription = if(entity.subscriptionActive) "PAID" else "FREE",
                partnerName = entity.partner?.name
            )
        }
    }
}

