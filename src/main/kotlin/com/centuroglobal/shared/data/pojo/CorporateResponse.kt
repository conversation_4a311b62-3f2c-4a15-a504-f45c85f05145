package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansResponse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Pattern

@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateResponse(

    val id: Long?=null,

    @field:Email
    @Schema(required = true)
    val email: String,

    @Schema()
    val firstName: String?,

    @Schema()
    val lastName: String?,

    @Schema()
    val jobTitle: String?,

    @Schema( required = true)
    val corporateName: String,

    @field:Pattern(regexp = "[A-Za-z]{2}")
    @Schema( required = true)
    val countryCode: String,

    @Schema()
    val referralCode: String?,

    @Schema()
    val keepMeInformed: Boolean =false,

    @Schema()
    val recaptchaResponse: String? = null,

    @Schema()
    val primaryColor : String? = null,

    @Schema()
    val secondaryColor:String? = null,

    @Schema()
    val companyLogoId:String? = null,

    @Schema()
    val aiMessageCount: Long? = null,

    @Schema()
    val assignedTeam: List<CorporateTeam>? = null,

    @Schema()
    val onboardingDocs: List<OnboardingDocs>? = null,

    val associatedPartner: Long? = null,

    val subscriptions: List<SubscriptionPlansResponse> = listOf(),

    val features:List<String>? = mutableListOf(),

    var isTeamEmail: Boolean = false

)

data class CorporateTeam(
    val designation: String,
    val userId: Long
)