package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class CorporateSearchFilterWithoutPartner(
    val name: String? = null,
    val country: String? = null,
    val accountId: Long? =null,
    val status: CorporateStatus? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null

    ) {
    object Builder {
        fun build(name: String?,country: String?, accountId: Long?,
                  status: CorporateStatus?, from: Long?, to: Long?) =
            CorporateSearchFilterWithoutPartner(
                name = if (name.isNullOrBlank()) null else "%$name%",
                country = if (country.isNullOrBlank()) null else country,
                accountId = accountId,
                status = status,
                from = if (from != null) TimeUtil.fromInstant(from / 1000) else null,
                to = if (to != null) TimeUtil.fromInstant(to / 1000) else null
            )
    }
}