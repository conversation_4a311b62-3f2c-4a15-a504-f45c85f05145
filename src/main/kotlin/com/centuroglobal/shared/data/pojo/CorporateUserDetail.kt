package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema

@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateUserDetail constructor(

    val id: Long,

    @Schema()
    val email: String,

    @Schema()
    val fullName: String,

    @Schema()
    val status: String,

    @Schema()
    val company: String?,

    @Schema()
    val band: String,

    @Schema()
    var accountName: List<String>,

    @Schema()
    val createdDate: Long?,

    val bandName: String,

    val subscriptionType: SubscriptionType?

) {
    object ModelMapper {
        fun from(entity: CorporateUserEntity) =
            CorporateUserDetail(
                id = entity.id!!,
                email = entity.email,
                fullName = entity.firstName+" "+entity.lastName,
                status = entity.status.name,
                company = entity.corporate.name,
                band = entity.band!!.name!!,
                accountName = entity.accounts!!.map { it.name!! },
                createdDate = entity.createdDate?.let { TimeUtil.toEpochMillis(it) },
                bandName = entity.band!!.name,
                subscriptionType = entity.subscriptionType
            )
    }
}
