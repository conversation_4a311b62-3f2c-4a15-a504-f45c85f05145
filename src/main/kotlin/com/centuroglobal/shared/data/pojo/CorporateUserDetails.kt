package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.repository.findByIdOrNull

@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateUserDetails(
    val id: Long?,
    @Schema()
    val email: String,
    @Schema()
    val name: String,

    @Schema()
    val bandName: String,

    @Schema()
    val subscription: String,

    @Schema()
    val status: String,

    @Schema()
    val createdDate: Long,

    @Schema()
    val joinedDate: Long?,

    @Schema()
    val lastLogonDate: Long?,

    @Schema()
    val companyName: String,

    @Schema()
    val countryCode: String,

    @Schema()
    val referredBy: String?,

    @Schema()
    val partnerName: String?,

    @Schema()
    val corporateId: Long


) {
    object ModelMapper {
        fun from(corporateUserEntity: CorporateUserEntity, loginAccountRepository: LoginAccountRepository): CorporateUserDetails {
            val reference = corporateUserEntity.referredBy?.let { loginAccountRepository.findByIdOrNull(it) }
            return CorporateUserDetails(
                id = corporateUserEntity.id,
                email = corporateUserEntity.email,
                name = "${corporateUserEntity.firstName} ${corporateUserEntity.lastName}",
                bandName = corporateUserEntity.band!!.name,
                subscription = corporateUserEntity.subscriptionType.toString(),
                status = corporateUserEntity.status.toString(),
                createdDate = TimeUtil.toEpochMillis(corporateUserEntity.createdDate),
                lastLogonDate = corporateUserEntity.lastLoginDate?.let { TimeUtil.toEpochMillis(it) },
                companyName = corporateUserEntity.corporate.name,
                countryCode = corporateUserEntity.countryCode!!,
                referredBy = reference?.email,
                partnerName = corporateUserEntity.partner?.name,
                corporateId = corporateUserEntity.corporate.id!!,
                joinedDate = corporateUserEntity.joinedDate?.let { TimeUtil.toEpochMillis(it) }
            )
        }
    }
}

