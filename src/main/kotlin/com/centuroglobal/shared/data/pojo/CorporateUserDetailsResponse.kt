package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema
import com.centuroglobal.shared.service.aws.AwsS3Service
@JsonIgnoreProperties(ignoreUnknown = true)
data class CorporateUserDetailsResponse(
    val id: Long?,
    @Schema()
    val email: String,
    @Schema()
    val firstName: String,
    @Schema()
    val lastName: String,
    @Schema()
    val jobTitle: String,
    @Schema()
    val keepMeInformed: Boolean = false,
    @Schema()
    val countryCode: String,
    @Schema()
    val corporateName: String,
    @Schema()
    val isPrimary: Boolean = false,
    @Schema()
    val primaryColor: String? = null,
    @Schema()
    val secondaryColor: String? = null,
    @Schema()
    var companyLogo: String? = null

) {
    object ModelMapper {
        fun from(corporateUserEntity: CorporateUserEntity, s3Service: AwsS3Service): CorporateUserDetailsResponse {
           return CorporateUserDetailsResponse(
                email = corporateUserEntity.email,
                firstName = corporateUserEntity.firstName,
                lastName = corporateUserEntity.lastName,
                jobTitle = corporateUserEntity.jobTitle,
                corporateName = corporateUserEntity.corporate.name,
                countryCode = corporateUserEntity.countryCode!!,
                keepMeInformed = corporateUserEntity.keepMeInformed,
                primaryColor = corporateUserEntity.corporate.primaryColor,
                secondaryColor = corporateUserEntity.corporate.secondaryColor,
                companyLogo = corporateUserEntity.corporate.companyLogoId?.let {
                    s3Service.getProfilePicUrl(
                        corporateUserEntity.corporate.companyLogoId
                    )
                },
               id = corporateUserEntity.id
            )
        }
    }
    }

