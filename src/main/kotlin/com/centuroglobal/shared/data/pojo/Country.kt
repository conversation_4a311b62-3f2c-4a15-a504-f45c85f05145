package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CountryRegionEntity

data class Country @JvmOverloads constructor(
    val code: String,
    val name: String,
    val dialCode: String?,
    val regions: List<CountryRegion>? = emptyList()
)

data class CountrySummary constructor(
    val code: String,
    val name: String
) {
    object ModelMapper {
        fun  from(country: Country) =
            CountrySummary(
                code = country.code,
                name = country.name
            )
    }
}

data class CountryRegion(
    val regionId: Int,
    val name: String,
    val code: String
) {

    object ModelMapper {
        fun from(entity: CountryRegionEntity) =
            CountryRegion(
                regionId = entity.id!!,
                name = entity.name,
                code = entity.code
            )
    }
}