package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.payload.account.signup.OnboardingDocs

data class DashboardCompanyInfo(
    val team: List<CompanyTeam>,
    var docs: List<OnboardingDocs>? = null,
    val activeUserCounts: CorporateUserCounts,
    val countryCode: String,
    val companyName: String
)

data class CompanyTeam(
    val designation: String,
    val userProfile: UserProfile
)

data class CorporateUserCounts(
    val accounts: Int,
    val superAdmins: Int,
    val accountManagers: Int,
    val applicants: Int
)