package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class ExpertList(
    @Schema
    val id : Long?,

    @Schema
    val name: String,

    @Schema
    val country: String? = null,

    @Schema(description = "size of expert users")
    val expertUser: Long,

    @Schema
    val status: String?,

    @Schema
    val primaryExpertUserId : String? = null,

    @Schema
    val createdOn: Long,

    val type: ExpertCompanyType,

    val associations: List<String>? = null

)
{
    object ModelMapper {
        fun from(entity: ExpertCompanyProfileEntity): ExpertList {
            val expertUser = entity.users.let{ (entity.users)[0]}
            return ExpertList(
                id= entity.id,
                name = entity.name,
                country = expertUser.countryCode,
                expertUser =  (entity.users).size.toLong(),
                status = entity.status.toString(),
                primaryExpertUserId = expertUser.id.toString(),
                createdOn = TimeUtil.toEpochMillis(entity.createdDate),
                type = expertUser.companyProfile!!.companyType,
                associations = expertUser.companyProfile!!.associatedPartners.map { it.name }
            )
        }
    }
}

