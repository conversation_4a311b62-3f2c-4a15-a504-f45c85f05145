package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class ExpertUserDetailsFilter(
    val search : String? = null,
    val countryCode: String? = null,
    val companyId: Long? = null,
    val expertType: String? = null,
    val status : AccountStatus? = null,
    val createdFrom: LocalDateTime? = null,
    val createdTo: LocalDateTime? = null,
    val isPartnerCompany: Boolean? = null,
    val partnerId: Long? = null,
    val joinedFrom: LocalDateTime? = null,
    val joinedTo: LocalDateTime? = null,
    val expertiseIds: List<Int>? = mutableListOf()
) {
    object Builder {
        fun build(search : String?, countryCode: String?, companyId: Long?, expertType: String?, status : String?,
                  createdFrom: Long?, createdTo: Long?, isPartnerCompany: Boolean?, partnerId: Long?,
                  joinedFrom: Long?, joinedTo: Long?, expertiseIds: String?) =
            ExpertUserDetailsFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                countryCode = if (countryCode.isNullOrBlank()) null else countryCode,
                companyId  = companyId,
                expertType = if (expertType.isNullOrBlank()) null else expertType,
                status = if (status.isNullOrBlank()) null else AccountStatus.valueOf(status),
                createdFrom = createdFrom?.let { TimeUtil.fromInstantMillis(it) },
                createdTo = createdTo?.let { TimeUtil.fromInstantMillis(it) },
                isPartnerCompany= isPartnerCompany,
                partnerId = partnerId,
                joinedFrom = joinedFrom?.let { TimeUtil.fromInstantMillis(it) },
                joinedTo = joinedTo?.let { TimeUtil.fromInstantMillis(it) },
                expertiseIds = if (expertiseIds.isNullOrBlank()) null else expertiseIds.split(",").map { it.toInt() }
            )
    }
}