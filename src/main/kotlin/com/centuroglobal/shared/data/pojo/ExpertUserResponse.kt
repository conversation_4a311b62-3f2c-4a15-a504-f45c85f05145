package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import io.swagger.v3.oas.annotations.media.Schema
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime


data class ExpertUserResponse(
    val email: String,
    val firstName: String,
    val lastName: String,
    val jobTitle: String,
    val countryCode: String,
    val companyName: String,
    val companyNumber: String?,
    val companyAddress: String?,
    val aboutBusiness: String?,
    val territory: String?,
    val services: String?,
    val effectiveDate: Long?,
    val effectiveEndDate: Long?,
    val contract: String?,
    val profileImage: String?
) {
    object ModelMapper {
        fun from(entity: ExpertUserEntity,s3Service: AwsS3Service): ExpertUserResponse {
          return  ExpertUserResponse(
                email = entity.email,
                firstName = entity.firstName,
                lastName = entity.lastName,
                jobTitle = entity.jobTitle,
                countryCode = entity.countryCode ?: "",
                companyName= entity.companyProfile?.name!!,
                companyNumber=entity.companyProfile?.companyNumber,
                companyAddress=entity.companyProfile?.companyAddress,
                aboutBusiness=entity.companyProfile?.aboutBusiness,
                territory=entity.companyProfile?.territory,
                services=entity.companyProfile?.services,
                effectiveDate= entity.companyProfile?.effectiveDate?.let { TimeUtil.toEpochMillis(it) },
                effectiveEndDate=entity.companyProfile?.effectiveEndDate?.let { TimeUtil.toEpochMillis(it) },
                contract=entity.companyProfile?.expertContract?.contractText ,
                profileImage= entity.profileImage?.let { s3Service.getProfilePicUrl(entity.profileImage) }
            )
    }
    }
}

