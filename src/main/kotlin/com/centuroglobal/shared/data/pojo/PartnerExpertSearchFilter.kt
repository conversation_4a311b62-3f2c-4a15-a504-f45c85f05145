package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ExpertCompanyType

data class PartnerExpertSearchFilter(
    val search: String? = null,
    val country: String? = null,
    val status: AccountStatus? = null,
    val isPartnerCompany: Boolean? = null,
    val companyType: ExpertCompanyType? = null

) {
    object Builder {
        fun build(search: String?,country: String?, status: String?, isPartnerCompany: Boolean? = null,
                  companyType: ExpertCompanyType? = null) =
            PartnerExpertSearchFilter(
                search = if (search.isNullOrBlank()) null else "%$search%",
                country = if (country.isNullOrBlank()) null else country,
                status = if (status.isNullOrBlank()) null else AccountStatus.valueOf(status),
                isPartnerCompany = isPartnerCompany,
                companyType = companyType

            )
    }
}