package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.PartnerEntity
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class  PartnerList constructor(

    @Schema()
    val id: Long?,

    @Schema()
    val name: String,

    @Schema()
    val rootUserId: Long?,

    @Schema()
    val rootUserName: String,

    @Schema()
    val rootUserEmail: String,

    @Schema()
    val country: String,

    @Schema(description = "size of partnerUsers")
    val users: Int,

    @Schema(description = "size of experts")
    val experts: Int,

    @Schema(description = "size of corporates")
    val corporates: Int,

    @Schema(description = "Created date in epoch milliseconds.")
    val createdDate: Long,

    @Schema(allowableValues = ["ACTIVE","SUSPENDED","PENDING_VERIFICATION"])
    val status: CorporateStatus,

    val features: List<String>?


)
{
    object ModelMapper {
        fun from(entity: PartnerEntity):PartnerList {
            val partnerUser = (entity.partnerUsers.filter { it.id == entity.rootUserId })[0]
            return PartnerList(
                        id= entity.id,
                        name = entity.name,
                        rootUserId= partnerUser.id,
                        rootUserName = partnerUser.firstName +" "+ partnerUser.lastName,
                        rootUserEmail = partnerUser.email,
                        country = entity.country!!,
                        users = (entity.partnerUsers).size,
                        experts = entity.associatedCompanies.size,
                        corporates = (entity.corporates).size,
                        status = entity.status,
                        createdDate = TimeUtil.toEpochMillis(entity.createdDate),
                        features = entity.band?.bandAccesses?.mapNotNull { it.access?.featureKey ?: it.visibility?.featureKey }?.distinct()
                        )

        }
    }
}

