package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.CorporateStatus

data class PartnerSearchFilter(
    val search: String? = null,
    val country: String? = null,
    val expertCompany: Long? =null,
    val corporate: Long? =null,
    val status: CorporateStatus? = null

) {
    object Builder {
        fun build(search: String?,country: String?, expertCompany: Long?,corporate: Long?,
                  status: CorporateStatus?) =
            PartnerSearchFilter(
                search = if (search.isNullOrBlank()) null else "%$search%",
                country = if (country.isNullOrBlank()) null else country,
                expertCompany = expertCompany,
                corporate= corporate,
                status = status

            )
    }
}