package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.RfpDocumentEntity
import com.centuroglobal.shared.util.TimeUtil

data class RfpDocumentCardDetails(

    val documentId: Long,

    val fileName: String? = null,

    var fileType: String? = null,

    var fileSize: Long? = null,

    var fileUploadDate: Long


){

    object ModelMapper {

        fun from(rfpDocumentEntity: RfpDocumentEntity): RfpDocumentCardDetails {

            return RfpDocumentCardDetails(
                documentId = rfpDocumentEntity.id!!,
                fileType = rfpDocumentEntity.fileType,
                fileName = rfpDocumentEntity.fileName,
                fileSize = rfpDocumentEntity.fileSize,
                fileUploadDate = TimeUtil.toEpochMillis(rfpDocumentEntity.fileUploadDate)
            )
        }
    }
}