package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.fasterxml.jackson.annotation.JsonProperty

data class TokenResult(
    @JsonProperty("token_type")
    val tokenType: String,
    @JsonProperty("refresh_token")
    val refreshToken: String?,
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("expires_in")
    val expiresIn: Int,
    val scope: String?,
    var onboard: Boolean? = false,
    var lastLoginDate: Long?,
    var validationToken: String?,
    var isTempPassword: Boolean? = false,
    var isLinkedin: Boolean? = false,
    var adminAuthorities: List<AdminAuthorities>?,
    var userAccess: List<UserAccess>? = null,
    var userVisibilities: List<UserAccess>? = null,
    var companyName: String?=null,
    var bandName: String?=null,
    var profilePhotoUrl: String? = null,
    var aiMessageCount: Long = 0,
    var userRoles: List<String>? = null,
    var loginToken: String?=null,
    val isFirstTimeLogin: Boolean = false,
    var showOnboardingDashboard: Boolean = false,
    var onboardingSwitchAvailable: Boolean = false,
    var casesManagedBy: PartnerCaseType? = null,
    var queryManagedBy: PartnerCaseType? = null,
    var companyLogo: String? = null,
    var isPartnerUser: Boolean =false
)