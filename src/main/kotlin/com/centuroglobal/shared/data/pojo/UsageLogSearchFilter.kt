package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class UsageLogSearchFilter(
    val country: String? = null,
    val corporate: Long? =null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val isPartner: Boolean? = null,
    val partnerId: Long? = null

) {
    object Builder {
        fun build(country: String?, corporate: Long?, from: Long?, to: Long?, isPartner: Boolean?, partnerId: Long?) =
            UsageLogSearchFilter(
                country = if (country.isNullOrBlank()) null else country,
                corporate= corporate,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                isPartner = isPartner,
                partnerId = partnerId
            )
    }
}