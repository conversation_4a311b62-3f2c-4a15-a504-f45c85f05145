package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.util.TimeUtil

data class UserCardDetails(

    val lastSeen: Long?,
    val bandColor: String? =null,
    val bandLabel: String? = null,
    val fullName: String? = null,
    val jobTitle: String,
    val email: String,
    val country: String?,
    val status: String,
    val casesCreated: Long,
    val reportees: Long,
    val id: Long,
    val profilePhotoUrl: String?

) {
    object ModelMapper {
        fun from(corporateUserEntity: CorporateUserEntity, casesCreated: Long, reportees: Long, profilePhotoUrl: String?) =
            UserCardDetails(
                lastSeen = if(corporateUserEntity.lastLoginDate != null) TimeUtil.toEpochMillis(corporateUserEntity.lastLoginDate!!) else null,
                bandColor = corporateUserEntity.band!!.color,
                bandLabel = corporateUserEntity.band!!.name,
                fullName = corporateUserEntity.firstName + " " + corporateUserEntity.lastName,
                jobTitle = corporateUserEntity.jobTitle,
                email = corporateUserEntity.email,
                country = if(corporateUserEntity.countryCode != null) corporateUserEntity.countryCode else null,
                status = corporateUserEntity.status.name,
                casesCreated = casesCreated,
                reportees = reportees,
                id = corporateUserEntity.id!!,
                profilePhotoUrl = profilePhotoUrl
            )
    }
}