package com.centuroglobal.shared.data.pojo


import com.centuroglobal.shared.data.entity.LoginAccountEntity
import io.swagger.v3.oas.annotations.media.Schema

data class UserDetails(

    @Schema
    val firstName: String?,

    @Schema
    val lastName: String?,

    @Schema
    val jobTitle: String?,

    @Schema
    val country: String?,

    @Schema
    val email: String?,

    @Schema
    val contactNumber: String?,

    @Schema
    val dialCode: String?,

    @Schema
    val profileImageUrl: String?,

    val companyName: String?

)
{
    object ModelMapper {
        fun from(entity: LoginAccountEntity, jobTitle: String?, profileImageUrl: String?): UserDetails {
            return UserDetails(
                firstName =  entity.firstName,
                lastName = entity.lastName,
                country = entity.countryCode,
                email = entity.email,
                contactNumber = entity.contactNo,
                dialCode = entity.dialCode,
                profileImageUrl = profileImageUrl,
                jobTitle = jobTitle,
                companyName = entity.partner?.name
            )
        }
    }
}

