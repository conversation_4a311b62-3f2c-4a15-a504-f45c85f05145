package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.enums.UserDocType
import com.centuroglobal.shared.data.enums.ClientDocType
import com.centuroglobal.shared.data.enums.UserType

data class UserDocumentSearchFilter(
    val search: String? = null,
    val country: String? = null,
    val createdBy: Long? = null,
    val refId: Long? = null,
    val refType: UserType? = null,
    val refTypeUser: UserDocType? = null,
    val fileType: String? = null,
    val docType: ClientDocType
) {
    object Builder {
        fun build(search: String?, country: String?, createdBy: Long?, corporateId: Long?,
                  refType: UserType?, fileType: String?, clientDocType: ClientDocType) =
            UserDocumentSearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                country = if(country.isNullOrBlank()) null else country,
                createdBy = createdBy,
                refId = corporateId,
                refType = refType,
                fileType = if(fileType.isNullOrBlank()) null else fileType,
                docType = clientDocType
            )

        fun build(search: String?, country: String?, createdBy: Long?, corporateId: Long?,
                  refType: UserDocType?, fileType: String?, clientDocType: ClientDocType) =
            UserDocumentSearchFilter(
                search = if (search.isNullOrBlank()) null else "%${search}%",
                country = if(country.isNullOrBlank()) null else country,
                createdBy = createdBy,
                refId = corporateId,
                refTypeUser = refType,
                fileType = if(fileType.isNullOrBlank()) null else fileType,
                docType = clientDocType
            )
    }
}