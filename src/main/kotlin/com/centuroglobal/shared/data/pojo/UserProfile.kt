package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.Role

data class UserProfile(
    val id: Long,
    val email: String,
    val firstName: String,
    val lastName: String,
    val status: AccountStatus,
    val role: Role,
    val countryCode: String? = null,
    val profilePictureFullUrl: String? = null,
    val companyName: String? = null,
    var userType: PartnerCaseType? = null
) {
    object ModelMapper {
        fun from(entity: LoginAccountEntity, profilePictureFullUrl: String?, companyName: String? = null) =
            UserProfile(
                id = entity.id!!,
                email = entity.email,
                firstName = entity.firstName,
                lastName = entity.lastName,
                status = entity.status,
                role = entity.role,
                countryCode = entity.countryCode,
                profilePictureFullUrl = profilePictureFullUrl,
                companyName = companyName
            )
    }
}
