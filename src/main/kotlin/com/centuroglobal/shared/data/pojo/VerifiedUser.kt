package com.centuroglobal.shared.data.pojo

import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.MasterDataDocType
import com.centuroglobal.shared.data.enums.UserType
import io.swagger.v3.oas.annotations.media.Schema
import mu.KotlinLogging

private val log = KotlinLogging.logger {}

data class VerifiedUser constructor(

    @Schema()
    val email: String,

    @Schema()
    val firstName: String,

    @Schema()
    val lastName: String,

    @Schema()
    val userType: UserType,

    @Schema()
    val jobTitle: String? = "",

    @Schema(
        
        required = false
    )
    val metadata: Map<String, Any?>,

    @Schema()
    val termsAndCondition: String? = null,

    @Schema()
    val contractDetails: ExpertCompanyProfile? = null
) {
    object ModelMapper {
        fun fromUserEntity(entity: LoginAccountEntity, masterContent: MutableList<MasterContentEntity>): VerifiedUser? {
            return when (entity.getUserType()) {
                UserType.PARTNER -> {
                    if (entity is CorporateUserEntity || entity is ExpertUserEntity) {
                        VerifiedUser(
                            email = entity.email,
                            firstName = entity.firstName,
                            lastName = entity.lastName,
                            userType = UserType.PARTNER,
                            jobTitle = entity.partnerJobTitle,
                            termsAndCondition = null,  //DON'T SHOW terms and conditions when user is signing up
                            metadata = mapOf("partnerName" to (entity.partner?.name))
                        )
                    } else {
                        log.warn("Entity is not of userType PartnerUser!!")
                        null
                    }
                }
                UserType.CORPORATE -> {
                    if (entity is CorporateUserEntity) {
                        VerifiedUser(
                            email = entity.email,
                            firstName = entity.firstName,
                            lastName = entity.lastName,
                            userType = entity.getUserType(),
                            jobTitle = entity.jobTitle,
                            termsAndCondition = null,  //DON'T SHOW terms and conditions when user is signing up
                            metadata = mapOf("corporateName" to entity.corporate.name)
                        )
                    } else {
                        log.warn("Entity is not of userType CorporateUser!!")
                        null
                    }
                }
                UserType.EXPERT -> {
                    if (entity is ExpertUserEntity) {
                        val companyName: String = entity.companyProfile?.name ?: ""
                        VerifiedUser(
                            email = entity.email,
                            firstName = entity.firstName,
                            lastName = entity.lastName,
                            userType = entity.getUserType(),
                            jobTitle = entity.jobTitle,
                            termsAndCondition = masterContent.find { it.docType == MasterDataDocType.PARTNER_TC.name }?.content,
                            contractDetails = if (entity.viewContract) ExpertCompanyProfile.ModelMapper.from(
                                entity.companyProfile!!,
                                null
                            ) else null,
                            metadata = mapOf(
                                "companyName" to companyName,
                                "countryCode" to entity.countryCode
                            )
                        )
                    } else {
                        log.warn("Entity is not of userType CorporateUser!!")
                        null
                    }
                }
                UserType.BACKOFFICE -> {
                    if (entity is BackofficeUserEntity) {
                        VerifiedUser(
                            email = entity.email,
                            firstName = entity.firstName,
                            lastName = entity.lastName,
                            userType = entity.getUserType(),
                            metadata = emptyMap()
                        )
                    } else {
                        log.warn("Entity is not of userType BackofficeUser!!")
                        null
                    }
                }
            }
        }
    }
}
