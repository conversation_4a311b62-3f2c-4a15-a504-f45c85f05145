package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class AggregatedCaseFilter(
    val caseId: Long? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val status: String? = null,
    val category: String? = null,
    val country: String? = null,
    val companyName: String? = null,
    val initiatedFor: String? = null,
    val search: String? = null,
    val actionFor: String? = null,
    var archive: Boolean = false,
    var isPriorityCase: Boolean? = null
) {
    object Builder {
        fun build(
            caseId: String?,
            from: Long?,
            to: Long?,
            status: String?,
            category: String?,
            country: String?,
            companyName: String?,
            initiatedFor: String?,
            search: String?,
            actionFor: String?,
            archive: Boolean,
            isPriorityCase: Boolean?
        ) =
            AggregatedCaseFilter(
                caseId = if (caseId.isNullOrBlank()) null else caseId.toLong(),
                from = if (from != null) TimeUtil.fromInstant(from / 1000) else null,
                to = if (to != null) TimeUtil.fromInstant(to / 1000) else null,
                status = if (status.isNullOrBlank()) null else status,
                category = if (category.isNullOrBlank()) null else category,
                country = if (country.isNullOrBlank()) null else country,
                companyName = if (companyName.isNullOrBlank()) null else "%$companyName%",
                initiatedFor = if (initiatedFor.isNullOrBlank()) null else "%$initiatedFor%",
                search = if (search.isNullOrBlank()) null else "%$search%",
                actionFor = if(actionFor.isNullOrBlank()) null else actionFor,
                archive = archive,
                isPriorityCase = isPriorityCase
            )
    }
}