package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseDoucmentsAuditEntity
import com.centuroglobal.shared.data.entity.CaseDoucmentsStatusLogsEntity
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonInclude

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CaseDocumentActivityDetails @JvmOverloads constructor(

    var ipAddress: String,

    var status: String?,

    var fileName: String? = null,

    var docType: String? = null,

    var createdDate: Long? = null,

    var otherDocumentName: String?,

    var createdBy: UserProfile

) {
    object ModelMapper {


        fun from(documentActivityView: CaseDoucmentsStatusLogsEntity, createdBy: UserProfile): CaseDocumentActivityDetails {
            return CaseDocumentActivityDetails(
                createdDate = documentActivityView.createdDate?.let { TimeUtil.toEpochMillis(it) },
                status = documentActivityView.status,
                ipAddress = documentActivityView.ipAddress,
                createdBy = createdBy,
                docType = documentActivityView.docType,
                otherDocumentName = documentActivityView.otherDocumentName
            )
        }

        fun from(documentAuditView: CaseDoucmentsAuditEntity, createdBy: UserProfile): CaseDocumentActivityDetails {
            return CaseDocumentActivityDetails(
                createdDate = documentAuditView.createdDate?.let { TimeUtil.toEpochMillis(it) },
                status = documentAuditView.action.name,
                ipAddress = documentAuditView.ipAddress,
                createdBy = createdBy,
                fileName = documentAuditView.fileName,
                otherDocumentName = documentAuditView.otherDocumentName
            )
        }
    }
}