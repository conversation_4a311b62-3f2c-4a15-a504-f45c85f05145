package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseDocumentsEntity
import com.centuroglobal.shared.util.TimeUtil

data class CaseDocumentDetails @JvmOverloads constructor(
    var documentId: Long,

    var docType: String,

    var documents: List<CaseDocumentFileDetails>?,

    var uploadDate: Long? = null

) {
    object ModelMapper {

        //Case Document Listing
        fun from(documentsEntity: CaseDocumentsEntity): CaseDocumentDetails {
            return CaseDocumentDetails(
                documentId = documentsEntity.id!!,
                uploadDate = documentsEntity.receivedDate?.let { TimeUtil.toEpochMillis(it) },
                docType = documentsEntity.documentCode,
                documents = documentsEntity.caseDocumentFiles?.map {
                    CaseDocumentFileDetails.ModelMapper.from(it)
                }
            )
        }
    }
}