package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseDocumentFileEntity
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonIgnore

data class CaseDocumentFileDetails @JvmOverloads constructor(
    val fileId: Long,

    val fileName: String? = null,

    var fileType: String? = null,

    var fileSize: Long? = null,

    var fileUploadDate: Long,

    var isLocked: Boolean? = null,

    @JsonIgnore
    var fileKey: String?
) {

    object ModelMapper {

        //Case Document file Listing
        fun from(documentsEntity: CaseDocumentFileEntity): CaseDocumentFileDetails {
            return CaseDocumentFileDetails(
                fileId = documentsEntity.id!!,
                fileUploadDate = TimeUtil.toEpochMillis(documentsEntity.createdDate),
                fileKey = documentsEntity.fileKey,
                fileType = documentsEntity.fileType,
                fileSize = documentsEntity.fileSize,
                isLocked = documentsEntity.isLocked,
                fileName = documentsEntity.fileName
            )
        }
    }
}