package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseFormEntity
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper

data class CaseFormDetails @JvmOverloads constructor(

    val id: Long?,

    val name: String,

    val description: String,

    val countries: List<String>,

    val category: String,

    val fields: JsonNode?,

    val visibility: TaskVisibility,

    val status: CaseFormStatus,

    var updatedBy: UserProfile?,

    var updatedOn: Long? = null,

    val mapping: CaseFormMapping? = null,

    val fieldCount: Long,

    val defaultDocumentList: List<String>? = null,

    val isDefault: Boolean

) {
    object ModelMapper {

        fun from(caseFormEntity: CaseFormEntity, updatedBy: UserProfile?): CaseFormDetails {
            return CaseFormDetails(
                id = caseFormEntity.id,
                updatedOn = TimeUtil.toEpochMillis(caseFormEntity.lastUpdatedDate),
                name = caseFormEntity.name,
                description = caseFormEntity.description,
                updatedBy = updatedBy,
                countries = caseFormEntity.countries.map { it.code },
                category = caseFormEntity.category,
                fields = caseFormEntity.fields?.let { ObjectMapper().readTree(it) },
                visibility = caseFormEntity.visibility,
                status = caseFormEntity.status,
                mapping = caseFormEntity.mappingFields,
                fieldCount = caseFormEntity.fieldCount?:0,
                defaultDocumentList = caseFormEntity.defaultDocuments?.split(","),
                isDefault = caseFormEntity.isDefault
            )
        }

        fun formListing(caseFormEntity: CaseFormEntity, updatedBy: UserProfile?): CaseFormDetails {
            return CaseFormDetails(
                id = caseFormEntity.id,
                updatedOn = TimeUtil.toEpochMillis(caseFormEntity.lastUpdatedDate),
                name = caseFormEntity.name,
                description = caseFormEntity.description,
                updatedBy = updatedBy,
                countries = caseFormEntity.countries.map { it.code },
                category = caseFormEntity.category,
                fields = null,
                visibility = caseFormEntity.visibility,
                status = caseFormEntity.status,
                fieldCount = caseFormEntity.fieldCount?:0,
                isDefault = caseFormEntity.isDefault
            )
        }
    }
}