package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseNotesEntity
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.annotation.JsonInclude

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CaseNotesDetails @JvmOverloads constructor(

    var id: Long?,

    var notes: String?,

    var updatedBy: UserProfile,

    var createdOn: Long? = null,

    var updatedOn: Long? = null

) {
    object ModelMapper {

        fun from(caseNote: CaseNotesEntity, updatedBy: UserProfile): CaseNotesDetails {
            return CaseNotesDetails(
                id = caseNote.id,
                createdOn = caseNote.createdDate?.let { TimeUtil.toEpochMillis(it) },
                updatedOn = caseNote.lastUpdatedDate?.let { TimeUtil.toEpochMillis(it) },
                notes = caseNote.note,
                updatedBy = updatedBy
            )
        }

    }
}