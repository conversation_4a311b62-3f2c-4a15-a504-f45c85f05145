package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.entity.CaseDocumentsEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.view.CaseView
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.CaseStatus
import com.centuroglobal.shared.data.payload.case.CaseFeesRequest
import com.centuroglobal.shared.data.payload.case.DocumentRequest
import com.centuroglobal.shared.data.payload.dashboard.KeyValuePair
import com.centuroglobal.shared.data.pojo.CaseMilestoneResponse
import com.centuroglobal.shared.data.pojo.CaseStatusHistoryResponse
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.task.dto.TaskDetails
import com.centuroglobal.shared.data.pojo.task.response.TaskWorkflowDetails
import java.time.ZoneId

data class CaseViewDetails @JvmOverloads constructor(
    var caseId: Long? = null,

    val initiatedDate: Long? = null,

    val startDate: Long? = null,

    var country: String? = null,

    var companyName: String? = null,

    var initiatedFor: String? = null,

    var email: String? = null,

    var category: String? = null,

    var initiatedBy: ClientView? = null,

    var status: String = CaseStatus.NOT_STARTED.name,

    var caseDetails: CaseEntity? = null,

    var statusUpdate: String? = null,

    var actionFor: String? = null,

    var isPriorityCase: Boolean = false,

    var documents: List<DocumentRequest>? = null,

    var statusHistory: List<CaseStatusHistoryResponse>? = null,

    var milestones: List<CaseMilestoneResponse>? = null,

    var assigneeList: List<UserProfile>? = null,

    var managersList: List<UserProfile>? = null,

    var accountManager: UserProfile? = null,

    var archive: Boolean,

    var notifyCaseOwner: Boolean,

    //var notes: String? = null,

    var lastUpdatedDate: Long ?= null,

    var caseFees: CaseFeesRequest?=null,

    var percentCompletion: Int = 0,

    var account: KeyValuePair?=null,

    var taskPending: List<TaskDetails>? = null,

    var recentUpdates: List<CaseStatusHistoryView>? = null,

    var isCaseMember: Boolean = false,

    var aliasName: String? = null,

    var partnerName: String? = null,

    var taskWorkflow: TaskWorkflowDetails? = null,

    var milestoneChart: CaseMilestoneChart? = null,

    var formName: String? = null
) {
    object ModelMapper {

        //Case Listing
        fun from(case: CaseView, accountManager: UserProfile?): CaseViewDetails {
            return CaseViewDetails(
                caseId = case.id,
                lastUpdatedDate = case.lastUpdatedDate?.let { it.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000 },
                initiatedDate = case.initiatedDate.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000,
                startDate = case.startDate?.let{it.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000},
                country = case.country,
                companyName = case.companyName,
                initiatedFor = case.initiatedFor,
                email = case.email,
                category = case.category?.subCategoryId,
                status = case.status,
                initiatedBy = case.createdBy,
                caseDetails = null,
                isPriorityCase = case.isPriorityCase,
                statusUpdate = case.statusUpdate,
                actionFor = case.actionFor,
                accountManager = accountManager,
                archive = case.archive,
                notifyCaseOwner = case.notifyCaseOwner,
                percentCompletion = case.percentCompletion,
                aliasName = case.aliasName,
                partnerName = case.partnerName,
                formName = case.caseForm?.name
            )
        }

        //Case Details
        fun fromCaseEntity(
            case: CaseEntity,
            documentList: List<CaseDocumentsEntity>,
            statusHistory: MutableList<CaseStatusHistoryResponse>,
            milestones: List<CaseMilestoneResponse>,
            assigneeList: MutableList<UserProfile>,
            managersList: MutableList<UserProfile>,
            accountManager: UserProfile?,
            isCaseMember: Boolean
        ): CaseViewDetails {
            return CaseViewDetails(
                caseId = case.id,
                initiatedDate = case.initiatedDate?.atZone(ZoneId.systemDefault())
                    ?.toInstant()?.epochSecond?.let { it * 1000 },
                startDate = case.startDate?.atZone(ZoneId.systemDefault())?.toInstant()?.epochSecond?.let { it * 1000 },
                country = case.country,
                email = case.email,
                category = case.category?.subCategoryId,
                status = case.status,
                initiatedBy = case.createdBy,
                caseDetails = case,
                statusUpdate = case.statusUpdate,
                isPriorityCase = case.isPriorityCase,
                documents = DocumentRequest.ModelMapper.from(documentList),
                statusHistory = statusHistory,
                milestones = milestones,
                assigneeList = assigneeList,
                managersList = managersList,
                accountManager = accountManager,
                archive = case.archive,
                notifyCaseOwner = case.notifyCaseOwner,
                //notes = case.notes,
                caseFees = case.caseFees?.let { CaseFeesRequest.ModelMapper.from(it) },
                lastUpdatedDate = case.lastUpdatedDate.let { it.atZone(ZoneId.systemDefault()).toInstant().epochSecond * 1000 },
                account = KeyValuePair(case.account!!.id.toString(), case.account!!.name),
                actionFor = case.actionFor,
                isCaseMember = isCaseMember,
                aliasName = case.aliasName,
                formName = case.caseForm?.name
            )
        }
    }
}