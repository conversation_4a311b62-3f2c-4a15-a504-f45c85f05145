package com.centuroglobal.shared.data.pojo.case


data class EmployeeOfRecord(

    var destinationCountry: String?,

    var entityInDestinationCountry: String?,

    var hiringDate: Long,

    var applicants: MutableList<EmployeeOfRecordApplicant> = mutableListOf(),

    var rightToWork: String,

    var signContract: String,

    var employmentTypes: String,

    var internationalTravel: String,

    var labourSafety: String,

    var certificationRequired: String,

    var governmentContract: String,

    var moreInformation: String?
)