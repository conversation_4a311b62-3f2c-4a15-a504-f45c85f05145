package com.centuroglobal.shared.data.pojo.case

import com.centuroglobal.shared.data.payload.case.PersonCaseRequest


data class EntitySetup(

    var caseCountry: String,
    var entityType: String,
    var setupCompanyName: String,
    var businessActivities: String? = null,

    var hqCountry: String? = null,
    var hqState: String? = null,
    var hqCity: String? = null,
    var hqZipcode: String? = null,
    var hqAddress: String? = null,

    var foreignOwned: Boolean = false,
    var requireAddress: Boolean = false,
    var supportTaxRegistration: Boolean = false,
    var otherInformation: String? = null,

    var companySecretary: PersonCaseRequest,
    var directors: List<Director> = mutableListOf(),
    var shareholders: List<Shareholder> = mutableListOf(),
    var moreInformation: String?,

)