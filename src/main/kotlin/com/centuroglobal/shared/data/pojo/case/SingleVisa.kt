package com.centuroglobal.shared.data.pojo.case

data class SingleVisa (

    var visaType: String ? = null,
    var nationality: String? = null,
    var caseCountry: String,
    var fromCountry: String? = null,
    var tripPurpose: String? = null,
    var foreName: String,
    var middleName: String? = null,
    var surname: String,
    var covidVaccinated: String? = null,
    var dependantTravelling: String? = null,
    var applicantStay: Long? = null,
    var estimatedSalary: Double? = null,
    var estimatedSalaryRange: String? = null,
    var paidCountry: String? = null,
    var entityInHostCountry: Boolean = false,
    var duties: String? = null,
    var qualification: String? = null,
    var applicantStayType: String,
    var currencySymbol: String,
    var accountName: String?=null,
    var shareApplicantInfo: Boolean?=null,
    var contactNo: String?=null,
    var emailAddress: String?=null,
    var visaIssueDate: Long?=null,
    var visaExpiryDate: Long?=null,
    var dependents: List<Dependent> = mutableListOf(),
    var moreInformation: String? = null,
    var jobTitle: String? = null,
    var jobStartDate: Long? = null
)