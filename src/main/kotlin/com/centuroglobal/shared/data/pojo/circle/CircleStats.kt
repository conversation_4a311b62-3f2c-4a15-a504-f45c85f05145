package com.centuroglobal.shared.data.pojo.circle

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import io.swagger.v3.oas.annotations.media.Schema

data class CircleStats constructor(

    @Schema()
    val circles: Int,

    @Schema()
    val active: Int,

    @Schema()
    val inactive: Int,

    @Schema()
    val public: Int,

    @Schema()
    val private: Int

) {
    object ModelMapper {
        fun from(entity: Unit): Nothing = throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
    }
}
