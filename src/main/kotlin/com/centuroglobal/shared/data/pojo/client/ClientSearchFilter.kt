package com.centuroglobal.shared.data.pojo.client

import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class ClientSearchFilter(
    val status: AccountStatus? = null,
    val userType: UserType? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val search: String? = null,
    val countryCodes: List<String>? = mutableListOf(),
    val expertiseIds: List<Long>? = mutableListOf(),
    val corporateId: Long? = null,
    val partnerId: Long? = null,
    val expertCompanyId: Long? = null
) {
    object Builder {
        fun build(
            status: String?,
            userType: String?,
            from: Long?,
            to: Long?,
            search: String?,
            countryCodes: String?,
            expertiseIds: String?,
            corporateId: Long?,
            partnerId: Long?,
            expertCompanyId: Long?
        ) =
            ClientSearchFilter(
                status = if (status.isNullOrBlank()) null else AccountStatus.valueOf(status),
                userType = if (userType.isNullOrBlank()) null else UserType.valueOf(userType),
                from = if (from != null) TimeUtil.fromInstant(from / 1000) else null,
                to = if (to != null) TimeUtil.fromInstant(to / 1000) else null,
                search = if (search.isNullOrBlank()) null else "%$search%",
                countryCodes = if (countryCodes.isNullOrBlank()) null else countryCodes.split(","),
                expertiseIds = if (expertiseIds.isNullOrBlank()) null else expertiseIds.split(",").map { it.toLong() },
                corporateId = corporateId,
                partnerId = partnerId,
                expertCompanyId = expertCompanyId
            )
    }
}