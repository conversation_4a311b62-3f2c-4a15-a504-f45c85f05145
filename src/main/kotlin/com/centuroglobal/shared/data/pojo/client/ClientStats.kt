package com.centuroglobal.shared.data.pojo.client

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.exception.ApplicationException
import io.swagger.v3.oas.annotations.media.Schema

data class ClientStats constructor(

    @Schema()
    val users: Int,

    @Schema()
    val corporate: Int,

    @Schema()
    val expert: Int,

    @Schema()
    val active: Int,

    @Schema()
    val subscribed: Int

) {
    object ModelMapper {
        fun from(entity: Unit): Nothing = throw ApplicationException(ErrorCode.NOT_IMPLEMENTED)
    }
}
