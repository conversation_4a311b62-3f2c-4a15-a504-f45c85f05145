package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class CaseFeesApprovalEmailContext(
    val caseId: Long?,
    val caseUrl: String,
    val publicUrl: String,
    val s3ServerUrl: String,
) {
    object ModelMapper {
        fun toContext(userContext: CaseFeesApprovalEmailContext): Context {
            val ctx = Context()
            ctx.setVariable("CASE_ID", userContext.caseId)
            ctx.setVariable("CASE_URL", userContext.caseUrl)
            ctx.setVariable("PUBLIC_URL", userContext.publicUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            return ctx
        }
    }
}