package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class CaseStatusUpdateReminderEmailContext(
    val caseId: Long?,
    val lastUpdatedDate: String,
    val caseCategory: String,
    val companyName: String,
    val s3ServerUrl: String,
    val caseUrl: String
) {
    object ModelMapper {
        fun toContext(userContext:CaseStatusUpdateReminderEmailContext ): Context {
            val ctx = Context()
            ctx.setVariable("CASE_ID", userContext.caseId)
            ctx.setVariable("STATUS_UPDATED_ON", userContext.lastUpdatedDate)
            ctx.setVariable("CASE_CATEGORY", userContext.caseCategory)
            ctx.setVariable("COMPANY_NAME", userContext.companyName)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            ctx.setVariable("CASE_URL", userContext.caseUrl)
            return ctx
        }
    }
}