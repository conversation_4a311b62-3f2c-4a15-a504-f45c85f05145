package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class CaseSubmitContext(
    val userName: String,
    val caseName: String,
    val caseId: Long?,
    val caseUrl: String,
    val s3ServerUrl: String,
    val publicCaseUrl: String?
) {
    object ModelMapper {
        fun toContext(userContext: CaseSubmitContext): Context {
            val ctx = Context()
            ctx.setVariable("DISPLAY_NAME", userContext.userName)
            ctx.setVariable("CASE", userContext.caseName)
            ctx.setVariable("CASE_ID", userContext.caseId)
            ctx.setVariable("CASE_URL", userContext.caseUrl)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            ctx.setVariable("PUBLIC_CASE_URL", userContext.publicCaseUrl)
            return ctx
        }
    }
}
