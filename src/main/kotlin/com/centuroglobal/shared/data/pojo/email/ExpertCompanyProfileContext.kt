package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class ExpertCompanyProfileContext(
    val effectiveDate: String,
    val effectiveEndDate: String,
    val name: String,
    val companyNumber: String,
    val companyAddress: String,
    val aboutBusiness: String,
    val membershipStatus: String,
    val feesAmount: String,
    val territory: String,
    val country: String,
    val specialTerms: String,
    val adminName: String,
    val primaryExpert: String,
    val termsAndCondition: String,
    val s3ServerUrl: String,
    val services: String,
    val feesCurrency: String
) {
    object ModelMapper {
        fun toContext(userContext: ExpertCompanyProfileContext): Context {
            val ctx = Context()
            ctx.setVariable("EFFECTIVE_DATE", userContext.effectiveDate)
            ctx.setVariable("EFFECTIVE_END_DATE", userContext.effectiveEndDate)
            ctx.setVariable("NAME", userContext.name)
            ctx.setVariable("COMPANY_NUMBER", userContext.companyNumber)
            ctx.setVariable("COMPANY_ADDRESS", userContext.companyAddress)
            ctx.setVariable("ABOUT_BUSINESS", userContext.aboutBusiness)
            ctx.setVariable("MEMBERSHIP_STATUS", userContext.membershipStatus)
            ctx.setVariable("FEES_AMOUNT", userContext.feesAmount)
            ctx.setVariable("TERRITORY", userContext.territory)
            ctx.setVariable("COUNTRY", userContext.country)
            ctx.setVariable("SPECIAL_TERMS", userContext.specialTerms)
            ctx.setVariable("ADMIN_NAME", userContext.adminName)
            ctx.setVariable("PRIMARY_EXPERT", userContext.primaryExpert)
            ctx.setVariable("TERMS_AND_CONDITION", userContext.termsAndCondition)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            ctx.setVariable("SERVICES", userContext.services)
            ctx.setVariable("FEES_CURRENCY", userContext.feesCurrency)
            return ctx
        }
    }
}
