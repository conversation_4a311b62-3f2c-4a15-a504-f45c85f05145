package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class ExpertSubmitContext(
    val auDisplayName: String,
    val auCountry: String?,
    val auEmail: String,
    val auContactNumber: String,
    val ceDisplayName: String,
    val ceCountry: String?,
    val ceEmail: String,
    val ceContactNumber: String,
    val expertise: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: ExpertSubmitContext): Context {
            val ctx = Context()
            ctx.setVariable("AU_DISPLAY_NAME", userContext.auDisplayName)
            ctx.setVariable("AU_COUNTRY", userContext.auCountry)
            ctx.setVariable("AU_EMAIL", userContext.auEmail)
            ctx.setVariable("AU_CONTACT_NUMBER", userContext.auContactNumber)
            ctx.setVariable("CE_DISPLAY_NAME", userContext.ceDisplayName)
            ctx.setVariable("CE_COUNTRY", userContext.ceCountry)
            ctx.setVariable("CE_EMAIL", userContext.ceEmail)
            ctx.setVariable("CE_CONTACT_NUMBER", userContext.ceContactNumber)
            ctx.setVariable("EXPERTISE", userContext.expertise)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            return ctx
        }
    }
}
