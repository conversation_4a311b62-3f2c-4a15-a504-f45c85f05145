package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class LeadResponseContext(
    val leadUrl: String,
    val leadTitle: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: LeadResponseContext): Context {
            val ctx = Context()
            ctx.setVariable("LEAD_URL", userContext.leadUrl)
            ctx.setVariable("LEAD_TITLE", userContext.leadTitle)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)

            return ctx
        }
    }
}
