package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context
import java.io.File

data class MailTemplate(
    val templateName: String,
    val ccRecipients: List<String> = emptyList(),
    val bccRecipients: List<String> = emptyList(),
    val subject: String,
    val from: String = "Centuro Global",
    val recipient: String,
    val context: Context? = null,
    val attachmentName: String? = null,
    val attachment: File? = null
)
