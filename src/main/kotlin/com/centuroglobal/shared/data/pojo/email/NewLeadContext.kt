package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class NewLeadContext(
    val leadUrl: String,
    val leadTitle: String,
    val leadExpertise: String,
    val leadCountry: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: NewLeadContext): Context {
            val ctx = Context()
            ctx.setVariable("LEAD_URL", userContext.leadUrl)
            ctx.setVariable("LEAD_TITLE", userContext.leadTitle)
            ctx.setVariable("LEAD_EXPERTISE", userContext.leadExpertise)
            ctx.setVariable("LEAD_COUNTRY", userContext.leadCountry)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)

            return ctx
        }
    }
}
