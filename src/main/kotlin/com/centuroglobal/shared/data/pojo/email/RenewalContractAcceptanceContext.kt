package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context

data class RenewalContractAcceptanceContext(
    val expertName: String,
    val companyName: String,
    val date: String,
    val s3ServerUrl: String
) {
    object ModelMapper {
        fun toContext(userContext: RenewalContractAcceptanceContext): Context {
            val ctx = Context()
            ctx.setVariable("EXPERT_NAME", userContext.expertName)
            ctx.setVariable("COMPANY_NAME", userContext.companyName)
            ctx.setVariable("DATE", userContext.date)
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            return ctx
        }
    }
}
