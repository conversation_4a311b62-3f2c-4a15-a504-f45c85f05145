package com.centuroglobal.shared.data.pojo.email

import org.thymeleaf.context.Context
import java.time.LocalDateTime

data class TaskContext(
    val s3ServerUrl: String,
    val verifyUrl: String?,
    val assignedTo: String,
    val createdBy: String?,
    val taskName: String,
    val dueDate: String?,
    val taskType: String,
    val cqpId: String?,
    val taskStatus: String?
) {
    object ModelMapper {
        fun toContext(userContext: TaskContext): Context {
            val ctx = Context()
            ctx.setVariable("S3_SERVER_URL", userContext.s3ServerUrl)
            ctx.setVariable("VERIFY_URL", userContext.verifyUrl)
            ctx.setVariable("ASSIGNED_TO",userContext.assignedTo)
            ctx.setVariable("CREATED_BY",userContext.createdBy)
            ctx.setVariable("TASK_NAME",userContext.taskName)
            ctx.setVariable("DUE_DATE",userContext.dueDate)
            ctx.setVariable("TASK_TYPE",userContext.taskType)
            ctx.setVariable("CQP_ID",userContext.cqpId)
            if(userContext.taskStatus!=null) ctx.setVariable("TASK_STATUS",userContext.taskStatus)

            return ctx
        }
    }
}
