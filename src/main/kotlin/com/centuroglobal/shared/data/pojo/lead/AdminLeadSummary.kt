package com.centuroglobal.shared.data.pojo.lead

import com.centuroglobal.shared.data.entity.view.AdminLeadSummaryView
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class AdminLeadSummary(

    override val id: String?,

    @Schema(
        allowableValues = ["EXPERT", "CORPORATE"]
    )
    val leadType: LeadType,

    @Schema()
    val createdByName: String,

    @Schema()
    override val title: String,

    @Schema()
    override val expertiseName: List<String>,

    @Schema()
    override val countryName: String,

    @Schema()
    override val regionName: String?,

    @Schema()
    val companyName: String,

    @Schema(
        allowableValues = ["ACTIVE", "UNRESOLVED", "RESOLVED", "DELETED"]
    )
    override val status: LeadStatus,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    override val createdDate: Long,

    @Schema()
    val responseCount: Int

) : ILeadSummary {
    object ModelMapper {
        fun from(
            view: AdminLeadSummaryView,
            countryName: String,
            regionName: String?
        ) =
            AdminLeadSummary(
                id = "${view.leadType}-${view.leadTypeId}-${view.id}",
                createdByName = view.createdByName,
                leadType = view.leadType,
                title = view.title,
                countryName = countryName,
                regionName = regionName,
                companyName = view.companyName,
                expertiseName = view.expertiseName.split(","),
                status = view.status,
                createdDate = TimeUtil.toEpochMillis(view.createdDate),
                responseCount = view.responseCount
            )
    }
}
