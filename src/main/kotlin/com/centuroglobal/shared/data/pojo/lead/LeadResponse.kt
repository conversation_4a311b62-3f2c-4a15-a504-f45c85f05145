package com.centuroglobal.shared.data.pojo.lead

import com.centuroglobal.shared.data.entity.LeadResponseEntity
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema

data class LeadResponse(

    val id: Long,

    @Schema()
    val description: String,

    @Schema()
    val expertProfile: ExpertProfileSummary?,

    @Schema(
        description = "Created date in epoch milliseconds."
    )
    val createdDate: Long,

    val unReadMessageCount: Int = 0,
    var lastMessageDateTime: Long = 0

) {
    object ModelMapper {
        fun from(entity: LeadResponseEntity) =
            LeadResponse(
                id = entity.id!!,
                description = entity.description,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate!!),
                expertProfile = null,
                unReadMessageCount = entity.unReadMessageCount,
                lastMessageDateTime = entity.lastMessageDateTime
            )

        fun fromWithExpertProfile(
            entity: LeadResponseEntity, retrieveExpertSummaryFunction: (m: Long) -> ExpertProfileSummary
        ) =
            LeadResponse(
                id = entity.id!!,
                description = entity.description,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate!!),
                expertProfile = retrieveExpertSummaryFunction(entity.expertId),
                unReadMessageCount = entity.unReadMessageCount,
                lastMessageDateTime = entity.lastMessageDateTime
            )
    }
}
