package com.centuroglobal.shared.data.pojo.playbook

import com.centuroglobal.shared.data.entity.playbook.PlaybookEntity
import com.centuroglobal.shared.data.pojo.ReferenceData
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.util.TimeUtil

data class PlaybookListResponse(
    val id: Long?,
    val date: Long,
    val country: String? = null,
    val createdBy: UserProfile?,
    val sharedWith: List<ReferenceData>

) {
    object ModelMapper {

        fun from(entity: PlaybookEntity, userProfile: UserProfile?): PlaybookListResponse {

            return PlaybookListResponse(
                entity.id,
                TimeUtil.toEpochMillis(entity.createdDate),
                entity.country,
                userProfile,
                entity.sharedWith.map { ReferenceData(it.id, "${it.firstName} ${it.lastName}") }
            )
        }
    }
}