package com.centuroglobal.shared.data.pojo.playbook

import com.centuroglobal.shared.data.entity.playbook.PlaybookSessionEntity
import com.centuroglobal.shared.util.TimeUtil
import java.time.temporal.ChronoUnit

data class PlaybookLogsResponse(
    val id: Long?,
    val corporateName: String,
    val accessedBy: String,
    val accessedDate: Long,
    val timeSpent: Long,
    val purpose: String,
    val messages: Long,
    val country: String? = null,
    val partnerName: String?,
    val sessionId: String,
    val playbookId: Long?,
    val bandName: String?,
    val industry: String?,
    val aiQuestions: Long?

) {
    object ModelMapper {

        fun from(entity: PlaybookSessionEntity): PlaybookLogsResponse {

            val createdBy = entity.corporateUser

            return PlaybookLogsResponse(
                entity.id,
                "${createdBy?.corporate?.name}",
                "${createdBy?.firstName} ${createdBy?.lastName}",
                TimeUtil.toEpochMillis(entity.createdDate),
                entity.endTime?.let { ChronoUnit.SECONDS.between(entity.startTime, entity.endTime) }?:0,
                entity.type,
                entity.playbookChats.size.toLong(),
                entity.playbook!!.country,
                createdBy?.corporate?.partner?.name,
                entity.sessionId,
                entity.playbook!!.id,
                createdBy?.band?.name,
                entity.playbook?.industryName,
                entity.playbookChats.size.toLong()
            )
        }
    }
}