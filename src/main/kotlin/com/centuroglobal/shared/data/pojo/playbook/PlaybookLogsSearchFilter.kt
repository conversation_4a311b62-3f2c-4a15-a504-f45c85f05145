package com.centuroglobal.shared.data.pojo.playbook

import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class PlaybookLogsSearchFilter(
    val country: String? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val corporateId: Long? = null,
    val partnerId: Long? = null,
    val userId: Long? = null,
    val isPartner: Boolean = false

) {
    object Builder {

        fun build(
            country: String?,
            from: Long?,
            to: Long?,
            corporateId: Long?,
            partner: Long?,
            user: Long?,
            isPartner: Boolean
        ) =
            PlaybookLogsSearchFilter(
                country = if(country.isNullOrBlank()) null else country,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                corporateId = corporateId,
                partnerId = partner,
                userId = user,
                isPartner = isPartner
            )
    }
}