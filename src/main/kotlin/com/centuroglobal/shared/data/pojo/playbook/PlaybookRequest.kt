package com.centuroglobal.shared.data.pojo.playbook

data class PlaybookRequest (

    val country: String,

    val industry: String,

    val about: String,

    val details: List<PlaybookDetails>,

    val industryName: String?
)

data class PlaybookDetails (

    val category: String,

    val children: List<PlaybookSubCategoryDetails>,

    val isSelected: Boolean
)

data class PlaybookSubCategoryDetails (

    val subCategory: String,

    val isSelected: Boolean
)