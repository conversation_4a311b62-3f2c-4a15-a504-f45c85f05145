package com.centuroglobal.shared.data.pojo.playbook

import com.centuroglobal.shared.data.enums.playbook.PlaybookType
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class PlaybookSearchFilter(
    val type: String?,
    val country: String? = null,
    val from: LocalDateTime? = null,
    val to: LocalDateTime? = null,
    val userId: Long

) {
    object Builder {

        fun build(
            type: PlaybookType?,
            country: String?,
            from: Long?,
            to: Long?,
            userId: Long
        ) =
            PlaybookSearchFilter(
                type = type?.name,
                country = if(country.isNullOrBlank()) null else country,
                from = from?.let { TimeUtil.fromInstantMillis(it) },
                to = to?.let { TimeUtil.fromInstantMillis(it) },
                userId = userId
            )
    }
}