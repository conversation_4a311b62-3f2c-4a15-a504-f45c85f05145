package com.centuroglobal.shared.data.pojo.query

import com.centuroglobal.shared.data.entity.query.ProposalEntity
import com.centuroglobal.shared.util.TimeUtil

data class ProposalCardDetails(

    val proposalId: Long,

    val fileName: String? = null,

    var fileType: String? = null,

    var fileSize: Long? = null,

    var fileUploadDate: Long,

    var isApproved: <PERSON>olean,

    var createdBy: String


){

    object ModelMapper {

        fun from(proposalEntity: ProposalEntity, createdBy: String): ProposalCardDetails {

            return ProposalCardDetails(
                proposalId = proposalEntity.id!!,
                fileType = proposalEntity.fileType,
                fileName = proposalEntity.fileName,
                fileSize = proposalEntity.fileSize,
                fileUploadDate = TimeUtil.toEpochMillis(proposalEntity.fileUploadDate),
                isApproved = proposalEntity.isApproved,
                createdBy = createdBy
            )
        }
    }
}