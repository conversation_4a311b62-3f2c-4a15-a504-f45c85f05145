package com.centuroglobal.shared.data.pojo.query

import com.centuroglobal.shared.data.entity.query.ProposalEntity
import com.centuroglobal.shared.data.entity.query.QueryEntity
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.util.TimeUtil

data class QueryCardDetails(

    val heading: String,
    val description: String,
    val raisedBy: CorporateUserProfile,
    val category: List<String>?,
    val submittedOn: Long,
    val country: String?,
    val id: Long,
    val proposal: Int = 0,
    val responses: Long? = null,
    val status: String,
    val assignedTo: List<UserProfile>? = null,
    val isProposalApproved: Boolean,
    val unreadMessages: Long? = null,
    val resolvedDate: Long? = null,
    val cgRequestedStatus: String? = null,
    val partnerName: String? = null,
    val lastUpdatedDate: Long? = null,
    val lastAssignedDate: Long?=null
){

    object ModelMapper {

        fun from(queryEntity: QueryEntity, createdBy: CorporateUserProfile, assignedTo: List<UserProfile>?, responses: Long?,
                 unreadMsgs: Long? = null, proposals: List<ProposalEntity>,lastAssignedDate: Long? ): QueryCardDetails {

            return QueryCardDetails(
                heading = queryEntity.heading,
                description = queryEntity.description,
                raisedBy = createdBy,
                submittedOn = TimeUtil.toEpochMillis(queryEntity.createdDate),
                country = queryEntity.country,
                id = queryEntity.id!!,
                proposal = proposals.size,
                category = queryEntity.categories?.map { it.name },
                assignedTo = assignedTo,
                status = queryEntity.status.name,
                responses = responses,
                isProposalApproved = if(proposals.isEmpty()) false else proposals.all { it.isApproved },
                unreadMessages = unreadMsgs,
                resolvedDate = queryEntity.resolvedDate?.let { TimeUtil.toEpochMillis(it) },
                cgRequestedStatus = queryEntity.cgRequestedStatus?.name,
                partnerName = queryEntity.partner?.name,
                lastUpdatedDate = TimeUtil.toEpochMillis(queryEntity.lastUpdatedDate),
                lastAssignedDate=lastAssignedDate
            )
        }
    }
}