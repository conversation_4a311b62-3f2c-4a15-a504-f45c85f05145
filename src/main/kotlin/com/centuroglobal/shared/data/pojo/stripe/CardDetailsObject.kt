package com.centuroglobal.shared.data.pojo.stripe

import com.stripe.model.PaymentMethod

data class CardDetailsObject(
    val brand: String,
    val country: String,
    val expiryMonth: Long,
    val expiryYear: Long,
    val last4: String
) {
    object ModelMapper {
        fun from(card: PaymentMethod.Card) =
            CardDetailsObject(
                brand = card.brand,
                country = card.country,
                expiryMonth = card.expMonth,
                expiryYear = card.expYear,
                last4 = card.last4
            )
    }
}
