package com.centuroglobal.shared.data.pojo.stripe

import com.stripe.model.Subscription
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

data class SubscriptionObject(
    @Schema(description =  "Subscription id.")
    val id: String,

    @Schema(
        description = "Customer id."
    )
    val customerId: String,

    @Schema(
        description = "Product id."
    )
    val productId: String,

    @Schema(
        description = "Price id."
    )
    val priceId: String,

    @Schema()
    val amount: BigDecimal,

    @Schema(
        description = "Created date in epoch millis."
    )
    val created: Long,

    @Schema(
        description = "Current Period start date in epoch millis."
    )
    val currentPeriodStart: Long,

    @Schema(
        description = "Current Period end date in epoch millis."
    )
    val currentPeriodEnd: Long,

    @Schema(
        description = "A date in the future (in epoch millis) at which the subscription will automatically get canceled."
    )
    val cancelAt: Long?,

    @Schema()
    val cancelAtPeriodEnd: Boolean?,

    @Schema(
        description = "If the subscription has been canceled, the date of that cancellation. If the subscription was canceled with [cancel_at_period_end], canceled_at will still reflect the date of the initial cancellation request, not the end of the subscription period when the subscription is automatically moved to a canceled state"
    )
    val cancelledAt: Long?,

    @Schema()
    val latestInvoiceId: String,

    @Schema()
    val threeDSecureAction: ThreeDSecureAction? = null

) {
    object ModelMapper {
        fun from(entity: Subscription, threeDSecureAction: ThreeDSecureAction? = null) =
            SubscriptionObject(
                id = entity.id,
                customerId = entity.customer,
                productId = entity.items.data[0].price.product,
                priceId = entity.items.data[0].price.id,
                amount = BigDecimal(entity.items.data[0].price.unitAmount).divide(
                    BigDecimal(100)
                ),
                created = entity.created * 1000, //`entity.created` is in epoch seconds
                currentPeriodStart = entity.currentPeriodStart * 1000, //`entity.currentPeriodStart` is in epoch seconds
                currentPeriodEnd = entity.currentPeriodEnd * 1000, //`entity.currentPeriodEnd` is in epoch seconds
                cancelAt = getDateOrNull(entity.cancelAt),
                cancelAtPeriodEnd = entity.cancelAtPeriodEnd,
                cancelledAt = getDateOrNull(entity.canceledAt),
                latestInvoiceId = entity.latestInvoice,
                threeDSecureAction = threeDSecureAction
            )
    }
}

data class ThreeDSecureAction(
    @Schema(description =  "The client secret necessary to the complete 3D Secure action")
    val clientSecret: String
)

fun getDateOrNull(dateInEpochSeconds: Long?): Long? {
    return if (dateInEpochSeconds != null) {
        dateInEpochSeconds * 1000
    } else {
        null
    }
}
