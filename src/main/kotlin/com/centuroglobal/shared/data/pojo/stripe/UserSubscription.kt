package com.centuroglobal.shared.data.pojo.stripe

import com.centuroglobal.shared.data.entity.stripe.StripeSubscriptionEntity
import com.centuroglobal.shared.data.enums.stripe.StripeTransactionStatus
import com.centuroglobal.shared.util.TimeUtil
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

data class UserSubscription(

    @Schema(description =  "Active subscription. This will be NUll if no active subscription is associated with the user.")
    val subscription: ActiveSubscription? = null,

    @Schema(description =  "List of available products to subscribe.")
    val products: List<SubscriptionProduct>
) {
    object ModelMapper {
        fun from(entity: StripeSubscriptionEntity?, products: List<SubscriptionProduct>) =
            UserSubscription(
                subscription = if (entity != null) ActiveSubscription.ModelMapper.from(entity) else null,
                products = products
            )
    }
}

data class ActiveSubscription(
    @Schema(description =  "Price id.")
    val priceId: String,

    @Schema()
    val amount: BigDecimal,

    @Schema(
        description = "Subscription id."
    )
    val subscriptionId: String? = null,

    @Schema(
        description = "End date of subscription in epoch millis."
    )
    val endDate: Long,

    @Schema(
        description = "Indicate if subscription is cancelled."
    )
    val cancelRequested: Boolean,

    @Schema(
        description = "Payment status.",
        allowableValues = ["PAID", "UNPAID", "PAST_DUE"]
    )
    val paymentStatus: String
) {
    object ModelMapper {
        fun from(entity: StripeSubscriptionEntity) =
            ActiveSubscription(
                subscriptionId = entity.subscriptionId!!,
                priceId = entity.priceId!!,
                amount = entity.subscriptionAmount,
                endDate = TimeUtil.toEpochMillis(entity.currentPeriodEndDate),
                cancelRequested = entity.cancelAt != null,
                paymentStatus = if (entity.latestInvoiceStatus == StripeTransactionStatus.PAID) {
                    StripeTransactionStatus.PAID.name
                } else if (entity.latestInvoiceStatus == StripeTransactionStatus.PAST_DUE) {
                    StripeTransactionStatus.PAST_DUE.name
                } else {
                    StripeTransactionStatus.UNPAID.name
                }
            )
    }
}
