package com.centuroglobal.shared.data.pojo.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionDetailsEntity

data class SubscriptionDetails (

    val id: Long?,

    val name: String,

    val code: String,

    val threshold: Long,

    val unit: String,

    val overageRate: Float,

    val trackingDuration: String,

    val isUnlimited: Boolean
){
    object ModelMapper {
        fun from(planDetail: SubscriptionDetailsEntity): SubscriptionDetails {
            return SubscriptionDetails(
                id = planDetail.id,
                name = planDetail.name,
                code = planDetail.code,
                threshold = planDetail.threshold,
                unit = planDetail.unit,
                overageRate = planDetail.overageRate,
                trackingDuration = planDetail.trackingDuration,
                isUnlimited = planDetail.isUnlimited
            )
        }
    }
}
