package com.centuroglobal.shared.data.pojo.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionUsageDetailsEntity

data class SubscriptionUsageDetailsResponse(

    val id: Long?,

    val name: String,

    val code: String,

    var threshold: Long,

    var unit: String,

    var trackingDuration: String,

    var totalUsage: Long?,

    var overage: Long = 0,

    var overageCharge: Float = 0F,

    val isUnlimited: Boolean,

    val currency: String
){
    object ModelMapper {
        fun from(plan: SubscriptionUsageDetailsEntity, currency: String): SubscriptionUsageDetailsResponse {

            return SubscriptionUsageDetailsResponse(
                id = plan.id,
                name = plan.name,
                code = plan.code,
                threshold = plan.threshold,
                unit = plan.unit,
                trackingDuration = plan.trackingDuration,
                totalUsage = plan.totalUsage,
                overage = plan.overage,
                overageCharge = plan.overageCharge,
                isUnlimited = plan.isUnlimited,
                currency = currency
            )
        }
    }
}