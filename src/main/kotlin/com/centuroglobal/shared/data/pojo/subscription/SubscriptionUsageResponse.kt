package com.centuroglobal.shared.data.pojo.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionUsageEntity
import com.centuroglobal.shared.data.enums.subscription.SubscriptionPaymentStatus
import com.centuroglobal.shared.util.TimeUtil
import java.time.LocalDateTime

data class SubscriptionUsageResponse(

    val id: Long?,

    val name: String,

    val amount: Float,

    val billingDate: Long,

    val status: SubscriptionPaymentStatus,

    val currency: String,

    val startDate: Long? = null,

    val endDate: Long? = null,

    val modules: List<SubscriptionUsageDetailsResponse>? = null
){
    object ModelMapper {
        fun from(
            plan: SubscriptionUsageEntity,
            modules: List<SubscriptionUsageDetailsResponse>? = null,
            startDate: LocalDateTime? = null,
            endDate: LocalDateTime? = null
        ): SubscriptionUsageResponse {

            return SubscriptionUsageResponse(
                id = plan.id,
                name = plan.name,
                amount = plan.overageCharge,
                billingDate = TimeUtil.toEpochMillis(plan.date),
                status = plan.status,
                currency = plan.currency,
                startDate = startDate?.let { TimeUtil.toEpochMillis(it) },
                endDate = endDate?.let { TimeUtil.toEpochMillis(it) },
                modules = modules
            )
        }
    }
}