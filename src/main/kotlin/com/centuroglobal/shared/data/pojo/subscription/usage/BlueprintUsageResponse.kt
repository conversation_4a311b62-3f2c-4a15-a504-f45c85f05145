package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.subscription.usage.BlueprintUsageEntity
import com.centuroglobal.shared.util.TimeUtil

data class BlueprintUsageResponse(

    val country: String,

    val accessedBy: String,

    val band: String?,

    val createdAt: Long?,

    val charges: Float

): AbstractUsageResponse() {

    object ModelMapper {

        fun from(usageEntity: BlueprintUsageEntity, createdBy: CorporateUserEntity): BlueprintUsageResponse {

            return BlueprintUsageResponse(
                country = usageEntity.country,
                accessedBy = "${createdBy.firstName} ${createdBy.lastName}",
                band = createdBy.band?.name,
                createdAt = usageEntity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }

        fun from(usageEntity: BlueprintUsageEntity): BlueprintUsageResponse {

            return BlueprintUsageResponse(
                country = usageEntity.country,
                accessedBy = usageEntity.accessedBy,
                band = usageEntity.bandName,
                createdAt = usageEntity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }
    }
}