package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.ComplianceCalendarUsageEntity
import com.centuroglobal.shared.data.payload.CorporateDocumentRequest
import com.centuroglobal.shared.data.payload.case.CaseDocumentsRequest
import com.centuroglobal.shared.data.payload.case.CaseTracking
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository
import com.centuroglobal.shared.util.TimeUtil
import org.springframework.data.repository.findByIdOrNull
import kotlin.reflect.KClass

class ComplianceCalendarUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository,
    private val caseRepository: CaseRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<ComplianceCalendarUsageEntity> {
        return userActions.map {
            val user = corporateUserRepository.findById(it.userId!!).get()
            createUsageEntity(it, user, id)
        }.flatten()
    }

    private fun createUsageEntity(
        userActionEntity: UserActionEntity,
        user: CorporateUserEntity,
        id: Long?
    ): MutableList<ComplianceCalendarUsageEntity> {

        val additionalData = userActionEntity.additionalData
        val caseId = mapper.readTree(userActionEntity.additionalData)["caseId"]?.longValue()

        val caseTracking = parsePayload(additionalData, CaseTracking::class)



        val usagesEntities = mutableListOf<ComplianceCalendarUsageEntity>()

        val trackingInitiatedFor = getInitiatedFor(caseId)

            caseTracking?.let {
            usagesEntities.add(
                ComplianceCalendarUsageEntity(
                    country = user.countryCode!!,
                    accessedBy = "${user.firstName} ${user.lastName}",
                    charges = 0F,
                    createdAt = userActionEntity.startTime,
                    subscriptionUsageDetailsId = id,
                    userId = userActionEntity.userId!!,
                    bandName = user.band?.name ?: "",
                    assetFor = trackingInitiatedFor,
                    assetType = "VISA",
                    expiry = caseTracking.visaExpiry?.let { TimeUtil.fromInstantMillis(it) },
                    name = "",
                    referenceId = caseId
                )
            )
        }

        val caseDocRequest = parsePayload(additionalData, CaseDocumentsRequest::class)

        val initiatedFor = getInitiatedFor(caseDocRequest?.caseId)

        caseDocRequest?.documents?.filter { it.expiryDate!=null }?.forEach {

            usagesEntities.add(
                ComplianceCalendarUsageEntity(
                    country = user.countryCode!!,
                    accessedBy = "${user.firstName} ${user.lastName}",
                    charges = 0F,
                    createdAt = userActionEntity.startTime,
                    subscriptionUsageDetailsId = id,
                    userId = userActionEntity.userId!!,
                    bandName = user.band?.name ?: "",
                    assetFor = initiatedFor,
                    assetType = "DOCUMENT",
                    expiry = TimeUtil.fromInstantMillis(it.expiryDate!!),
                    name = it.documentCode,
                    referenceId = caseDocRequest.caseId
                )
            )
        }

        val corporateDocRequest = parsePayload(additionalData, CorporateDocumentRequest::class)
        corporateDocRequest?.expiryDate?.let {
            usagesEntities.add(
                ComplianceCalendarUsageEntity(
                    country = corporateDocRequest.country,
                    accessedBy = "${user.firstName} ${user.lastName}",
                    charges = 0F,
                    createdAt = userActionEntity.startTime,
                    subscriptionUsageDetailsId = id,
                    userId = userActionEntity.userId!!,
                    bandName = user.band?.name ?: "",
                    assetFor = "",
                    assetType = "DOCUMENT",
                    expiry = TimeUtil.fromInstantMillis(corporateDocRequest.expiryDate!!),
                    name = corporateDocRequest.docName,
                    referenceId = null
                )
            )
        }

        return usagesEntities

    }

    private fun getInitiatedFor(caseId: Long?): String? {
        return caseId?.let { caseRepository.findByIdOrNull(it)?.initiatedFor }
    }

    private fun <T: Any> parsePayload(additionalData: String?, kClass: KClass<T>): T? {
        return try {
            getPayload(additionalData, kClass)
        } catch (e: Exception) {
            //silently ignore class cast exception
            null
        }
    }


    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            ComplianceCalendarUsageResponse.ModelMapper.from(it)
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
           ComplianceCalendarUsageResponse.ModelMapper.from(
                it as ComplianceCalendarUsageEntity
            )
        }
    }

}