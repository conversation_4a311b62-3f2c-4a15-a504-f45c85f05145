package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.subscription.usage.ComplianceCalendarUsageEntity
import com.centuroglobal.shared.util.TimeUtil

data class ComplianceCalendarUsageResponse(

    val name: String?,

    val country: String,

    val band: String?,

    val createdAt: Long?,

    val charges: Float,

    val assetType: String?,

    val assetFor: String?,

    val referenceId: Long?,

    val expiry: Long?,

    val accessedBy: String

    ): AbstractUsageResponse() {

    object ModelMapper {

        fun from(entity: ComplianceCalendarUsageEntity): ComplianceCalendarUsageResponse {

            return ComplianceCalendarUsageResponse(
                name = entity.name,
                country = entity.country,
                band = entity.bandName,
                createdAt = entity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F,
                expiry = entity.expiry?.let { TimeUtil.toEpochMillis(it) },
                assetType = entity.assetType,
                assetFor = entity.assetFor,
                referenceId = entity.referenceId,
                accessedBy = entity.accessedBy
            )
        }
    }
}