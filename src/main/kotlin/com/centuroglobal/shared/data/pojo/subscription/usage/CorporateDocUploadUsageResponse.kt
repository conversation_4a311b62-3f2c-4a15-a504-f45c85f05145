package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.subscription.usage.CorporateDocUploadUsageEntity
import com.centuroglobal.shared.util.TimeUtil

data class CorporateDocUploadUsageResponse(

    val name: String,

    val country: String,

    val band: String?,

    val createdAt: Long?,

    val charges: Float,

    val expiry: Long?,

    val documentType: String?,

    val documentName: String?,

    val fileSize: Long?

    ): AbstractUsageResponse() {

    object ModelMapper {

        fun from(entity: CorporateDocUploadUsageEntity): CorporateDocUploadUsageResponse {

            return CorporateDocUploadUsageResponse(
                name = entity.name,
                country = entity.country,
                band = entity.bandName,
                createdAt = entity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F,
                expiry = entity.expiry?.let { TimeUtil.toEpochMillis(it) },
                documentType = entity.documentType,
                documentName = entity.documentName,
                fileSize = entity.fileSize
            )
        }
    }
}