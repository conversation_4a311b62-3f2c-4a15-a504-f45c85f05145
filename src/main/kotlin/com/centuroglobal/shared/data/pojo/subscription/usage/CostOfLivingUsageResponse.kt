package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.dto.usage.CostOfLivingDto
import com.centuroglobal.shared.data.entity.subscription.usage.CostOfLivingUsageEntity
import com.centuroglobal.shared.util.TimeUtil
import java.time.temporal.ChronoUnit

data class CostOfLivingUsageResponse(

    val name: String,

    val country: String,

    val band: String?,

    val createdAt: Long?,

    val charges: Float,

    val corporateName: String? = null,

    val timeSpent: Long? = null,

    val partnerName: String? = null


): AbstractUsageResponse() {

    object ModelMapper {

        fun from(entity: CostOfLivingUsageEntity, createdBy: CorporateUserEntity): CostOfLivingUsageResponse {

            return CostOfLivingUsageResponse(
                name = entity.accessedBy,
                country = entity.country,
                band = createdBy.band?.name,
                createdAt = entity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }

        fun from(entity: CostOfLivingUsageEntity): CostOfLivingUsageResponse {

            return CostOfLivingUsageResponse(
                name = entity.accessedBy,
                country = entity.country,
                band = entity.bandName,
                createdAt = entity.createdAt?.let { TimeUtil.toEpochMillis(it) },
                charges = 0F
            )
        }

        fun from(entity: CostOfLivingDto): CostOfLivingUsageResponse {

            return CostOfLivingUsageResponse(
                name = entity.createdBy,
                country = entity.country?:"",
                band = entity.bandName,
                createdAt = TimeUtil.toEpochMillis(entity.createdDate),
                charges = 0F,
                corporateName = entity.corporateName,
                partnerName = entity.partnerName,
                timeSpent = entity.endTimestamp?.let { ChronoUnit.SECONDS.between(entity.createdDate, it)}
            )
        }
    }
}