package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.PlaybookUsageEntity
import com.centuroglobal.shared.data.pojo.playbook.PlaybookRequest
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class PlaybookUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<PlaybookUsageEntity> {

        return userActions.map {
            val payload = getPayload(it.additionalData, PlaybookRequest::class)
            val user = corporateUserRepository.findById(it.userId!!).get()
            PlaybookUsageEntity(
                accessedBy = "${user.firstName} ${user.lastName}",
                country = payload.country,
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                bandName = user.band?.name ?: "",
                industry = payload.industryName ?: "",
                userId = it.userId!!,
                sessionId = it.sessionId
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            PlaybookUsageResponse.ModelMapper.from(it)
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
        return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
            PlaybookUsageResponse.ModelMapper.from(
                it as PlaybookUsageEntity
            )
        }
    }

}