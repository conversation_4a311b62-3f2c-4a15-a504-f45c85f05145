package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.UserUsageEntity
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class UserUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<UserUsageEntity> {

        return userActions.map {
            val data = mapper.readTree(it.additionalData)
            val payload = mapper.readValue(data.get("request").textValue(), CorporateUserRequest::class.java)
            val user = corporateUserRepository.findByEmail(payload.email)
            UserUsageEntity(
                name = "${payload.firstName} ${payload.lastName}",
                country = user?.countryCode!!,
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                userId = it.userId!!,
                bandName = user.band?.name?:""
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            UserUsageResponse.ModelMapper.from(
                it,
                corporateUserRepository.findById(it.userId).get()
            )
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
            UserUsageResponse.ModelMapper.from(
                it as UserUsageEntity,
                corporateUserRepository.findById(it.userId).get()
            )
        }
    }

}