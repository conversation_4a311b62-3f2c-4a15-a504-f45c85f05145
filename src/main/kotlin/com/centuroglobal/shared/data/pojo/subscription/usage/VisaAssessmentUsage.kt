package com.centuroglobal.shared.data.pojo.subscription.usage

import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.entity.subscription.usage.VisaAssessmentUsageEntity
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.subscription.usage.UsageRepository

class VisaAssessmentUsage(
    private val userActions: List<UserActionEntity>,
    private val corporateUserRepository: CorporateUserRepository,
    private val usageRepository: UsageRepository
) : AbstractUsage() {

    override fun getUsage(): Long {
        return userActions.size.toLong()
    }

    override fun populateFeatureTable(id: Long) {
        usageRepository.saveAll(getUsageDetails(id))
    }

    override fun getUsageDetails(id: Long?): List<VisaAssessmentUsageEntity> {

        return userActions.map {
            val data = mapper.readTree(it.additionalData)
            val user = corporateUserRepository.findById(it.userId!!).get()
            VisaAssessmentUsageEntity(
                accessedBy = "${user.firstName} ${user.lastName}",
                issueCountry = data.get("sourceCountry").textValue(),
                charges = 0F,
                createdAt = it.startTime,
                subscriptionUsageDetailsId = id,
                bandName = user.band?.name?:"",
                destinationCountry = data.get("destinationCountry").textValue(),
                userId = it.userId!!
            )
        }
    }

    override fun getUsageDetailsResponse(): List<AbstractUsageResponse> {
        return getUsageDetails(null).map {
            VisaAssessmentUsageResponse.ModelMapper.from(
                it
            )
        }
    }

    override fun getHistoricalUsageDetailsResponse(id: Long): List<AbstractUsageResponse> {
       return usageRepository.findAllBySubscriptionUsageDetailsId(id).map {
           VisaAssessmentUsageResponse.ModelMapper.from(
                it as VisaAssessmentUsageEntity
            )
        }
    }

}