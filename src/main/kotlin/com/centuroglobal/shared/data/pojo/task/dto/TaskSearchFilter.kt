package com.centuroglobal.shared.data.pojo.task.dto

import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.*
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.util.TimeUtil
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.TemporalAdjusters

data class TaskSearchFilter(
    val name: String? = null,
    var createdBy: Long? = null,
    val type: List<ReferenceType>? = null,
    var referenceId : List<Long>? = null,
    val status: List<TaskStatus?>? = null,
    val currentUser: Long? = null,
    var visibility: TaskVisibility?= null,
    var taskView: String? = null,
    var priority: String? = null,
    override var partnerId: Long?,
    val month: Int? = null,
    val year: Int? = null,
    val dueDate: LocalDate? = null,
    val showBy: ShowBy? = null,
    var assignedTo: Long? = null,
    var visibilityString: String?,
    val companyId: Long,
    val companyType: TaskCompanyType,
    val fromDate: LocalDateTime? = null,
    val toDate: LocalDateTime? = null,
    var currentUserPartnerId: Long? = null,
    var accountUsers: List<Long>? = null,
    var expertAssignedCases: List<Long>? = null

): AbstractSearchFilter() {
    object Builder {

        fun build(
            name: String?,
            createdBy: Long?,
            type: List<ReferenceType>?,
            referenceId: Long?,
            status: List<TaskStatus?>?,
            currentUser: Long?,
            visibility: TaskVisibility?,
            taskView: String?,
            priority: String?,
            partnerId: Long?,
            showBy: ShowBy?,
            assignedTo: Long?,
            companyId: Long,
            companyType: TaskCompanyType
        ) =
            TaskSearchFilter(
                name = if (name.isNullOrBlank()) null else "%${name}%",
                createdBy = if (createdBy==0L) null else createdBy,
                type = type,
                referenceId = referenceId?.let { listOf(it)},
                status = status,
                currentUser = currentUser,
                visibility= visibility,
                taskView = if (taskView.isNullOrBlank()) null else taskView,
                priority = priority,
                partnerId = companyId,
                assignedTo = assignedTo,
                visibilityString = "",
                companyId = companyId,
                companyType = companyType,
                fromDate = getFromDate(showBy),
                toDate = getToDate(showBy),
                showBy = showBy
            )

        private fun getFromDate(showBy: ShowBy?): LocalDateTime? {

           return when(showBy) {
                ShowBy.CURRENT_MONTH -> TimeUtil.startDateTimeOfMonth()
                ShowBy.CURRENT_WEEK -> TimeUtil.atStartOfDay(LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY)))
                ShowBy.TODAY -> TimeUtil.atStartOfDay()
                else -> null
            }
        }
        private fun getToDate(showBy: ShowBy?): LocalDateTime? {
            return when(showBy) {
                ShowBy.CURRENT_MONTH -> TimeUtil.lastDateTimeOfMonth()
                ShowBy.CURRENT_WEEK -> TimeUtil.atEndOfDay(LocalDate.now().with(TemporalAdjusters.nextOrSame(DayOfWeek.SATURDAY)))
                ShowBy.TODAY -> TimeUtil.atEndOfDay()
                else -> null
            }
        }

        fun build(
            month: Int?,
            year: Int?,
            partnerId: Long?,
            taskView: String?,
            currentUser: Long?,
            visibility: TaskVisibility?,
            companyId: Long,
            companyType: TaskCompanyType
        ) = TaskSearchFilter(
            month = month,
            year = year,
            partnerId = partnerId,
            taskView = if (taskView.isNullOrBlank()) null else taskView,
            currentUser = currentUser,
            visibility = visibility,
            visibilityString = visibility.toString(),
            companyId = companyId,
            companyType = companyType
        )

        fun build(
            dueDate: LocalDate?,
            partnerId: Long?,
            taskView: TaskView?,
            currentUser: Long?,
            visibility: TaskVisibility?,
            companyId: Long,
            companyType: TaskCompanyType
        ) = TaskSearchFilter(
            dueDate = dueDate,
            partnerId = partnerId,
            taskView = taskView?.name,
            currentUser = currentUser,
            visibility = visibility,
            visibilityString = "",
            companyId = companyId,
            companyType = companyType
        )
    }
}