package com.centuroglobal.shared.data.pojo.task.dto

import com.centuroglobal.shared.data.enums.task.TaskWorkflowStatus
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter

data class TaskWorkflowSearchFilter(
    val name: String? = null,
    val country: String? = null,
    val status: TaskWorkflowStatus? = null,
    val category: String? = null,
    var updatedBy: Long? = null,
    val isPartner: Boolean? = null,
    val referenceId: Long? = null,
    val referenceType: String? = null,
    val id: Long? = null,
    override var partnerId: Long? = null,
    var showToPartner: Boolean? = null

) : AbstractSearchFilter() {
    object Builder {

        fun build(
            name: String? = null,
            country: String? = null,
            status: TaskWorkflowStatus? = null,
            category: String? = null,
            updatedBy: Long? = null,
            isPartner: Boolean? = null,
            referenceId: Long? = null,
            referenceType: String? = null,
            partnerId: Long? = null,
            id: Long? = null,
            showToPartner: Boolean? = null
        ) =
            TaskWorkflowSearchFilter(
                name = if (name.isNullOrBlank()) null else "%${name}%",
                country = if (country.isNullOrBlank()) null else country,
                status = status,
                category = if (category.isNullOrBlank()) null else category,
                updatedBy = updatedBy,
                isPartner = isPartner,
                referenceId = referenceId,
                referenceType = if (referenceType.isNullOrBlank()) null else referenceType,
                partnerId = partnerId,
                id = id,
                showToPartner = showToPartner
            )

    }
}