package com.centuroglobal.shared.data.pojo.task.response

import com.centuroglobal.shared.data.entity.TaskAssigneeEntity
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility

data class AdminTaskDetails(

    var id: Long?,

    val name: String,

    val description: String,

    var referenceId: Long?,

    var referenceType: ReferenceType,

    var visibility: TaskVisibility?,

    val dueDate: Long?,

    val completedDate: Long?,

    var priority: String?,

    var status: TaskStatus?,

    var assignee: MutableList<TaskAssigneeEntity>?

    )
