package com.centuroglobal.shared.data.pojo.travel

import com.centuroglobal.shared.data.entity.travel.view.TravelAssessmentLogView
import com.centuroglobal.shared.util.TimeUtil
import java.time.temporal.ChronoUnit

data class TravelAssessmentLogsResponse(
    val id: Long?,
    val corporateName: String,
    val accessedBy: String,
    val accessedDate: Long,
    val timeSpent: Long,
    val purpose: String,
    val messages: Long,
    val country: String,
    val visaType: String?,
    val caseId: Long? = null,
    val sessionId: String?,
    val partnerName: String?,
    val assessmentTime: Long?,
    val visaName: String?,
    val dossierCount: Long

) {
    object ModelMapper {

        fun from(entity: TravelAssessmentLogView): TravelAssessmentLogsResponse {

            return TravelAssessmentLogsResponse(
                entity.assessmentId,
                entity.corporateName,
                entity.createdBy,
                TimeUtil.toEpochMillis(entity.createdAt),
                entity.endTimestamp?.let { ChronoUnit.SECONDS.between(entity.createdAt, entity.endTimestamp) }?:0,
                entity.type,
                entity.aiQuestions,
                entity.destination,
                entity.visaType,
                entity.caseId,
                entity.taSessionId,
                entity.partnerName,
                entity.assessmentTime,
                entity.visaName,
                entity.dossierCount
            )
        }
    }
}