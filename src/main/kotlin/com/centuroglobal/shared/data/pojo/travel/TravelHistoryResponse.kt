package com.centuroglobal.shared.data.pojo.travel

import com.centuroglobal.shared.data.entity.travel.TravelHistoryEntity
import com.centuroglobal.shared.data.enums.travel.TravelStayUnit
import com.centuroglobal.shared.util.TimeUtil

class TravelHistoryResponse(
    val id: Long?,

    val originCountry: String,

    val destinationCountry: String,

    val originCity: String?,

    val destinationCity: String?,

    val arrival: Long,

    val departure: Long,

    val periodOfStay: Long,

    val stayUnit: TravelStayUnit,

    val purpose: String
) {

    object ModelMapper {
        fun from(entity: TravelHistoryEntity): TravelHistoryResponse {

            return TravelHistoryResponse(
                entity.id,
                entity.originCountry,
                entity.destinationCountry,
                entity.originCity,
                entity.destinationCity,
                TimeUtil.toEpochMillis(entity.arrival),
                TimeUtil.toEpochMillis(entity.departure),
                entity.periodOfStay,
                entity.stayUnit,
                entity.purpose
            )
        }
    }
}
