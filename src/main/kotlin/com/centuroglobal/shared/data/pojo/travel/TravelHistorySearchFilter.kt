package com.centuroglobal.shared.data.pojo.travel

import java.time.LocalDateTime

data class TravelHistorySearchFilter(
    val originCountry: String? = null,
    val destinationCountry: String? = null,
    val purpose: String? = null,
    val from: LocalDateTime? = null,
    val userId: Long? = null
) {
    object Builder {
        fun build(originCountry: String?, destinationCountry: String?, purpose: String?, from: LocalDateTime?=null, userId: Long?=null): TravelHistorySearchFilter {
            return TravelHistorySearchFilter(
                originCountry = if (originCountry.isNullOrBlank()) null else originCountry,
                destinationCountry = if (destinationCountry.isNullOrBlank()) null else destinationCountry,
                purpose = if (purpose.isNullOrBlank()) null else purpose,
                from = from,
                userId = userId
            )
        }
    }
}