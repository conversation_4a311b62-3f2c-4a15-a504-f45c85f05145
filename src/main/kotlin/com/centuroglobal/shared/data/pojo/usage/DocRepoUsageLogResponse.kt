package com.centuroglobal.shared.data.pojo.usage

import com.centuroglobal.shared.data.entity.dto.usage.ClientDocDto
import com.centuroglobal.shared.util.TimeUtil

data class DocRepoUsageLogResponse(

    val id: Long,

    val docType: String?,

    val docName: String,

    val size: Long,

    val corporateName: String,

    val country: String?,

    val createdBy: String,

    val createdDate: Long,

    val expiryDate: Long?,

    val partnerName: String?
) {
    object ModelMapper {

        fun from(entity: ClientDocDto): DocRepoUsageLogResponse {
            return DocRepoUsageLogResponse(
                country = entity.country,
                createdBy = entity.createdBy,
                createdDate = TimeUtil.toEpochMillis(entity.createdDate),
                expiryDate = entity.expiryDate?.let { TimeUtil.toEpochMillis(it) },
                docType = entity.docType?:entity.docName,
                corporateName = entity.corporateName,
                id = entity.id,
                docName = entity.docName,
                size = entity.size,
                partnerName = entity.partnerName
            )
        }
    }
}