package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.AIChatEntity
import com.centuroglobal.shared.data.entity.ChatThreadEntity
import com.centuroglobal.shared.data.entity.dto.usage.AIChatDto
import com.centuroglobal.shared.data.pojo.usage.AIChatUsageLogSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository


@Repository
interface AIChatRepository : JpaRepository<AIChatEntity, Long> {

    fun findAllByCreatedBy(userId: Long): List<AIChatEntity>

    fun findAllByCreatedBy(userId: Long, page: Pageable): Page<AIChatEntity>
    @Query(value = """
        SELECT ac.question AS question, ac.created_by AS createdBy, ac.created_date AS createdDate, p.name AS partnerName
        FROM ai_chat ac JOIN corporate_user cu ON ac.created_by = cu.id JOIN corporate co ON co.id = cu.corporate_id LEFT JOIN
        partner p ON p.id = co.partner_id
        WHERE
        (:#{#filter.question} IS NULL OR ac.question LIKE :#{#filter.question}) AND
        (:#{#filter.corporateId} IS NULL OR cu.corporate_id = :#{#filter.corporateId}) AND
        (:#{#filter.partnerId} IS NULL OR co.partner_id = :#{#filter.partnerId}) AND
        (:#{#filter.from} IS NULL  OR (ac.created_date) BETWEEN :#{#filter.from} AND :#{#filter.to}) AND
        ((false=:#{#filter.isPartner} AND co.partner_id IS NULL) OR (true=:#{#filter.isPartner} AND co.partner_id IS NOT NULL))
        GROUP BY ac.id
    """, nativeQuery = true)
    fun searchByCriteria(filter: AIChatUsageLogSearchFilter, page: Pageable): Page<AIChatDto>

    fun findByChatThread(chatThread: ChatThreadEntity): List<AIChatEntity>
    fun findByChatThreadOrderByCreatedDateDesc(chatThread: ChatThreadEntity): List<AIChatEntity>

}