package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.pojo.AccountSearchFilter
import com.centuroglobal.shared.data.pojo.ReferenceData
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface AccountEntityRepository : JpaRepository<AccountEntity, Long> {

    fun findAllByCorporate(corporate : CorporateEntity): List<AccountEntity>

    @Query(
        """
            select a from corporate_accounts a
            where
            a.corporate.id = :#{#corporate.id} AND
            (:#{#name} IS NULL OR a.name = :#{#name}) AND
            (:#{#status} IS NULL OR a.status = :#{#status})
        """
    )
    fun searchByNameAndStatus(name: String?, status: AccountStatus?,corporate : CorporateEntity): List<AccountEntity>
    fun findByIdAndCorporate(accountId: Long?, corporate: CorporateEntity): AccountEntity?
    fun findAllByCorporateAndCreatedByIn(corporate: CorporateEntity, createdBy: List<Long?>): List<AccountEntity>

    @Query("""
        SELECT ca FROM corporate_accounts ca JOIN login_account la ON la.id=ca.createdBy
        WHERE(
        (:#{#filter.search} IS NULL OR (ca.name LIKE :#{#filter.search}) OR (la.email LIKE :#{#filter.search})) AND
        (:#{#filter.status} IS NULL OR ca.status =:#{#filter.status}) AND
        ca.corporate.id = :#{#corporateId}
        )
        group by ca.id
    """)
     fun searchByCriteriaForAdmin(
        @Param("filter") filter: AccountSearchFilter,
        corporateId: Long,
        pageable: Pageable
    ): Page<AccountEntity>

    @Query("""
        SELECT ca FROM corporate_accounts ca JOIN login_account la ON la.id=ca.createdBy
        WHERE(
        (:#{#filter.search} IS NULL OR (ca.name LIKE :#{#filter.search}) OR (la.email LIKE :#{#filter.search})) AND
        (:#{#filter.status} IS NULL OR ca.status =:#{#filter.status}) AND
        ca.corporate.id = :#{#corporateId} AND
        ca.createdBy IN :#{#createdBy}
        )
        group by ca.id
    """)
    fun searchByCriteria(
        @Param("filter") filter: AccountSearchFilter,
        corporateId: Long,
        createdBy: List<Long>,
        pageable: Pageable
    ): Page<AccountEntity>

    fun findByCorporateUsersId(userId: Long): List<ReferenceData>

    fun findByCorporateId(corporateId: Long): List<ReferenceData>


}