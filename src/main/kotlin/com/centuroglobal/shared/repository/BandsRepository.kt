package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BandsEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface BandsRepository : JpaRepository<BandsEntity, Long> {

    fun findByIdAndCorporate(id: Long?, corporate: CorporateEntity) : BandsEntity?

    fun findByCorporateId(corporateId: Long): List<BandsEntity>?

    fun findByNameAndCorporate(name: String?, corporate: CorporateEntity) : BandsEntity?
    fun deleteByCorporateRootUserId(loginAccountId: Long)
}