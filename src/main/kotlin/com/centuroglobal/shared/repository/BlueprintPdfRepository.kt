package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BlueprintPdfEntity
import com.centuroglobal.shared.data.enums.StepName
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface BlueprintPdfRepository : JpaRepository<BlueprintPdfEntity, Long> {

    fun findAllByBlueprintIdAndUploaded(blueprintId: String, uploaded: Boolean): List<BlueprintPdfEntity>

    fun findByBlueprintIdAndStepName(blueprintId: String, step: StepName): BlueprintPdfEntity?

    fun findByBlueprintIdAndStepNameAndUploaded(blueprintId: String, step: StepName, uploaded: Boolean): BlueprintPdfEntity?
}