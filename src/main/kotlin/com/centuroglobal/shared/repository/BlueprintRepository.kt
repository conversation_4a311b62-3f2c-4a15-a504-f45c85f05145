package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.BlueprintEntity
import com.centuroglobal.shared.data.entity.dto.usage.BlueprintDto
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.pojo.UsageLogSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface BlueprintRepository : JpaRepository<BlueprintEntity, String> {

    fun findByCountryCodeAndStatusIsNot(countryCode: String, status: BlueprintStatus): BlueprintEntity?

    fun findAllByCountryCodeIn(codes: List<String>): List<BlueprintEntity>

    fun findAllByStatus(status: BlueprintStatus): List<BlueprintEntity>

    fun countByStatus(status: BlueprintStatus): Long

    @Query(value = """
        SELECT 
            ub.id as id,
            ub.country AS country,
            ub.created_at AS startTime,
            ub.end_timestamp AS endTime,
            la.id AS createdBy,
            ub.created_at AS createdDate,
            p.name AS partnerName
        FROM usage_blueprint ub JOIN corporate_user cu ON ub.user_id=cu.id JOIN
        corporate co ON co.id = cu.corporate_id JOIN login_account la ON la.id=ub.user_id LEFT JOIN
        partner p ON p.id = co.partner_id
        WHERE
            (
                (:#{#filter.country} IS NULL OR ub.country = :#{#filter.country}) AND
                (:#{#filter.corporate} IS NULL OR cu.corporate_id = :#{#filter.corporate}) AND
                (:#{#filter.partnerId} IS NULL OR co.partner_id = :#{#filter.partnerId}) AND
                (:#{#filter.from} IS NULL  OR (ub.created_at) BETWEEN :#{#filter.from} AND :#{#filter.to}) AND
        ((false=:#{#filter.isPartner} AND co.partner_id IS NULL) OR (true=:#{#filter.isPartner} AND co.partner_id IS NOT NULL))
            AND ub.end_timestamp IS NOT NULL
            )
         group by ub.id
    """, nativeQuery = true)
    fun searchUsageLogByCriteria(
        filter: UsageLogSearchFilter,
        pageRequest: Pageable
    ): Page<BlueprintDto>
}
