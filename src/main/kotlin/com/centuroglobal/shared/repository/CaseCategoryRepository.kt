package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseCategoryEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface CaseCategoryRepository : JpaRepository<CaseCategoryEntity, Long> {
    fun getReferenceBySubCategoryIdAndParentCategoryId(subCategoryId: String, parentCategoryId: String): CaseCategoryEntity
    fun getReferenceBySubCategoryId(subCaseCategoryId: String): CaseCategoryEntity?
    @Query("SELECT DISTINCT c.parentCategoryId FROM case_category c")
    fun findDistinctParentCategoryIds(): List<String>
}