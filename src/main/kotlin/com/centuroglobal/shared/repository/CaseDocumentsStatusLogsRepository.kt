package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseDoucmentsStatusLogsEntity
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CaseDocumentsStatusLogsRepository : JpaRepository<CaseDoucmentsStatusLogsEntity, Long> {
    fun findAllByCaseId(caseId: Long, pageRequest: PageRequest): List<CaseDoucmentsStatusLogsEntity>?
    fun findAllByCaseIdAndDocType(caseId: Long, docType: String, pageRequest: PageRequest): List<CaseDoucmentsStatusLogsEntity>?
}