package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.CaseManagerEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CaseManagerRepository : JpaRepository<CaseManagerEntity, Long> {

    fun findAllByCase(case: CaseEntity): MutableList<CaseManagerEntity>
}