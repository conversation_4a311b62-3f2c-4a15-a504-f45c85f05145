package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.entity.case.CaseMilestonesEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CaseMilestonesRepository : JpaRepository<CaseMilestonesEntity, Long> {
    fun findAllByCase(case: CaseEntity): MutableList<CaseMilestonesEntity>
    fun findByCaseAndMilestoneKey(case: CaseEntity, milestoneKey: String): CaseMilestonesEntity?
    fun findFirstByCaseOrderByLastUpdatedDateDesc(case: CaseEntity): CaseMilestonesEntity?

    fun findTopByCaseAndMilestoneKeyOrderByCreatedDateDesc(
        case: CaseEntity,
        milestoneKey: String
    ): CaseMilestonesEntity?

    fun deleteAllByCase(case: CaseEntity)

}