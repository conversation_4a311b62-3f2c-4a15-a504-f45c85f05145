package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.case.SingleVisaEntity
import com.centuroglobal.shared.data.entity.dto.EntityIdDto
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.data.pojo.CaseReferenceData
import com.centuroglobal.shared.data.pojo.case.ApplicantCaseReferenceData
import com.centuroglobal.shared.data.pojo.case.DashboardCaseSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Optional

@Repository
interface CaseRepository : BaseRepository<CaseEntity, Long> {
    fun countByCreatedBy(userId: ClientView): Long

    fun findAllByCreatedByIn(userIds: List<ClientView>): MutableList<CaseEntity>

    fun findAllIdByCreatedByUserIdIn(userIds: List<Long>): MutableList<ApplicantCaseReferenceData>

    fun findAllByStatusInAndArchive(status: List<String>, archive: Boolean): List<CaseEntity>

    fun findByUuid(uuid: String): CaseEntity?

    fun findByFeeToken(feeToken: String): CaseEntity?

    fun findByAssessmentId(assessmentId: Long): CaseEntity?

    fun findByCaseFees_NeedApprovalAndCaseFees_IsApprovedAndCaseFees_FeesCreationDateBetween(needApproval: Boolean, isApproved: Boolean,
                                                                                             start: LocalDateTime, end: LocalDateTime
    ): List<CaseEntity>

    fun findAllByAccountAndCreatedByIn(account:AccountEntity, userIds: List<ClientView>): MutableList<CaseEntity>


    fun findAllByCreatedByAndParentCategoryIdIn(createdBy:ClientView,categories:List<String>): List<CaseReferenceData>
    
    @Query(""" 
         select c from cases c 
         where
         (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} )
         AND 
         (c.createdBy in (:#{#filter.users})
         ) AND
          (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId})
          AND (:#{#filter.country} IS NULL OR c.country = :#{#filter.country})
          AND  (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId})
          group by c.id
    """)
    fun searchByCriteria(
        @Param("filter") filter: DashboardCaseSearchFilter) : List<CaseEntity>

    fun findByAccountIsNull(): List<CaseEntity>

    fun findByActionForIsNull(): List<CaseEntity>

    fun findAllByAccountAndArchiveAndCreatedByIn(account: AccountEntity?, archive: Boolean, userList: List<ClientView>): MutableList<CaseEntity>
    fun findAllByArchiveAndCreatedByIn(archive: Boolean, userList: List<ClientView>): MutableList<CaseEntity>
    @Query("""
        
        SELECT c, ch from cases c LEFT JOIN case_history ch 
        ON c.id=ch.case.id 
        WHERE c.status NOT IN ('CANCELLED', 'CASE_COMPLETE','CASE_DECLINED', 'COMPLETED') AND
        (:#{#fromCreatedDate} IS NULL OR c.createdDate > :#{#fromCreatedDate}) AND
        c.sendUpdateReminder = TRUE
        GROUP BY c.id
        HAVING MAX(ch.lastUpdatedDate) < :#{#limitDate} OR 
        (ch.lastUpdatedDate IS NULL AND c.lastUpdatedDate < :#{#limitDate})

    """)
    fun searchCasesPendingForStatusUpdate(
    @Param("limitDate") limitDate: LocalDateTime, @Param("fromCreatedDate") fromCreatedDate: LocalDateTime?
    ): List<CaseEntity>

    @Query(value = """
        
        SELECT COUNT(*) FROM linked_cases WHERE (case_id=:#{#linkFrom} AND linked_case_id=:#{#linkTo}) OR 
        (case_id=:#{#linkTo} AND linked_case_id=:#{#linkFrom})
        
    """, nativeQuery = true)
    fun countLinks(@Param("linkFrom") linkFrom: Long, @Param("linkTo") linkTo: Long): Long
    @Query(value = """
        
        DELETE FROM linked_cases WHERE (case_id=:#{#linkFrom} AND linked_case_id=:#{#linkTo}) OR 
        (case_id=:#{#linkTo} AND linked_case_id=:#{#linkFrom})
        
    """, nativeQuery = true)
    @Modifying
    fun unlinkCases(@Param("linkFrom") linkFrom: Long, @Param("linkTo") linkTo: Long): Int?
    @Query(value = """
        
        SELECT linked_case_id AS linked_cases FROM linked_cases WHERE case_id=:#{#caseId} UNION ALL SELECT case_id FROM linked_cases WHERE linked_case_id=:#{#caseId}
        
    """, nativeQuery = true)
    fun getLinkedCaseIds(caseId: Long): List<Long>

    @Query(value = """
        select c from cases c LEFT JOIN c.assignee ca
        where c.id=:#{#id} AND
        (
            c.createdBy.userId = :#{#userId} OR
            c.createdBy.id = :#{#companyProfileId} OR
            ca.id = :#{#userId}
        )

    """)
    override fun findByIdForExpert(id: Long, userId: Long, companyProfileId: Long): Optional<CaseEntity>

    @Query("""
        SELECT c FROM cases c WHERE
        c.id = :#{#id} AND
        c.createdBy.id = :#{#corporateId}
    """)
    override fun findByIdAndCreatedByCorporateId(id: Long, corporateId: Long): Optional<CaseEntity>

    @Query("""
        SELECT c FROM cases c WHERE
        c.id = :#{#id} AND
        c.createdBy.userId IN :#{#createdByIn}
    """)
    override fun findByIdAndCreatedByIdIn(id: Long, createdByIn: MutableList<Long>): Optional<CaseEntity>

    //TODO Fix the query as per criteria
    @Query(value = """
        select c from cases c
    """)
    override fun searchByCriteriaForFullAccess(filter: AbstractSearchFilter, pageable: Pageable): Page<CaseEntity>
    //TODO Fix the query as per criteria
    @Query(value = """
        select c from cases c
    """)
    override fun searchByCriteria(filter: AbstractSearchFilter, userIds: List<Long>, pageable: Pageable): Page<CaseEntity>


    fun countByCreatedByIdAndStatusNotIn(corporateId: Long, statusesToSkip: List<String>): Long
    fun countByAssigneeIdIn(assigneeList: List<Long>): Long

//    override fun findByIdAndCreatedByCorporate(queryId: Long, corporate: CorporateEntity): Optional<CaseEntity>

    fun findByCreatedById(corporateId: Long): List<CaseEntity>

    @Query(value = """
        UPDATE cases c SET c.lastUpdatedDate = :#{#lastUpdatedDate} WHERE id = :#{#id}
    """)
    @Modifying
    fun touch(id: Long, lastUpdatedDate: LocalDateTime)

    fun findAllByAssigneeId(userId: Long): List<EntityIdDto>

    @Query(value = """
        UPDATE cases SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replaceCaseId(newCaseId: Long, caseId: Long)
    @Query(value = """
        UPDATE business_visa SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replaceBusinessVisa(newCaseId: Long, caseId: Long)

    @Query(value = """
        UPDATE entry_visa SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replaceEntryVisa(newCaseId: Long, caseId: Long)

    @Query(value = """
        UPDATE single_immigration_visa SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replaceSingleVisa(newCaseId: Long, caseId: Long)

    @Query(value = """
        UPDATE posted_worker_notification SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replacePWN(newCaseId: Long, caseId: Long)

    @Query(value = """
        UPDATE right_to_work_check SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replaceRTW(newCaseId: Long, caseId: Long)

    @Query(value = """
        UPDATE generic_case SET id=:#{#caseId} WHERE id=:#{#newCaseId}
    """, nativeQuery = true)
    @Modifying
    fun replaceDynamicCase(newCaseId: Long, caseId: Long)

    @Query("""
        SELECT c FROM cases c 
        WHERE c.visaExpiryDate IS NOT NULL AND 
        CAST(from_unixtime(c.visaExpiryDate/1000) AS DATE) = :#{#expiryDate}
    """)
    fun findAllByVisaExpiryDate(expiryDate: LocalDate): List<CaseEntity>?

}