package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CaseStatusMasterEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface CaseStatusMasterRepository : JpaRepository<CaseStatusMasterEntity, Long> {
    fun findByStatus(status: String): CaseStatusMasterEntity?
    fun findBySubCategory(subCategory: String): List<CaseStatusMasterEntity>
    fun findBySubCategoryAndStatus(subCategoryId: String, status: String): CaseStatusMasterEntity

    @Query("""
        SELECT distinct(c.status) as status, c.statusDisplayText as displayText FROM case_status_master c
    """)
    fun findAllDistinctStatus(): List<Map<String, String>>
}