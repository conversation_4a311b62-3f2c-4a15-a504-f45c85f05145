package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ChatThreadEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ChatThreadRepository : JpaRepository<ChatThreadEntity, Long> {
    fun findByThreadId(threadId: String): ChatThreadEntity?
    fun findByReferenceTypeAndReferenceId(referenceType: String, referenceId: String): List<ChatThreadEntity>
    fun findAllByAccountId(userId: Long): List<ChatThreadEntity>

    @Query("""
        SELECT ct FROM chat_thread ct LEFT JOIN ct.chats c
        WHERE
        (:#{#search} IS NULL OR c.question LIKE :#{#search}) AND
        ct.account.id = :#{#userId}
        GROUP BY ct.id
        ORDER BY ct.createdDate DESC
    """)
    fun searchByCriteria(search: String?, userId: Long): List<ChatThreadEntity>
}
