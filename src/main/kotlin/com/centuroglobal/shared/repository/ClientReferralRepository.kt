package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ClientReferralEntity
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.pojo.referral.ClientReferralSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface ClientReferralRepository : JpaRepository<ClientReferralEntity, Long> {

    @Query(
        """
            select cr from client_referral cr left join cr.experts expert left join expert.expertises expertise
            where 
            (cr.status IN :#{#status}) AND 
            (:#{#filter.search} IS NULL OR (cr.title like :#{#filter.search} or  cr.description like :#{#filter.search})) AND
            (:#{#filter.countryCode} IS NULL OR cr.clientCountryCode = :#{#filter.countryCode}) AND
            (:#{#filter.expertiseId} IS NULL OR expertise.id = :#{#filter.expertiseId}) AND
            (:#{#filter.from} IS NULL OR cr.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR cr.createdDate <= :#{#filter.to}) AND
            (:#{#filter.status} IS NULL OR cr.status = :#{#filter.status})
            group by cr.id
        """
    )
    fun searchByCriteria(
        @Param("status")
        status: Array<LeadStatus>,
        @Param("filter")
        filter: ClientReferralSearchFilter,
        pageable: Pageable
    ): Page<ClientReferralEntity>

    @Query(
        """
            select cr from client_referral cr left join cr.experts expert left join expert.expertises expertise
            where 
            (cr.createdBy = :#{#expertId} OR expert.id = :#{#expertId}) AND 
            (cr.status IN :#{#status}) AND 
            (:#{#filter.countryCode} IS NULL OR cr.clientCountryCode = :#{#filter.countryCode}) AND
            (:#{#filter.expertiseId} IS NULL OR expertise.id = :#{#filter.expertiseId}) AND
            (:#{#filter.from} IS NULL OR cr.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR cr.createdDate <= :#{#filter.to}) AND
            (:#{#filter.status} IS NULL OR cr.status = :#{#filter.status})
            group by cr.id
        """
    )
    fun searchByCriteriaForExpert(
        @Param("expertId")
        expertId: Long,
        @Param("status")
        status: Array<LeadStatus>,
        @Param("filter")
        filter: ClientReferralSearchFilter,
        pageable: Pageable
    ): Page<ClientReferralEntity>
}