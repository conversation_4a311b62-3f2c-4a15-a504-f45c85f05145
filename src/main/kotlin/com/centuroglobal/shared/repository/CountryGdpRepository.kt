package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CountryGdpEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface CountryGdpRepository : JpaRepository<CountryGdpEntity, Long> {
    fun findAllByCreatedDateGreaterThan(date: LocalDateTime?): MutableList<CountryGdpEntity>
}