package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.EventEntity
import com.centuroglobal.shared.data.entity.EventInviteeEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface EventInviteeRepository : JpaRepository<EventInviteeEntity, Long> {
    fun findFirstByEventAndClient_UserId(eventEntity: EventEntity, userId: Long): EventInviteeEntity?
}