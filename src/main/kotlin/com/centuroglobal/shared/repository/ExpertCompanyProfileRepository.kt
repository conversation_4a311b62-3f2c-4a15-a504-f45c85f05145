package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.pojo.PartnerExpertSearchFilter
import com.centuroglobal.shared.data.pojo.ReferenceData
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface ExpertCompanyProfileRepository : JpaRepository<ExpertCompanyProfileEntity, Long> {
    fun findByUsers_Id(userId: Long): ExpertCompanyProfileEntity?

    fun findAllByAssociatedPartnersIdAndIdIn(partnerId: Long, companyIds: List<Long>): List<ExpertCompanyProfileEntity>

    fun findByCompanyType(type: ExpertCompanyType): List<ReferenceData>

    @Query(
        value = """
            SELECT e FROM expert_company_profile e LEFT JOIN e.associatedPartners ap
            LEFT JOIN e.users eu ON eu.expertType='PRIMARY'
            WHERE(
                (:#{#filter.search} IS NULL OR e.name LIKE :#{#filter.search}) AND 
                (:#{#filter.status} IS NULL OR e.status = :#{#filter.status}) AND
				(:#{#filter.country} IS NULL OR eu.countryCode = :#{#filter.country}) AND
                (:#{#partnerId} IS NULL OR ap.id = :#{#partnerId}) AND
                (:#{#filter.companyType} IS NULL OR e.companyType = :#{#filter.companyType}) AND
                ((:#{#filter.isPartnerCompany} IS NULL) OR 
                    (true=:#{#filter.isPartnerCompany} 
                        AND ((e.companyType='SUPPLIER' AND ap.id IS NOT NULL) OR (e.companyType='EXPERT' AND ap.id IS NOT NULL))
                    ) OR 
                    (false=:#{#filter.isPartnerCompany} 
                        AND ((e.companyType='SUPPLIER' AND ap.id IS NULL) OR (e.companyType='EXPERT'))
                    )
                )
            )
            GROUP BY e.id
            
        """
    )
    fun searchByCriteria(
        @Param("partnerId") partnerId: Long?,
        @Param("filter")
        filter: PartnerExpertSearchFilter,
        pageable: Pageable
    ): Page<ExpertCompanyProfileEntity>

    @Query(
        value = """
            SELECT e.status , COUNT(DISTINCT e.id) FROM expert_company_profile e LEFT JOIN e.associatedPartners ap
            LEFT JOIN e.users eu ON eu.expertType='PRIMARY'
            WHERE(
            (:#{#filter.search} IS NULL OR e.name LIKE :#{#filter.search}) AND
            (:#{#filter.status} IS NULL OR e.status = :#{#filter.status}) AND
			(:#{#filter.country} IS NULL OR eu.countryCode = :#{#filter.country}) AND
            (:#{#partnerId} IS NULL OR ap.id = :#{#partnerId}) AND
            (:#{#filter.companyType} IS NULL OR e.companyType = :#{#filter.companyType}) AND
                ((:#{#filter.isPartnerCompany} IS NULL) OR 
                    (true=:#{#filter.isPartnerCompany} 
                        AND ((e.companyType='SUPPLIER' AND ap.id IS NOT NULL) OR (e.companyType='EXPERT' AND ap.id IS NOT NULL))
                    ) OR 
                    (false=:#{#filter.isPartnerCompany} 
                        AND ((e.companyType='SUPPLIER' AND ap.id IS NULL) OR (e.companyType='EXPERT'))
                    )
                )
            )
            GROUP BY e.status 
            
        """
    )
    fun findStatsByCriteria(
        @Param("partnerId") partnerId: Long?,
        @Param("filter")
        filter: PartnerExpertSearchFilter
    ): List<List<Any>>

    fun findByIdAndAssociatedPartnersId(id:Long, partnerId:Long): ExpertCompanyProfileEntity?

//    fun findByIdAndAssociatedPartners_(id:Long,partnerId:Long): ExpertCompanyProfileEntity?

    fun findAllByEffectiveEndDate(effectiveEndDate: LocalDateTime): List<ExpertCompanyProfileEntity>

    fun findAllByEffectiveEndDateAndCompanyType(effectiveEndDate: LocalDateTime, type: ExpertCompanyType): List<ExpertCompanyProfileEntity>

    //fun findByPartnerIdNotNull(): List<ExpertCompanyProfileEntity>

    @Query(
        value = """
            SELECT e FROM expert_company_profile e LEFT JOIN e.associatedPartners ap
            WHERE (e.id = :#{#companyId} and ap.id = :#{#partnerId} ) GROUP BY e.id
        """
    )
    fun searchByCompanyAndPartner(
        @Param("companyId") companyId: Long,
        @Param("partnerId") partnerId: Long
    ): List<ExpertCompanyProfileEntity>

    fun findAllByAssociatedPartnersId(partnerId: Long): List<ReferenceData>
    fun getReferenceByUsersId(userId: Long): ExpertCompanyProfileEntity?
    fun getReferenceByAssociatedPartnersIdAndIdIn(
        partnerId: Long,
        companyIds: List<Long>
    ): List<ExpertCompanyProfileEntity>
    fun getReferenceByIdIn(companyIds: List<Long>): List<ExpertCompanyProfileEntity>
    fun getReferenceByCompanyTypeAndAssociatedPartnersId(
        companyType: ExpertCompanyType,
        partnerId: Long
    ): List<ExpertCompanyProfileEntity>
    fun getReferenceByCompanyType(companyType: ExpertCompanyType): List<ExpertCompanyProfileEntity>

}
