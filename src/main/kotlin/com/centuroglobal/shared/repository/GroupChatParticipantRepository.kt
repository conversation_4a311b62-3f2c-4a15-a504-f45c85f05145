package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.GroupChatEntity
import com.centuroglobal.shared.data.entity.GroupChatParticipantEntity
import com.centuroglobal.shared.data.entity.dto.EntityIdDto
import com.centuroglobal.shared.data.enums.ChatType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface GroupChatParticipantRepository: JpaRepository<GroupChatParticipantEntity, Long> {

    fun findAllByGroupChat(groupChat: GroupChatEntity): List<GroupChatParticipantEntity>?

    fun findAllByGroupChatAndIsActive(groupChat: GroupChatEntity, isActive: Boolean): List<GroupChatParticipantEntity>?

    fun findByGroupChatAndUserId(groupChat: GroupChatEntity, userId: Long): Optional<GroupChatParticipantEntity>

    fun findByGroupChatReferenceIdAndGroupChatChatTypeAndUserIdAndIsActive(
        referenceId: Long,
        type: ChatType,
        userId: Long,
        isActive: Boolean
    ): List<EntityIdDto>

}