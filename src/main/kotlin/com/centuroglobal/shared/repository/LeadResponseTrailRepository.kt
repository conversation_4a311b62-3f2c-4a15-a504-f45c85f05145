package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.LeadResponseTrailEntity
import org.springframework.data.jpa.repository.JpaRepository

interface LeadResponseTrailRepository : JpaRepository<LeadResponseTrailEntity, Long> {

    fun findAllByLeadResponseId(leadResponseId: Long): List<LeadResponseTrailEntity>

    fun countAllByLeadResponse_IdAndRootUserIdNotAndIsSeen(leadResponseId: Long, userId: Long, seen: Boolean): Int

    fun findTopByLeadResponse_IdAndRootUserIdNotAndIsSeenOrderByCreatedDate(leadResponseId: Long, userId: Long, seen: Boolean): LeadResponseTrailEntity
}