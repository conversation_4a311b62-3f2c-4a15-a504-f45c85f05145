package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.PartnerEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.data.pojo.ClientUser
import com.centuroglobal.shared.data.pojo.PartnerUserSearchFilter
import com.centuroglobal.shared.data.pojo.playbook.PlaybookShareUserReferenceData
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.*

@Repository
interface LoginAccountRepository : JpaRepository<LoginAccountEntity, Long> {
    fun findByEmail(email: String): LoginAccountEntity?

    fun findByStatus(status: AccountStatus): List<LoginAccountEntity>
    @Query(value = """
        SELECT 
            la.id AS id,
            la.email AS email,
            concat(la.first_name, ' ', la.last_name) AS name,
            la.user_type AS userType
        FROM login_account la
        WHERE la.status IN (:#{#status})
    """, nativeQuery = true)
    fun findByStatusIn(status: List<String>): List<ClientUser>

    @Query(value = """
        SELECT
            la.id AS id,
            la.email AS email,
            concat(la.first_name, ' ', la.last_name) AS name,
            la.user_type AS userType
        FROM login_account la
        WHERE
            la.status = (:#{#status}) AND
            la.role IN (:#{#roles})
    """, nativeQuery = true)
    fun findByStatusAndRoleIn(status: String, roles: List<String>): List<ClientUser>

    fun findByEmailAndStatusIn(email: String, status: List<AccountStatus>): LoginAccountEntity?

    fun findByRefreshToken(refreshToken: String): LoginAccountEntity?

    fun findAllByIdIn(userIds: List<Long>): List<LoginAccountEntity>

    fun findByIdAndStatus(userId: Long, status: AccountStatus): LoginAccountEntity?

    fun findAllByIdInAndStatus(userIds: List<Long>, status: AccountStatus): List<LoginAccountEntity>

    fun findAllByRoleIn(roles: List<String>): List<LoginAccountEntity>?

    fun countByReferredBy(userId: Long): Long
    fun findAllBySubscriptionTypeAndRoleAndCreatedDateBetween(
        paid: SubscriptionType,
        role: Role,
        start: LocalDateTime,
        end: LocalDateTime
    ): List<LoginAccountEntity>

    @Query("""
        SELECT COUNT(SUBSTRING_INDEX(la.email, '@', -1)) AS domain_count FROM login_account la 
            WHERE SUBSTRING_INDEX(la.email, '@', -1) = SUBSTRING_INDEX(:#{#email}, '@', -1) AND la.role=:#{#role}
    """)
    fun countEmailDomain(email: String, role: Role): Long

    fun findByPartnerId(partnerId: Long): List<LoginAccountEntity>

    @Query(
        value = """
            SELECT pu FROM login_account pu LEFT JOIN pu.partner.corporates c
            WHERE(
            (:#{#filter.search} IS NULL OR (pu.firstName LIKE :#{#filter.search} OR pu.lastName LIKE :#{#filter.search}
            OR pu.email LIKE :#{#filter.search})) AND
            (:#{#filter.country} IS NULL OR pu.countryCode = :#{#filter.country}) AND
            (:#{#filter.corporate} IS NULL OR c.id = :#{#filter.corporate}) AND
            (:#{#filter.status} IS NULL OR pu.status = :#{#filter.status}) AND
            (pu.partner.id=:#{#partnerId})
            )
            GROUP BY pu.id
            
        """
    )
    fun searchByCriteria(
        @Param("partnerId") partnerId: Long?,
        @Param("filter") filter: PartnerUserSearchFilter,
        pageable: Pageable
    ): Page<LoginAccountEntity>

    @Query(
        value = """
            SELECT pu FROM login_account pu LEFT JOIN pu.partner.corporates c
            WHERE(
            (:#{#filter.search} IS NULL OR (pu.firstName LIKE :#{#filter.search} OR pu.lastName LIKE :#{#filter.search}
            OR pu.email LIKE :#{#filter.search})) AND
            (:#{#filter.country} IS NULL OR pu.countryCode = :#{#filter.country}) AND
            (:#{#filter.corporate} IS NULL OR c.id = :#{#filter.corporate}) AND
            (:#{#filter.status} IS NULL OR pu.status = :#{#filter.status}) AND
            (:#{#partnerId} IS NULL OR pu.partner.id = :#{#partnerId}) AND
            (pu.partner.id IS NOT NULL)
            )
            GROUP BY pu.id
            
        """
    )
    fun searchPartnerUsersByCriteria(
        @Param("filter") filter: PartnerUserSearchFilter,
        partnerId: Long?,
        pageable: Pageable
    ): Page<LoginAccountEntity>

    fun findByPartnerAndId(partnerEntity: PartnerEntity, userId: Long): Optional<LoginAccountEntity>

    fun findByLoginToken(loginToken: String): LoginAccountEntity?
    fun findByIdAndPartnerId(id: Long, partnerId: Long): LoginAccountEntity?

    fun findPlaybookUsersByPartnerIdAndStatusIn(partnerId: Long, status: List<AccountStatus>): List<PlaybookShareUserReferenceData>

    @Query("""
        SELECT CONCAT(la.firstName, ' ', la.lastName) FROM login_account la WHERE la.id = :#{#id}
    """)
    fun getFirstNameLastNameById(id: Long): String


}