package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.MilestonesEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface MilestonesRepository : JpaRepository<MilestonesEntity, Long> {
    fun findByStatus(status: String): MilestonesEntity?
    fun findBySequence(sequence: Long): MilestonesEntity
    fun findFirstByMilestoneKey(milestoneKey: String): MilestonesEntity

    fun findFirstByMilestoneKeyAndCaseSubCategory(milestoneKey: String, caseSubCategory: String): MilestonesEntity
    fun findByCaseSubCategoryAndStatus(caseSubCategory: String, status: String): MilestonesEntity?
    fun findBySequenceAndCaseSubCategory(i: Long, subCategoryId: String): List<MilestonesEntity>
    fun findByCaseSubCategoryOrderBySequence(subCategoryId: String): List<MilestonesEntity>

    @Query("SELECT DISTINCT m.milestoneKey FROM milestones m where m.caseSubCategory = :#{#subCategoryId}")
    fun findDistinctByCaseSubCategoryOrderBySequence(subCategoryId: String): List<MilestonesEntity>

    @Query("""
        SELECT DISTINCT (m.milestoneKey) AS key, m.milestone AS value FROM milestones m
    """)
    fun findAllDistinctMilestones(): List<Map<String, String>>
    fun findAllByMilestoneKeyIn(workflowMilestones: List<String>): List<MilestonesEntity>
}