package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.PartnerEntity
import com.centuroglobal.shared.data.pojo.ClientUser
import com.centuroglobal.shared.data.pojo.PartnerSearchFilter
import com.centuroglobal.shared.data.pojo.ReferenceData
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface PartnerRepository:JpaRepository<PartnerEntity,Long> {


    @Query(
        value = """
            SELECT p FROM partner p LEFT JOIN p.corporates c LEFT JOIN p.associatedCompanies ec
            LEFT JOIN p.partnerUsers pu ON pu.id=p.rootUserId
            WHERE(
            (:#{#filter.search} IS NULL OR (p.name LIKE :#{#filter.search} OR pu.email LIKE :#{#filter.search} OR pu.firstName LIKE :#{#filter.search} OR pu.lastName LIKE :#{#filter.search})) AND
            (:#{#filter.country} IS NULL OR p.country = :#{#filter.country}) AND
            (:#{#filter.expertCompany} IS NULL OR ec.id = :#{#filter.expertCompany}) AND
            (:#{#filter.corporate} IS NULL OR c.id = :#{#filter.corporate}) AND
            (:#{#filter.status} IS NULL OR p.status = :#{#filter.status})
            )
            GROUP BY p.id
            
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: PartnerSearchFilter,
        pageable: Pageable
    ): Page<PartnerEntity>


    fun findByPartnerUsersId(partnerUserId: Long): PartnerEntity?

    @Query(value = """
        
        SELECT 
            la.id AS id,
            la.email AS email,
            CONCAT(la.first_name, ' ', la.last_name) AS name,
            la.user_type AS userType 
        FROM 
            login_account la JOIN
            corporate_user cu ON la.id = cu.id JOIN
            corporate c ON c.id = cu.corporate_id 
        WHERE c.partner_id = :#{#partnerId}
        
        UNION ALL
        
        SELECT 
            la.id AS id,
            la.email AS email,
            CONCAT(la.first_name, ' ', la.last_name) AS name,
            la.user_type AS userType 
        FROM 
            login_account la JOIN
            expert_user eu ON la.id = eu.id JOIN
            partner_experts pe ON pe.ecp_id = eu.company_profile_id 
        WHERE pe.partner_id = :#{#partnerId}
        
    """, nativeQuery = true)
    fun findPartnerAssociatedUsers(partnerId: Long): List<ClientUser>

    fun findAllByIdGreaterThanEqual(int: Long): List<ReferenceData>

    @Query(value = """
        SELECT 
            la.id AS id,
            la.email AS email,
            CONCAT(la.first_name, ' ', la.last_name) AS name,
            la.user_type AS userType 
        FROM 
            login_account la
        WHERE la.partner_id = :#{#partnerId}
        """, nativeQuery = true)
    fun findPartnerUsers(partnerId: Long): List<ClientUser>


}