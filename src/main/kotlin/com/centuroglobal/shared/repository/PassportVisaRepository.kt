package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.ClientDocFileEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.PassportVisaEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface PassportVisaRepository : JpaRepository<PassportVisaEntity, Long> {

    @Query(
        value = """
            SELECT pv FROM passport_visa pv
            WHERE
            pv.user.id = :#{#userId}
            order by pv.docType
        """
    )
    fun searchByUserId(
        @Param("userId") userId: Long
    ): List<PassportVisaEntity>

    @Query(
        value = """
            SELECT pv FROM passport_visa pv
            WHERE
            pv.user.id = :#{#userId} and pv.id = :#{#docId} and pv.docType = :#{#docType}
        """
    )
    fun retrievePassportVisaByDocType(
        @Param("userId") userId: Long, @Param("docId") docId: Long, @Param("docType") docType: String
    ): PassportVisaEntity?


    @Query(
        value = """
            SELECT pv FROM passport_visa pv
            WHERE
            pv.user.id = :#{#userId} and pv.id = :#{#docId}
        """
    )
    fun retrievePassportVisa(
        @Param("userId") userId: Long, @Param("docId") docId: Long
    ): PassportVisaEntity?


    @Query(value = """
       SELECT COUNT(*) FROM client_doc_file cdf
       WHERE cdf.clientDoc.id=:#{#clientDocId}
    """)
    fun getCountDocFiles(clientDocId: Long): Int?

    @Query(value = """
       SELECT cdf FROM client_doc_file cdf
       WHERE cdf.clientDoc.id=:#{#clientDocId}
    """)
    fun getClientDocFiles(clientDocId: Long): List<ClientDocFileEntity>?

    fun getDocByUserAndDocType(loginAccountEntity: LoginAccountEntity, docType: String): List<PassportVisaEntity>

}