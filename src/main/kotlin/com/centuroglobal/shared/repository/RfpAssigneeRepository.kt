package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.RfpAssigneeEntity
import com.centuroglobal.shared.data.entity.RfpEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface RfpAssigneeRepository : JpaRepository<RfpAssigneeEntity, Long> {
    fun findAllByRfp(rfpEntity: RfpEntity): MutableList<RfpAssigneeEntity>
    fun deleteByUserIn(users: MutableList<LoginAccountEntity>)
}