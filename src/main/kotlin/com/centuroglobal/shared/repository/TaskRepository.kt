package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.TaskEntity
import com.centuroglobal.shared.data.enums.task.TaskCompanyType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.AbstractSearchFilter
import com.centuroglobal.shared.data.pojo.task.dto.ITaskCountDto
import com.centuroglobal.shared.data.pojo.task.dto.TaskSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDate
import java.util.*

@Repository
interface TaskRepository : BaseRepository<TaskEntity, Long> {


    @Query("""
        SELECT t FROM task t JOIN t.assignee ta
        WHERE(
        (:#{#searchFilter.name} IS NULL OR t.name LIKE :#{#searchFilter.name}) AND
        (:#{#searchFilter.status} IS NULL OR t.status IN :#{#searchFilter.status}) AND
        (:#{#searchFilter.createdBy} IS NULL OR t.createdBy = :#{#searchFilter.createdBy}) AND
        (:#{#searchFilter.type} IS NULL OR t.referenceType IN :#{#searchFilter.type}) AND
        (:#{#searchFilter.priority} IS NULL OR :#{#searchFilter.priority} = '' OR t.priority = 'YES') AND
        (:#{#searchFilter.referenceId} IS NULL OR t.referenceId IN :#{#searchFilter.referenceId}) AND
        (
        (:#{#searchFilter.taskView} IS NULL) OR
        (:#{#searchFilter.taskView} = 'ALL_TASK' AND (ta.assigneeId.id = :#{#searchFilter.currentUser} OR ((t.companyId = :#{#searchFilter.companyId} AND t.companyType = :#{#searchFilter.companyType}) AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})))) OR
        (:#{#searchFilter.taskView} = 'MY_TASK' AND (ta.assigneeId.id = :#{#searchFilter.currentUser})) OR
        (:#{#searchFilter.taskView} = 'TEAM_TASK' AND ta.type IN ('OWN', 'TEAM') AND ta.assigneeId.id != :#{#searchFilter.currentUser} AND t.companyId = :#{#searchFilter.companyId} AND t.companyType = :#{#searchFilter.companyType} AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})) OR
        (:#{#searchFilter.taskView} = 'EXPERT' AND ta.type = 'EXPERT' AND t.companyId = :#{#searchFilter.companyId} AND t.companyType = :#{#searchFilter.companyType} AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})) OR
        (:#{#searchFilter.taskView} = 'CORPORATE' AND ta.type = 'CORPORATE' AND ((:#{#searchFilter.companyType.name} = 'EXPERT' AND ((t.referenceType='CASE' AND t.referenceId IN :#{#searchFilter.expertAssignedCases} AND t.visibility IN :#{#searchFilter.visibility}) OR (t.createdBy = :#{#searchFilter.currentUser} AND t.companyType=:#{#searchFilter.companyType}))) OR (:#{#searchFilter.companyType.name} != 'EXPERT' AND (ta.corporatePartnerId = :#{#searchFilter.currentUserPartnerId}) AND (t.visibility IN :#{#searchFilter.visibility} OR (t.createdBy = :#{#searchFilter.currentUser} AND t.companyType=:#{#searchFilter.companyType})))))
        ) AND
        (:#{#searchFilter.assignedTo} IS NULL OR ta.assigneeId.id = :#{#searchFilter.assignedTo})
        ) AND 
        (
            (:#{#searchFilter.fromDate} IS NULL OR :#{#searchFilter.toDate} IS NULL) OR 
            (
                (COALESCE(t.expectedDueDate, t.dueDate) BETWEEN :#{#searchFilter.fromDate} AND :#{#searchFilter.toDate}) OR
                (:#{#searchFilter.showBy} IS NOT NULL AND :#{#searchFilter.showBy?.name} = 'TODAY' AND (COALESCE(t.expectedDueDate, t.dueDate) < CURRENT_DATE AND t.status != 'COMPLETED'))
            )
        )
        group by t.id
        """
    )
    override fun searchByCriteriaForAdmin(
        @Param("searchFilter") searchFilter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<TaskEntity>


    @Query("""
        SELECT t FROM task t LEFT JOIN t.assignee ta
        WHERE(
        (
        (:#{#searchFilter.taskView} = 'MY_TASK' AND ta.assigneeId.id = :#{#searchFilter.currentUser}) OR
        (:#{#searchFilter.taskView} = 'TEAM_TASK' AND ta.type IN ('OWN', 'TEAM') AND ta.assigneeId.id != :#{#searchFilter.currentUser} AND t.companyId = :#{#searchFilter.companyId} AND t.companyType = :#{#searchFilter.companyType} AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})) OR
        (:#{#searchFilter.taskView} = 'ALL_TASK' AND (ta.assigneeId.id = :#{#searchFilter.currentUser} OR ((t.companyId = :#{#searchFilter.companyId} AND t.companyType = :#{#searchFilter.companyType}) AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})))) 
        ) AND
        (
        (:#{#searchFilter.dueDate} = CURRENT_DATE AND ((COALESCE(t.expectedDueDate, t.dueDate) < CURRENT_DATE AND t.status != 'COMPLETED') OR COALESCE(t.expectedDueDate, t.dueDate) = CURRENT_DATE)) OR
        (:#{#searchFilter.dueDate} = DATE(COALESCE(t.expectedDueDate, t.dueDate)))
        )
        ) group by t.id
        """)
    fun searchByDueDateCriteria(
        @Param("searchFilter") searchFilter: TaskSearchFilter
    ): List<TaskEntity>


    @Query(
        value = "select DATE(COALESCE(tc.expected_due_date, tc.due_date)) as dt, tc.status as status, " +
                "count(distinct tc.id) as count from task as tc left join task_assignee as ta on tc. id = ta.task_id " +
                "WHERE EXTRACT(MONTH FROM DATE(COALESCE(tc.expected_due_date, tc.due_date))) = :#{#filter.month} AND " +
                "EXTRACT(YEAR FROM DATE(COALESCE(tc.expected_due_date, tc.due_date))) = :#{#filter.year} AND " +
                "((:#{#filter.taskView} = 'ALL_TASK' AND (ta.assignee_id = :#{#filter.currentUser} OR ((tc.company_id = :#{#filter.companyId} AND tc.company_type = :#{#filter.companyType.name}) AND (tc.created_by = :#{#filter.currentUser} OR tc.visibility = :#{#filter.visibilityString})))) OR " +
                "(:#{#filter.taskView} = 'MY_TASK' AND (ta.assignee_id = :#{#filter.currentUser})) OR " +
                "(:#{#filter.taskView} = 'TEAM_TASK' AND ta.type IN ('OWN', 'TEAM') AND ta.assignee_id <> :#{#filter.currentUser} AND tc.company_id = :#{#filter.companyId} AND tc.company_type = :#{#filter.companyType.name} AND (tc.created_by = :#{#filter.currentUser} OR tc.visibility = :#{#filter.visibilityString}))) " +
                "GROUP BY COALESCE(tc.expected_due_date, tc.due_date),tc.status"
        , nativeQuery = true
    )
    fun getStatusCount(@Param("filter") filter: TaskSearchFilter): List<ITaskCountDto>

    @Query(
        value = "select DATE(COALESCE(tc.expected_due_date, tc.due_date)) as dt, tc.status as status, " +
                "count(distinct tc.id) as count from task as tc left join task_assignee as ta on tc. id = ta.task_id " +
                "WHERE EXTRACT(MONTH FROM DATE(COALESCE(tc.expected_due_date, tc.due_date))) = :#{#filter.month} AND " +
                "EXTRACT(YEAR FROM DATE(COALESCE(tc.expected_due_date, tc.due_date))) = :#{#filter.year} AND " +
                "((:#{#filter.taskView} = 'ALL_TASK' AND (ta.assignee_id = :#{#filter.currentUser} OR (((COALESCE(:#{#filter.reportees}) IS NULL AND ta.company_id = :#{#filter.companyId} AND ta.type = 'CORPORATE') OR (COALESCE(:#{#filter.reportees}) IS NOT NULL AND ta.assignee_id IN :#{#filter.reportees})) AND (tc.created_by = :#{#filter.currentUser} OR tc.visibility = :#{#filter.visibilityString})))) OR " +
                "(:#{#filter.taskView} = 'MY_TASK' AND (ta.assignee_id = :#{#filter.currentUser})) OR " +
                "(:#{#filter.taskView} = 'TEAM_TASK' AND ta.company_id = :#{#filter.companyId} AND ta.type = 'CORPORATE' AND (((COALESCE(:#{#filter.reportees}) IS NULL AND ta.company_id = :#{#filter.companyId} AND ta.type = 'CORPORATE') OR (COALESCE(:#{#filter.reportees}) IS NOT NULL AND ta.assignee_id IN :#{#filter.reportees})) AND ta.assignee_id != :#{#filter.currentUser} AND tc.visibility = :#{#filter.visibilityString}))) " +
                "GROUP BY COALESCE(tc.expected_due_date, tc.due_date),tc.status"
        , nativeQuery = true
    )
    fun getStatusCountForCorporate(@Param("filter") filter: TaskSearchFilter): List<ITaskCountDto>

    @Query("""
        SELECT t FROM task t LEFT JOIN t.assignee ta
        WHERE(
        (
        (:#{#searchFilter.taskView} = 'MY_TASK' AND ta.assigneeId.id = :#{#searchFilter.currentUser}) OR
        (:#{#searchFilter.taskView} = 'TEAM_TASK' AND ((COALESCE(:#{#searchFilter.reportees}, NULL) IS NULL AND ta.companyId=:#{#searchFilter.companyId} AND ta.type='CORPORATE') OR (COALESCE(:#{#searchFilter.reportees}, NULL) IS NOT NULL AND ta.assigneeId.id IN :#{#searchFilter.reportees})) AND ta.assigneeId.id != :#{#searchFilter.currentUser} AND t.visibility IN :#{#searchFilter.visibility}) OR
        (:#{#searchFilter.taskView} = 'ALL_TASK' AND (ta.assigneeId.id = :#{#searchFilter.currentUser} OR (((COALESCE(:#{#searchFilter.reportees}, NULL) IS NULL AND ta.companyId=:#{#searchFilter.companyId} AND ta.type='CORPORATE') OR (COALESCE(:#{#searchFilter.reportees}, NULL) IS NOT NULL AND ta.assigneeId.id IN :#{#searchFilter.reportees})) AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})))) 
        ) AND
        (
        (:#{#searchFilter.dueDate} = CURRENT_DATE AND ((COALESCE(t.expectedDueDate, t.dueDate) < CURRENT_DATE AND t.status != 'COMPLETED') OR COALESCE(t.expectedDueDate, t.dueDate) = CURRENT_DATE)) OR
        (:#{#searchFilter.dueDate} = DATE(COALESCE(t.expectedDueDate, t.dueDate)))
        )
        ) group by t.id
        """)
    fun searchByDueDateCriteriaForCorporate(
        @Param("searchFilter") searchFilter: TaskSearchFilter
    ): List<TaskEntity>

    @Query(value = """
        SELECT t FROM task t JOIN t.assignee ta
        WHERE(
        (:#{#searchFilter.name} IS NULL OR t.name LIKE :#{#searchFilter.name}) AND
        (:#{#searchFilter.status} IS NULL OR t.status IN :#{#searchFilter.status}) AND
        (:#{#searchFilter.createdBy} IS NULL OR t.createdBy = :#{#searchFilter.createdBy}) AND
        (:#{#searchFilter.type} IS NULL OR t.referenceType IN :#{#searchFilter.type}) AND
        (:#{#searchFilter.priority} IS NULL OR :#{#searchFilter.priority} = '' OR t.priority = 'YES') AND
        (:#{#searchFilter.referenceId} IS NULL OR t.referenceId IN :#{#searchFilter.referenceId}) AND
        (
        ((:#{#searchFilter.taskView} IS NULL OR :#{#searchFilter.taskView} = 'ALL_TASK') AND (ta.assigneeId.id = :#{#searchFilter.currentUser} OR ((ta.companyId = :#{#searchFilter.companyId} AND ta.type = 'CORPORATE') AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})))) OR
        (:#{#searchFilter.taskView} = 'MY_TASK' AND (ta.assigneeId.id = :#{#searchFilter.currentUser})) OR
        (:#{#searchFilter.taskView} = 'TEAM_TASK' AND ta.companyId = :#{#searchFilter.companyId} AND ta.type = 'CORPORATE' AND ta.assigneeId.id != :#{#searchFilter.currentUser} AND t.visibility IN :#{#searchFilter.visibility}) OR
        (:#{#searchFilter.taskView} = 'CORPORATE' AND ta.type = 'CORPORATE' AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility}))
        ) AND
        (:#{#searchFilter.assignedTo} IS NULL OR ta.assigneeId.id = :#{#searchFilter.assignedTo}) AND 
        (:#{#searchFilter.accountUsers} IS NULL OR ta.assigneeId.id IN :#{#searchFilter.accountUsers}) AND
        (
        (:#{#searchFilter.dueDate} IS NULL OR :#{#searchFilter.dueDate} = CURRENT_DATE AND ((COALESCE(t.expectedDueDate, t.dueDate) < CURRENT_DATE AND t.status != 'COMPLETED') OR COALESCE(t.expectedDueDate, t.dueDate) = CURRENT_DATE)) OR
        (:#{#searchFilter.dueDate} IS NULL OR :#{#searchFilter.dueDate} = DATE(COALESCE(t.expectedDueDate, t.dueDate)))
        )
        )
        group by t.id
    """)
    override fun searchByCriteriaForFullAccess(
        @Param("searchFilter") filter: AbstractSearchFilter,
        pageable: Pageable
    ): Page<TaskEntity>

    @Query(value = """
        SELECT t FROM task t JOIN t.assignee ta
        WHERE(
        (:#{#searchFilter.name} IS NULL OR t.name LIKE :#{#searchFilter.name}) AND
        (:#{#searchFilter.status} IS NULL OR t.status IN :#{#searchFilter.status}) AND
        (:#{#searchFilter.createdBy} IS NULL OR t.createdBy = :#{#searchFilter.createdBy}) AND
        (:#{#searchFilter.type} IS NULL OR t.referenceType IN :#{#searchFilter.type}) AND
        (:#{#searchFilter.priority} IS NULL OR :#{#searchFilter.priority} = '' OR t.priority = 'YES') AND
        (:#{#searchFilter.referenceId} IS NULL OR t.referenceId IN :#{#searchFilter.referenceId}) AND
        (
        ((:#{#searchFilter.taskView} IS NULL OR :#{#searchFilter.taskView} = 'ALL_TASK') AND (ta.assigneeId.id IN :#{#userIds} OR ((t.createdBy IN :#{#userIds}) AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility})))) OR
        (:#{#searchFilter.taskView} = 'MY_TASK' AND (ta.assigneeId.id = :#{#searchFilter.currentUser})) OR
        (:#{#searchFilter.taskView} = 'TEAM_TASK' AND ta.assigneeId.id IN :#{#userIds} AND ta.assigneeId.id != :#{#searchFilter.currentUser} AND t.visibility IN :#{#searchFilter.visibility}) OR
        (:#{#searchFilter.taskView} = 'CORPORATE' AND ta.type = 'CORPORATE' AND (t.createdBy = :#{#searchFilter.currentUser} OR t.visibility IN :#{#searchFilter.visibility}))
        ) AND
        (:#{#searchFilter.assignedTo} IS NULL OR ta.assigneeId.id = :#{#searchFilter.assignedTo})
        ) AND 
        (:#{#searchFilter.accountUsers} IS NULL OR ta.assigneeId.id IN :#{#searchFilter.accountUsers}) AND
        ((:#{#searchFilter.fromDate} IS NULL OR :#{#searchFilter.toDate} IS NULL) OR (COALESCE(t.expectedDueDate, t.dueDate) BETWEEN :#{#searchFilter.fromDate} AND :#{#searchFilter.toDate}))
        group by t.id
    """)
    override fun searchByCriteria(
        @Param("searchFilter") filter: AbstractSearchFilter,
        @Param("userIds") userIds: List<Long>,
        pageable: Pageable
    ): Page<TaskEntity>


    /*
    * Methods overriden after this are not being used in Task flow
    * */


    @Query(value = """
        select t from task t
    """)
    override fun findByIdAndCreatedByIdIn(queryId: Long, reportees: MutableList<Long>): Optional<TaskEntity>

    @Query(value = """
        select t from task t
    """)
    override fun findByIdAndCreatedByCorporateId(queryId: Long, corporateId: Long): Optional<TaskEntity>

    @Query("""
        SELECT t FROM task t JOIN t.reminders tr JOIN t.assignee ta
        WHERE :#{#currentDate} = DATE(tr.dateTime)
        group by t.id
        """)
    fun searchForDueDateReminder(
        @Param("currentDate") currentDate: LocalDate
    ): List<TaskEntity>

    @Query(value = """
        SELECT t FROM task t JOIN t.assignee ta
        where t.id=:#{#id} AND ta.assigneeId.id=:#{#userId} AND :#{#companyProfileId} IS NOT NULL

    """)
    override fun findByIdForExpert(id: Long, userId: Long, companyProfileId: Long): Optional<TaskEntity>
    @Query(value = """
        SELECT t FROM task t LEFT JOIN t.assignee ta
        WHERE t.id=:#{#id} AND (ta.assigneeId.id=:#{#userId}
        OR t.createdBy = :#{#userId}
        OR (((t.companyId = :#{#companyId} AND t.companyType = :#{#companyType})
        OR (:#{#companyType.name}='EXPERT' AND :#{#referenceIds} IS NOT NULL AND t.referenceId IN :#{#referenceIds})
        OR (ta.companyId = :#{#companyId} AND t.companyType = :#{#companyType})
        OR (ta.corporatePartnerId= :#{#companyPartnerId})) AND (t.visibility = :#{#visibility})))
        GROUP BY t.id
    """)
    fun getTask(
        id: Long,
        userId: Long,
        companyId: Long,
        companyType: TaskCompanyType,
        visibility: TaskVisibility,
        referenceIds: List<Long>?,
        companyPartnerId: Long?
    ): Optional<TaskEntity>

    @Query(value = """
       SELECT t FROM task t LEFT JOIN t.assignee ta
       WHERE t.id=:#{#id} AND
       (ta.assigneeId.id = :#{#userId} OR 
       (((:#{#reportees} IS NULL AND ta.companyId=:#{#companyId} AND ta.type='CORPORATE') 
       OR (:#{#reportees} IS NOT NULL AND ta.assigneeId.id IN :#{#reportees})) 
       AND (t.createdBy = :#{#userId} OR t.visibility = 'PUBLIC')))
   
       GROUP BY t.id

    """)
    fun getTaskForCorporate(id: Long, companyId: Long, userId: Long,  reportees: List<Long>?): TaskEntity?

    @Query(value = """
       SELECT t FROM task t LEFT JOIN t.assignee ta
       WHERE 
       t.referenceId=:#{#id} AND
       t.referenceType IN :#{#types} AND
       (:#{#statuses} IS NULL OR t.status IN :#{#statuses})
       
       GROUP BY t.id

    """)
    fun getTasksByReferenceIdAndType(id: Long, types: List<String>, statuses: List<TaskStatus>?, pageRequest: Pageable): Page<TaskEntity>

}