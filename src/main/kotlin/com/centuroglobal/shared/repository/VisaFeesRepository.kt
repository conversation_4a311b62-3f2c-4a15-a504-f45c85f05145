package com.centuroglobal.shared.repository

import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.VisaFeesEntity
import com.centuroglobal.shared.data.entity.dto.EntityIdDto
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.pojo.PartnerCorporateSearchFilter
import com.centuroglobal.shared.data.pojo.ReferenceData
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface VisaFeesRepository : JpaRepository<VisaFeesEntity, Long> {
}
