package com.centuroglobal.shared.repository.auth

import com.centuroglobal.shared.data.entity.auth.MfaTokenEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface MfaTokenRepository : JpaRepository<MfaTokenEntity, Long> {

    fun findByUserIdAndCodeAndIsValidated(userId: Long, code: String, isValidated: Boolean): MfaTokenEntity?
    fun findByUserIdAndIsValidated(userId: Long, isValidated: Boolean): List<MfaTokenEntity>
}