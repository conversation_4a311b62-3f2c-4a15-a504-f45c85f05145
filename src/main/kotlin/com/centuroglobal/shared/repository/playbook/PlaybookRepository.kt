package com.centuroglobal.shared.repository.playbook

import com.centuroglobal.shared.data.entity.playbook.PlaybookEntity
import com.centuroglobal.shared.data.pojo.playbook.PlaybookSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface PlaybookRepository : JpaRepository<PlaybookEntity, Long> {

    @Query("""
        select p from playbook p LEFT JOIN p.sharedWith s
        where
            (:#{#filter.country} IS NULL OR p.country = :#{#filter.country}) AND
            (((:#{#filter.type} = 'OWN' AND p.createdBy = :#{#filter.userId})) OR ((:#{#filter.type} = 'SHARED' AND s.id = :#{#filter.userId}))) AND
            (:#{#filter.from} IS NULL  OR (p.lastUpdatedDate) BETWEEN :#{#filter.from} AND :#{#filter.to})
    """)
    fun searchByCriteria(filter: PlaybookSearchFilter, pageable: Pageable): Page<PlaybookEntity>
}