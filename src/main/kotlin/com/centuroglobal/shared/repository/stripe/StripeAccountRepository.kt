package com.centuroglobal.shared.repository.stripe

import com.centuroglobal.shared.data.entity.stripe.StripeAccountEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface StripeAccountRepository : JpaRepository<StripeAccountEntity, Long> {
    fun findByUserId(userId: Long): StripeAccountEntity?
    fun findByCustomerId(customerId: String): StripeAccountEntity?
}
