package com.centuroglobal.shared.repository.stripe

import com.centuroglobal.shared.data.entity.stripe.StripeEventEntity
import com.centuroglobal.shared.data.enums.stripe.StripeEventStatus
import com.centuroglobal.shared.data.enums.stripe.StripeEventType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface StripeEventRepository : JpaRepository<StripeEventEntity, Long> {

    fun findAllByStatusAndTypeIn(
        status: StripeEventStatus,
        type: List<StripeEventType>,
        page: Pageable
    ): Page<StripeEventEntity>

    fun findByEventId(eventId: String): StripeEventEntity

    @Query(
        value = """
            INSERT INTO `stripe_event`
            (`created_date`, `last_updated_date`, `api_version`, `customer_id`,`event_id`, `response`, `status`, `stripe_logged_date`, `type`) 
            VALUES
            (now(), now(), ?6, ?1, ?2, ?3, ?4, ?7, ?5)
            ON DUPLICATE KEY UPDATE `id`=`id` 
        """,
        nativeQuery = true
    )
    fun insertOnDuplicateIgnore(
        customerId: String,
        eventId: String,
        response: String,
        status: String,
        type: String,
        apiVersion: String?,
        stripeLoggedDate: LocalDateTime?
    )
}