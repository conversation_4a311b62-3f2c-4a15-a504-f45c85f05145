package com.centuroglobal.shared.repository.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionPlanEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SubscriptionPlanRepository : JpaRepository<SubscriptionPlanEntity, Long> {

    fun deleteAllByCompanyId(companyId: Long)
    fun findAllByCompanyId(companyId: Long): List<SubscriptionPlanEntity>

    @Query("""
        select s from subscription_plan s
            where
            s.companyId = :#{#companyId} AND
            (:#{#currency} IS NULL OR s.currency = :#{#currency})
            order by s.lastUpdatedDate desc
    """)
    fun searchByCriteria(companyId: Long, currency: String?): List<SubscriptionPlanEntity>
    fun findByCompanyIdAndIsActive(corporateId: Long, isActive: Boolean): SubscriptionPlanEntity?

    fun deleteByCompanyIdAndIsActive(corporateId: Long, isActive: Boolean)
}