package com.centuroglobal.shared.repository.subscription

import com.centuroglobal.shared.data.entity.subscription.SubscriptionUsageEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface SubscriptionUsageRepository : JpaRepository<SubscriptionUsageEntity, Long> {

    fun findAllByCompanyId(corporateId: Long): List<SubscriptionUsageEntity>
    fun findByCompanyIdAndDateBetween(corporateId: Long, from: LocalDateTime, to: LocalDateTime): SubscriptionUsageEntity?

}