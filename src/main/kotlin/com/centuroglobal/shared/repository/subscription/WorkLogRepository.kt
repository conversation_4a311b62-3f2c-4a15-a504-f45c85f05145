package com.centuroglobal.shared.repository.subscription

import com.centuroglobal.shared.data.entity.subscription.WorkLogEntity
import com.centuroglobal.shared.data.pojo.subscription.WorkLogSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface WorkLogRepository : JpaRepository<WorkLogEntity, Long> {

    @Query("""
        SELECT w FROM work_log w
        WHERE(
        (:#{#filter.corporateId} IS NULL OR w.corporate.id = :#{#filter.corporateId}) AND
        (:#{#filter.referenceId} IS NULL OR w.referenceId = :#{#filter.referenceId}) AND
        (:#{#filter.referenceType} IS NULL OR w.referenceType = :#{#filter.referenceType}) AND
        (:#{#filter.loggedBy} IS NULL OR w.createdBy = :#{#filter.loggedBy}) AND
        (:#{#filter.from} IS NULL  OR (DATE(w.createdDate) BETWEEN :#{#filter.from} AND :#{#filter.to}))
       
        )
        group by w.id
    """)
    fun searchByCriteria(filter: WorkLogSearchFilter, pageable: Pageable): Page<WorkLogEntity>
    @Query("""
        SELECT sum(w.timeSpent) FROM work_log w 
        WHERE w.corporate.id = :#{#corporateId}
    """)
    fun totalTimeSpentByCorporate(corporateId: Long): Long

}