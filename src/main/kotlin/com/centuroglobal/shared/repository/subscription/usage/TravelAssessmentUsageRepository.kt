package com.centuroglobal.shared.repository.subscription.usage

import com.centuroglobal.shared.data.entity.subscription.usage.TravelAssessmentUsageEntity
import com.centuroglobal.shared.data.entity.subscription.usage.VisaAssessmentUsageEntity
import org.springframework.stereotype.Repository

@Repository
interface TravelAssessmentUsageRepository : UsageRepository {
    override fun findAllBySubscriptionUsageDetailsId(id: Long): List<TravelAssessmentUsageEntity>
}