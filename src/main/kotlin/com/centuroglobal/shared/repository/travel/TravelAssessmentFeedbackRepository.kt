package com.centuroglobal.shared.repository.travel

import com.centuroglobal.shared.data.entity.travel.TravelAssessmentFeedbackEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TravelAssessmentFeedbackRepository: JpaRepository<TravelAssessmentFeedbackEntity, Long> {
    fun findBySourceAndDestination(source: String, destination: String): TravelAssessmentFeedbackEntity?

}
