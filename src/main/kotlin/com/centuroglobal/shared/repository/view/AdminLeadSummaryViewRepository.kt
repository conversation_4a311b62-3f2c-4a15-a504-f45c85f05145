package com.centuroglobal.shared.repository.view

import com.centuroglobal.shared.data.entity.view.AdminLeadSummaryView
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.pojo.lead.LeadSearchFilter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface AdminLeadSummaryViewRepository : JpaRepository<AdminLeadSummaryView, String> {

    @Query(
        value = """
            SELECT vw FROM  AdminLeadSummaryView vw
            WHERE
            (:#{#filter.search} IS NULL OR vw.createdByName LIKE :#{#filter.search} OR vw.companyName LIKE :#{#filter.search} OR vw.title LIKE :#{#filter.search}) AND
            (:#{#filter.countryCode} IS NULL OR vw.countryCode = :#{#filter.countryCode}) AND
            (:#{#filter.expertiseId} IS NULL OR vw.expertiseId = :#{#filter.expertiseId}) AND
            (:#{#filter.from} IS NULL OR vw.createdDate >= :#{#filter.from}) AND
            (:#{#filter.to} IS NULL OR vw.createdDate <= :#{#filter.to}) AND
            (:#{#filter.status} IS NULL OR vw.status = :#{#filter.status}) AND
            (vw.status IN :#{#status})
            group by vw.id
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: LeadSearchFilter,
        @Param("status")
        status: Array<LeadStatus>,
        pageable: Pageable
    ): Page<AdminLeadSummaryView>
}
