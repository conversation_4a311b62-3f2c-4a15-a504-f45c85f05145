package com.centuroglobal.shared.repository.view

import com.centuroglobal.shared.data.entity.view.CaseView
import com.centuroglobal.shared.data.entity.view.CaseViewForResidenceCountry
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.entity.view.UserView
import com.centuroglobal.shared.data.pojo.case.CaseSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface CaseViewRepository : JpaRepository<CaseView, Long> {

    //Admin filter
    @Query(
        """
            select c from CaseView c 
            where
            (c.archive = :#{#filter.archive})AND
            (:#{#filter.isPriorityCase} IS NULL OR c.isPriorityCase = :#{#filter.isPriorityCase})AND
            (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} ) AND
            (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId} ) AND
            (:#{#filter.search} IS NULL OR (c.initiatedFor LIKE :#{#filter.search} OR c.companyName LIKE :#{#filter.search})) AND
            (:#{#filter.status} IS NULL OR c.status = :#{#filter.status}) AND
            (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
            (:#{#filter.actionFor} IS NULL OR c.actionFor IN (:#{#filter.actionFor})) AND
            (:#{#filter.companyName} IS NULL OR c.companyName LIKE :#{#filter.companyName})AND
            (:#{#filter.accountManagerId} IS NULL OR c.accountManager.id = :#{#filter.accountManagerId}) AND
            (:#{#filter.caseOwner} IS NULL OR c.createdBy.userId = :#{#filter.caseOwner}) AND
            (:#{#filter.minPercent} IS NULL OR c.percentCompletion >= :#{#filter.minPercent}) AND
            (:#{#filter.maxPercent} IS NULL OR c.percentCompletion <= :#{#filter.maxPercent}) AND
            (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId}) AND
            (:#{#clientList} IS NULL OR c.createdBy.userId IN (:#{#clientList})) AND
            (:#{#filter.partnerId} IS NULL OR c.partnerId = :#{#filter.partnerId}) AND
            ((:#{#filter.isPartnerCases} IS NULL OR false=:#{#filter.isPartnerCases} ) OR (true=:#{#filter.isPartnerCases} AND c.partnerId IS NOT NULL)) AND
            ((:#{#filter.cgRequested} IS NULL OR false=:#{#filter.cgRequested}) OR (true=:#{#filter.cgRequested} AND (c.managedBy='CG' OR c.cgRequestedStatus IN ('CG_REQUESTED', 'CG_ACCEPTED'))))
            
            group by c.id
        """
    )
    fun searchByCriteria(
        @Param("filter")
        filter: CaseSearchFilter,
        pageable: Pageable,
        clientList: List<Long>?
    ): Page<CaseView>


    //Admin filter for download
    @Query(
        """
            select c from CaseViewForResidenceCountry c 
            where(
            (c.archive = :#{#filter.archive})AND
            (:#{#filter.isPriorityCase} IS NULL OR c.isPriorityCase = :#{#filter.isPriorityCase})AND
            (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} ) AND
            (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId} ) AND
            (:#{#filter.search} IS NULL OR (c.initiatedFor LIKE :#{#filter.search} OR c.companyName LIKE :#{#filter.search})) AND
            (:#{#filter.status} IS NULL OR c.status = :#{#filter.status}) AND
            (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
            (:#{#filter.actionFor} IS NULL OR c.actionFor IN (:#{#filter.actionFor})) AND
            (:#{#filter.companyName} IS NULL OR c.companyName LIKE :#{#filter.companyName})AND
            (:#{#filter.accountManagerId} IS NULL OR c.accountManager.id = :#{#filter.accountManagerId}) AND
            (:#{#filter.caseOwner} IS NULL OR c.createdBy.userId = :#{#filter.caseOwner}) AND
            (:#{#filter.minPercent} IS NULL OR c.percentCompletion >= :#{#filter.minPercent}) AND
            (:#{#filter.maxPercent} IS NULL OR c.percentCompletion <= :#{#filter.maxPercent}) AND
            (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId}) AND
            (:#{#clientList} IS NULL OR c.createdBy.userId IN (:#{#clientList})) AND
            (:#{#filter.partnerId} IS NULL OR c.partnerId = :#{#filter.partnerId})) AND
            ((:#{#filter.isPartnerCases} IS NULL OR false=:#{#filter.isPartnerCases}) OR (true=:#{#filter.isPartnerCases} AND c.partnerId IS NOT NULL)) AND
            ((:#{#filter.cgRequested} IS NULL OR false=:#{#filter.cgRequested}) OR (true=:#{#filter.cgRequested} AND (c.managedBy='CG' OR c.cgRequestedStatus IN ('CG_REQUESTED', 'CG_ACCEPTED'))))
            group by c.id
        """
    )
    fun searchByCriteriaForResidenceCountry(
        @Param("filter")
        filter: CaseSearchFilter,
        clientList: List<Long>?
    ): List<CaseViewForResidenceCountry>

    //Corporate User - Download
    @Query(
        """
            select c from CaseViewForResidenceCountry c 
            where
            (c.archive = :#{#filter.archive})AND
            (:#{#filter.isPriorityCase} IS NULL OR c.isPriorityCase = :#{#filter.isPriorityCase})AND
            (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} ) AND
            (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId} ) AND
            (:#{#filter.search} IS NULL OR (c.initiatedFor LIKE :#{#filter.search} OR c.companyName LIKE :#{#filter.search})) AND
            (:#{#filter.status} IS NULL OR c.status = :#{#filter.status}) AND
            c.createdBy.userId IN (:#{#createdByUserIds}) AND
            (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
            (:#{#filter.actionFor} IS NULL OR c.actionFor IN (:#{#filter.actionFor})) AND
            (:#{#filter.companyName} IS NULL OR c.companyName LIKE :#{#filter.companyName})AND
            (:#{#filter.accountManagerId} IS NULL OR c.accountManager.id = :#{#filter.accountManagerId}) and
            (:#{#filter.caseOwner} IS NULL OR c.createdBy.userId = :#{#filter.caseOwner}) AND
            (:#{#filter.minPercent} IS NULL OR c.percentCompletion >= :#{#filter.minPercent}) AND
            (:#{#filter.maxPercent} IS NULL OR c.percentCompletion <= :#{#filter.maxPercent}) AND
            (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId}) AND
            (:#{#filter.partnerId} IS NULL OR c.partnerId = :#{#filter.partnerId})
            group by c.id
        """
    )
    fun searchByCriteriaForDownloadAndCreatedUser(
        filter: CaseSearchFilter,
        createdByUserIds: List<Long>
    ): List<CaseViewForResidenceCountry>

    //Corporate User - case Listing
    @Query(
        """
            select c from CaseView c 
            where
            (c.archive = :#{#filter.archive})AND
            (:#{#filter.isPriorityCase} IS NULL OR c.isPriorityCase = :#{#filter.isPriorityCase})AND
            (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} ) AND
            (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId} ) AND
            (:#{#filter.search} IS NULL OR (c.initiatedFor LIKE :#{#filter.search} OR c.companyName LIKE :#{#filter.search})) AND
            (:#{#filter.status} IS NULL OR c.status = :#{#filter.status}) AND
            c.createdBy.userId IN (:#{#createdByUserIds}) AND
            (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
            (:#{#filter.actionFor} IS NULL OR c.actionFor IN (:#{#filter.actionFor})) AND
            (:#{#filter.companyName} IS NULL OR c.companyName LIKE :#{#filter.companyName})AND
            (:#{#filter.accountManagerId} IS NULL OR c.accountManager.id = :#{#filter.accountManagerId}) AND
            (:#{#filter.caseOwner} IS NULL OR c.createdBy.userId = :#{#filter.caseOwner}) AND
            (:#{#filter.minPercent} IS NULL OR c.percentCompletion >= :#{#filter.minPercent}) AND
            (:#{#filter.maxPercent} IS NULL OR c.percentCompletion <= :#{#filter.maxPercent}) AND
            (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId}) AND
            (:#{#filter.partnerId} IS NULL OR c.partnerId = :#{#filter.partnerId})
            group by c.id
        """
    )
    fun searchByCriteriaAndCreatedUser(
        filter: CaseSearchFilter, pageable: Pageable,
        createdByUserIds: List<Long>
    ): Page<CaseView>


    //Expert User - Download
    @Query(
        """
            select c from CaseViewForResidenceCountry c , case_assignee ca
            where
            (c.archive = :#{#filter.archive})AND
            (:#{#filter.isPriorityCase} IS NULL OR c.isPriorityCase = :#{#filter.isPriorityCase})AND
            (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} ) AND
            (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId} ) AND
            (:#{#filter.search} IS NULL OR (c.initiatedFor LIKE :#{#filter.search} OR c.companyName LIKE :#{#filter.search})) AND
            (:#{#filter.status} IS NULL OR c.status = :#{#filter.status}) AND
            ca.expert.id = :#{#authenticatedUser.userId} AND
            ca.case.id = c.id AND
            (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
            (:#{#filter.actionFor} IS NULL OR c.actionFor IN (:#{#filter.actionFor})) AND
            (:#{#filter.companyName} IS NULL OR c.companyName LIKE :#{#filter.companyName})AND
            (:#{#filter.accountManagerId} IS NULL OR c.accountManager.id = :#{#filter.accountManagerId}) AND
            (:#{#filter.caseOwner} IS NULL OR c.createdBy.userId = :#{#filter.caseOwner}) AND
            (:#{#filter.minPercent} IS NULL OR c.percentCompletion >= :#{#filter.minPercent}) AND
            (:#{#filter.maxPercent} IS NULL OR c.percentCompletion <= :#{#filter.maxPercent}) AND
            (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId}) AND
            (:#{#filter.partnerId} IS NULL OR c.partnerId = :#{#filter.partnerId})
            group by c.id
        """
    )
    fun searchByCriteriaForDownloadAndAssigneeUser(
        filter: CaseSearchFilter,
        authenticatedUser: AuthenticatedUser
    ): List<CaseViewForResidenceCountry>



    //Expert User - Case listing
    @Query(
        """
            select c from CaseView c , case_assignee ca
            where
            (c.archive = :#{#filter.archive})AND
            (:#{#filter.isPriorityCase} IS NULL OR c.isPriorityCase = :#{#filter.isPriorityCase})AND
            (:#{#filter.category} IS NULL OR c.parentCategoryId = :#{#filter.category} ) AND
            (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId} ) AND
            (:#{#filter.search} IS NULL OR (c.initiatedFor LIKE :#{#filter.search} OR c.companyName LIKE :#{#filter.search})) AND
            (:#{#filter.status} IS NULL OR c.status = :#{#filter.status}) AND
            ca.expert.id = :#{#authenticatedUser.userId} AND
            ca.case.id = c.id AND
            (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
            (:#{#filter.actionFor} IS NULL OR c.actionFor IN (:#{#filter.actionFor})) AND
            (:#{#filter.companyName} IS NULL OR c.companyName LIKE :#{#filter.companyName})AND
            (:#{#filter.accountManagerId} IS NULL OR c.accountManager.id = :#{#filter.accountManagerId}) AND
            (:#{#filter.caseOwner} IS NULL OR c.createdBy.userId = :#{#filter.caseOwner}) AND
            (:#{#filter.minPercent} IS NULL OR c.percentCompletion >= :#{#filter.minPercent}) AND
            (:#{#filter.maxPercent} IS NULL OR c.percentCompletion <= :#{#filter.maxPercent}) AND 
            (:#{#filter.accountId} IS NULL OR c.account.id = :#{#filter.accountId}) AND
            (:#{#filter.partnerId} IS NULL OR c.partnerId = :#{#filter.partnerId})
            group by c.id
        """
    )
    fun searchByCriteriaAndAssigneeUser(
        filter: CaseSearchFilter, pageable: Pageable,
        authenticatedUser: AuthenticatedUser
    ): Page<CaseView>

    fun findAllByIdIn(idList: List<Long>, pageable: Pageable): Page<CaseView>

//    @Query(
//        """ SELECT * FROM
//    (SELECT
//        c.*,
//            (@num\:=IF(@group = parent_category_id, @num + 1, IF(@group\:=parent_category_id, 1, 1))) row_number
//    FROM
//        cases c
//    CROSS JOIN (SELECT @num\:=0, @group\:=NULL) c WHERE
//    (c.archive = :#{#filter.archive})AND
//    (:#{#filter.isPriorityCase} IS NULL OR c.is_priority_case = :#{#filter.isPriorityCase})AND
//    (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId}) AND
//    (:#{#filter.search} IS NULL OR (c.initiated_for LIKE :#{#filter.search} OR c.company_name LIKE :#{#filter.search}) ) AND
//    (:#{#filter.status}  IS NULL OR status = :#{#filter.status} ) AND
//    (:#{#filter.companyName} IS NULL OR c.company_name LIKE CONCAT('%', :#{#filter.companyName}, '%')) AND
//    (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
//    (:#{#filter.actionFor} IS NULL OR c.action_for = :#{#filter.actionFor}) AND
//    ORDER BY parent_category_id , created_date DESC) AS x
//    WHERE
//    x.row_number <= 5
//        """, nativeQuery = true
//    )
//    fun searchByCriteria(
//        @Param("filter")
//        filter: AggregatedCaseFilter
//    ): List<CaseView>
//
//
//    @Query(
//        """ SELECT * FROM
//    (SELECT
//        c.*,
//            (@num\:=IF(@group = parent_category_id, @num + 1, IF(@group\:=parent_category_id, 1, 1))) row_number
//    FROM cases c
//    CROSS JOIN (SELECT @num\:=0, @group\:=NULL) c , case_assignee ca WHERE
//    (c.archive = :#{#filter.archive})AND
//    (:#{#filter.isPriorityCase} IS NULL OR c.is_priority_case = :#{#filter.isPriorityCase})AND
//    (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId}) AND
//    (:#{#filter.search} IS NULL OR (c.initiated_for LIKE :#{#filter.search} OR c.company_name LIKE :#{#filter.search})) AND
//    (:#{#filter.status}  IS NULL OR status = :#{#filter.status} ) AND
//    (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
//    (:#{#filter.actionFor} IS NULL OR c.action_for = :#{#filter.actionFor}) AND
//    (:#{#filter.companyName} IS NULL OR c.company_name LIKE CONCAT('%', :#{#filter.companyName}, '%')) AND
//    ca.case_id = c.id AND
//    ca.expert_id = :#{#authenticatedUser.userId}
//    ORDER BY parent_category_id , created_date DESC) AS x
//    WHERE
//    x.row_number <= 5
//        """, nativeQuery = true
//    )
//    fun searchByCriteriaAndAssigneeUser(
//        filter: AggregatedCaseFilter,
//        authenticatedUser: AuthenticatedUser
//    ): List<CaseView>
//
//    @Query(
//        """ SELECT * FROM
//    (SELECT
//        c.*,
//            (@num\:=IF(@group = parent_category_id, @num + 1, IF(@group\:=parent_category_id, 1, 1))) row_number
//    FROM cases c
//    CROSS JOIN (SELECT @num\:=0, @group\:=NULL) c WHERE
//    (c.archive = :#{#filter.archive})AND
//    (:#{#filter.isPriorityCase} IS NULL OR c.is_priority_case = :#{#filter.isPriorityCase})AND
//    (:#{#filter.caseId} IS NULL OR c.id = :#{#filter.caseId}) AND
//    (:#{#filter.search} IS NULL OR (c.initiated_for LIKE :#{#filter.search} OR c.company_name LIKE :#{#filter.search})) AND
//    (:#{#filter.status}  IS NULL OR status = :#{#filter.status} ) AND
//    (:#{#filter.country} IS NULL OR c.country = :#{#filter.country}) AND
//    (:#{#filter.actionFor} IS NULL OR c.action_for = :#{#filter.actionFor}) AND
//    (:#{#filter.companyName} IS NULL OR c.company_name LIKE CONCAT('%', :#{#filter.companyName}, '%')) AND
//    c.created_by = :#{#authenticatedUser.userId}
//    ORDER BY parent_category_id , created_date DESC) AS x
//    WHERE
//    x.row_number <= 5
//        """, nativeQuery = true
//    )
//    fun searchByCriteriaAndCreatedUser(
//        filter: AggregatedCaseFilter,
//        authenticatedUser: AuthenticatedUser
//    ): List<CaseView>

    fun countByStatusAndArchive(status: String, archive: Boolean): Long

    fun countByCreatedByAndParentCategoryIdAndArchive(userId: ClientView, category: String?, archive: Boolean): Long

    fun countByStatusAndCreatedByAndParentCategoryIdAndArchive(status: String, clientView: ClientView, category: String?, archive: Boolean): Long

    fun countByStatusAndAssigneeAndParentCategoryIdAndArchive(status: String, user: UserView, category: String?, archive: Boolean): Long

    fun countByAssigneeAndParentCategoryIdAndArchive(user: UserView, category: String?, archive: Boolean): Long

    fun countByArchive(archive: Boolean): Long
    @Query("""
        SELECT c.id FROM CaseView c , case_assignee ca
        WHERE 
        (:#{#partnerId} IS NULL OR c.partnerId =:#{#partnerId}) AND
        (:#{#createdBy} IS NULL OR c.createdBy.userId IN :#{#createdBy}) AND
        (:#{#assignedTo} IS NULL OR ca.id IN :#{#assignedTo})
        
        GROUP BY c.id
        
    """)
    fun getIdByCriteria(partnerId: Long?, createdBy: List<Long>?, assignedTo: List<Long>?): List<Long>
}