package com.centuroglobal.shared.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.view.CountryView
import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.data.pojo.CountryRegion
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CountryRegionRepository
import com.centuroglobal.shared.repository.view.CountryViewRepository
import io.micrometer.core.instrument.util.StringUtils
import mu.KotlinLogging
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CountryService(
    private val countryRegionService: CountryRegionService,
    private val countryViewRepository: CountryViewRepository
) {

    fun listCountries(prefix: String?): List<Country> {
        val countries = if (StringUtils.isNotEmpty(prefix)) {
            countryViewRepository.findAllByCountryStartsWith(prefix!!)
        } else {
            countryViewRepository.findAll()
        }
        return computeCountryRegions(countries)
    }

    fun retrieveByCountryCode(countryCode: String): Country {
        val countries = countryViewRepository.findAllByCountryCode(countryCode)
        if (countries.isEmpty())
            throw ApplicationException(ErrorCode.COUNTRY_CODE_INVALID)
        return computeCountryRegions(countries)[0]
    }

    fun validateRegionWithinCountry(regionId: Int?, countryCode: String): CountryRegion? {
        val regionList = countryRegionService.listRegionsGroupedByCountry()[countryCode]

        return if (!regionList.isNullOrEmpty() && regionId != null) {
            regionList.find { it.regionId == regionId } ?: throw ApplicationException(ErrorCode.REGION_INVALID)
        } else if (regionList.isNullOrEmpty() && regionId == null) {
            null
        } else {
            //throw ApplicationException(ErrorCode.REGION_INVALID)
            //stopped throwing error
            null
        }
    }

    private fun computeCountryRegions(countries: List<CountryView>): List<Country> {
        val regionMap = countryRegionService.listRegionsGroupedByCountry()
        return countries.map {
            Country(
                code = it.countryCode,
                name = it.country,
                dialCode = it.dialCode,
                regions = regionMap[it.countryCode] ?: emptyList()
            )
        }
    }
}

@Service
class CountryRegionService(private val countryRegionRepository: CountryRegionRepository) {

    @Cacheable("country-region")
    fun listRegionsGroupedByCountry(): Map<String, List<CountryRegion>> {
        log.debug("Retrieving country regions from repository")
        return countryRegionRepository.findAll().groupBy(
            { it.countryCode },
            { CountryRegion.ModelMapper.from(it) }
        )
    }
}