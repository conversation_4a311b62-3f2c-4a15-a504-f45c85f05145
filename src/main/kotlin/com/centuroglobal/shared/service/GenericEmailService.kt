package com.centuroglobal.shared.service

import com.centuroglobal.shared.data.pojo.email.MailTemplate
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.thymeleaf.context.Context

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

private const val HUBSPOT_JOB_NOTIFICATION = "email/hubspot_job_email"

@Service
class GenericEmailService(

    @Value("\${app.hubspot.email-recipients:}")
    private val hubspotMailRecipients: List<String>,

    private val awsS3Service: AwsS3Service,

    private val mailSendingService: MailSendingService

) {


    /**
     * Sends Async email
     */
    @Async
    fun sendEmail(subject: String, templateName: String, variableMap: Map<String, Any>,
                  recipient: List<String>, cc: List<String> = listOf(), bcc: List<String> = listOf()
    ) {

        val ctx = Context()
        ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))
        variableMap.forEach { (k, v) ->
            ctx.setVariable(k, v)
        }

        mailSendingService.sendEmail(
            MailTemplate(
                templateName = templateName,
                subject = subject,
                context = ctx,
                recipient = recipient.joinToString(","),
                ccRecipients = cc,
                bccRecipients = bcc
            )
        )
    }

    fun sendHubSpotNotification(mailBody: String) {
        sendEmail("Hubspot Jobs", HUBSPOT_JOB_NOTIFICATION, mapOf("MAIL_TEXT" to mailBody),  hubspotMailRecipients)
    }

}