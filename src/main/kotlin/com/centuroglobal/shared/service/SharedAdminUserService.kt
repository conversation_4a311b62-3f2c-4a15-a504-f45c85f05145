package com.centuroglobal.shared.service

import com.centuroglobal.shared.client.HubspotApiClient
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.*
import com.centuroglobal.shared.data.payload.Properties
import com.centuroglobal.shared.data.payload.hubspot.Filter
import com.centuroglobal.shared.data.payload.hubspot.FilterGroup
import com.centuroglobal.shared.data.payload.hubspot.HubspotFilter
import com.centuroglobal.shared.data.payload.hubspot.Result
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.util.AdminAccessUtil
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import java.util.*


private val log = KotlinLogging.logger {}

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"
private const val HUBSPOT_JOB_NOTIFICATION = "email/hubspot_job_email"

@Service
class SharedAdminUserService(

    @Value("\${app.hubspot.apiKey}")
    private val apiKey: String,
    @Value("\${app.hubspot.email-recipients}")
    private val hubspotMailRecipients: List<String>,
    private val hubspotApiClient: HubspotApiClient,
    private val loginAccountRepository: LoginAccountRepository,
    private val corporateUserRepository: CorporateUserRepository,

    private val expertUserRepository: ExpertUserRepository,

    private val genericEmailService: GenericEmailService
) {

    @Transactional

    fun createHubspotUsers(): MutableList<HubspotResponse> {
        log.info("Hubspot process started at ${TimeZone.getDefault().id}")
        val userList = loginAccountRepository.findAll().filter { it.getUserType() != UserType.BACKOFFICE }
        val hubspotResponseList: MutableList<HubspotResponse> = mutableListOf()
        try {
            var failedUserList: MutableList<String> = mutableListOf()

            for (loginUser in userList) {
                when (AdminAccessUtil.getUserType(loginUser)) {
                    UserType.CORPORATE -> {
                        val corporateEntity = (loginUser as CorporateUserEntity).corporate
                        if (corporateEntity.partner !=null)
                            continue
                    }
                    else -> {
                    }
                }
                log.info("${loginUser.email} request started ")
                val hubspotUser = findHubspotUser(loginUser.email)

                if (hubspotUser.isEmpty()) {
                    log.info("${loginUser.email} not found on hubspot")

                    try {
                        val hubspotResponse = createHubspotUser(loginUser)
                        hubspotResponseList.add(hubspotResponse)
                    } catch (ex: Exception) {
                        failedUserList.add(loginUser.email)
                        log.info(" ${loginUser.email} user failed with following exception")
                        log.error("error while creating ${ex.toString()} ", ex)
                    }
                    log.info("${loginUser.email} user created successfully on hubspot")
                } else {
                    log.info("${loginUser.email} found on hubspot")
                    val hubspotProperties = hubspotUser[0].properties!!
                    val hubspotRequest = getHubspotRequest(
                        hubspotProperties.email, hubspotProperties.firstname,
                        hubspotProperties.lastname, loginUser)
                    val hubspotResponse =
                        hubspotApiClient.updateUser(hubspotProperties.hs_object_id!!.toLong(), apiKey, hubspotRequest)
                    hubspotResponseList.add(hubspotResponse)
                    log.info("${loginUser.email} updated successfully on hubspot")
                }
            }

            if(failedUserList.isNotEmpty()) log.info ("Hubspot Users Failed : $failedUserList")
            log.info("Hubspot process end at ${TimeZone.getDefault().id}")
            genericEmailService.sendHubSpotNotification("Hubspot job completed successfully at: ${LocalDateTime.now()}")
        }
        catch (ex: Exception) {
            log.error(ex.message, ex)
            genericEmailService.sendHubSpotNotification("Hubspot job failed with below error: <br> ${ex.message}")
        }
        return hubspotResponseList
    }

    private fun getHubspotRequest(email: String?, firstname: String?, lastname: String?, loginUser: LoginAccountEntity): HubspotRequest {
        val hubspotRequest = HubspotRequest()
        val properties = Properties()

        properties.email = email
        properties.firstname = firstname
        properties.lastname = lastname

        properties.cg_platform_status = loginUser.status.toString()
        properties.last_login = TimeUtil.toEpochMillis(getLastLoginDate(loginUser.lastLoginDate))
        properties.subscription_type = loginUser.subscriptionType.toString()
        properties.opted_in_to_services_and_offering_marketing = false

        if (loginUser.getUserType() == UserType.EXPERT) {
            val expertUser = expertUserRepository.findByIdOrNull(loginUser.id!!)
            properties.contact_category = "Member"
            if (expertUser != null) {
                properties.jobtitle = expertUser.jobTitle
                properties.member_type = expertUser.expertType
                val expertCompany = expertUser.companyProfile
                if (expertCompany != null) {
                    properties.company = expertCompany.name
                    properties.contract_start_date = TimeUtil.toEpochMillis(expertCompany.effectiveDate.toLocalDate())
                    properties.contract_end_date = TimeUtil.toEpochMillis(expertCompany.effectiveEndDate.toLocalDate())
                }
            }
        } else {
            properties.contact_category = "Client"
            val corporateUser = corporateUserRepository.findByIdOrNull(loginUser.id!!)
            if (corporateUser != null) {
                properties.opted_in_to_services_and_offering_marketing = corporateUser.keepMeInformed
                properties.jobtitle = corporateUser.jobTitle
                properties.company = corporateUser.corporate.name
            }
        }
        hubspotRequest.properties = properties
        return hubspotRequest
    }

    private fun getLastLoginDate(loginDate: LocalDateTime?): LocalDate {
        if (loginDate != null) {
            return loginDate.toLocalDate()
        }
        return LocalDate.of(2021, Month.JANUARY, 1)
    }


    private fun getHubspotFilter(email: String, propertyValue: String): HubspotFilter {
        val hubspotFilter = HubspotFilter()
        val filterGroupList: MutableList<FilterGroup> = mutableListOf()
        val group = FilterGroup()
        val filterList: MutableList<Filter> = mutableListOf()
        val filter = Filter()
        filter.operator = "EQ"
        filter.propertyName = propertyValue
        filter.value = email
        filterList.add(filter)
        group.filters = filterList
        filterGroupList.add(group)
        hubspotFilter.filterGroups = filterGroupList
        return hubspotFilter
    }

    private fun getHubspotEmailFilter(email: String): HubspotFilter {

        val existingFilter = getHubspotFilter(email, "email")

        val group = FilterGroup()
        val filterList: MutableList<Filter> = mutableListOf()
        val filter = Filter()
        filter.operator = "CONTAINS_TOKEN"
        filter.propertyName = "hs_additional_emails"
        filter.value = email
        filterList.add(filter)
        group.filters = filterList

        existingFilter.filterGroups = existingFilter.filterGroups.plus(group)

        return existingFilter
    }

    fun createHubspotUser(
        loginUser: LoginAccountEntity
    ): HubspotResponse {

        val hubspotRequest = getHubspotRequest(loginUser.email, loginUser.firstName,
            loginUser.lastName, loginUser)

        return hubspotApiClient.saveUser(apiKey, hubspotRequest)
    }

    fun findHubspotUser(email: String): List<Result> {
        return hubspotApiClient.findAllUser(apiKey, getHubspotEmailFilter(email)).results
    }

    fun findHubspotCompany(domain: String): List<Result> {
        return hubspotApiClient.findAllCompany(apiKey, getHubspotFilter(domain, "domain")).results
    }

    fun createCompany(domain: String): HubspotResponse {
        return hubspotApiClient.saveCompany(apiKey, getHubspotCompanyRequest(domain))
    }

    private fun getHubspotCompanyRequest(domain: String): HubspotCompanyRequest {

        val hubspotRequest = HubspotCompanyRequest()
        val properties = CompanyProperties()

        properties.domain = domain
        hubspotRequest.properties = properties

        return hubspotRequest
    }

    fun createHubspotUser(email: String, firstname: String?, lastname: String?, jobTitle: String?): HubspotResponse {

        val hubspotRequest = HubspotRequest()
        val properties = Properties()

        properties.email = email
        properties.firstname = firstname
        properties.lastname = lastname

        properties.opted_in_to_services_and_offering_marketing = false
        properties.contact_category = "Client"
        properties.jobtitle = jobTitle

        hubspotRequest.properties = properties

        return hubspotApiClient.saveUser(apiKey, hubspotRequest)
    }

}