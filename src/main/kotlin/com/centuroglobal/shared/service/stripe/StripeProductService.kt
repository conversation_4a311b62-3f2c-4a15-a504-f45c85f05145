package com.centuroglobal.shared.service.stripe

import com.centuroglobal.shared.data.ErrorCode.STRIPE_PRODUCT_NOT_FOUND
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.stripe.SubscriptionProduct
import com.centuroglobal.shared.exception.ApplicationException
import com.stripe.Stripe
import com.stripe.model.Price
import com.stripe.model.PriceCollection
import com.stripe.model.Product
import com.stripe.param.PriceListParams
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class StripeProductService(
    @Value("\${app.stripe.secret-key}")
    private val secretKey: String,
    @Value("\${app.stripe.product.corporate}")
    private val corporateProductId: String
) {
    init {
        Stripe.apiKey = secretKey
    }

    @Cacheable("stripe-product")
    fun getProduct(userType: UserType): SubscriptionProduct {
        log.debug("Retrieving product/price from stripe server")
        if (userType == UserType.CORPORATE) {
            val product: Product = Product.retrieve(corporateProductId)
            val priceParams = PriceListParams.builder()
                .setActive(true)
                .setType(PriceListParams.Type.RECURRING)
                .setProduct(corporateProductId)
                .setLimit(1L)
                .build()
            val priceCollection: PriceCollection = Price.list(priceParams)
            if (priceCollection.data.isEmpty()) {
                throw ApplicationException(STRIPE_PRODUCT_NOT_FOUND)
            } else {
                return SubscriptionProduct.ModelMapper.from(priceCollection.data[0], product)
            }

        } else {
            throw ApplicationException(STRIPE_PRODUCT_NOT_FOUND)
        }
    }
}