package com.centuroglobal.shared.service.stripe

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.stripe.StripeAccountEntity
import com.centuroglobal.shared.data.entity.stripe.StripeSubscriptionEntity
import com.centuroglobal.shared.data.entity.stripe.StripeTransactionEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.stripe.StripeRequestType
import com.centuroglobal.shared.data.enums.stripe.StripeTransactionStatus
import com.centuroglobal.shared.data.payload.stripe.CheckoutSessionRequest
import com.centuroglobal.shared.data.payload.stripe.SubscriptionPaymentRequest
import com.centuroglobal.shared.data.pojo.stripe.*
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.stripe.StripeAccountRepository
import com.centuroglobal.shared.repository.stripe.StripeSubscriptionRepository
import com.centuroglobal.shared.repository.stripe.StripeTransactionRepository
import com.centuroglobal.shared.service.TransactionService
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.stripe.Stripe
import com.stripe.exception.CardException
import com.stripe.exception.InvalidRequestException
import com.stripe.exception.StripeException
import com.stripe.model.*
import com.stripe.model.checkout.Session
import com.stripe.net.ApiResource
import com.stripe.param.CustomerCreateParams
import com.stripe.param.PaymentMethodAttachParams
import com.stripe.param.PaymentMethodListParams
import com.stripe.param.SubscriptionUpdateParams
import com.stripe.param.checkout.SessionCreateParams
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.*


private val log = KotlinLogging.logger {}

@Service
class StripeService(
    @Value("\${app.stripe.secret-key}")
    private val secretKey: String,
    @Value("\${app.stripe.mock-mode}")
    private val mockMode: Boolean,
    private val transactionService: TransactionService,
    private val stripeProductService: StripeProductService,
    private val stripeAccountRepository: StripeAccountRepository,
    private val loginAccountRepository: LoginAccountRepository,
    private val stripeTransactionRepository: StripeTransactionRepository,
    private val stripeSubscriptionRepository: StripeSubscriptionRepository
) {

    init {
        Stripe.apiKey = secretKey
    }

    @Transactional
    fun createSubscription(userId: Long, subscriptionRequest: SubscriptionPaymentRequest): SubscriptionObject {
        val stripeAccount = findStripeAccount(userId, true)

        try {
            // Set the default payment method on the customer
            val pm = PaymentMethod.retrieve(subscriptionRequest.paymentMethodId)
            pm.attach(PaymentMethodAttachParams.builder().setCustomer(stripeAccount!!.customerId).build())

            updateDefaultPaymentMethod(stripeAccount.customerId, subscriptionRequest.paymentMethodId)

            // Retrieve product info
            val product = retrieveProduct(subscriptionRequest.priceId)

            // Create the subscription
            val item: MutableMap<String, Any> = HashMap()
            item["plan"] = subscriptionRequest.priceId
            val items: MutableMap<String, Any> = HashMap()
            items["0"] = item
            val subscriptionParameters: MutableMap<String, Any> = HashMap()
            subscriptionParameters["customer"] = stripeAccount.customerId
            subscriptionParameters["items"] = items

            val expandList: MutableList<String> = ArrayList()
            expandList.add("latest_invoice.payment_intent")
            subscriptionParameters["expand"] = expandList

            val subscription = Subscription.create(subscriptionParameters)
            val invoice =
                if (subscription.latestInvoiceObject != null) subscription.latestInvoiceObject else Invoice.retrieve(
                    subscription.latestInvoice
                )
            val paymentIntent =
                if (invoice.paymentIntentObject != null) invoice.paymentIntentObject else PaymentIntent.retrieve(invoice.paymentIntent)

            transactionService.executeInNewTransaction {
                // Persist the transaction
                stripeTransactionRepository.save(
                    StripeTransactionEntity(
                        customerId = stripeAccount.customerId,
                        requestType = StripeRequestType.PRICE,
                        requestTypeId = subscriptionRequest.priceId,
                        paymentMethodId = subscriptionRequest.paymentMethodId,
                        amount = product.amount,
                        request = convertMapToJSON(subscriptionParameters),
                        status = StripeTransactionStatus.CREATED
                    )
                )
                // Persist the subscription
                stripeSubscriptionRepository.save(
                    StripeSubscriptionEntity(
                        stripeAccount = stripeAccount,
                        paymentMethodId = subscriptionRequest.paymentMethodId,
                        subscriptionId = subscription.id,
                        productId = subscription.items.data[0].price.product,
                        priceId = subscriptionRequest.priceId,
                        subscriptionActive = subscription.status == "active",
                        subscriptionAmount = product.amount,
                        currentPeriodStartDate = TimeUtil.fromInstant(subscription.currentPeriodStart),
                        currentPeriodEndDate = TimeUtil.fromInstant(subscription.currentPeriodEnd),
                        latestInvoiceId = subscription.latestInvoice,
                        latestInvoiceStatus = StripeTransactionStatus.fromKey(subscription.status)
                    )
                )
            }

            // Handles payment error.....
            if (paymentIntent.status == StripeTransactionStatus.REQUIRES_PAYMENT_METHOD.key) {
                // Cancel the subscription immediately
                subscription.cancel()
                // Return payment error
                val errorMessage =
                    if (paymentIntent.lastPaymentError != null) paymentIntent.lastPaymentError.message else "Your card was declined."
                throw ApplicationException(ErrorCode.STRIPE_CARD_FAIL(errorMessage))
            }

            return SubscriptionObject.ModelMapper.from(
                ApiResource.GSON.fromJson(
                    subscription.toJson(),
                    Subscription::class.java
                ),
                if (paymentIntent.status == StripeTransactionStatus.REQUIRES_ACTION.key) {
                    ThreeDSecureAction(clientSecret = paymentIntent.clientSecret)
                } else {
                    null
                }
            )
        } catch (ex: Exception) {
            log.error("Stripe error", ex)
            when (ex) {
                is ApplicationException -> throw ex
                is CardException -> throw ApplicationException(ErrorCode.STRIPE_CARD_FAIL(ex.stripeError.message))
                is InvalidRequestException -> throw ApplicationException(ErrorCode.STRIPE_INVALID_REQUEST_FAIL(ex.stripeError.message))
                else -> throw ApplicationException(ErrorCode.STRIPE_EXCEPTION)
            }
        }
    }

    @Transactional
    fun cancelSubscription(
        userId: Long,
        subscriptionId: String
    ): SubscriptionObject {
        return subscriptionCancellation(userId, subscriptionId, false)
    }

    @Transactional
    fun undoCancelSubscription(
        userId: Long,
        subscriptionId: String
    ): SubscriptionObject {
        return subscriptionCancellation(userId, subscriptionId, true)
    }

    @Transactional(readOnly = true)
    fun retrieveSubscriptionPaymentMethod(userId: Long, subscriptionId: String): PaymentMethodObject? {
        val stripeAccount = findStripeAccount(userId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val activeSubscriptions =
            stripeSubscriptionRepository.findAllBySubscriptionIdAndSubscriptionActiveOrderByCreatedDateDesc(
                subscriptionId,
                true
            )

        if (activeSubscriptions.isEmpty()) {
            val error = ErrorCode.NO_ACTIVE_SUBSCRIPTION_FOUND(stripeAccount.customerId, subscriptionId)
            log.error(error.errorMessage)
            throw ApplicationException(error)
        }

        // retrieve Subscription from Stripe
        val subscription = Subscription.retrieve(subscriptionId)
        if (subscription == null) {
            log.error("Unable to retrieve subscription $subscriptionId")
            throw ApplicationException(ErrorCode.STRIPE_SUBSCRIPTION_NOT_FOUND)
        }
        // determine the paymentMethodId
        val paymentMethodId = if (!subscription.defaultPaymentMethod.isNullOrBlank()) {
            subscription.defaultPaymentMethod
        } else if (!subscription.defaultSource.isNullOrBlank()) {
            subscription.defaultSource
        } else {
            val customer = Customer.retrieve(stripeAccount.customerId)
            if (!customer.invoiceSettings.defaultPaymentMethod.isNullOrBlank()) {
                customer.invoiceSettings.defaultPaymentMethod
            } else {
                customer.defaultSource
            }
        }

        return if (paymentMethodId != null) {
            PaymentMethodObject.ModelMapper.from(PaymentMethod.retrieve(paymentMethodId))
        } else {
            log.error("Unable to retrieve payment details for customer ${stripeAccount.customerId} and subscription $subscriptionId")
            throw ApplicationException(ErrorCode.PAYMENT_METHOD_NOT_FOUND)
        }
    }

    fun retrieveCustomerPaymentMethods(userId: Long): List<PaymentMethodObject> {
        val stripeAccount = findStripeAccount(userId)
        return if (stripeAccount == null) {
            emptyList()
        } else {
            retrieveCustomerPaymentMethods(stripeAccount.customerId)
                .map { PaymentMethodObject.ModelMapper.from(it) }
        }
    }

    @Transactional
    fun createCustomer(userId: Long, email: String): StripeAccountEntity {
        val params = CustomerCreateParams.builder()
            .setBalance(0L)
            .setEmail(email)
            .build()

        try {
            val accountEntity: StripeAccountEntity = if (mockMode) {
                StripeAccountEntity(
                    userId = userId,
                    customerId = "cus_${UUID.randomUUID().toString().replace(Regex("[-]"), "")}"
                )
            } else {
                val customer = Customer.create(params)
                StripeAccountEntity(userId = userId, customerId = customer.id)
            }
            return stripeAccountRepository.save(accountEntity)
        } catch (ex: StripeException) {
            log.error("Error while creating a Stripe customer", ex)
            throw ApplicationException(ErrorCode.STRIPE_CUSTOMER_CREATION_FAIL(ex.stripeError.message))
        }
    }

    fun createCheckoutSession(
        userId: Long,
        subscriptionId: String,
        checkoutSessionRequest: CheckoutSessionRequest
    ): CheckoutSession {
        val stripeAccount = findStripeAccount(userId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val params: SessionCreateParams = SessionCreateParams.builder()
            .addPaymentMethodType(SessionCreateParams.PaymentMethodType.CARD)
            .setMode(SessionCreateParams.Mode.SETUP)
            .setCustomer(stripeAccount.customerId)
            .setSetupIntentData(
                SessionCreateParams.SetupIntentData.builder()
                    .putMetadata("customer_id", stripeAccount.customerId)
                    .putMetadata("subscription_id", subscriptionId)
                    .build()
            )
            .setSuccessUrl(checkoutSessionRequest.successUrl)
            .setCancelUrl(checkoutSessionRequest.cancelUrl)
            .build()

        val session: Session = Session.create(params)
        return CheckoutSession(id = session.id)
    }

    @Transactional(readOnly = true)
    fun getActiveSubscription(userId: Long): UserSubscription {
        val stripeAccount = findStripeAccount(userId)
        return if (stripeAccount != null) {
            val userSubscriptions =
                stripeSubscriptionRepository.findAllByStripeAccountOrderByCreatedDateDesc(
                    stripeAccount
                )
            if (userSubscriptions.isNotEmpty() &&
                (
                        userSubscriptions[0]!!.subscriptionActive ||
                                listOf(
                                    StripeTransactionStatus.OPEN,
                                    StripeTransactionStatus.PAST_DUE,
                                    StripeTransactionStatus.INCOMPLETE
                                ).contains(userSubscriptions[0]!!.latestInvoiceStatus)
                        )
            ) {
                UserSubscription.ModelMapper.from(userSubscriptions[0]!!, listOf(retrieveProduct(null)))
            } else {
                log.info("No active subscriptions for user $userId")
                retrieveNullSubscription()
            }
        } else {
            retrieveNullSubscription()
        }
    }

    fun updateDefaultPaymentMethod(customerId: String, paymentMethodId: String) {
        // retrieve customer
        val customer = findStripeCustomer(customerId)
        // update default payment method
        val customerParams: MutableMap<String, Any> = HashMap()
        val invoiceSettings: MutableMap<String, String> = HashMap()
        invoiceSettings["default_payment_method"] = paymentMethodId
        customerParams["invoice_settings"] = invoiceSettings
        customer.update(customerParams)
        // Detach all other payment methods
        retrieveCustomerPaymentMethods(customerId)
            .filter { it.id != paymentMethodId }
            .forEach {
                PaymentMethod.retrieve(it.id)?.detach()
            }
    }

    private fun subscriptionCancellation(
        userId: Long,
        subscriptionId: String,
        isUndo: Boolean
    ): SubscriptionObject {
        val stripeAccount = findStripeAccount(userId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        val subscription: Subscription
        try {
            subscription = Subscription.retrieve(subscriptionId)
        } catch (ex: Exception) {
            log.error("Unable to retrieve subscription $subscriptionId")
            when (ex) {
                is StripeException -> throw ApplicationException(ErrorCode.STRIPE_SUBSCRIPTION_NOT_FOUND)
                else -> throw ApplicationException(ErrorCode.STRIPE_EXCEPTION)
            }
        }
        val price: Price = subscription.items.data[0].price

        stripeTransactionRepository.save(
            StripeTransactionEntity(
                customerId = stripeAccount.customerId,
                requestType = StripeRequestType.PRICE,
                requestTypeId = price.id,
                paymentMethodId = subscription.defaultPaymentMethod,
                amount = price.unitAmountDecimal.divide(BigDecimal(100)),
                status = if (isUndo) StripeTransactionStatus.UNDO_REQUEST_CANCELLATION else StripeTransactionStatus.REQUEST_CANCELLATION
            )
        )

        // if subscription is not paid.... cancel immediately,
        // else scheduled cancellation at the end of the period
        return if (subscription.status == StripeTransactionStatus.ACTIVE.key) {
            cancelSubscriptionAtPeriodEnd(subscription, isUndo)
        } else if (!isUndo &&
            listOf(
                StripeTransactionStatus.PAST_DUE.key,
                StripeTransactionStatus.INCOMPLETE.key
            ).contains(subscription.status)
        ) {
            cancelSubscriptionImmediately(subscription)
        } else {
            throw ApplicationException(ErrorCode.STRIPE_SUBSCRIPTION_NOT_FOUND)
        }
    }

    private fun cancelSubscriptionImmediately(subscription: Subscription): SubscriptionObject {
        try {
            val deletedSubscription = subscription.cancel()
            return SubscriptionObject.ModelMapper.from(
                ApiResource.GSON.fromJson(
                    deletedSubscription.toJson(),
                    Subscription::class.java
                )
            )
        } catch (ex: Exception) {
            when (ex) {
                is StripeException -> throw ApplicationException(ErrorCode.STRIPE_UPDATE_SUBSCRIPTION_FAIL(ex.stripeError.message))
                else -> throw ApplicationException(ErrorCode.STRIPE_EXCEPTION)
            }
        }
    }

    private fun cancelSubscriptionAtPeriodEnd(subscription: Subscription, isUndo: Boolean): SubscriptionObject {
        try {
            val updateParams = SubscriptionUpdateParams.builder()
                .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.NONE)
                .setCancelAtPeriodEnd(!isUndo)
                .build()
            val updatedSubscription = subscription.update(updateParams)
            return SubscriptionObject.ModelMapper.from(
                ApiResource.GSON.fromJson(
                    updatedSubscription.toJson(),
                    Subscription::class.java
                )
            )
        } catch (ex: Exception) {
            when (ex) {
                is StripeException -> throw ApplicationException(ErrorCode.STRIPE_UPDATE_SUBSCRIPTION_FAIL(ex.stripeError.message))
                else -> throw ApplicationException(ErrorCode.STRIPE_EXCEPTION)
            }
        }
    }

    /*
    * Currently only supporting Corporate
    */
    private fun findStripeAccount(userId: Long, createIfNotExist: Boolean? = false): StripeAccountEntity? {
        val loginAccount = loginAccountRepository.findByIdOrNull(userId)
            ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        if (loginAccount !is CorporateUserEntity) {
            throw ApplicationException(ErrorCode.NOT_FOUND)
        } else {
            val rootUserId = loginAccount.corporate.rootUserId
            return stripeAccountRepository.findByUserId(rootUserId)
                ?: if (createIfNotExist!!) {
                    val rootAccount = loginAccountRepository.findByIdOrNull(rootUserId)
                    createCustomer(rootUserId, rootAccount!!.email)
                } else {
                    null
                }
        }
    }

    private fun retrieveCustomerPaymentMethods(customerId: String): List<PaymentMethod> {
        val listParams = PaymentMethodListParams.builder()
            .setCustomer(customerId)
            .setType(PaymentMethodListParams.Type.CARD)
            .build()

        return PaymentMethod.list(listParams).data
    }

    private fun findStripeCustomer(customerId: String): Customer {
        try {
            return Customer.retrieve(customerId)
        } catch (ex: StripeException) {
            log.error("Error while retrieving the Stripe customer", ex)
            throw ApplicationException(ErrorCode.STRIPE_EXCEPTION)
        }
    }

    private fun convertMapToJSON(params: Any): String {
        val objectMapper = ObjectMapper()

        try {
            return objectMapper.writeValueAsString(params)
        } catch (e: JsonProcessingException) {
            log.error("Could not convert map to JSON!", e)
            throw ApplicationException(ErrorCode.BAD_REQUEST)
        }
    }

    private fun retrieveNullSubscription(): UserSubscription {
        return UserSubscription.ModelMapper.from(null, listOf(retrieveProduct(null)))
    }

    /*
     * Currently only supporting Corporate
     */
    private fun retrieveProduct(priceId: String?): SubscriptionProduct {
        val product = stripeProductService.getProduct(UserType.CORPORATE)
        if (priceId != null && product.priceId != priceId) {
            throw ApplicationException(ErrorCode.STRIPE_PRODUCT_NOT_FOUND)
        } else {
            return product
        }
    }
}
