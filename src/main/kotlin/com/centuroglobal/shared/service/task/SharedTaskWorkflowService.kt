package com.centuroglobal.shared.service.task

import com.centuroglobal.shared.data.entity.TaskEntity
import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.repository.TaskRepository
import com.centuroglobal.shared.util.TimeUtil
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}

@Service
class SharedTaskWorkflowService(

    private val taskRepository: TaskRepository,

) {

    @Transactional
    fun populateTimelines(filteredTasks: List<TaskTemplateEntity>, startDate: LocalDateTime) {
        val tasks = mutableListOf<TaskEntity>()
        var currentStartDate = startDate

        filteredTasks.sortedBy { it.displayOrder }.forEach {
            val task = it.task!!

            task.startDate = TimeUtil.getNthWorkingDay(currentStartDate, 1)

            task.expectedDueDate = TimeUtil.getNthWorkingDay(task.startDate!!, it.expectedTimeline - 1)

            currentStartDate = task.expectedDueDate!!

            tasks.add(task)
        }
        taskRepository.saveAll(tasks)
    }
}