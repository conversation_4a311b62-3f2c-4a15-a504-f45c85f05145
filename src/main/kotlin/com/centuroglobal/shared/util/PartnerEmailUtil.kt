package com.centuroglobal.shared.util

import com.centuroglobal.shared.data.AppConstant.STATIC_PARTNER_LOGO_FOLDER
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.thymeleaf.context.Context

private const val SIGN_UP_EMAIL_S3_STATIC_FOLDER = "static/email_template"

@Service
class PartnerEmailUtil(
    @Value("\${spring.profiles.active:unknown}")
    private val activeProfile: String,
    private val awsS3Service: AwsS3Service,
    private val loginAccountRepository: LoginAccountRepository
) {

    @Transactional(readOnly = true)
    fun updateContext(ctx: Context, userEntity: LoginAccountEntity) {

        ctx.setVariable("CLOSE_MSG_COMPANY_NAME", "Centuro Global Team")
        ctx.setVariable("LOGO_URL", ctx.getVariable("S3_SERVER_URL").toString()+"/centuro-global-logo.png")

        if(userEntity.getUserType() != UserType.CORPORATE) {
            return
        }

        val corporateUserEntity = userEntity as CorporateUserEntity

        val partner = corporateUserEntity.corporate.partner ?: return

        ctx.setVariable("CLOSE_MSG_COMPANY_NAME", partner.name)
        partner.companyLogo?.let { ctx.setVariable("LOGO_URL", getPublicPartnerLogoUrl(partner.id!!)) }
    }

    private fun getPublicPartnerLogoUrl(partnerId: Long): String {
        return awsS3Service.getS3PublicUrl(getPartnerLogoURI(partnerId))
    }

    @Transactional
    fun updateMap(variableMap: MutableMap<String, String>, loginAccountEntity: LoginAccountEntity) {

        val ctx = Context()
        variableMap.forEach { (t, u) ->
            ctx.setVariable(t, u)
        }

        ctx.setVariable("S3_SERVER_URL", awsS3Service.getS3PublicUrl(SIGN_UP_EMAIL_S3_STATIC_FOLDER))

        updateContext(ctx, loginAccountEntity)

        ctx.variableNames.forEach {
            variableMap[it] = ctx.getVariable(it).toString()
        }
        var email = "<EMAIL>"
        if(loginAccountEntity.getUserType() == UserType.CORPORATE) {
            val partner = (loginAccountEntity as CorporateUserEntity).corporate.partner
            if(partner!=null) {
                email = loginAccountRepository.findById(partner.rootUserId!!).get().email
            }
        }
        variableMap["OTP_SUPPORT_EMAIL"] = email
    }

    fun getPartnerLogoURI(partnerId: Long): String {
        return "$activeProfile/$STATIC_PARTNER_LOGO_FOLDER/$partnerId"
    }
}