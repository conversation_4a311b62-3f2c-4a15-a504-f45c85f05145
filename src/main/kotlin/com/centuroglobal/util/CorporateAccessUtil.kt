package com.centuroglobal.util

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CorporateRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class CorporateAccessUtil(
    private val corporateUserRepository: CorporateUserRepository,
    private val corporateRepository: CorporateRepository,
) {

    fun getUser(requestUserId: Long, user: AuthenticatedUser, userId: Long): CorporateUserEntity {

        if(user.role == Role.ROLE_ADMIN.name || user.role == Role.ROLE_SUPER_ADMIN.name){
            return corporateUserRepository.findById(userId).orElseThrow {
                throw ApplicationException(ErrorCode.NOT_FOUND)
            }
        }
        if (user.role == Role.ROLE_PARTNER.name) {
            return corporateUserRepository.findByIdAndCorporatePartnerId(userId, user.partnerId!!)
                ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        }

        val accesses = UserAccessUtil.getAccessesByKey(user, "USER")
        val corporate = corporateRepository.getReferenceById(user.companyId!!)

        return when {
            accesses.contains("FULL") -> {
                corporateUserRepository.findByIdAndCorporate(requestUserId, corporate)
            }
            accesses.contains("REPORTEES") -> {
                val reporters = corporateUserRepository.findIdByManagersManagerId(user.userId)

                if (reporters.map { it.id }.contains(requestUserId) || requestUserId == user.userId) {
                    corporateUserRepository.findByIdAndCorporate(requestUserId, corporate)
                }
                else {
                    null
                }
            }
            accesses.contains("OWN") -> {
                corporateUserRepository.findByIdAndCorporate(user.userId, corporate)
            }

            else -> {
                log.error("User with id: ${user.userId} is having invalid users accesses.")
                throw ApplicationException(ErrorCode.FORBIDDEN)
            }
        }?: throw ApplicationException(ErrorCode.NOT_FOUND)
    }
}