package com.centuroglobal.util

import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.repository.CaseRepository
import com.centuroglobal.shared.repository.RfpRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class GroupChatUtil(
    private val caseRepository: CaseRepository,
    private val queryRepository: QueryRepository,
    private val rfpRepository: RfpRepository
) {


    @Async
    @Transactional
    fun touchReferenceEntity(referenceId: Long, type: ChatType) {

        when(type) {
            ChatType.CASE, ChatType.CLIENT_CASE, ChatType.APPLICANT_CASE -> caseRepository.touch(referenceId, LocalDateTime.now())
            ChatType.QUERY -> queryRepository.touch(referenceId, LocalDateTime.now())
            ChatType.RFP -> rfpRepository.touch(referenceId, LocalDateTime.now())
            ChatType.LEAD -> { /* do nothing */}
        }
    }
}