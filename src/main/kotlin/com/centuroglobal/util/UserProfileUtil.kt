package com.centuroglobal.util

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class UserProfileUtil(
    private val loginAccountRepository: LoginAccountRepository,
    private val awsS3Service: AwsS3Service
) {


    fun retrieveProfile(userId: Long, companyName: String?): UserProfile {
        val user = loginAccountRepository.findByIdOrNull(userId) ?: throw ApplicationException(ErrorCode.NOT_FOUND)
        return retrieveProfile(user, companyName)
    }

    fun retrieveProfile(user: LoginAccountEntity, companyName: String?): UserProfile {
        val profilePictureFullUrl = awsS3Service.getProfilePicUrl(user.profilePhotoUrl)
        return UserProfile.ModelMapper.from(user, profilePictureFullUrl, companyName)
    }

    fun retrieveProfile(userId: Long): UserProfile {
        return retrieveProfile(userId, null)
    }

    fun retrieveProfileWithUserType(user: LoginAccountEntity, userType: PartnerCaseType?): UserProfile {
        val userProfile = retrieveProfile(user, null)
        userProfile.userType = userType
        return userProfile
    }

    fun retrieveCorporateProfile(userId: Long, companyName: String? = null): CorporateUserProfile {
        val user = loginAccountRepository.findByIdOrNull(userId)
        val corporateUserEntity = user as CorporateUserEntity
        val profilePictureFullUrl = awsS3Service.getProfilePicUrl(user.profilePhotoUrl)
        return CorporateUserProfile.ModelMapper.from(
            corporateUserEntity,
            profilePictureFullUrl,
            companyName ?: user.corporate.name,
            corporateUserEntity.corporate.partner?.name
        )
    }
}