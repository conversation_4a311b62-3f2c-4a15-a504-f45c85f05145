app:
  cors:
    domains: "http://localhost:4200, https://beta.centuroglobal.com, https://centuroglobal.com, https://designprefect.co.in"

  authentication:
    mfa:
      static-token: 123321
  api:
    python:
      base-url: https://beta-python-api.centuroglobal.com

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

spring:
  cloud:
    openfeign:
      client:
        config:
          PythonApiClient:
            connectTimeout: 300000
            readTimeout: 300000

  mail:
    cg-support:
      case:
        to: <EMAIL>, <EMAIL>
      query:
        to: <EMAIL>, <EMAIL>
      rfp:
        to: <EMAIL>, <EMAIL>
  aws:
    s3:
      default-bucket: cg-goglobal-demo
      proxied-url:
      master-data-bucket:  cg-goglobal-admin-demo
      member-contract-bucket: cg-goglobal-contracts-demo
      case-doc-bucket: case-doc-repo-demo


#logging:
#  level:
#    org:
#      hibernate:
#        SQL: DEBUG
#        orm:
#          jdbc:
#            bind: TRACE