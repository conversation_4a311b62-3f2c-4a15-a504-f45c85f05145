spring.mail:
  expert-contact-email:
    to: <EMAIL>
    cc: <EMAIL>, <EMAIL>
  member-contract-email:
    to: <EMAIL>
    cc: <EMAIL>
  case-initiate-email:
    to: <EMAIL>
    cc: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
  renewal-contract-acceptance:
    to: <EMAIL>
  query-submission-email:
    bcc: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>, <EMAIL>
  rfp-submission-email:
    bcc: <EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
app.aws.s3:
  member-contract-bucket: cg-goglobal-contracts-prod
  case-doc-bucket: case-doc-repo-prod
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
app:
  cors:
    domains: "https://goglobal.centuroglobal.com, https://centuroglobal.com"
  api:
    python:
      base-url: https://prod-python-api.centuroglobal.com
