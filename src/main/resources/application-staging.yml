authentication:
  jwt:
    secret: 2Jmma7ETKdKnT2oDiFzB
    temp-token-expiry-in-secs: 1800   # 30min token
    access-token-expiry-in-secs: 7200 # 2hour token

  # App-Cache Settings (Unit = Milliseconds)
  app-cache.settings:
    country-region: 600000
    expertise: 600000
    stripe-product: 600000

  verification:
    token-expiry-in-days: 7
    cleanup-after-expiry-in-days: 30
    cron-schedule: '0/10 * * * * *'

  subscription:
    cron-schedule: '0/10 * * * * *'
    expiry-days: 7

  lead-notification:
    cleanup-after-sent-in-days: 10
    cron-schedule: '0/10 * * * * *'
    max-email-per-cron: 20
    centuro-address: '<EMAIL>'

  concierge-notification:
    cleanup-after-sent-in-days: 10
    cron-schedule: '0/10 * * * * *'
    max-email-per-cron: 20
    contact-address: '<EMAIL>'

  completed-case-archive:
    cron-schedule: "0 0 1 * * *"
    diff-days: 7

  case-fees-reminder-email:
    cron-schedule: "0 0 2 * * *"

  case-status-reminder-email:
    cron-schedule: "0 30 8 * * *"
    start-date: 2023-01-10 00:00

  case-visa-expiry-reminder-email:
    cron-schedule: "0 0 3 * * *"

  scheduler:
    task:
      pool-size: 5