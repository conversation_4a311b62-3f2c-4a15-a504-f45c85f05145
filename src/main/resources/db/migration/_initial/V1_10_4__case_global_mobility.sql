CREATE TABLE IF NOT EXISTS `global_mobility` (
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `global_mobility_country` varchar(255) NOT NULL,
  `relocating_country` varchar(255) NOT NULL,
  `agree_term_of_use` bit(1) NOT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf7o9` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

insert into case_category (id, name)
  VALUES
('GLOBAL_MOBILITY', 'Global Mobility');