CREATE TABLE IF NOT EXISTS `country_indices_category` (
  `category` varchar(255) DEFAULT NULL,
  `category_code` varchar(255) NOT NULL,
  PRIMARY KEY (`category_code`)
);

INSERT INTO country_indices_category(category, category_code) VALUES
 ('Cost of Living Index','costOfLivingIndex'),
 ('Quality of Life Index','qualityOfLifeIndex'),
 ('Crime Index','crimeIndex'),
 ('Health Care Index','healthCareIndex'),
 ('Purchasing Power Incl Rent Index','purchasingPowerInclRentIndex'),
 ('Pollution Index','pollutionIndex'),
 ('Safety Index','safetyIndex'),
 ('Property Price to Income Ratio','propertyPriceToIncomeRatio');
