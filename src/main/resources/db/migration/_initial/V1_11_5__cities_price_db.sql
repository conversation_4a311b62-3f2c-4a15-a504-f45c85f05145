CREATE TABLE IF NOT EXISTS `city_price` (
    `id` bigint(11) NOT NULL AUTO_INCREMENT,
    `country_code` varchar(255) DEFAULT NULL,
    `country` varchar(255) DEFAULT NULL,
    `city` varchar(255) DEFAULT NULL,
    `city_id` bigint(5) DEFAULT NULL,
    `category` VARCHAR(255)  DEFAULT NULL,
    `item_id` bigint(11)  DEFAULT NULL,
    `item_name` VARCHAR(255)  DEFAULT NULL,
    `lowest_price` DOUBLE  DEFAULT NULL,
    `average_price` DOUBLE  DEFAULT NULL,
    `highest_price` DOUBLE  DEFAULT NULL,
    `month_last_update` bigint(5) DEFAULT NULL,
    `year_last_update` bigint(5) DEFAULT NULL,
    `created_date` datetime(6) DEFAULT NULL,
    `last_updated_date` datetime(6) DEFAULT NULL,
    PRIMARY KEY (`id`)
);