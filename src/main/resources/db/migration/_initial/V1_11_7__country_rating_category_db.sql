CREATE TABLE IF NOT EXISTS `country_rating_category` (
  `category` varchar(255) DEFAULT NULL,
  `category_code` varchar(255) NOT NULL,
  PRIMARY KEY (`category_code`)
);

INSERT INTO country_rating_category(category, category_code) VALUES
 ('Overall','overallRating'),
 ('Entrepreneurship','entrepreneurshipRating'),
 ('Open for Business','openForBusinessRating'),
 ('Quality Of Life','qualityOfLifeRating'),
 ('Social Purpose','socialPurposeRating'),
 ('Global Travel Risk','globalTravelRiskRating'),
 ('GDP','gdpRating'),
 ('Economic Freedom','economicFreedomRating'),
 ('Highest Inflation Rate','highestInflationRateRating'),
 ('Lowest Inflation Rate','lowestInflationRateRating');
