CREATE TABLE IF NOT EXISTS `country_tax` (
`id` bigint(11) NOT NULL AUTO_INCREMENT,
  `corporate_tax_rate` bigint(11)   DEFAULT NULL,
  `employer_tax_rate`  bigint(11)   DEFAULT NULL,
  `employee_tax_rate`  bigint(11)   DEFAULT NULL,
  `info_text`         VARCHAR(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `country_code` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO country_tax(country_code,country,corporate_tax_rate,employer_tax_rate,employee_tax_rate,info_text) VALUES
    ('AF','Afghanistan',20,50,20,'lorem ipsum'),
    ('AL','Albania',15,15,9.5,'lorem ipsum'),
    ('DZ','Algeria',26,26,9,'lorem ipsum'),
    ('AD','Andorra',10,NULL,NULL,'lorem ipsum'),
    ('AO','Angola',25,8,3,'lorem ipsum'),
    ('AI','Anguilla',0,5,5,'lorem ipsum'),
    ('AG','Antigua and Barbuda',25,8,6,'lorem ipsum'),
    ('AR','Argentina',25,20.4,17,'lorem ipsum'),
    ('AM','Armenia',18,0,0,'lorem ipsum'),
    ('AW','Aruba',25,11,5,'lorem ipsum'),
    ('AU','Australia',30,NULL,0,'lorem ipsum'),
    ('AT','Austria',25,21.38,18.12,'lorem ipsum'),
    ('AZ','Azerbaijan',20,NULL,NULL,'lorem ipsum'),
    ('BS','Bahamas',0,5.9,3.9,'lorem ipsum'),
    ('BH','Bahrain',0,NULL,NULL,'lorem ipsum'),
    ('BD','Bangladesh',32.5,0,0,'lorem ipsum'),
    ('BB','Barbados',5.5,12.75,11.1,'lorem ipsum'),
    ('BY','Belarus',18,34,1,'lorem ipsum'),
    ('BE','Belgium',25,25,13.07,'lorem ipsum'),
    ('BZ','Belize',NULL,NULL,NULL,'lorem ipsum'),
    ('BJ','Benin',30,NULL,NULL,'lorem ipsum'),
    ('BM','Bermuda',0,0,0,'lorem ipsum'),
    ('BT','Bhutan',NULL,NULL,NULL,'lorem ipsum'),
    ('BO','Bolivia',25,16.71,12.71,'lorem ipsum'),
    ('BA','Bosnia and Herzegovina',10,10.5,31,'lorem ipsum'),
    ('BW','Botswana',22,0,0,'lorem ipsum'),
    ('BR','Brazil',34,31.7,14,'lorem ipsum'),
    ('IO','British Indian Ocean Territory',NULL,NULL,NULL,'lorem ipsum'),
    ('BN','Brunei',18.5,8.5,8.5,'lorem ipsum'),
    ('BG','Bulgaria',10,19.02,13.78,'lorem ipsum'),
    ('BF','Burkina Faso',28,NULL,NULL,'lorem ipsum'),
    ('BI','Burundi',30,NULL,NULL,'lorem ipsum'),
    ('KH','Cambodia',20,3.4,3.4,'lorem ipsum'),
    ('CM','Cameroon',33,NULL,NULL,'lorem ipsum'),
    ('CA','Canada',26.5,7.66,6.72,'lorem ipsum'),
    ('CV','Cape Verde',NULL,NULL,NULL,'lorem ipsum'),
    ('KY','Cayman Islands',0,0,0,'lorem ipsum'),
    ('CF','Central African Republic',NULL,NULL,NULL,'lorem ipsum'),
    ('TD','Chad',35,NULL,NULL,'lorem ipsum'),
    ('CL','Chile',27,5,18.5,'lorem ipsum'),
    ('CN','China',25,28.52,10.5,'lorem ipsum'),
    ('CO','Colombia',31,21,10,'lorem ipsum'),
    ('CG','Congo',28,20.28,4,'lorem ipsum'),
    ('CK','Cook Islands',NULL,NULL,NULL,'lorem ipsum'),
    ('CR','Costa Rica',30,26.5,10.5,'lorem ipsum'),
    ('HR','Croatia',18,16.5,20,'lorem ipsum'),
    ('CU','Cuba',NULL,NULL,NULL,'lorem ipsum'),
    ('CY','Cyprus',12.5,8.3,8.3,'lorem ipsum'),
    ('CZ','Czech Republic',19,33.8,11,'lorem ipsum'),
    ('DK','Denmark',22,0,0,'lorem ipsum'),
    ('DJ','Djibouti',25,NULL,NULL,'lorem ipsum'),
    ('DM','Dominica',25,7,6,'lorem ipsum'),
    ('DO','Dominican Republic',27,15.5,6,'lorem ipsum'),
    ('EC','Ecuador',25,12.15,9.45,'lorem ipsum'),
    ('EG','Egypt',22.5,26,14,'lorem ipsum'),
    ('SV','El Salvador',30,7.5,3,'lorem ipsum'),
    ('GQ','Equatorial Guinea',35,NULL,NULL,'lorem ipsum'),
    ('ER','Eritrea',NULL,NULL,NULL,'lorem ipsum'),
    ('EE','Estonia',20,33.8,3.6,'lorem ipsum'),
    ('ET','Ethiopia',30,11,7,'lorem ipsum'),
    ('FO','Faroe Islands',NULL,NULL,NULL,'lorem ipsum'),
    ('FJ','Fiji',20,5,5,'lorem ipsum'),
    ('FI','Finland',20,20.66,10.89,'lorem ipsum'),
    ('FR','France',26.5,45,23,'lorem ipsum'),
    ('PF','French Polynesia',NULL,NULL,NULL,'lorem ipsum'),
    ('GA','Gabon',30,NULL,NULL,'lorem ipsum'),
    ('GM','Gambia',27,NULL,NULL,'lorem ipsum'),
    ('GE','Georgia',15,2,2,'lorem ipsum'),
    ('DE','Germany',30,19.98,20.23,'lorem ipsum'),
    ('GH','Ghana',25,13,5.5,'lorem ipsum'),
    ('GI','Gibraltar',10,20,10,'lorem ipsum'),
    ('GR','Greece',24,22.54,14.12,'lorem ipsum'),
    ('GL','Greenland',NULL,NULL,NULL,'lorem ipsum'),
    ('GD','Grenada',28,6,5,'lorem ipsum'),
    ('GP','Guadeloupe',NULL,NULL,NULL,'lorem ipsum'),
    ('GU','Guam',NULL,NULL,NULL,'lorem ipsum'),
    ('GT','Guatemala',25,12.67,4.83,'lorem ipsum'),
    ('GG','Guernsey',0,6.6,6.6,'lorem ipsum'),
    ('GN','Guinea',NULL,NULL,NULL,'lorem ipsum'),
    ('GW','Guinea-Bissau',NULL,NULL,NULL,'lorem ipsum'),
    ('GY','Guyana',NULL,NULL,NULL,'lorem ipsum'),
    ('HT','Haiti',NULL,NULL,NULL,'lorem ipsum'),
    ('HN','Honduras',25,10.2,6.5,'lorem ipsum'),
    ('HK','Hong Kong',16.5,0,0,'lorem ipsum'),
    ('HU','Hungary',9,17,18.5,'lorem ipsum'),
    ('IS','Iceland',20,6.1,0,'lorem ipsum'),
    ('IN','India',30,12,12,'lorem ipsum'),
    ('ID','Indonesia',22,5.74,2,'lorem ipsum'),
    ('IR','Iran',NULL,NULL,NULL,'lorem ipsum'),
    ('IQ','Iraq',35,12,5,'lorem ipsum'),
    ('IE','Ireland',12.5,11.05,4,'lorem ipsum'),
    ('IM','Isle of Man',0,12.8,11,'lorem ipsum'),
    ('IL','Israel',23,7.6,12,'lorem ipsum'),
    ('IT','Italy',24,30,9.49,'lorem ipsum'),
    ('CI','Ivory Coast',25,NULL,NULL,'lorem ipsum'),
    ('JM','Jamaica',25,12.25,7.25,'lorem ipsum'),
    ('JP','Japan',30.62,16.24,15.28,'lorem ipsum'),
    ('JE','Jersey',0,6.5,6,'lorem ipsum'),
    ('JO','Jordan',20,14.25,7.5,'lorem ipsum'),
    ('KZ','Kazakhstan',20,3.5,2,'lorem ipsum'),
    ('KE','Kenya',30,0,0,'lorem ipsum'),
    ('KW','Kuwait',15,11.5,10.5,'lorem ipsum'),
    ('KG','Kyrgyzstan',10,NULL,NULL,'lorem ipsum'),
    ('LA','Laos',20,NULL,NULL,'lorem ipsum'),
    ('LV','Latvia',20,23.59,10.5,'lorem ipsum'),
    ('LB','Lebanon',17,22.5,3,'lorem ipsum'),
    ('LR','Liberia',NULL,NULL,NULL,'lorem ipsum'),
    ('LY','Libya',20,NULL,NULL,'lorem ipsum'),
    ('LI','Liechtenstein',12.5,NULL,NULL,'lorem ipsum'),
    ('LT','Lithuania',15,1.77,19.5,'lorem ipsum'),
    ('LU','Luxembourg',24.94,15.17,12.45,'lorem ipsum'),
    ('MO','Macao',12,0,0,'lorem ipsum'),
    ('MG','Madagascar',20,NULL,NULL,'lorem ipsum'),
    ('MW','Malawi',30,0,0,'lorem ipsum'),
    ('MY','Malaysia',24,12,9,'lorem ipsum'),
    ('MV','Maldives',NULL,NULL,NULL,'lorem ipsum'),
    ('ML','Mali',NULL,NULL,NULL,'lorem ipsum'),
    ('MT','Malta',35,10,10,'lorem ipsum'),
    ('MH','Marshall Islands',NULL,NULL,NULL,'lorem ipsum'),
    ('MQ','Martinique',NULL,NULL,NULL,'lorem ipsum'),
    ('MR','Mauritania',25,20,5,'lorem ipsum'),
    ('MU','Mauritius',15,6,3,'lorem ipsum'),
    ('MX','Mexico',30,51.15,2.78,'lorem ipsum'),
    ('MD','Moldova',12,24,9,'lorem ipsum'),
    ('MC','Monaco',33,27.27,8.95,'lorem ipsum'),
    ('MN','Mongolia',25,15.5,12.5,'lorem ipsum'),
    ('ME','Montenegro',9,8.3,24,'lorem ipsum'),
    ('MS','Montserrat',NULL,NULL,NULL,'lorem ipsum'),
    ('MA','Morocco',31,21.09,6.74,'lorem ipsum'),
    ('MZ','Mozambique',32,4,3,'lorem ipsum'),
    ('MM','Myanmar',25,3,2,'lorem ipsum'),
    ('NA','Namibia',32,0.9,0.9,'lorem ipsum'),
    ('NP','Nepal',NULL,NULL,NULL,'lorem ipsum'),
    ('NL','Netherlands',25,23.59,27.65,'lorem ipsum'),
    ('NC','New Caledonia',NULL,NULL,NULL,'lorem ipsum'),
    ('NZ','New Zealand',28,NULL,0,'lorem ipsum'),
    ('NI','Nicaragua',30,22.5,7,'lorem ipsum'),
    ('NE','Niger',NULL,NULL,NULL,'lorem ipsum'),
    ('NG','Nigeria',30,10,8,'lorem ipsum'),
    ('NU','Niue',NULL,NULL,NULL,'lorem ipsum'),
    ('NF','Norfolk Island',NULL,NULL,NULL,'lorem ipsum'),
    ('KP','North Korea',NULL,NULL,NULL,'lorem ipsum'),
    ('MK','North Macedonia',NULL,NULL,NULL,'lorem ipsum'),
    ('NO','Norway',22,14.1,8.2,'lorem ipsum'),
    ('OM','Oman',15,11.5,7,'lorem ipsum'),
    ('PK','Pakistan',29,NULL,NULL,'lorem ipsum'),
    ('PS','Palestine',15,NULL,NULL,'lorem ipsum'),
    ('PA','Panama',25,12.25,9.75,'lorem ipsum'),
    ('PG','Papua New Guinea',30,NULL,NULL,'lorem ipsum'),
    ('PY','Paraguay',10,NULL,NULL,'lorem ipsum'),
    ('PE','Peru',29.5,9,13,'lorem ipsum'),
    ('PH','Philippines',30,8.5,4.5,'lorem ipsum'),
    ('PL','Poland',19,22.14,13.71,'lorem ipsum'),
    ('PT','Portugal',21,23.75,11,'lorem ipsum'),
    ('PR','Puerto Rico',18.5,7.65,7.65,'lorem ipsum'),
    ('QA','Qatar',10,0,0,'lorem ipsum'),
    ('RE','Reunion',26.5,NULL,NULL,'lorem ipsum'),
    ('RO','Romania',16,2.25,35,'lorem ipsum'),
    ('RU','Russia',20,30,0,'lorem ipsum'),
    ('RW','Rwanda',30,NULL,NULL,'lorem ipsum'),
    ('SH','Saint Helena',NULL,NULL,NULL,'lorem ipsum'),
    ('KN','Saint Kitts and Nevis',33,NULL,5,'lorem ipsum'),
    ('LC','Saint Lucia',30,5,5,'lorem ipsum'),
    ('PM','Saint Pierre and Miquelon',NULL,NULL,NULL,'lorem ipsum'),
    ('VC','Saint Vincent and the Grenadines',30,5.5,4.5,'lorem ipsum'),
    ('WS','Samoa',27,5,5,'lorem ipsum'),
    ('SM','San Marino',NULL,NULL,NULL,'lorem ipsum'),
    ('SA','Saudi Arabia',20,12,10,'lorem ipsum'),
    ('SN','Senegal',30,24,11,'lorem ipsum'),
    ('RS','Serbia',15,16.65,19.9,'lorem ipsum'),
    ('SC','Seychelles',30,NULL,NULL,'lorem ipsum'),
    ('SL','Sierra Leone',30,10,5,'lorem ipsum'),
    ('SG','Singapore',17,17,20,'lorem ipsum'),
    ('SK','Slovakia',21,16.1,13.4,'lorem ipsum'),
    ('SI','Slovenia',19,NULL,22.1,'lorem ipsum'),
    ('SB','Solomon Islands',30,NULL,NULL,'lorem ipsum'),
    ('SO','Somalia',NULL,NULL,NULL,'lorem ipsum'),
    ('ZA','South Africa',28,0,0,'lorem ipsum'),
    ('KR','South Korea',NULL,NULL,NULL,'lorem ipsum'),
    ('ES','Spain',25,29.9,6.35,'lorem ipsum'),
    ('LK','Sri Lanka',24,12,8,'lorem ipsum'),
    ('SD','Sudan',35,17,8,'lorem ipsum'),
    ('SR','Suriname',36,9,6,'lorem ipsum'),
    ('SZ','Swaziland',27.5,0,0,'lorem ipsum'),
    ('SE','Sweden',20.6,31.42,7,'lorem ipsum'),
    ('CH','Switzerland',14.93,6.4,6.4,'lorem ipsum'),
    ('SY','Syria',28,17.1,7,'lorem ipsum'),
    ('TW','Taiwan',20,18.25,3.65,'lorem ipsum'),
    ('TJ','Tajikistan',NULL,NULL,NULL,'lorem ipsum'),
    ('TZ','Tanzania',30,10,10,'lorem ipsum'),
    ('TH','Thailand',20,5,5,'lorem ipsum'),
    ('CD','The Democratic Republic of the Congo',NULL,NULL,NULL,'lorem ipsum'),
    ('TL','Timor-Leste',10,NULL,NULL,'lorem ipsum'),
    ('TG','Togo',27,NULL,NULL,'lorem ipsum'),
    ('TK','Tokelau',NULL,NULL,NULL,'lorem ipsum'),
    ('TO','Tonga',NULL,NULL,NULL,'lorem ipsum'),
    ('TT','Trinidad and Tobago',30,8.8,4.4,'lorem ipsum'),
    ('TN','Tunisia',15,16.57,9.18,'lorem ipsum'),
    ('TR','Turkey',20,22.5,15,'lorem ipsum'),
    ('TM','Turkmenistan',20,NULL,NULL,'lorem ipsum'),
    ('TC','Turks and Caicos Islands',0,NULL,NULL,'lorem ipsum'),
    ('TV','Tuvalu',NULL,NULL,NULL,'lorem ipsum'),
    ('UG','Uganda',30,10,5,'lorem ipsum'),
    ('UA','Ukraine',18,22,0,'lorem ipsum'),
    ('AE','United Arab Emirates',55,0,0,'lorem ipsum'),
    ('GB','United Kingdom',19,13.8,14,'lorem ipsum'),
    ('US','United States',27,7.65,7.65,'lorem ipsum'),
    ('UM','United States Minor Outlying Islands',NULL,NULL,NULL,'lorem ipsum'),
    ('UY','Uruguay',25,12.63,23.1,'lorem ipsum'),
    ('UZ','Uzbekistan',7.5,NULL,NULL,'lorem ipsum'),
    ('VU','Vanuatu',0,NULL,NULL,'lorem ipsum'),
    ('VA','Vatican City',NULL,NULL,NULL,'lorem ipsum'),
    ('VE','Venezuela',34,11,4,'lorem ipsum'),
    ('VN','Vietnam',20,21.5,10.5,'lorem ipsum'),
    ('VG','Virgin Islands, British',NULL,NULL,NULL,'lorem ipsum'),
    ('VI','Virgin Islands, U.S.',21,NULL,NULL,'lorem ipsum'),
    ('EH','Western Sahara',NULL,NULL,NULL,'lorem ipsum'),
    ('YE','Yemen',20,9,6,'lorem ipsum'),
    ('ZM','Zambia',35,5,5,'lorem ipsum'),
    ('ZW','Zimbabwe',24,3.5,3.5,'lorem ipsum');
