CREATE TABLE IF NOT EXISTS `immigration_category` (
    `id` bigint(11) NOT NULL AUTO_INCREMENT,
    `category` VARCHAR(255) DEFAULT NULL,
    `visa_code`  VARCHAR(255) DEFAULT NULL,
    `visa_display_name`  VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
);

INSERT INTO immigration_category(category,visa_code,visa_display_name) VALUES
    ('BUSINESS_VISA','ATTENDING_A_CONFERENCE_OR_SEMINAR','Attending a conference or seminar'),
    ('BUSINESS_VISA','FAMILIARISATION_ACTIVITIES','Familiarisation activities'),
    ('BUSINESS_VISA','INSTALLATION_OF_SOFTWARE','Installation of software'),
    ('BUSINESS_VISA','NEGOTIATE_CONTRACTS','Negotiate contracts'),
    ('BUSINESS_VISA','ATTENDING_MEETINGS','Attending meetings'),
    ('WORK_VISA','TRAINING','Training'),
    ('WORK_VISA','EMPLOYMENT','Employment'),
    ('WORK_VISA','CONSULTANCY','Consultancy'),
    ('WORK_VISA','TECH_COMPANY_SET_UP','Tech company set up'),
    ('WORK_VISA','PROJECT_RELATED_WORK','Project related work'),
    ('WORK_VISA','DESIGN_AND_DEVELOPMENT','Design and development'),
    ('WORK_VISA','PAID_SHORT_TERM_ENGAGEMENT','Paid short term engagement');
