ALTER TABLE bank_account MODIFY trading bit(1) DEFAULT false;
ALTER TABLE bank_account MODIFY business_activity VARCHAR(255) DEFAULT NULL;
ALTER TABLE bank_account MODIFY address VARCHAR(255) DEFAULT NULL;
ALTER TABLE bank_account MODIFY full_time bigint(20) DEFAULT NULL;
ALTER TABLE bank_account MODIFY part_time bigint(20) DEFAULT NULL;
ALTER TABLE bank_account MODIFY non_contract_workers bigint(20) DEFAULT NULL;
ALTER TABLE bank_account MODIFY expected_turnover VARCHAR(255) DEFAULT NULL;
ALTER TABLE bank_account MODIFY tax_status VARCHAR(255) DEFAULT NULL;
ALTER TABLE bank_account MODIFY income_come VARCHAR(255) DEFAULT NULL;

ALTER TABLE office_space MODIFY assistance_finding_office bit(1) DEFAULT false;
ALTER TABLE office_space MODIFY contract VARCHAR(255) DEFAULT NULL;
ALTER TABLE office_space MODIFY space_type VARCHAR(255) DEFAULT NULL;
ALTER TABLE office_space MODIFY seating_space bigint(20) DEFAULT NULL;

ALTER TABLE assignee_cases MODIFY accommodation_support bit(1) DEFAULT false;
ALTER TABLE assignee_cases MODIFY shipping_support bit(1) DEFAULT false;
ALTER TABLE assignee_cases MODIFY education_support bit(1) DEFAULT false;
ALTER TABLE assignee_cases MODIFY assignees_pay_type varchar(255) DEFAULT NULL;
ALTER TABLE assignee_cases MODIFY tax_support bit(1) DEFAULT false;
ALTER TABLE assignee_cases MODIFY expert_tax_services varchar(255) DEFAULT NULL;

ALTER TABLE payroll_cases MODIFY entity_in_jurisdiction bit(1) DEFAULT false;
ALTER TABLE payroll_cases MODIFY staff_count bigint(20) DEFAULT NULL;
ALTER TABLE payroll_cases MODIFY staff_type varchar(255) DEFAULT NULL;
ALTER TABLE payroll_cases MODIFY salary_range varchar(255) DEFAULT NULL;
ALTER TABLE payroll_cases MODIFY benefits_support bit(1) DEFAULT false;
ALTER TABLE payroll_cases MODIFY employment_support bit(1) DEFAULT false;

ALTER TABLE hr_and_employment_support MODIFY entity_in_jurisdiction bit(1) DEFAULT false;
ALTER TABLE hr_and_employment_support MODIFY contracts_support bit(1) DEFAULT false;
ALTER TABLE hr_and_employment_support MODIFY policy_support bit(1) DEFAULT false;
ALTER TABLE hr_and_employment_support MODIFY liability_insurance bit(1) DEFAULT false;
ALTER TABLE hr_and_employment_support MODIFY employment_support bit(1) DEFAULT false;

ALTER TABLE intellectual_property MODIFY ip_support varchar(255) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY interested_ip varchar(500) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY protect_word_logo varchar(255) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY trademark_name varchar(255) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY copyright_nature varchar(255) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY copyright_details varchar(1000) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY patent_description varchar(1000) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY design_description varchar(1000) DEFAULT NULL;
ALTER TABLE intellectual_property MODIFY ip_assign_support bit(1) DEFAULT false;
ALTER TABLE intellectual_property MODIFY ip_dispute_support bit(1) DEFAULT false;

ALTER TABLE single_immigration_visa MODIFY visa_type varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY from_country varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY trip_purpose varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY middle_name varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY covid_vaccinated varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY dependant_travelling varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY applicant_stay bigint(20) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY estimated_salary DOUBLE DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY paid_country varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY entity_in_host_country bit(1) DEFAULT false;
ALTER TABLE single_immigration_visa MODIFY duties varchar(255) DEFAULT NULL;
ALTER TABLE single_immigration_visa MODIFY qualification varchar(255) DEFAULT NULL;

ALTER TABLE multiple_immigration_visa MODIFY visa_type varchar(255) DEFAULT NULL;
ALTER TABLE multiple_immigration_visa MODIFY number_visa_permit bigint(20) DEFAULT NULL;

ALTER TABLE traveller MODIFY estimated_salary DOUBLE DEFAULT NULL;
ALTER TABLE traveller MODIFY duration_of_stay bigint(20) DEFAULT NULL;
ALTER TABLE traveller MODIFY duties varchar(255) DEFAULT NULL;
ALTER TABLE traveller MODIFY qualification varchar(255) DEFAULT NULL;
ALTER TABLE traveller MODIFY dependant_travelling varchar(255) DEFAULT NULL;
ALTER TABLE traveller MODIFY dependant_duration_of_stay varchar(255) DEFAULT NULL;

ALTER TABLE entity_setup MODIFY business_activities varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY hq_country varchar(3) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY hq_state varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY hq_city varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY hq_zipcode varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY hq_address varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY foreign_owned bit(1) DEFAULT false;
ALTER TABLE entity_setup MODIFY require_address bit(1) DEFAULT false;
ALTER TABLE entity_setup MODIFY support_tax_registration bit(1) DEFAULT false;
ALTER TABLE entity_setup MODIFY other_information varchar(1000) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY cs_full_Name varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY cs_dob varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY cs_occupation varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY cs_nationality varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY cs_residential_address varchar(255) DEFAULT NULL;
ALTER TABLE entity_setup MODIFY cs_service_address varchar(255) DEFAULT NULL;

ALTER TABLE director MODIFY dob varchar(255) DEFAULT NULL;
ALTER TABLE director MODIFY occupation varchar(255) DEFAULT NULL;
ALTER TABLE director MODIFY nationality varchar(255) DEFAULT NULL;
ALTER TABLE director MODIFY residential_address varchar(255) DEFAULT NULL;

ALTER TABLE shareholder MODIFY full_Name varchar(255) DEFAULT NULL;
ALTER TABLE shareholder MODIFY percentage_share DOUBLE DEFAULT NULL;
ALTER TABLE shareholder MODIFY service_address varchar(255) DEFAULT NULL;

ALTER TABLE data_protection MODIFY data_protection bit(1) DEFAULT false;
ALTER TABLE data_protection MODIFY data_officer bit(1) DEFAULT false;
ALTER TABLE data_protection MODIFY data_controller bit(1) DEFAULT false;
ALTER TABLE data_protection MODIFY data_compliance bit(1) DEFAULT false;
ALTER TABLE data_protection MODIFY data_policies bit(1) DEFAULT false;

ALTER TABLE commercial_support MODIFY legal_advice varchar(255) DEFAULT NULL;
ALTER TABLE commercial_support MODIFY current_contracts varchar(255) DEFAULT NULL;
ALTER TABLE commercial_support MODIFY purchase_contract varchar(255) DEFAULT NULL;
ALTER TABLE commercial_support MODIFY commercial_needs varchar(255) DEFAULT NULL;

ALTER TABLE risk_management MODIFY risk_policy varchar(255) DEFAULT NULL;
ALTER TABLE risk_management MODIFY last_policy_updated date DEFAULT NULL;
ALTER TABLE risk_management MODIFY identified_potential_risks varchar(255) DEFAULT NULL;
ALTER TABLE risk_management MODIFY potential_risks varchar(1000) DEFAULT NULL;
ALTER TABLE risk_management MODIFY strategy_advice varchar(255) DEFAULT NULL;
ALTER TABLE risk_management MODIFY insurance_for_jurisdiction varchar(255) DEFAULT NULL;

ALTER TABLE employee_benefits_and_insurance MODIFY employees_health_care varchar(255) DEFAULT NULL;
ALTER TABLE employee_benefits_and_insurance MODIFY existing_insurance varchar(255) DEFAULT NULL;
ALTER TABLE employee_benefits_and_insurance MODIFY insurance_partner varchar(255) DEFAULT NULL;
ALTER TABLE employee_benefits_and_insurance MODIFY local_insurance varchar(255) DEFAULT NULL;
ALTER TABLE employee_benefits_and_insurance MODIFY physical_products varchar(255) DEFAULT NULL;
ALTER TABLE employee_benefits_and_insurance MODIFY physical_office varchar(255) DEFAULT NULL;

ALTER TABLE tax_support MODIFY tax_support varchar(255) DEFAULT NULL;
ALTER TABLE tax_support MODIFY estimated_revenue varchar(255) DEFAULT NULL;
ALTER TABLE tax_support MODIFY payroll varchar(255) DEFAULT NULL;
ALTER TABLE tax_support MODIFY employee_benefits varchar(2000) DEFAULT NULL;
ALTER TABLE tax_support MODIFY benefits_taxable varchar(255) DEFAULT NULL;