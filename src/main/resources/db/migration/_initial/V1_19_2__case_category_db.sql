
ALTER TABLE cases DROP CONSTRAINT FKreu6oq7v0sr3m0buw7d9vba7h;

drop table if exists case_category;

CREATE TABLE IF NOT EXISTS `case_category` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `sub_category_id` varchar(255) DEFAULT NULL,
  `sub_category_name` varchar(255) DEFAULT NULL,
  `parent_category_id` varchar(255) DEFAULT NULL,
  `parent_category_name` varchar(255) DEFAULT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_updated_date` datetime(6) DEFAULT NULL,
   PRIMARY KEY (`id`)
);


insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES
  ('BUSINESS_INCORPORATION', 'Business Incorporation', 'START_A_COMPANY', 'Start A Company'),
  ('ENTITY_SETUP', 'Entity Setup', 'START_A_COMPANY', 'Start A Company'),
  ('BANK_ACCOUNT', 'Bank Account', 'START_A_COMPANY', 'Start A Company'),
  ('OFFICE_SPACE', 'Office Space', 'START_A_COMPANY', 'Start A Company'),
  ('COMPLEX_REQUIREMENT', 'Complex Requirement', 'START_A_COMPANY', 'Start A Company'),

  ('IMMIGRATION', 'Immigration', 'IMMIGRATION_GM', 'Immigration GM'),
  ('SINGLE_VISA', 'Apply For A Single Visa', 'IMMIGRATION_GM', 'Immigration GM'),
  ('MULTIPLE_VISA', 'Apple For Multiple Visa', 'IMMIGRATION_GM', 'Immigration GM'),
  ('MANAGE_ASSIGNESS', 'Manage Assigness', 'IMMIGRATION_GM', 'Immigration GM'),
  ('COMPLEX_REQUIREMENT', 'Complex Requirement', 'IMMIGRATION_GM', 'Immigration GM'),

  ('MANAGE_PAYROLL', 'Manage Payroll', 'HR_PAYROLL', 'HR Payroll'),
  ('HR_EMP_SUPPORT', 'Hr & Employment Support', 'HR_PAYROLL', 'HR Payroll'),
  ('COMPLEX_REQUIREMENT', 'Complex Requirement', 'HR_PAYROLL', 'HR Payroll'),

  ('INSURANCE', 'Insurance', 'INSURANCE', 'Insurance'),
  ('COMPLEX_REQUIREMENT', 'Complex Requirement', 'INSURANCE', 'Insurance'),

  ('LEGAL', 'Legal', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('INTELLECTUAL_PROPERTY', 'Intellectual Property', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('DATA_PROTECTION', 'Data Protection', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('COM_LAW_CONTRACTS', 'Commercial Support', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('EMP_BENEFITS_INSURANCE', 'Employee Benefits & Insurance', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('COMPLEX_REQUIREMENT', 'Complex Requirement', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('RISK_MANAGEMENT', 'Risk Management', 'LEGAL_COMPLIANCE', 'Legal Compliance'),
  ('INSURANCE', 'Insurance', 'LEGAL_COMPLIANCE', 'Legal Compliance'),

  ('TAX_SUPPORT', 'Tax Support', 'TAX_ACCOUNTING', 'Tax Accounting'),
  ('COMPLEX_REQUIREMENT', 'Complex Requirement', 'TAX_ACCOUNTING', 'Tax Accounting');

update cases set category_id=6 where category_id='IMMIGRATION';

update cases set category_id=1 where category_id='BUSINESS_INCORPORATION';

update cases set category_id=16 where category_id='LEGAL';

ALTER TABLE cases CHANGE COLUMN `category_id` `category_id` bigint(20) DEFAULT NULL;
ALTER TABLE cases ADD FOREIGN KEY (category_id) REFERENCES case_category(id);
ALTER TABLE cases ADD parent_category_id varchar(255) DEFAULT NULL;