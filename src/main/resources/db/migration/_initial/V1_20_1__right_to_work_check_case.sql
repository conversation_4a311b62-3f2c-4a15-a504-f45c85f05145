CREATE TABLE IF NOT EXISTS `right_to_work_check` (
  `nationality` varchar(255) NOT NULL,
  `case_country` varchar(3) NOT NULL,
  `from_country` varchar(3) DEFAULT NULL,
  `time_to_work_left` varchar(255) DEFAULT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykagtpj6t5iib98je9e4hf724` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `applicant_info` (
  `first_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) NOT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `share_applicant_info` bit(1) DEFAULT NULL,
  `contact_no` varchar(255) DEFAULT NULL,
  `email_address` varchar(255) DEFAULT NULL,
  `case_id` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`case_id`) REFERENCES `right_to_work_check` (`id`)
);

insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('RIGHT_TO_WORK_CHECK', 'Right To Work Check', 'IMMIGRATION_GM', 'Immigration GM');