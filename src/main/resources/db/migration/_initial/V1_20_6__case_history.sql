CREATE TABLE `case_history` (
   `id` bigint(11) NOT NULL AUTO_INCREMENT,
   `status` varchar(100) NOT NULL,
   `status_update` varchar(4000) DEFAULT NULL,
   `created_date` datetime(3) NOT NULL,
   `last_updated_date` datetime(3) NOT NULL,
   `last_updated_by` bigint(11) NOT NULL,
   `case_id` bigint(11) NOT NULL,
   PRIMARY KEY (`id`),
   FOREIGN KEY (`case_id`) REFERENCES `cases`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;