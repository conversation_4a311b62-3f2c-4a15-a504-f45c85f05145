ALTER TABLE cases ADD case_fees_comments  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD case_fees_approver_emails  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD case_fees_need_approval bit(1) default false;
ALTER TABLE cases ADD case_fees_is_approved bit(1) default false;

ALTER TABLE cases ADD details_professional_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_government_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_apostille_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_certificate_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_translation_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_third_party_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_total_fees  varchar(100) DEFAULT NULL;
ALTER TABLE cases ADD details_income_raised bit(1) default false;
ALTER TABLE cases ADD details_income_paid bit(1) default false;

ALTER TABLE cases ADD approver_first_name  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD approver_last_name  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD approver_job_title  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD approver_email  varchar(255) DEFAULT NULL;
ALTER TABLE cases ADD approver_fees_agreed_date datetime(6) DEFAULT NULL;