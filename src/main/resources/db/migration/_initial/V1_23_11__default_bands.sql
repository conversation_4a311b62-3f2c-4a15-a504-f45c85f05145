INSERT INTO `visibility_master` VALUES(8, 'USER', 'OWN');

INSERT INTO corporate
         (id, name, country_code, status, subscription_active, root_user_id, created_date, last_updated_date, last_updated_by)
         VALUES(-1, 'Dummy corporate', 'US', 'SUSPENDED', FALSE, -1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, -1);


INSERT INTO `bands`(id, name, description, color, status, corporate_id, created_date) VALUES
    (1, 'L1', '(Single User) Company Admin', 'PURPLE', 'ACTIVE', -1, current_timestamp);

INSERT INTO `bands`(id, name, description, color, status, corporate_id, created_date) VALUES
    (2, 'L1(Free)', '(Single User) Company Admin', 'ORANGE', 'ACTIVE', -1, current_timestamp);

INSERT INTO `bands`(id, name, description, color, status, corporate_id, created_date) VALUES
    (3, 'L2', '(Single User) Company Admin', 'PINK', 'ACTIVE', -1, current_timestamp);

INSERT INTO `bands`(id, name, description, color, status, corporate_id, created_date) VALUES
    (4, 'L3', '(Single User) Company Admin', 'BLUE', 'ACTIVE', -1, current_timestamp);

-- CASES
-- Initiate case
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 4);
INSERT INTO `band_details`(band_id, access_id) VALUES(2, 4);
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 4);
INSERT INTO `band_details`(band_id, access_id) VALUES(4, 4);
-- FEE approval
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 7);
-- Band L1(Free)
INSERT INTO `band_details`(band_id, access_id) VALUES(2, 7);

-- USERS
-- View users
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 9);
-- Band L2
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 9);

-- Add Users
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 10);
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 10);

-- Edit & Delete Users
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 11);
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 11);

-- User accesses
-- Case
INSERT INTO `band_details`(band_id, visibility_id) VALUES(1, 1);
-- access to all users data
INSERT INTO `band_details`(band_id, visibility_id) VALUES(1, 4);
-- access to reportees
INSERT INTO `band_details`(band_id, visibility_id) VALUES(3, 2);

-- View Accounts
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 12);

-- Add Accounts
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 13);

-- Edit & Delete Accounts
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 14);

-- DASHBOARD
-- admin dashboard
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 1);
-- Band L1(free)
INSERT INTO `band_details`(band_id, access_id) VALUES(2, 1);
-- Band L2
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 1);
-- Employee dashboard
-- Band L3
INSERT INTO `band_details`(band_id, access_id) VALUES(4, 2);

-- COMPANY_INFO
-- View company info
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 26);
-- Band L1(free)
INSERT INTO `band_details`(band_id, access_id) VALUES(2, 26);
-- Band L2
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 26);

-- EDIT company info
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 27);
-- Band L1(free)
INSERT INTO `band_details`(band_id, access_id) VALUES(2, 27);

-- BLUEPRINTS
-- All countries
-- Band L1
INSERT INTO `band_details`(band_id, visibility_id) VALUES(1, 6);
-- Band L2
INSERT INTO `band_details`(band_id, visibility_id) VALUES(3, 6);
INSERT INTO `band_details`(band_id, visibility_id) VALUES(3, 5);
INSERT INTO `band_details`(band_id, visibility_id) VALUES(4, 8);

-- Home countries
-- Band L1(free)
INSERT INTO `band_details`(band_id, visibility_id) VALUES(2, 7);
INSERT INTO `band_details`(band_id, visibility_id) VALUES(2, 8);
-- Band L3
INSERT INTO `band_details`(band_id, visibility_id) VALUES(4, 7);

-- TOOLS
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 28);
-- Band L2
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 28);

-- CONCIERGE
-- Band L1
INSERT INTO `band_details`(band_id, access_id) VALUES(1, 29);
-- Band L1(Free)
INSERT INTO `band_details`(band_id, access_id) VALUES(2, 29);
-- Band L2
INSERT INTO `band_details`(band_id, access_id) VALUES(3, 29);
