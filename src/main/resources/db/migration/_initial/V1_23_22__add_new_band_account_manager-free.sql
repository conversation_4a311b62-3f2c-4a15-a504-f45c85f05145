INSERT INTO `bands`(id, name, description, color, status, corporate_id, created_date) VALUES
    (5, 'L4', 'Account Manager (free)', 'PURPLE', 'ACTIVE', -1, current_timestamp);

-- CASES
-- Initiate case
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 4);
-- FEE approval
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 7);


-- View Accounts
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 12);

-- Add Accounts
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 13);

-- Edit & Delete Accounts
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 14);

-- DASHBOARD
-- admin dashboard
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 1);

-- Employee dashboard
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 2);

-- COMPANY_INFO
-- View company info
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 26);

-- EDIT company info
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 27);

-- Home countries
INSERT INTO `band_details`(band_id, visibility_id) VALUES(5, 7);
INSERT INTO `band_details`(band_id, visibility_id) VALUES(5, 8);

-- TOOLS
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 28);

-- CONCIERGE
INSERT INTO `band_details`(band_id, access_id) VALUES(5, 29);