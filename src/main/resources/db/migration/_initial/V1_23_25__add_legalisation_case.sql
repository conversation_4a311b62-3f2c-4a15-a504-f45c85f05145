CREATE TABLE IF NOT EXISTS `legalisation` (
  `nationality` varchar(255) NULL,
  `legalised_country` varchar(255) NULL,
  `legalised_document` varchar(255) NULL,
  `other_documents` varchar(255) NULL,
  `first_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) NULL,
  `last_name` varchar(255) NOT NULL,
  `share_applicant_info` boolean NULL,
  `account_name` varchar(255) NULL,
  `contact_no` varchar(255) NULL,
  `email_address` varchar(255) NULL,
  `id` bigint(20),
  PRIMARY KEY (`id`),
  CONSTRAINT `FK2ykprfmjs6t5iib98je9e4hf717` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('LEGALISATION', 'Legalisation', 'IMMIGRATION_GM', 'Immigration GM');