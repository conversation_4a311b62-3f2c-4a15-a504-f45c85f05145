CREATE TABLE IF NOT EXISTS `company_tax_assessment` (
  `entity_type` varchar(255) NULL,
  `employee_type` varchar(255) NULL,
  `company_hq` varchar(255) NULL,
  `id` bigint(20),
  PRIMARY KEY (`id`),
  CONSTRAINT `FK6lpdkqx0q1x1psvkb91w91h2krj` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('COMP_TAX_ASS', 'Company Tax Assessment', 'TAX_ACCOUNTING', 'Tax & Accounting');

CREATE TABLE IF NOT EXISTS `individual_tax_assessment` (
  `company_entity` boolean NULL,
  `company_type` varchar(255) NULL,
  `employee_type` varchar(255) NULL,
  `employee_resident_type` varchar(255) NULL,
  `contract_with_organization` varchar(255) NULL,
  `employee_earn_income` varchar(255) NULL,
  `citizen_country` varchar(255) NULL,
  `permission_to_work` boolean NULL,
  `id` bigint(20),
  PRIMARY KEY (`id`),
  CONSTRAINT `FK3k4u6a7kskz4ld5gg3g3je40acl` FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('INDVL_TAX_ASS', 'Individual Tax Assessment', 'TAX_ACCOUNTING', 'Tax & Accounting');