CREATE TABLE `feature_master`(
    `id` bigint primary key auto_increment NOT NULL,
    `feature_key` varchar(255),
    `feature_value` varchar(255)
);

CREATE TABLE `micro_access_master`(
    `id` bigint primary key auto_increment NOT NULL,
    `feature_key` varchar(255),
    `access_level` varchar(255),
    `access_level_value` varchar(255)
);

CREATE TABLE `visibility_master`(
    `id` bigint primary key auto_increment NOT NULL,
    `feature_key` varchar(255),
    `visibility` varchar(255)
);

CREATE TABLE `bands`(
    `id` bigint primary key auto_increment NOT NULL,
    `name` varchar(255) NOT NULL,
    `description` varchar(255),
    `color` varchar(50),
    `status` varchar(100),
    `corporate_id` bigint,
    FOREIGN KEY(`corporate_id`) REFERENCES `corporate`(`id`)
 );

CREATE TABLE `band_details`(
    `id` bigint primary key auto_increment NOT NULL,
    `band_id` bigint,
    `access_id` bigint,
    `visibility_id` bigint,
    FOREI<PERSON>N KEY(`band_id`) REFERENCES `bands`(`id`),
    FOREIGN KEY(`access_id`) REFERENCES `micro_access_master`(`id`),
    FOREIGN KEY(`visibility_id`) REFERENCES `visibility_master`(`id`)
);

CREATE TABLE `corporate_user_accounts`(
    `id` bigint primary key auto_increment NOT NULL,
    `account_id` bigint,
    `user_id` bigint,
    FOREIGN KEY(`account_id`) REFERENCES `corporate_accounts`(`id`),
    FOREIGN KEY(`user_id`) REFERENCES `corporate_user`(`id`)
);

ALTER TABLE `corporate_user` ADD COLUMN band_id bigint, ADD CONSTRAINT FOREIGN KEY(`band_id`) REFERENCES `bands`(`id`);

/* -- Populate feature_master with initial features */
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('DASHBOARD', 'Dashboard');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('CASE', 'Cases');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('USER', 'Users');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('BLUEPRINT', 'Blueprints');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('TOOL', 'Tools');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('CONCIERGE', 'Concierge');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('EVENT', 'Events');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('COMPANY_INFO', 'Company Info');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('INVOICE', 'Invoices');
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('PROPOSAL', 'Proposals');

/* -- Populate micro_access_master with initial micro accesses */
/* ---- DASHBOARD */
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('DASHBOARD', 'ADMIN', 'Admin/Manager Dashboard');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('DASHBOARD', 'EMPLOYEE', 'Employee Dashboard');

/* ---- CASE */

INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'VIEW', 'View Cases');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'INITIATE', 'Initiate a Case');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'EDIT', 'Edit Case Details');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'UPLOAD', 'Upload Documents');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'VIEW_FEE', 'View Case Fee');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'INVITE', 'Invite to initiate a case');

/* ---- USER */
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'VIEW', 'View Users');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'ADD', 'Add & Upload Users');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'EDIT', 'Edit & Delete Users');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'VIEW_ACC', 'View Accounts');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'ADD_ACC', 'Add Account');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'EDIT_ACC', 'Edit Accounts');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'VIEW_BAND', 'View Bands');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'ADD_BAND', 'Add Band');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('USER', 'EDIT_BAND', 'Edit Bands');

/* ---- BLUEPRINT */
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'COUNTRY_FACT', 'Country Facts');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'BUSINESS_INC', 'Business Incorporation');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'TAX_ACC', 'Tax & Accounting');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'LEGAL', 'Legal');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'IMMIGRATION', 'Immigration');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'HR_PAYROLL', 'HR, Payroll & EOR');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'GLOBAL_MOBILITY', 'Global Mobility');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'INSURANCE', 'Insurance');
/* -- INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('BLUEPRINT', 'ALL', 'Select All');*/

/* ---- COMPANY_INFO */
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('COMPANY_INFO', 'VIEW', 'View Company Info');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('COMPANY_INFO', 'EDIT', 'Edit Company Info');

/* -- Populate visibility_master with initial micro accesses */
/* ---- CASE */
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('CASE', 'FULL');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('CASE', 'OWN');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('CASE', 'REPORTEES');

/* ---- USER */
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('USER', 'FULL');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('USER', 'REPORTEES');
