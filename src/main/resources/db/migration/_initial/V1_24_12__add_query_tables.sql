CREATE TABLE query(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    heading varchar(500) NOT NULL,
    country varchar(50),
    description varchar(500),
    status varchar(50),
    created_date DATETIME(3),
    last_updated_date DATETIME(3),
    created_by bigint(11) NOT NULL,
    FOREIGN KEY (`created_by`) REFERENCES `corporate_user` (`id`)
);

CREATE TABLE query_category(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name varchar(500) NOT NULL,
    query_id bigint(20),
    created_date  DATETIME(3),
    FOREIGN KEY (`query_id`) REFERENCES `query` (`id`)
);

CREATE TABLE query_proposal(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    file_name varchar(500) NOT NULL,
    file_type varchar(100),
    file_size bigint(20),
    file_upload_date DATETIME(3),
    query_id bigint(20),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`query_id`) REFERENCES `query` (`id`)
);

CREATE TABLE query_assignee(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    query_id bigint(20),
    user_id bigint(20),
    FOREIGN KEY (`query_id`) REFERENCES `query` (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `login_account` (`id`)
);

/*---- accesses for queries */
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('QUERY', 'Query');

INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('QUERY', 'INITIATE', 'Initiate a Query');

INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('QUERY', 'FULL');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('QUERY', 'OWN');
INSERT INTO `visibility_master`(`feature_key`, `visibility`) VALUES('QUERY', 'REPORTEES');

/*-- add Initiate Query access to all corporate bands */

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='QUERY' AND mam.access_level='INITIATE')
    FROM bands b
    WHERE b.name IN ('Account Manager', 'Applicant', 'Super Admin (free)', 'Super Admin');

/*-- add query visibilities */

/*--- Super admin will have full access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='QUERY' AND vm.visibility='FULL')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin');

/*---- Account manager will have reportees access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='QUERY' AND vm.visibility='REPORTEES')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager');

/*---- Applicant will have own access */
INSERT INTO band_details(band_id, visibility_id)
    SELECT b.id,
        (SELECT vm.id FROM visibility_master vm WHERE vm.feature_key='QUERY' AND vm.visibility='OWN')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');
