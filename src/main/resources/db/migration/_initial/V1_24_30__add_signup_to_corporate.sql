CREATE TABLE IF NOT EXISTS terms_condition(
`id` bigint(11) NOT NULL AUTO_INCREMENT,
`terms_and_conditions` varchar(255),
PRIMARY KEY (`id`)
);


ALTER TABLE corporate
ADD COLUMN `theme_primary_color` varchar(50),
ADD COLUMN `theme_secondary_color` varchar(50),
ADD COLUMN `company_logo_id` varchar(50),
ADD COLUMN `terms_conditions_id` bigint(11),
ADD FOREIGN KEY (terms_conditions_id) REFERENCES terms_condition(id);