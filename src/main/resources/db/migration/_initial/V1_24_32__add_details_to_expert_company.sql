CREATE TABLE IF NOT EXISTS expert_contracts(
`id` bigint(11) NOT NULL AUTO_INCREMENT,
`expert_company_id` bigint(11),
`contract_text` varchar(255),
`last_updated_date` DATETIME(3),
FOREI<PERSON><PERSON> KEY (expert_company_id) REFERENCES expert_company_profile(id),
PRIMARY KEY (`id`)
);

ALTER TABLE expert_company_profile
ADD COLUMN `expert_contract_id` bigint(11),
ADD COLUMN `profile_image` varchar(50),
ADD FOREIGN KEY (expert_contract_id) REFERENCES expert_contracts(id);

