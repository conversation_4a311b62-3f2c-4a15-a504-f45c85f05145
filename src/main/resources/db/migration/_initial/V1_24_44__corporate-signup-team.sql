create table corporate_team(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    user_id bigint(11) NOT NULL,
    corporate_id bigint(11) NOT NULL,
    designation varchar(255) NOT NULL,
    created_date  DATETIME(3) NOT NULL,
    last_updated_date  DATETIME(3),
    foreign key(corporate_id) references corporate(id)
);

create table onboarding_docs(
    id bigint(20)  NOT NULL AUTO_INCREMENT PRIMARY KEY,
    doc_name varchar(255) NOT NULL,
    doc_key varchar(255) NOT NULL,
    corporate_id bigint(11) NOT NULL,
    created_date  DATETIME(3) NOT NULL,
    last_updated_date  DATETIME(3),
    foreign key(corporate_id) references corporate(id)
);