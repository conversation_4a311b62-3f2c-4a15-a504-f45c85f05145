INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('DASHBOARD', 'VIEW_DOCUMENTS', 'View Documents');

ALTER TABLE `login_account` ADD COLUMN first_login_date DATETIME(3);

UPDATE `login_account` SET first_login_date = password_creation_date WHERE first_login_date IS NULL;

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
    (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='VIEW_DOCUMENTS' AND feature_key='DASHBOARD')
    FROM bands b WHERE b.name='Super Admin';

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
    (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='VIEW_DOCUMENTS' AND feature_key='DASHBOARD')
    FROM bands b WHERE b.name='Super Admin (free)';

