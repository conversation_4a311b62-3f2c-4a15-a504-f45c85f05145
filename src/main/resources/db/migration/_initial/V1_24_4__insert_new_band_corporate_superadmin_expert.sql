INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', 'MANAGE_CASE', 'Manage case');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('CASE', '<PERSON><PERSON>GE_FEE', 'Manage case fee');

INSERT INTO `bands`(name, description, color, status, corporate_id, created_date) VALUES
    ('Corporate Super Admin - expert', 'Corporate Super Admin - expert', 'PURPLE', 'ACTIVE', -1, current_timestamp);

-- Populate all accesses that superadmin band has.

INSERT INTO `band_details`(band_id, access_id,visibility_id)
SELECT (SELECT id FROM bands WHERE name='Corporate Super Admin - expert') AS band_id,
access_id,visibility_id FROM band_details WHERE band_id IN (SELECT id FROM bands WHERE name='Super Admin' and corporate_id=-1);

-- Add new EDIT, MANAGE_CASE, <PERSON><PERSON><PERSON>_FEE accesses to expert band

INSERT INTO `band_details`(band_id, access_id)
SELECT (SELECT id FROM bands WHERE name='Corporate Super Admin - expert') AS band_id,
id FROM micro_access_master WHERE access_level='EDIT' and feature_key='CASE';

INSERT INTO `band_details`(band_id, access_id)
SELECT (SELECT id FROM bands WHERE name='Corporate Super Admin - expert') AS band_id,
id FROM micro_access_master WHERE access_level='MANAGE_CASE';

INSERT INTO `band_details`(band_id, access_id)
SELECT (SELECT id FROM bands WHERE name='Corporate Super Admin - expert') AS band_id,
id FROM micro_access_master WHERE access_level='MANAGE_FEE';