ALTER TABLE expert_company_profile ADD COLUMN company_type varchar(50) DEFAULT 'EXPERT';
UPDATE expert_company_profile SET company_type = 'SUPPLIER' WHERE partner_id IS NOT NULL;

ALTER TABLE expert_company_profile DROP FOREIGN KEY `expert_company_profile_ibfk_1`;
ALTER TABLE expert_company_profile DROP COLUMN partner_id;

ALTER TABLE onboarding_docs RENAME TO corporate_docs;
ALTER TABLE corporate_docs ADD COLUMN country varchar(255) DEFAULT NULL AFTER file_type;
ALTER TABLE corporate_docs ADD COLUMN doc_type varchar(255) DEFAULT NULL AFTER country;
ALTER TABLE corporate_docs ADD COLUMN created_by bigint(11) DEFAULT NULL AFTER doc_type;
ALTER TABLE corporate_docs ADD COLUMN expiry_date datetime(6) DEFAULT NULL AFTER created_date;

UPDATE corporate_docs SET doc_type = 'ON_BOARD' WHERE doc_type IS NULL;
