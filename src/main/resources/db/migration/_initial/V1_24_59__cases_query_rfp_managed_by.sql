ALTER TABLE cases ADD COLUMN managed_by VARCHAR(20) NOT NUll;
ALTER TABLE query ADD COLUMN managed_by VARCHAR(20) NOT NUll;
ALTER TABLE rfp ADD COLUMN managed_by VARCHAR(20) NOT NUll;

UPDATE cases SET managed_by = 'PARTNER' WHERE partner_id IS NOT NULL;
UPDATE query SET managed_by = 'PARTNER' WHERE partner_id IS NOT NULL;
UPDATE rfp SET managed_by = 'PARTNER' WHERE partner_id IS NOT NULL;

UPDATE cases SET managed_by = 'CG' WHERE partner_id IS NULL;
UPDATE query SET managed_by = 'CG' WHERE partner_id IS NULL;
UPDATE rfp SET managed_by = 'CG' WHERE partner_id IS NULL;
