-- create group chat from existing cases

INSERT INTO group_chat(chat_type, reference_id, created_by, created_date) SELECT "CASE", id, created_by, created_date FROM cases WHERE id NOT IN (SELECT reference_id FROM group_chat);

-- Insert case owner as chat participant
INSERT INTO group_chat_participants(chat_id, user_id) select gc.id, c.created_by FROM cases c JOIN
group_chat gc ON c.id=gc.reference_id LEFT JOIN
group_chat_participants gcp ON gc.id = gcp.chat_id AND gcp.user_id = c.created_by WHERE c.created_by IS NOT null AND gcp.user_id IS NULL;

-- Insert account_manager as chat participant
INSERT INTO group_chat_participants(chat_id, user_id) select gc.id, c.account_manager FROM cases c JOIN 
group_chat gc ON c.id=gc.reference_id LEFT JOIN 
group_chat_participants gcp ON gc.id = gcp.chat_id AND gcp.user_id = c.account_manager WHERE c.account_manager IS NOT null AND gcp.user_id IS NULL;

-- Insert case managers as chat participants

INSERT INTO group_chat_participants(chat_id, user_id) select gc.id, cm.user_id FROM case_managers cm JOIN 
group_chat gc ON cm.case_id=gc.reference_id LEFT JOIN 
group_chat_participants gcp ON gc.id = gcp.chat_id AND gcp.user_id = cm.user_id WHERE cm.user_id IS NOT null AND gcp.user_id IS NULL;

-- Insert case assignees as chat participants

INSERT INTO group_chat_participants(chat_id, user_id) SELECT gc.id, ca.expert_id FROM case_assignee ca JOIN 
group_chat gc ON ca.case_id=gc.reference_id LEFT JOIN 
group_chat_participants gcp ON gc.id = gcp.chat_id AND gcp.user_id = ca.expert_id WHERE ca.expert_id IS NOT NULL AND gcp.user_id IS NULL;