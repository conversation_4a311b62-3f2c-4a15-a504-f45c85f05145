CREATE TABLE IF NOT EXISTS `business_industry` (
  `id` bigint(20) auto_increment,
  `name` varchar(255) NOT NULL,
  `code` varchar(100),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11),
  `updated_by` bigint(11),
   PRIMARY KEY (`id`)
);

INSERT INTO business_industry(name, code, created_date, last_updated_date)
VALUES
('Healthcare', 'HEALTHCARE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Financial Services', 'FINANCIAL_SERVICES', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('E-Commerce', 'E_COMMERCE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Retail', 'RETAIL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Technology', 'TECHNOLOGY', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Food & Beverage', 'FOOD_BEVERAGE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Education', 'EDUCATION', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Real Estate', 'REAL_ESTATE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Manufacturing & Engineering', 'MANUFACTURING_ENGINEERING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Energy', 'ENERGY', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('Film & Entertainment', 'FILM_ENTERTAINMENT', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

CREATE TABLE IF NOT EXISTS `playbook` (
  `id` bigint(20) auto_increment,
  `country` varchar(255) NOT NULL,
  `industry` varchar(100),
  `about` varchar(5000),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11),
  `updated_by` bigint(11),
   PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `playbook_details` (
  `id` bigint(20) auto_increment,
  `category` varchar(255) NOT NULL,
  `sub_category` varchar(255),
  `is_selected` boolean,
  `playbook_id` bigint(20),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11),
  `updated_by` bigint(11),
   PRIMARY KEY (`id`),
   FOREIGN KEY(`playbook_id`) REFERENCES `playbook`(`id`)
);

CREATE TABLE IF NOT EXISTS `playbook_sessions` (
  `id` bigint(20) auto_increment,
  `playbook_id` bigint(20),
  `start_time` datetime(3) NOT NULL,
  `end_time` datetime(3),
  `question_count` bigint(20),
  `session_id` varchar(100),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11),
  `updated_by` bigint(11),
   PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `playbook_share` (
  `id` bigint(20) auto_increment,
  `playbook_id` bigint(20),
  `user_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY(`playbook_id`) REFERENCES `playbook`(`id`),
   FOREIGN KEY(`user_id`) REFERENCES `login_account`(`id`)
);

CREATE TABLE IF NOT EXISTS `library_metadata` (
  `id` bigint(20) auto_increment,
  `content_id` bigint(20),
  `m_key` varchar(200),
  `m_value` varchar(200),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11),
  `updated_by` bigint(11),
   PRIMARY KEY (`id`),
   FOREIGN KEY(`content_id`) REFERENCES `content_library`(`id`)
);
-- subscription dates for corporate
ALTER TABLE corporate ADD COLUMN subscription_start_date datetime(3);
ALTER TABLE corporate ADD COLUMN subscription_end_date datetime(3);

ALTER TABLE content_library MODIFY data MEDIUMTEXT NOT NULL;

CREATE TABLE IF NOT EXISTS `playbook_content` (
  `id` bigint(20) auto_increment,
  `playbook_id` bigint(20),
  `data` mediumtext,
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11),
  `updated_by` bigint(11),
   PRIMARY KEY (`id`),
   FOREIGN KEY(`playbook_id`) REFERENCES `playbook`(`id`)
);