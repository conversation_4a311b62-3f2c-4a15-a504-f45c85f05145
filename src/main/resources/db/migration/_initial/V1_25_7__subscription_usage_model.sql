CREATE TABLE IF NOT EXISTS `subscription_usage` (
  `id` bigint(20) auto_increment,
  `name` varchar(255) NOT NULL,
  `currency` varchar(10),
  `company_id` bigint(20) NOT NULL,
  `company_type` varchar(20) NOT NULL,
  `price` FLOAT DEFAULT 0,
  `is_active` BOOLEAN DEFAULT TRUE,
  `overage_charge` FLOAT DEFAULT 0,
  `date` datetime(3) NOT NULL,
  `status` varchar(100),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11) NOT NULL,
  `updated_by` bigint(11) NOT NULL,
   PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `subscription_usage_details` (
  `id` bigint(20) auto_increment,
  `name` varchar(255) NOT NULL,
  `code` varchar(20),
  `threshold` bigint(20),
  `unit` varchar(20),
  `overage_rate` float,
  `tracking_duration` varchar(100),
  `is_unlimited` BOOLEAN DEFAULT FALSE,
  `overage` bigint(20),
  `overage_charge` FLOAT DEFAULT 0,
  `total_usage` bigint(20),
  `date` datetime(3) NOT NULL,
  `subscription_id` bigint(20),
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
  `created_by` bigint(11) NOT NULL,
  `updated_by` bigint(11) NOT NULL,
   PRIMARY KEY (`id`),
   FOREIGN KEY(`subscription_id`) REFERENCES `subscription_usage`(`id`)
);

INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('SUBSCRIPTION', 'Subscription');
INSERT INTO micro_access_master(feature_key, access_level, access_level_value) VALUES('SUBSCRIPTION', 'VIEW', 'View Subscription');

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key = 'SUBSCRIPTION' AND mam.access_level='VIEW')
    FROM bands b
    WHERE b.name IN ('Super Admin (free)', 'Super Admin');