CREATE TABLE IF NOT EXISTS `usage_blueprint` (
  `id` bigint(20) auto_increment,
  `country` varchar(255) NOT NULL,
  `accessed_by` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_case` (
  `id` bigint(20) auto_increment,
  `case_type` varchar(255) NOT NULL,
  `initiated_by` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_user` (
  `id` bigint(20) auto_increment,
  `name` varchar(255) NOT NULL,
  `country` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_ask_ai` (
  `id` bigint(20) auto_increment,
  `name` varchar(255) NOT NULL,
  `country` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_corporate_doc_upload` (
  `id` bigint(20) auto_increment,
  `name` varchar(255) NOT NULL,
  `country` varchar(200),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_cost_of_living` (
  `id` bigint(20) auto_increment,
  `country` varchar(200),
  `accessed_by` varchar(255) NOT NULL,
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_compliance_calendar` (
  `id` bigint(20) auto_increment,
  `country` varchar(200),
  `accessed_by` varchar(255) NOT NULL,
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

CREATE TABLE IF NOT EXISTS `usage_expert_support` (
  `id` bigint(20) auto_increment,
  `log_type` varchar(200),
  `log_id` bigint(20) NOT NULL,
  `submitted_by` varchar(255),
  `support_time` bigint(20),
  `user_id` bigint(20),
  `band_name` varchar(200),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

ALTER TABLE user_action ADD COLUMN created_by bigint(20);
UPDATE user_action SET created_by=user_id WHERE created_by IS NULL;