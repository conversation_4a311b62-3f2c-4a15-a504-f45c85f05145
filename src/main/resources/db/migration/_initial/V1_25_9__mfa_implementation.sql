CREATE TABLE IF NOT EXISTS `mfa_token` (
  `id` bigint(20) auto_increment,
  `user_id` bigint(20) NOT NULL,
  `code` varchar(255) NOT NULL,
  `method` varchar(200),
  `expiry_date` datetime(3) NOT NULL,
  `is_validated` boolean default false,
  `created_date` datetime(3) NOT NULL,
  `last_updated_date` datetime(3) NOT NULL,
   PRIMARY KEY (`id`)
);

ALTER TABLE login_history ADD COLUMN ip_address varchar(100);
ALTER TABLE login_history ADD COLUMN city varchar(500);
ALTER TABLE login_history ADD COLUMN country varchar(50);
ALTER TABLE login_history ADD COLUMN browser varchar(500);
ALTER TABLE login_history ADD COLUMN os varchar(500);
ALTER TABLE login_history ADD COLUMN login_success boolean;
