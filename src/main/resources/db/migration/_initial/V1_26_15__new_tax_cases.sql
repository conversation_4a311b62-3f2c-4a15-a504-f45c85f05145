CREATE TABLE IF NOT EXISTS `expatriate_tax_services` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  host_country VARCHAR(10) NOT NULL,
  state VARCHAR(255),
  services VARCHAR(1000),
  date_of_hire BIGINT(11),
  assignment_start_date BIGINT(11),
  end_date BIGINT(11),
  service_years BIGINT(11),
  first_name varchar(50),
  last_name varchar(50),
  citizen_country varchar(50),
  residence_country varchar(50),
  job_title varchar(50) DEFAULT NULL,
  applicant_state varchar(50) DEFAULT NULL,
  contact_no varchar(50) DEFAULT NULL,
  email_address varchar(255) DEFAULT NULL,
  share_applicant_info BOOLEAN DEFAULT NULL,
  permanent_transfer BOOLEAN DEFAULT NULL,

  PRIMARY KEY (`id`),
  FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `tax_return_preparation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  host_country VARCHAR(10) NOT NULL,
  state VARCHAR(255),
  date_of_hire BIGINT(11),
  assignment_start_date BIGINT(11),
  end_date BIGINT(11),
  service_years BIGINT(11),
  duties VARCHAR(500),
  first_name varchar(50),
  last_name varchar(50),
  citizen_country varchar(50),
  residence_country varchar(50),
  job_title varchar(50) DEFAULT NULL,
  applicant_state varchar(50) DEFAULT NULL,
  contact_no varchar(50) DEFAULT NULL,
  email_address varchar(255) DEFAULT NULL,
  share_applicant_info BOOLEAN DEFAULT NULL,
  permanent_transfer BOOLEAN DEFAULT NULL,

  PRIMARY KEY (`id`),
  FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `remote_work_assessment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  host_country VARCHAR(10) NOT NULL,
  anticipated_start_date BIGINT(11),
  work_duration BIGINT(11),
  spent_time_past_year BOOLEAN,
  work_spent_duration BIGINT(11),
  entity_in_host_country BOOLEAN,
  payroll_in_destination_country BOOLEAN,
  duties VARCHAR(500),
  first_name varchar(50),
  last_name varchar(50),
  citizen_country varchar(50),
  residence_country varchar(50),
  job_title varchar(50) DEFAULT NULL,
  applicant_state varchar(50) DEFAULT NULL,
  contact_no varchar(50) DEFAULT NULL,
  email_address varchar(255) DEFAULT NULL,
  share_applicant_info BOOLEAN DEFAULT NULL,

  PRIMARY KEY (`id`),
  FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);