CREATE TABLE IF NOT EXISTS `task` (
    `id` BIGINT(11) AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `reference_id` BIGINT(11),
    `reference_type` VA<PERSON>HA<PERSON>(255),
    `visibility` VARCHAR(255),
    `due_date` DATETIME,
    `completed_date` DATETIME,
    `priority` VARCHAR(255),
    `status` VARCHAR(50),
    `created_by` BIGINT(11),
    `created_date` DATETIME,
    `updated_by` BIGINT(11),
    `last_updated_date` DATETIME
);

CREATE TABLE IF NOT EXISTS `task_assignee` (
    `id` BIGINT(11) AUTO_INCREMENT PRIMARY KEY,
    `task_id` BIGINT(11),
    `assignee_id` BIGINT(11),
    `type` VARCHAR(255),
    `created_by` BIGINT(11),
    `created_date` <PERSON><PERSON>ETIM<PERSON>,
    `updated_by` BIGINT(11),
    `last_updated_date` DATETIME,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`task_id`) <PERSON><PERSON>ERENCES `task` (`id`) ON DELETE CASCADE,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`assignee_id`) REFERENCES `login_account` (`id`) ON DELETE CASCADE
);


CREATE TABLE IF NOT EXISTS `task_reminder` (
    `id` BIGINT(11) AUTO_INCREMENT PRIMARY KEY,
    `task_id` BIGINT(11),
    `date_time` DATETIME,
    `created_by` BIGINT(11),
    `created_date` DATETIME,
    `updated_by` BIGINT(11),
    `last_updated_date` DATETIME,
    FOREIGN KEY (`task_id`) REFERENCES `task` (`id`) ON DELETE CASCADE
);