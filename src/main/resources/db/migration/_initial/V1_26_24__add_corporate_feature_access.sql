ALTER TABLE `feature_master` ADD COLUMN is_default B<PERSON><PERSON>EAN DEFAULT TRUE;

INSERT INTO `feature_master`(`feature_key`, `feature_value`, `is_default`) VALUES
    ('TAX_CALC', 'Tax Calculator', FALSE),
    ('PLAYBOOK', 'Playbook', FALSE),
    ('VISA_ASSESSMENT', 'Visa Assessment', FALSE),
    ('CG_AI', 'CG AI', FALSE),
    ('COST_OF_LIVING', 'Cost of Living', FALSE);

INSERT INTO micro_access_master(feature_key, access_level, access_level_value) VALUES
    ('TAX_CALC', 'VIEW', 'View Tax Calculator'),
    ('PLAYBOOK', 'VIEW', 'View Playbook'),
    ('VISA_ASSESSMENT', 'VIEW', 'View Visa Assessment'),
    ('CG_AI', 'VIEW', 'View CG AI'),
    ('COST_OF_LIVING', 'VIEW', 'View Cost of Living');

-- Populate existing corporate bands to add new accesses
INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='TAX_CALC')
    FROM bands b
    WHERE b.corporate_id<0;

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='PLAYBOOK')
    FROM bands b
    WHERE b.corporate_id<0;

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='VISA_ASSESSMENT')
    FROM bands b
    WHERE b.corporate_id<0;

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='CG_AI')
    FROM bands b
    WHERE b.corporate_id<0;

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
        (SELECT mam.id FROM micro_access_master mam WHERE mam.feature_key='COST_OF_LIVING')
    FROM bands b
    WHERE b.corporate_id<0;
