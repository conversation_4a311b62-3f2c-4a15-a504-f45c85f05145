CREATE TABLE IF NOT EXISTS `employee_of_record` (
  `destination_country` varchar(3) NOT NULL,
  `entity_in_destination_country` varchar(3),
  `hiring_date` DATETIME(3),
  `right_to_work` varchar(255) DEFAULT NULL,
  `sign_contract` varchar(255) DEFAULT NULL,
  `employment_types` varchar(255) DEFAULT NULL,
  `international_travel` varchar(255) DEFAULT NULL,
  `labour_safety` varchar(255) DEFAULT NULL,
  `certification_required` varchar(255) DEFAULT NULL,
  `government_contract` varchar(255) DEFAULT NULL,
  `id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`id`) REFERENCES `cases` (`id`)
);

CREATE TABLE IF NOT EXISTS `employee_record_applicant` (
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `residence_country` varchar(3) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `salary_currency` varchar(255) DEFAULT NULL,
  `salary` bigint(20) DEFAULT NULL,
  `case_id` bigint(20) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`case_id`) REFERENCES `employee_of_record` (`id`)
);

INSERT INTO case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('EMPLOYEE_OF_RECORD', 'Employee of record', 'HR_PAYROLL', 'HR Payroll');