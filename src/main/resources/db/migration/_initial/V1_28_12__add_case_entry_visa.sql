CREATE TABLE IF NOT EXISTS `entry_visa` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  destination_country VARCHAR(10) NOT NULL,
  trip_purpose VARCHAR(255) NOT NULL,
  travel_date date,
  trip_length BIGINT(11),
  trip_length_details VARCHAR(255),
  destination_country_duration BIGINT(11) ,
  destination_country_duration_details VARCHAR(255) ,
  visa_entry_type VARCHAR(10),
  first_name varchar(50),
  middle_name varchar(50) DEFAULT NULL,
  last_name varchar(50),
  company_name varchar(50),
  citizen_country varchar(50),
  residence_country varchar(50),
  job_title varchar(50) DEFAULT NULL,
  share_applicant_info BOOLEAN DEFAULT NULL,
  contact_no varchar(50) DEFAULT NULL,
  email_address varchar(255) DEFAULT NULL,
  duties varchar(3000),
  visa_service_type varchar(255) default null,
  visa_issue_date bigint DEFAULT null,
  visa_expiry_date bigint DEFAULT null,
  visa_type varchar(255) DEFAULT null,
  visa_renewal_date BIGINT,

  PRIMARY KEY (`id`)
);

insert into case_category (sub_category_id, sub_category_name, parent_category_id, parent_category_name)
  VALUES ('ENTRY_VISA', 'Entry Visa', 'IMMIGRATION_GM', 'Immigration GM');