/*---- accesses for tasks */
INSERT INTO `feature_master`(`feature_key`, `feature_value`) VALUES('TRAVEL_ASSESSMENT', 'Travel Compliance Assistant');

/*-- add travel assessment accesses */

INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('TRAVEL_ASSESSMENT', 'VIEW', 'View Assessment');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('TRAVEL_ASSESSMENT', 'CREATE', 'Create Assessment');
INSERT INTO `micro_access_master`(`feature_key`, `access_level`, `access_level_value`) VALUES('TRAVEL_ASSESSMENT', 'DELETE', 'Delete Assessment');

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
    (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='VIEW' AND feature_key='TRAVEL_ASSESSMENT')
    FROM bands b WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');

INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
    (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='CREATE' AND feature_key='TRAVEL_ASSESSMENT')
    FROM bands b WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');


INSERT INTO band_details(band_id, access_id)
    SELECT b.id,
    (SELECT mam.id FROM micro_access_master mam WHERE mam.access_level='DELETE' AND feature_key='TRAVEL_ASSESSMENT')
    FROM bands b WHERE b.name IN ('Super Admin (free)', 'Super Admin', 'Account Manager', 'Applicant');


CREATE TABLE IF NOT EXISTS `usage_travel_assessment` (
  `id` bigint(20) auto_increment,
  `destination_country` varchar(10) NOT NULL,
  `accessed_by` varchar(200),
  `user_id` bigint(20),
  `charges` FLOAT DEFAULT 0,
  `created_at` datetime(3) NOT NULL,
  `subscription_usage_details_id` bigint(20),
   PRIMARY KEY (`id`),
   FOREIGN KEY (`subscription_usage_details_id`) REFERENCES `subscription_usage_details`(`id`)
);

INSERT INTO subscription_details(subscription_id, name, code, threshold, unit, overage_rate, tracking_duration, created_date, last_updated_date, created_by, updated_by, is_unlimited)
SELECT s.id,
'Travel Compliance Assistant', 'TRAVEL_ASSESSMENT', '0', 'COUNTRIES', '100', 'YEAR', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1', '1', '0'
FROM subscription_plan s where s.id not in (select subscription_id from subscription_details where code='TRAVEL_ASSESSMENT');


/*-- legacy case flag changes */

ALTER TABLE cases ADD COLUMN is_legacy BOOLEAN DEFAULT FALSE;
UPDATE cases SET is_legacy = true WHERE id NOT IN (SELECT reference_id FROM task_workflow WHERE reference_type='CASE');
