CREATE TABLE `lead_request` (
    `id` bigint(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `expertise_id` int(8) NOT NULL,
    `country_code` varchar(2) NOT NULL,
    `region_id` int(8) DEFAULT NULL,
    `status` varchar(20) NOT NULL,
    `created_date` datetime(3) NOT NULL,
    `lead_type` varchar(20) NOT NULL,
    `lead_type_id` bigint(11) DEFAULT NULL,
    `created_by` bigint(11) NOT NULL,
    `last_updated_date` datetime(3) NOT NULL,
    `last_updated_by` bigint(11) NOT NULL,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`expertise_id`) REFERENCES `expertise` (`id`),
    FOREIGN KEY (`created_by`) REFERENCES `login_account` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `lead_response` (
    `id` bigint(11) NOT NULL AUTO_INCREMENT,
    `lead_id` bigint(11) NOT NULL,
    `expert_id` bigint(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `created_date` datetime(3) NOT NULL,
    `last_updated_date` datetime(3) NOT NULL,
    `last_updated_by` bigint(11) NOT NULL,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`lead_id`) REFERENCES `lead_request` (`id`),
    FOREIGN KEY (`expert_id`) REFERENCES `expert_user` (`id`),
    UNIQUE KEY expert_lead_response (`lead_id`, `expert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
