ALTER TABLE `case_form` ADD COLUMN case_country_field VARCHAR(255);
ALTER TABLE `case_form` ADD COLUMN case_applicant_first_name_field VARCHAR(255);
ALTER TABLE `case_form` ADD COLUMN case_applicant_last_name_field VARCHAR(255);
ALTER TABLE `case_form` ADD COLUMN case_email_field VARCHAR(255);

CREATE TABLE IF NOT EXISTS `generic_case` (
    `id` BIGINT(11) AUTO_INCREMENT PRIMARY KEY,
    `case_data` TEXT
);

ALTER TABLE `cases` ADD COLUMN case_form_id BIGINT(11);