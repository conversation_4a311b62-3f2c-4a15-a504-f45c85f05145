ALTER TABLE `corporate` ADD COLUMN visa_sign_auth_name VA<PERSON>HAR(255) DEFAULT NULL;
ALTER TABLE `corporate` ADD COLUMN visa_sign_auth_designation VARCHAR(255) DEFAULT NULL;
ALTER TABLE `corporate` ADD COLUMN mode_of_payment VARCHAR(255) DEFAULT NULL;
ALTER TABLE `corporate` ADD COLUMN signature_key VARCHAR(255) DEFAULT NULL;
ALTER TABLE `corporate` ADD COLUMN business_letter_key VARCHAR(255) DEFAULT NULL;
ALTER TABLE `corporate` ADD COLUMN visa_fees_key VARCHAR(255) DEFAULT NULL;

CREATE TABLE corporate_visa_fees (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    country_code VARCHAR(2) NOT NULL,
    e_visa_fees FLOAT NOT NULL,
    business_visa_fees FLOAT NOT NULL,
    corporate_id BIGINT(11) NOT NULL,
    created_date  DATETIME(3) NOT NULL,
    last_updated_date  DATETIME(3),
    created_by BIGINT(11) NOT NULL,
    updated_by BIGINT(11),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (`corporate_id`) REFERENCES `corporate`(`id`)
);