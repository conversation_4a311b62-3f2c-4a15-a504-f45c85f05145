DELETE FROM `step` WHERE `step_name` IN ('STEP_7', 'STEP_8', 'STEP_9', 'STEP_11');
UPDATE `step` SET `step_name` = 'STEP_7' WHERE `step_name` = 'STEP_6';
UPDATE `step` SET `step_name` = 'STEP_6' WHERE `step_name` = 'STEP_5';
UPDATE `step` SET `step_name` = 'STEP_9' WHERE `step_name` = 'STEP_10';

INSERT INTO `step`
VALUES
(null, 'ZZ', 'STEP_5',
'<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>',
'<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>',
0 , null, null,'2020-08-01 00:00:00', '2020-08-01 00:00:00',  0),
(null, 'ZZ', 'STEP_8',
'<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>',
'<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>',
0 , null, null,'2020-08-01 00:00:00', '2020-08-01 00:00:00',  0);

INSERT INTO step (`blueprint_country_code`, `id`, `step_name`, `content`, `content_draft`, `show_chart`, `start_at`, `duration`, `created_date`, `last_updated_date`, `last_updated_by`)
SELECT DISTINCT country_code, null, 'STEP_5', '<p></p>', '<p></p>',  0 , null, null, NOW(), NOW(), 0 FROM blueprint WHERE country_code != 'ZZ';

INSERT INTO step (`blueprint_country_code`, `id`, `step_name`, `content`, `content_draft`, `show_chart`, `start_at`, `duration`, `created_date`, `last_updated_date`, `last_updated_by`)
SELECT DISTINCT country_code, null, 'STEP_8', '<p></p>', '<p></p>',  0 , null, null, NOW(), NOW(), 0 FROM blueprint WHERE country_code != 'ZZ';

UPDATE `step` SET `content_draft`='<p>Before you enter a new market, conducting a new market assessment will be crucial. This will be bespoke to your business and your target region.</p><p>You should think about undertaking the following activities:</p><h2>Market Analysis</h2><p>This entails researching the market size, target customers, customer attitudes and behaviours, state of the economy and infrastructure. It is also important to consider the potential for growth within a region.</p><h2>Competitor Analysis</h2><p>It is important to review the local competition to understand how they operate and enable you to position your company against competitors.</p><h2>Marketing Strategy</h2><p>You will need to consider how you will generate awareness of your product or service and promote your brand. Where will you position your brand in line with the results of your market analysis?</p><p>Our local experts can help you undertake this new market assessment to assist you with your international expansion plans.</p>'
WHERE `step_name` = 'STEP_1';

UPDATE `step` SET `content`=`content_draft`
WHERE `step_name` = 'STEP_1';
