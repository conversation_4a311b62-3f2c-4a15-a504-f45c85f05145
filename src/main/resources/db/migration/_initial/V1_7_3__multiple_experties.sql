create table if not exists expert_expertise
(
    id int auto_increment,
    expert_id bigint(11) null,
    expertise_id int(8) null,
    constraint expert_expertise_pk
        primary key (id),
    constraint expert_expertise_expert_user_id_fk
        foreign key (expert_id) references expert_user (id),
    constraint expert_expertise_expertise_id_fk
        foreign key (expertise_id) references expertise (id)
);

insert into expert_expertise (expert_id, expertise_id) select id,expertise_id from expert_user where expertise_id is not null
