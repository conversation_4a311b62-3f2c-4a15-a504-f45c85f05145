create table if not exists lead_expertise
(
    id int auto_increment,
    lead_id bigint(11) null,
    expertise_id int(8) null,
    constraint lead_expertise_pk
        primary key (id),
    constraint lead_expertise_expert_user_id_fk
        foreign key (lead_id) references lead_request (id),
    constraint lead_expertise_expertise_id_fk
        foreign key (expertise_id) references expertise (id)
);

insert into lead_expertise (lead_id, expertise_id) select id,expertise_id from lead_request where expertise_id is not null;

alter table lead_request modify expertise_id int(8) null;


