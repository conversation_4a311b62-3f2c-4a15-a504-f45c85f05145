create table lead_response_trail
(
    id bigint auto_increment,
    lead_response_id bigint not null,
    message text null,
    root_user_id bigint not null,
    asset_url varchar(255) null,
    responded_by varchar(255) not null,
    is_seen bool not null default false,
    created_date datetime(3) not null,
    last_updated_date datetime(3) not null,
    last_updated_by bigint(11) null,
    constraint lead_response_chat_pk
        primary key (id),
    constraint lead_response_trail_lead_response_id_fk
        foreign key (lead_response_id) references lead_response (id),
    constraint lead_response_trail_login_account_id_fk
        foreign key (root_user_id) references login_account (id)
);

insert into lead_response_trail (lead_response_id, message, root_user_id, responded_by, created_date, last_updated_date, last_updated_by) select id,description,expert_id,'EXPERT',created_date,last_updated_date,last_updated_by from lead_response lr

