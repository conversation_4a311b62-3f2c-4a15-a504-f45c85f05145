create table if not exists client_referral
(
    id                    bigint auto_increment
        primary key,
    title                 varchar(255) not null,
    description           text         not null,
    client_name           varchar(255) not null,
    client_company_name   varchar(255) not null,
    client_email          varchar(255) null,
    client_contact_number varchar(255) null,
    client_country_code   varchar(255) null,
    deal_currency         varchar(255) null,
    deal_value            varchar(255) null,
    status                varchar(45)  not null,
    created_by            bigint       not null,
    created_date          datetime(6)  not null,
    last_updated_date     datetime(6)  not null,
    last_updated_by       bigint       not null
);

create table if not exists client_referral_experts
(
    id                    bigint auto_increment
        primary key,
    referral_id bigint not null,
    expert_id   bigint not null,
    constraint client_referral_experts_referral_id
        foreign key (referral_id) references client_referral (id),
    constraint client_referral_experts_expert_id
        foreign key (expert_id) references expert_user (id)
);

create table if not exists client_referral_read
(
    id                    bigint auto_increment
        primary key,
    referral_id bigint not null,
    expert_id   bigint not null,
    constraint client_referral_read_referral_id
        foreign key (referral_id) references client_referral (id),
    constraint client_referral_read_expert_id
        foreign key (expert_id) references expert_user (id)
);

