create table if not exists event
(
    id                bigint auto_increment
        primary key,
    name              varchar(255) null,
    about             text         null,
    banner_photo_key  varchar(255) null,
    end_date          date         null,
    end_time          time         null,
    invitees_external text         null,
    start_date        date         null,
    start_time        time         null,
    status            varchar(255) null,
    time_zone         varchar(255) null,
    created_date      datetime(6)  null,
    last_updated_date datetime(6)  null,
    last_updated_by   bigint       not null
);

create table if not exists event_invitees
(
    id bigint auto_increment
        primary key,
    event_id  bigint not null,
    client_id bigint not null,
    constraint FKjugkio8vhkvey2831e2s9g81s
        foreign key (event_id) references event (id)
);

create table if not exists event_session
(
    id                bigint auto_increment
        primary key,
    name              varchar(255) null,
    about             text         null,
    date              date         null,
    end_time          time         null,
    start_time        time         null,
    status            varchar(255) null,
    time_zone         varchar(255) null,
    event_id          bigint       null,
    created_date      datetime(6)  null,
    last_updated_date datetime(6)  null,
    last_updated_by   bigint       not null,
    constraint FKb6b508lhqgk49<PERSON>uoafqyja6n
        foreign key (event_id) references event (id)
);

create table event_speaker
(
    id                   bigint auto_increment
        primary key,
    company_name         varchar(255) null,
    country_code         varchar(255) null,
    email_id             varchar(255) null,
    first_name           varchar(255) null,
    internal_member_id   bigint       null,
    internal_member_role varchar(255) null,
    is_host              bit          not null,
    last_name            varchar(255) null,
    about                text         null,
    profile_picture_key  varchar(255) null,
    type                 varchar(255) null,
    event_id             bigint       null,
    created_date         datetime(6)  null,
    last_updated_date    datetime(6)  null,
    last_updated_by      bigint       not null,
    constraint FKjmtxwwiao8a0t7jdd6bp5r6bk
        foreign key (event_id) references event (id)
);

create table event_session_speaker
(
    id bigint auto_increment
        primary key,
    session_id bigint not null,
    speaker_id bigint not null,
    constraint FKeh6adv0qw1k7exi7vna2qcvs6
        foreign key (session_id) references event_session (id),
    constraint FKrhj9qejj91oys045hqvisw0b6
        foreign key (speaker_id) references event_speaker (id)
);

