package com.centuroglobal.controller

import com.centuroglobal.service.AIChatService
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.AIChatResponse
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.AIThreadResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.UUID

class AIChatControllerTest {

    private val aiChatService: AIChatService = mockk()
    private val aiChatController = AIChatController(aiChatService)

    @Test
    fun `postMessage should return Response with AIChatResponse`() {
        val chatRequest = mockk<AIMessageRequest>()
        val authenticatedUser = mockk<AuthenticatedUser> {
            every { userId } returns 1L
        }
        val aiChatResponse = mockk<AIChatResponse>()

        every { aiChatService.createMessage(chatRequest, authenticatedUser) } returns aiChatResponse

        val response = aiChatController.postMessage(authenticatedUser, chatRequest)

        assertEquals(Response(true, aiChatResponse), response)
        verify { aiChatService.createMessage(chatRequest, authenticatedUser) }
    }

    @Test
    fun `getMessage should return Response with list of AIChatResponse`() {
        val threadId = UUID.randomUUID().toString()
        val authenticatedUser = mockk<AuthenticatedUser> {
            every { userId } returns 1L
        }
        val aiChatResponses = mockk<AIThreadResponse>()

        every { aiChatService.getMessages(threadId, any()) } returns aiChatResponses

        val response = aiChatController.getMessage(authenticatedUser, threadId)

        assertEquals(Response(true, aiChatResponses), response)
    }

}
