package com.centuroglobal.controller

import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.account.AddAccountRequest
import com.centuroglobal.shared.data.pojo.AccountListing
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.ReferenceData
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@WebMvcTest(controllers = [AccountController::class])
@AutoConfigureMockMvc(addFilters = false)
class AccountControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var corporateService: CorporateService

    @MockkBean
    lateinit var accessLogService: AccessLogService


    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.companyId } returns 1L
    }

    @Test
    fun `test addAccount`() {
        val accountRequest = AddAccountRequest(
            id = 1,
            name = "abc",
            description = "desc",
            companyName = "abc",
            status = AccountStatus.ACTIVE,
            corporateId = 4
        )
        every { corporateService.addAccount(any(), any(), authenticatedUser) } returns 1L

        mockMvc.perform(post("/api/v1/account")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(accountRequest)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test listAccounts`() {

        val search = ""
        val status = AccountStatus.ACTIVE
        val isDownload = false
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"


        val accountListing = AccountListing(
            id = 3,
            name = "abc",
            status = AccountStatus.ACTIVE,
            createdDate = 1234,
            updatedDate = 2345,
            activeCases = 123,
            accountUsers = 12,
            createdBy = UserProfile(id = 2L, email = "<EMAIL>", firstName = "String", lastName = "String",
                role = Role.ROLE_ADMIN, status = AccountStatus.ACTIVE),
            lastUpdatedBy = UserProfile(id = 3L, email = "<EMAIL>", firstName = "String", lastName = "String",
                role = Role.ROLE_ADMIN, status = AccountStatus.ACTIVE)
        )
        val pagedResult = PagedResult(listOf(accountListing), 1, 0, 1)

        every {
            corporateService.retrieveAccounts(any(),
            PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
            authenticatedUser,1) } returns pagedResult

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/account/listing")
            .param("search",search)
            .param("status",status.toString())
            .param("isDownload",isDownload.toString())
            .param("pageIndex", pageIndex.toString())
            .param("pageSize", pageSize.toString())
            .param("sort", sort)
            .param("sortBy", sortBy)
            .contentType(MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test updateAccount`() {
        val accountRequest = AddAccountRequest(
            id = 1,
            name = "abc",
            description = "desc",
            companyName = "abc",
            status = AccountStatus.ACTIVE,
            corporateId = 4
        )
        val accountEntity = AccountEntity(
            id = 1,
            name = "abc",
            status = AccountStatus.ACTIVE,
            description = "desc",
            companyName = "abc",
            corporate = CorporateEntity(
                1,
                "String",
                "US",
                CorporateStatus.ACTIVE,
                true,
                null,
                1L,
                listOf(),
                listOf(),
                1L),
            corporateUsers = listOf(),
            cases = mutableListOf()
        )
        every { corporateService.updateAccount(accountRequest.id!!, authenticatedUser, any()) } returns accountEntity

        mockMvc.perform(put("/api/v1/account")
            .content(objectMapper.writeValueAsString(accountRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test getUser`() {
        val accountEntity = AccountEntity(
            id = 1,
            name = "abc",
            status = AccountStatus.ACTIVE,
            description = "desc",
            companyName = "abc",
            corporate = CorporateEntity(
                1,
                "String",
                "US",
                CorporateStatus.ACTIVE,
                true,
                null,
                1L,
                listOf(),
                listOf(),
                1L),
            corporateUsers = listOf(),
            cases = mutableListOf()
        )
        every { corporateService.getAccountDetails(1L) } returns accountEntity

        mockMvc.perform(get("/api/v1/account/1"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test corporateAccounts`() {
        val referenceData = listOf(ReferenceData(
            id = 2,
            name = "ref"
        ))
        every { corporateService.retrieveCorporateAccounts(any()) } returns referenceData

        mockMvc.perform(get("/api/v1/account"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }
}
