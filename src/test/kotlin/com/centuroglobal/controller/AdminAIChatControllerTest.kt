package com.centuroglobal.controller

import com.centuroglobal.service.AIChatService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.AIChatResponse
import com.centuroglobal.shared.data.pojo.AIThreadResponse
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.UUID

@WebMvcTest(controllers = [AdminAIChatController::class])
class AdminAIChatControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var aiChatService: AIChatService
    @MockkBean
    lateinit var accessLogService: AccessLogService


    private val authenticatedUser: AuthenticatedUser = mockk()
    private val mapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.isAuthenticated } returns true
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.name } returns "admin"
    }

    @Test
    fun `test getMessage`() {
        val userId = 1L
        val threadId = UUID.randomUUID().toString()
        val aiChatResponses = listOf(AIChatResponse(
            id = 3,
            question = "Q",
            answer = "A",
            createdBy = UserProfile(id = 2L, email = "<EMAIL>", firstName = "String", lastName = "String",
                role = Role.ROLE_ADMIN, status = AccountStatus.ACTIVE),
            createdAt = 1234,
            questionsQuota = 3,
            annotations = emptyList()
        ))
        val aiThreadResponse = AIThreadResponse(1, threadId, "Subject", aiChatResponses[0].createdAt, aiChatResponses)
        every { aiChatService.getMessages(threadId, userId) } returns aiThreadResponse

        mockMvc.perform(get("/api/v1/admin/ai-chat/$threadId/message/$userId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }
}
