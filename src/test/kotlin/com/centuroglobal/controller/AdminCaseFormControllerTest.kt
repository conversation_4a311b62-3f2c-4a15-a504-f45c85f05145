package com.centuroglobal.controller

import com.centuroglobal.data.payload.case.CaseFormContent
import com.centuroglobal.data.payload.case.CaseFormRequest
import com.centuroglobal.service.CaseFormService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.case.CaseFormMapping
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [AdminCaseFormController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCaseFormControllerTest(@Autowired val mockMvc: MockMvc) {

    private val objectMapper = ObjectMapper()

    @MockkBean
    lateinit var caseFormService: CaseFormService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `create should return created case form ID`() {
        val request = CaseFormRequest(
            name = "",
            description = "",
            countries = listOf(),
            category = "",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 22,
            defaultDocumentList = listOf(),
            isDefault = false
        )
        val createdId = 1L

        every { caseFormService.create(any(), any()) } returns createdId

        mockMvc.perform(
            MockMvcRequestBuilders.post("/api/v1/admin/case-form")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `update should return success`() {
        val request = CaseFormRequest(
            name = "",
            description = "",
            countries = listOf(),
            category = "",
            fields = null,
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 22,
            defaultDocumentList = listOf(),
            isDefault = false
        )
        val caseFormId = 1L

        every { caseFormService.update(any(), any(), any()) } returns true

        mockMvc.perform(
            MockMvcRequestBuilders.put("/api/v1/admin/case-form/$caseFormId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `copy should return new case form ID`() {
        val caseFormId = 1L
        val newId = 2L

        every { caseFormService.copy(caseFormId, authenticatedUser) } returns newId

        mockMvc.perform(
            MockMvcRequestBuilders.post("/api/v1/admin/case-form/$caseFormId/copy")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `delete should return success`() {
        val caseFormId = 1L

        every { caseFormService.delete(caseFormId, authenticatedUser) } returns true

        mockMvc.perform(
            MockMvcRequestBuilders.delete("/api/v1/admin/case-form/$caseFormId")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `updateStatus should return success`() {
        val caseFormId = 1L
        val status = CaseFormStatus.ACTIVE

        every { caseFormService.updateStatus(caseFormId, status, authenticatedUser) } returns true

        mockMvc.perform(
            MockMvcRequestBuilders.put("/api/v1/admin/case-form/$caseFormId/status/$status")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `updateContent should return success`() {
        val caseFormId = 1L
        val request = CaseFormContent(
            content = JsonNodeFactory.instance.objectNode(),
            mapping = CaseFormMapping(
                firstName = "",
                lastName = "",
                email = "",
                company = ""
            ),
            fieldCount = 4,
            defaultDocumentList = listOf()
        )

        every { caseFormService.updateContent(caseFormId, request, authenticatedUser) } returns true

        mockMvc.perform(
            MockMvcRequestBuilders.put("/api/v1/admin/case-form/$caseFormId/content")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isOk)
    }
}
