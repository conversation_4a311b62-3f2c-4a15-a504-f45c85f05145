package com.centuroglobal.controller

import com.centuroglobal.facade.AdminCircleFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Circle
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.*
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDateTime
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AdminCircleController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCircleControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminCircleFacade: AdminCircleFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()
    private val DEFAULT_SORT_ORDER_CLIENT = Sort.by(Sort.Direction.DESC, "createdDate")

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test listing circles`() {
        val circleListing = CircleListing(
            data = PagedResult(
                listOf(Circle(
                    id = 11,
                    name = "Test Circle",
                    about = "About test circle",
                    status = CircleStatus.ACTIVE,
                    bannerImageFullUrl = "https://example.com/image.jpg",
                    circleAccessType = CircleType.PUBLIC,
                    createdDate = 1234,
                    countryCodes = listOf("US", "UK"),
                    expertiseIds = listOf(1, 2),
                    member = 1,
                    invitee = 2,
                    request = 2,
                    members = listOf(),
                    requests = listOf(),
                    messages = listOf(),
                    memberAction = null,
                    membersThumbnail = listOf(),
                )),
                totalElements = 12,
                currentPage = 0,
                totalPages = 1
            ),
            stats = null
        )
        every { adminCircleFacade.listCircle(any(), any(), any()) } returns CompletableFuture.completedFuture(circleListing)

        mockMvc.get("/api/v1/admin/circle/listing")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isArray()
            }
    }

    @Test
    fun `test circle details`() {
        val circleId = 1L
        val circle = Circle(
            id = 11,
            name = "Test Circle",
            about = "About test circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/image.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = 1234,
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 1,
            invitee = 2,
            request = 2,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf(),
        )
        every { adminCircleFacade.circleDetails(circleId, authenticatedUser) } returns CompletableFuture.completedFuture(circle)

        mockMvc.get("/api/v1/admin/circle/circle-details/$circleId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isNotEmpty()
            }
    }

    @Test
    fun `test create circle`() {
        val request = CreateUpdateCircleRequest(
            name = "New Circle",
            about = "About new circle",
            circleAccessType = CircleType.PUBLIC,
            countryCode = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            status = CircleStatus.ACTIVE,
            members = listOf()
        )
        val circle = Circle(
            id = 11,
            name = "New Circle",
            about = "About new circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/image.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = 1234,
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 0,
            invitee = 0,
            request = 0,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf(),
        )
        every { adminCircleFacade.createCircle(request, authenticatedUser) } returns CompletableFuture.completedFuture(circle)

        mockMvc.post("/api/v1/admin/circle") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success").value(true)
        }
    }

    @Test
    fun `test update circle`() {
        val circleId = 1L
        val request = CreateUpdateCircleRequest(
            name = "Updated Circle",
            about = "About updated circle",
            circleAccessType = CircleType.PUBLIC,
            countryCode = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            status = CircleStatus.ACTIVE,
            members = listOf()
        )
        val circle = Circle(
            id = 11,
            name = "Updated Circle",
            about = "About updated circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/image.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = 1234,
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 1,
            invitee = 2,
            request = 2,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf(),
        )
        every { adminCircleFacade.updateCircle(circleId, request, authenticatedUser) } returns CompletableFuture.completedFuture(circle)

        mockMvc.put("/api/v1/admin/circle/$circleId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
        }
    }

    @Test
    fun `test delete circle`() {
        val circleId = 1L
        every { adminCircleFacade.deleteCircle(circleId, authenticatedUser) } returns CompletableFuture.completedFuture(true)

        mockMvc.delete("/api/v1/admin/circle/$circleId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
            }
    }

    @Test
    fun `test changeStatus`() {
        val changeCircleRequest = ChangeCircleStatus(id = 1L, status = CircleStatus.ACTIVE)
        val response = Response<Any>(true, null)
        
        every { adminCircleFacade.circleStatusUpdate(changeCircleRequest.id, changeCircleRequest.status, authenticatedUser) } returns 
            CompletableFuture.completedFuture(response)

        mockMvc.post("/api/v1/admin/circle/circle-status-update") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(changeCircleRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
        }
    }

    @Test
    fun `test approveMember`() {
        val approveExpertRequest = ApproveExpertRequest(approve = true, circleId = 1L, expertId = 2L)
        val response = Response<Any>(true, null)
        
        every { adminCircleFacade.approveMember(approveExpertRequest.approve, approveExpertRequest.circleId, 
            approveExpertRequest.expertId, authenticatedUser) } returns CompletableFuture.completedFuture(response)

        mockMvc.post("/api/v1/admin/circle/approve-circle-member") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(approveExpertRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
        }
    }

    @Test
    fun `test circleMemberSearch`() {
        val circleId = 1L
        val search = "John"
        val pageIndex = 0
        val pageSize = 20
        val countryCode = "US"
        val expertiseId = "123"
        
        val pagedResult = PagedResult(
            listOf(ExpertProfileSummary(
                id = 2L,
                bio = "Bio",
                companyProfile = ExpertCompanyProfile(

                    name = "Test Name",
                    aboutBusiness = "",
                    companyAddress = "", companyNumber = "",
                    effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                    effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                    feesAmount = "",
                    feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
                    size = CompanySize.Size1,
                    summary = "",
                    renewContract = false,
                    profileImage = "",
                    associatedPartners = mutableListOf(),
                    logoFullUrl = "",
                    sizeName = "",
                    partnerId = 4,
                    contract = "",
                    aiMessageCount = 5
                ),
                contactEmail = "<EMAIL>",
                contactNumber = "*********",
                contactWebsite = "mail.com",
                countryCode = "US",
                countryName = "US",
                displayName = "Trial",
                infoVideoUrl = "url.com",
                jobTitle = "Manager",
                profilePictureFullUrl = "url.com"
            )),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { adminCircleFacade.circleMemberSearch(circleId, any(), any(), authenticatedUser) } returns 
            CompletableFuture.completedFuture(pagedResult)

        mockMvc.perform(
            get("/api/v1/admin/circle/circle-member-search/$circleId")
                .param("search", search)
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("countryCode", countryCode)
                .param("expertiseId", expertiseId)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk())
    }

    @Test
    fun `test circleRequestSearch`() {
        val circleId = 1L
        val search = "test"
        val pageIndex = 0
        val pageSize = 20
        val countryCode = "US"
        val expertiseId = "123"
        
        val pagedResult = PagedResult(
            listOf(ExpertProfileSummary(
                id = 2L,
                bio = "Bio",
                companyProfile = ExpertCompanyProfile(

                    name = "Test Name",
                    aboutBusiness = "",
                    companyAddress = "", companyNumber = "",
                    effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                    effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                    feesAmount = "",
                    feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
                    size = CompanySize.Size1,
                    summary = "",
                    renewContract = false,
                    profileImage = "",
                    associatedPartners = mutableListOf(),
                    logoFullUrl = "",
                    sizeName = "",
                    partnerId = 4,
                    contract = "",
                    aiMessageCount = 5
                ),
                contactEmail = "<EMAIL>",
                contactNumber = "*********",
                contactWebsite = "mail.com",
                countryCode = "US",
                countryName = "US",
                displayName = "Trial",
                infoVideoUrl = "url.com",
                jobTitle = "Manager",
                profilePictureFullUrl = "url.com"
            )),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { adminCircleFacade.circleRequestSearch(circleId, any(), any()) } returns 
            CompletableFuture.completedFuture( pagedResult)

        mockMvc.perform(
            get("/api/v1/admin/circle/circle-request-search/$circleId")
                .param("search", search)
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("countryCode", countryCode)
                .param("expertiseId", expertiseId)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk())
    }

    @Test
    fun `test uploadProfilePicture`() {
        val circleId = 1L
        val photo = MockMultipartFile(
            "bannerImage",
            "test.jpg",
            "image/jpeg",
            "test image content".toByteArray()
        )
        
        every { adminCircleFacade.uploadCoverPicture(circleId, any(), authenticatedUser) } returns 
            CompletableFuture.completedFuture(CircleBannerUploadResponse(
                "https://example.com/banner.jpg",
                bannerPhotoUrl = "https://example.com/banner.jpg"
            ))

        mockMvc.perform(
            MockMvcRequestBuilders.multipart("/api/v1/admin/circle/$circleId/banner-image")
                .file(photo)
                .with { request ->
                    request.method = "PUT"
                    request
                }
        )
        .andExpect(status().isOk())
    }
}