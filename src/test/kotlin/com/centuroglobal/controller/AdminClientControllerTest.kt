package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.ClientService
import com.centuroglobal.service.TokenVerificationService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.payload.account.signup.UpdateAccountStatusRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put

@WebMvcTest(controllers = [AdminClientController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminClientControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminUserService: AdminUserService

    @MockkBean
    private lateinit var tokenVerificationService: TokenVerificationService

    @MockkBean
    private lateinit var clientService: ClientService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test resendEmail`() {
        val userId = 1L
        every { clientService.resendVerificationCode(userId) } returns Unit

        mockMvc.post("/api/v1/admin/client/$userId/resend-verification-email") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test updateUserStatus`() {
        val request = UpdateAccountStatusRequest(
            userId = 1L,
            accountStatus = AccountStatus.ACTIVE
        )

        every { clientService.updateUserStatus(request.userId, request.accountStatus) } returns Unit

        mockMvc.put("/api/v1/admin/client/${request.userId}/status") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test temporaryPassword`() {
        val userId = 1L
        val tempPassword = "TempPass123!"

        every { adminUserService.temporaryPassword(userId, authenticatedUser) } returns tempPassword

        mockMvc.post("/api/v1/admin/client/$userId/temporary-password") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test accessPassword`() {
        val userId = 1L
        val accessPassword = "AccessPass123!"

        every { adminUserService.accessPassword(userId, authenticatedUser) } returns accessPassword

        mockMvc.post("/api/v1/admin/client/$userId/access-password") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test generateVerificationLink`() {
        val userId = 1L
        val verificationLink = "https://example.com/verify?token=abc123"

        every { tokenVerificationService.generateVerificationLink(userId) } returns verificationLink

        mockMvc.post("/api/v1/admin/client/$userId/verification-link") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
        }
    }
}