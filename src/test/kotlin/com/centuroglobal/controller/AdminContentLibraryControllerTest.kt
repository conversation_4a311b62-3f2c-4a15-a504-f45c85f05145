package com.centuroglobal.controller

import com.centuroglobal.service.ContentLibraryService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayInputStream
import java.io.OutputStream

@WebMvcTest(controllers = [AdminContentLibraryController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminContentLibraryControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var contentLibraryService: ContentLibraryService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test create content library item`() {
        val request = ContentLibraryRequest(
            countryCode = "US",
            identifier = "test-identifier",
            title = "Test Content",
            data = "Test content data",
            metadata = listOf()
        )
        val createdId = 1L

        every { contentLibraryService.create(any(), any()) } returns createdId

        mockMvc.post("/api/v1/admin/content-library") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").value(createdId)
        }
    }

    @Test
    fun `test get content by country and identifier`() {
        val country = "US"
        val identifier = "test-identifier"
        val contentResponse = ContentLibraryResponse(
            id = 1L,
            countryCode = country,
            identifier = identifier,
            title = "Test Content",
            data = "Test content data",
            updatedBy = "Admin User",
            updatedOn = 1234567890L,
            metadata = listOf()
        )

        every { contentLibraryService.getByCountryAndIdentifier(country, identifier) } returns contentResponse

        mockMvc.get("/api/v1/admin/content-library") {
            param("country", country)
            param("identifier", identifier)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload.id").value(1)
            jsonPath("$.payload.countryCode").value(country)
            jsonPath("$.payload.identifier").value(identifier)
        }
    }

    @Test
    fun `test list content library items`() {
        val title = "Test"
        val country = "US"
        val identifier = "test"
        val updatedBy = 1L
        val updatedFrom = 1234567000L
        val updatedTo = 1234567999L
        val isTemplate = false
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "lastUpdatedDate"
        val download = false

        val contentListResponse = ContentLibraryListResponse(
            id = 1L,
            countryCode = country,
            identifier = identifier,
            title = "Test Content",
            updatedOn = 1234567890L,
            updatedByUser = "",
            isActive = true,
            metadata = listOf()
        )

        val pagedResult = PagedResult(
            listOf(contentListResponse),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        every { 
            contentLibraryService.list(
                any<ContentLibrarySearchFilter>(),
                any<PageRequest>(),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.get("/api/v1/admin/content-library/listing") {
            param("title", title)
            param("country", country)
            param("identifier", identifier)
            param("updatedBy", updatedBy.toString())
            param("updatedFrom", updatedFrom.toString())
            param("updatedTo", updatedTo.toString())
            param("isTemplate", isTemplate.toString())
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
            param("sort", sort)
            param("sortBy", sortBy)
            param("download", download.toString())
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload.rows[0].id").value(1)
            jsonPath("$.payload.rows[0].countryCode").value(country)
            jsonPath("$.payload.rows[0].identifier").value(identifier)
        }
    }

    @Test
    fun `test delete content library item`() {
        val id = 1L
        
        every { contentLibraryService.delete(id, authenticatedUser) } returns true

        mockMvc.delete("/api/v1/admin/content-library/$id")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").value(true)
            }
    }

    @Test
    fun `test get identifiers`() {
        val identifiers = listOf("identifier1", "identifier2", "identifier3")
        
        every { contentLibraryService.identifiers() } returns identifiers

        mockMvc.get("/api/v1/admin/content-library/identifiers")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isArray()
                jsonPath("$.payload[0]").value("identifier1")
                jsonPath("$.payload[1]").value("identifier2")
                jsonPath("$.payload[2]").value("identifier3")
            }
    }

    @Test
    fun `test update status`() {
        val status = true
        val country = "US"
        val identifier = "test-identifier"
        
        every { contentLibraryService.updateStatus(country, identifier, status) } returns true

        mockMvc.put("/api/v1/admin/content-library/active/$status") {
            param("country", country)
            param("identifier", identifier)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").value(true)
        }
    }

    @Test
    fun `test download`() {
        val countryCode = "US"
        val fileBytes = "test content".toByteArray()
        val streamingResponseBody = StreamingResponseBody { outputStream: OutputStream ->
            val inputStream = ByteArrayInputStream(fileBytes)
            inputStream.use {
                val bytes = inputStream.readAllBytes()
                outputStream.write(bytes, 0, bytes.size)
            }
        }
        
        every { contentLibraryService.download(countryCode) } returns ResponseEntity.ok(streamingResponseBody)

        mockMvc.get("/api/v1/admin/content-library/download/$countryCode")
            .andExpect {
                status { isOk() }
            }
    }
}