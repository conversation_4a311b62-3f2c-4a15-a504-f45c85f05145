package com.centuroglobal.controller

import com.centuroglobal.facade.CorporateFacade
import com.centuroglobal.service.BandService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.SubscriptionService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CorporateStatus
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.data.payload.account.SubscriptionUpdateRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateRequest
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionPlansRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath

@WebMvcTest(controllers = [AdminCorporateController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCorporateControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var corporateFacade: CorporateFacade

    @MockkBean
    private lateinit var bandService: BandService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @MockkBean
    private lateinit var partnerService: PartnerService

    @MockkBean
    private lateinit var corporateService: CorporateService

    @MockkBean
    private lateinit var subscriptionService: SubscriptionService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test get corporate`() {
        val corporateId = 1L
        val corporateResponse = CorporateResponse(
            id = corporateId,
            countryCode = "US",
            primaryColor = "#FF0000",
            secondaryColor = "#00FF00",
            companyLogoId = "logo123",
            email = "<EMAIL>",
            firstName = "",
            lastName = "",
            jobTitle = "",
            corporateName = "",
            referralCode = "",
            keepMeInformed = true,
            recaptchaResponse = "",
            aiMessageCount = 3,
            assignedTeam = listOf(),
            onboardingDocs = listOf(),
            associatedPartner = 3,
            subscriptions = listOf(),
            features = listOf(),
            isTeamEmail = true,
        )

        every { corporateService.retrieveByCorporateId(corporateId) } returns corporateResponse

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload.id").value(corporateId)
                jsonPath("$.payload.name").value("Test Corporate")
            }
    }

    @Test
    fun `test corporate user listing`() {
        val corporateId = 1L
        val corporateUsers = listOf(
            CorporateUsers(
                id = 1L,
                firstName = "John",
                lastName = "Doe",
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE
            ),
            CorporateUsers(
                id = 2L,
                firstName = "Jane",
                lastName = "Smith",
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE
            )
        )

        every { corporateService.retrieveCorporateUsers(corporateId) } returns corporateUsers

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId/users")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isArray()
                jsonPath("$.payload[0].id").value(1)
                jsonPath("$.payload[0].firstName").value("John")
                jsonPath("$.payload[1].id").value(2)
                jsonPath("$.payload[1].firstName").value("Jane")
            }
    }

    @Test
    fun `test update corporate`() {
        val corporateId = 1L
        val updateRequest = UpdateCorporateRequest(
            countryCode = "UK",
            primaryColor = "#0000FF",
            secondaryColor = "#FFFF00",
            companyLogoId = "newlogo123",
            corporateName = "",
            aiMessageCount = 23,
            referralCode = "",
            assignedTeam = listOf(),
            onboardingDocs = listOf(),
            subscriptionPlan = "",
            customSubscription = SubscriptionPlansRequest(
                name = "",
                currency = "",
                price = 12.23F,
                isActive = true,
                modules = listOf()
            ),
            subscriptionStartDate = 21445,
            subscriptionEndDate = 23455,
            features = listOf(),
            isTeamEmail = true
        )

        every { corporateService.update(any(), any()) } returns true

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(updateRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").value(true)
        }
    }

    @Test
    fun `test create corporate`() {
        val signUpRequest = SignUpRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            countryCode = "US",
            jobTitle = "CEO",
            partnerId = 4,
            corporateName = "",
            aiMessageCount = 23,
            referralCode = "",
            assignedTeam = listOf(),
            onboardingDocs = listOf(),
            subscriptionPlan = "",
            customSubscription = SubscriptionPlansRequest(
                name = "",
                currency = "",
                price = 12.23F,
                isActive = true,
                modules = listOf()
            ),
            subscriptionStartDate = 21445,
            subscriptionEndDate = 23455,
            features = listOf(),
            isTeamEmail = true,
        )
        val createdId = 1L

        every { corporateService.createCorporate(any()) } returns createdId

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/corporate") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(signUpRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").value(createdId)
        }
    }

    @Test
    fun `test secondary corporates`() {
        val corporateId = 1L
        val secondaryCorporates = listOf(
            CorporateUserDetail(
                id = 2L,
                email = "<EMAIL>",
                bandName = "Admin",
                status = "ACTIVE",
                fullName = "abcd",
                company = "abcd",
                band = "abcd",
                accountName = listOf(),
                createdDate = 12345,
                subscriptionType = SubscriptionType.FREE
            )
        )

        every { corporateService.retrieveSecondaryCorporateUsers(corporateId) } returns secondaryCorporates

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId/secondary")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isArray()
                jsonPath("$.payload[0].id").value(2)
                jsonPath("$.payload[0].firstName").value("Jane")
            }
    }

    @Test
    fun `test ref list`() {
        val isPartnerCompany = true
        val referenceData = listOf(
            ReferenceData(1L, "Corporate 1"),
            ReferenceData(2L, "Corporate 2"),
            ReferenceData(3L, "Corporate 3")
        )

        every { corporateService.retrieveCorporates(any(), any()) } returns referenceData

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/companies") {
            param("isPartnerCompany", isPartnerCompany.toString())
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").isArray()
            jsonPath("$.payload[0].id").value(1)
            jsonPath("$.payload[0].name").value("Corporate 1")
        }
    }

    @Test
    fun `test corporate accounts`() {
        val corporateId = 1L
        val accounts = listOf(
            ReferenceData(1L, "Account 1"),
            ReferenceData(2L, "Account 2")
        )

        every { corporateFacade.listCorporateAccounts(corporateId) } returns accounts

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId/accounts")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isArray()
                jsonPath("$.payload[0].id").value(1)
                jsonPath("$.payload[0].name").value("Account 1")
            }
    }

    @Test
    fun `test corporate bands`() {
        val corporateId = 1L
        val bands = listOf(
            ReferenceData(1L, "Super Admin"),
            ReferenceData(2L, "Admin"),
            ReferenceData(3L, "User")
        )

        every { bandService.listBands(corporateId) } returns bands

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId/bands")
            .andExpect {
                status { isOk() }
                jsonPath("$.success").value(true)
                jsonPath("$.payload").isArray()
                jsonPath("$.payload[0].id").value(1)
                jsonPath("$.payload[0].name").value("Super Admin")
            }
    }

    @Test
    fun `test corporate listing`() {
        val name = "Test"
        val country = "US"
        val accountId = 1L
        val status = CorporateStatus.ACTIVE
        val from = 1234567000L
        val to = 1234567999L
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"

        val corporateResponse = CorporateResponse(
            id = 3,
            countryCode = "US",
            primaryColor = "#FF0000",
            secondaryColor = "#00FF00",
            companyLogoId = "logo123",
            email = "<EMAIL>",
            firstName = "",
            lastName = "",
            jobTitle = "",
            corporateName = "Test Corporate",
            referralCode = "",
            keepMeInformed = true,
            recaptchaResponse = "",
            aiMessageCount = 3,
            assignedTeam = listOf(),
            onboardingDocs = listOf(),
            associatedPartner = 3,
            subscriptions = listOf(),
            features = listOf(),
            isTeamEmail = true,
        )

        val pagedResult = PagedResult(
            listOf(corporateResponse),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        val listingWithStatsDetails = ListingWithStatsDetails(
            stats = ListingStats(
                total = 12,
                active = 3,
                suspended = 5,
                pending = 4
            ),
                data = PagedResult(
                listOf(CorporateList(
                    id = 2,
                    name = name,
                    country = country,
                    status = CorporateStatus.ACTIVE.toString(),
                    rootUserId = "0",
                    accounts = 2L,
                    corporateUsers = 0,
                    createdOn = 1234,
                    activeCases = 12,
                    completedCases = 1,
                    subscription = "",
                    partnerName = "",
                )),
                totalElements = 1,
                currentPage = 0,
                totalPages = 1
            )
        )

        every {
            partnerService.listCorporateStats(any(), any(), any(), any())
        } returns listingWithStatsDetails

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/listing") {
            param("name", name)
            param("country", country)
            param("account id", accountId.toString())
            param("status", status.toString())
            param("createdFrom", from.toString())
            param("createdTo", to.toString())
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
            param("sort", sort)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload.rows[0].id").value(1)
            jsonPath("$.payload.rows[0].name").value("Test Corporate")
        }
    }

    @Test
    fun `test update subscription`() {
        val corporateId = 1L
        val request = SubscriptionUpdateRequest(
            id = 4,
            subscription = SubscriptionType.FREE
        )

        every { subscriptionService.updateSubscription() } returns Unit
        every { subscriptionService.updateCorporateSubscriptionType(any()) } returns Unit

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/corporate/$corporateId/subscription") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").value(true)
        }
    }
}