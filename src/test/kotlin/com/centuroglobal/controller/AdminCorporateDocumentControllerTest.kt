package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.ClientDocType
import com.centuroglobal.shared.data.enums.DocSubType
import com.centuroglobal.shared.data.enums.UserDocType
import com.centuroglobal.shared.data.payload.CorporateDocumentRequest
import com.centuroglobal.shared.data.pojo.CorporateDocumentResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserDocumentSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@WebMvcTest(controllers = [AdminCorporateDocumentController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCorporateDocumentControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminUserService: AdminUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test upload corporate documents`() {
        val corporateId = 1L
        val documentRequest = CorporateDocumentRequest(
            docName = "Test Document",
            expiryDate = 1672531200000,
            s3Key = "",
            fileName = "",
            fileSize = 123,
            fileType = "PDF",
            country = "IN"
        )

        every { adminUserService.uploadDocumentCorporate(documentRequest, corporateId, authenticatedUser) } returns true

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/corporate/document/$corporateId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(documentRequest)
        }.andExpect {
            status { isCreated() }
        }
    }

    @Test
    fun `test list corporate documents`() {
        val corporateId = 1L
        val search = "test"
        val country = "US"
        val createdBy = 1L
        val uploadedType = "pdf"
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = false

        val documentResponse = CorporateDocumentResponse(
            id = 1L,
            docName = "Test Document",
            docSubType = DocSubType.PASSPORT.toString(),
            expiryDate = 1672531200000,
            fileType = "pdf",
            fileSize = 1024,
            country = "US",
            fileName = "",
            uploadDate = 12344,
            createdBy = "",
            userId = 123,
            issueCountry = "IN",
            lastUpdated = 123,
            filesUploaded = 12,
            filesData = listOf()
        )

        val pagedResult = PagedResult(
            rows = listOf(documentResponse),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        val searchFilter = UserDocumentSearchFilter.Builder.build(
            search,
            country,
            createdBy,
            corporateId,
            UserDocType.CORPORATE,
            uploadedType,
            ClientDocType.CORPORATE
        )

        every { 
            adminUserService.listDocuments(
                searchFilter,
                any(),
                authenticatedUser,
                true
            ) 
        } returns pagedResult

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/document/$corporateId") {
            param("search", search)
            param("country", country)
            param("createdBy", createdBy.toString())
            param("uploadedType", uploadedType)
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
            param("sort", sort)
            param("sortBy", sortBy)
            param("isDownload", isDownload.toString())
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test delete corporate document`() {
        val corporateId = 1L
        val documentId = 1L

        every { adminUserService.deleteDocumentCorporate(documentId, corporateId, authenticatedUser) } returns true

        mockMvc.delete("/api/${AppConstant.API_VERSION}/admin/corporate/document/$corporateId/$documentId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test view corporate document url`() {
        val corporateId = 1L
        val fileId = 1L
        val documentUrl = "https://example.com/documents/test-document.pdf"

        every { adminUserService.getDocUrlCorporate(fileId, corporateId, authenticatedUser) } returns documentUrl

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/document/$corporateId/$fileId/view")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test download corporate document`() {
        val corporateId = 1L
        val fileId = 1L
        val streamingResponseBody = mockk<StreamingResponseBody>()
        val responseEntity = ResponseEntity.ok(streamingResponseBody)

        every { adminUserService.downloadDocCorporate(fileId, corporateId, authenticatedUser) } returns responseEntity

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/corporate/document/$corporateId/download")
                .param("fileId", fileId.toString())
        ).andExpect {
            MockMvcResultMatchers.status().isOk
        }
    }
}