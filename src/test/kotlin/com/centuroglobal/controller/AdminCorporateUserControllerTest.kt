package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.CorporateUserService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.payload.account.*
import com.centuroglobal.shared.data.payload.corporate.CorporateUserInfoRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.*

@WebMvcTest(controllers = [AdminCorporateUserController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCorporateUserControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var corporateUserService: CorporateUserService

    @MockkBean
    private lateinit var corporateService: CorporateService

    @MockkBean
    private lateinit var adminUserService: AdminUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test create corporate user`() {
        val request = CorporateUserRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            bandId = 1L,
            corporateId = 1L,
            countryCode = "US",
            referralCode = "",
            keepMeInformed = true,
            accounts = listOf(),
            managerUserIds = listOf(),
            dialCode = "",
            contactNo = "12345",
            isDraft = true
        )

        every { corporateUserService.createCorporateUser(any(), any(), any()) } returns mockk()

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/corporate/user") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test list corporate users`() {
        val corporateUsers = listOf(
            CorporateUsers(
                id = 1L,
                firstName = "John",
                lastName = "Doe",
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE
            ),
            CorporateUsers(
                id = 2L,
                firstName = "Jane",
                lastName = "Smith",
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE
            )
        )

        every { corporateService.retrieveAllCorporateUsers(any()) } returns corporateUsers

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/user")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test retrieve corporate user`() {
        val userId = 1L
        val corporate = Corporate(
            id = 1L,
            countryCode = "US",
            email = "<EMAIL>",
            firstName = "",
            lastName = "",
            jobTitle = "",
            status = AccountStatus.ACTIVE,
            corporateName = "",
            onBoardingInfo = null,
            keepMeInformed = true,
            bandId = 1234,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            dialCode = "",
            contactNo = "12345",
            isPrimary = true,
            aiMessageCount = 12,
            notificationSettings = listOf(),
            corporateCountryCode = "",
            partnerName = "",
            profilePhotoUrl = "",
        )

        every { corporateService.retrieveCorporate(userId) } returns corporate

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/user/$userId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test corporate user accounts listing`() {
        val userId = 1L
        val accounts = listOf(
            ReferenceData(1L, "Account 1"),
            ReferenceData(2L, "Account 2")
        )

        every { corporateService.retrieveCorporateUserAccounts(userId) } returns accounts

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/user/$userId/accounts")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test list corporate users with filters`() {
        val search = "John"
        val countryCode = "US"
        val corporateId = 1L
        val subscription = "FREE"
        val status = "ACTIVE"
        val bandName = "1"
        val createdFrom = 1640995200000L // 2022-01-01
        val createdTo = 1672531200000L // 2023-01-01
        val joinedFrom = 1640995200000L // 2022-01-01
        val joinedTo = 1672531200000L // 2023-01-01
        val partnerId = 4L
        val isPartnerCompany = true
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = false

        val userDetails = CorporateUserDetails(
            id = 1L,
            email = "<EMAIL>",
            bandName = "1",
            status = "ACTIVE",
            createdDate = 1640995200000L,
            name = "",
            subscription = "",
            joinedDate = 1234,
            lastLogonDate = 1234,
            companyName = "",
            countryCode = "",
            referredBy = "",
            partnerName = "",
            corporateId = 12,
        )

        val listingWithStats = ListingWithStatsDetails(
            data = PagedResult(listOf(userDetails), 1, 0, 1),
            stats = ListingStats(
                total = 10,
                active = 1,
                suspended = 8,
                pending = 1
            )
        )

        every { 
            corporateUserService.listCorporateUser(
                any(), any(), any()
            ) 
        } returns listingWithStats

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/user/listing") {
            param("search", search)
            param("country", countryCode)
            param("corporateId", corporateId.toString())
            param("subscription", subscription)
            param("status", status)
            param("bandId", bandName)
            param("createdFrom", createdFrom.toString())
            param("createdTo", createdTo.toString())
            param("joinedFrom", joinedFrom.toString())
            param("joinedTo", joinedTo.toString())
            param("partnerId", partnerId.toString())
            param("isPartnerCompany", isPartnerCompany.toString())
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
            param("sort", sort)
            param("sortBy", sortBy)
            param("isDownload", isDownload.toString())
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test update corporate user profile`() {
        val userId = 1L
        val request = UpdateCorporateProfileRequest(
            corporateInfo = UpdateCorporateInfoRequest(
                firstName = "John",
                lastName = "Doe",
                jobTitle = "Manager",
                corporateName = "ABC Corporation",
                countryCode = "US",
                keepMeInformed = true,
                dialCode = "+1",
                contactNo = "1234567890",
                corporateId = 1L,
                isPrimary = true,
                aiMessageCount = 10,
                notificationSettings = listOf(
                    NotificationSettingRequest(
                        key = NotificationType.CASE_GCHAT_EMAIL,
                        value = true
                    ),
                    NotificationSettingRequest(
                        key = NotificationType.CASE_UPDATE_EMAIL,
                        value = false
                    )
                ),
                profilePicS3Key = "",
                educationQualification = "",
                salary = "12345",
                relevantExperience = "4",
                bandId = 2,
                managerUserIds = listOf(),
                accounts = listOf(),
                email = "<EMAIL>"
            ),
            status = UpdateCorporateStatusRequest(
                status = "ACTIVE"
            ),
            userInfo = CorporateUserInfoRequest(
                bandId = 123,
                accounts = listOf(),
                managerUserIds = listOf()
            )
        )

        every { authenticatedUser.userType } returns "ADMIN"

        every { corporateService.updateCorporate(any(),any(), any(),any()) } returns Corporate(
            id = 1,
            email = "",
            firstName = "",
            lastName = "",
            jobTitle = "",
            status = AccountStatus.ACTIVE,
            corporateName = "",
            countryCode = "",
            onBoardingInfo = null,
            keepMeInformed = true,
            bandId = 3,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            dialCode = "",
            contactNo = "",
            isPrimary = true,
            aiMessageCount = 13,
            notificationSettings = listOf(),
            corporateCountryCode = "",
            partnerName = "",
            profilePhotoUrl = ""
        )

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/corporate/user/$userId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
        }
    }

   /* @Test
    fun `test update corporate user status`() {
        val userId = 1L
        val status = "SUSPENDED"

        every { corporateUserService.updateCorporateUserStatus(userId, status, authenticatedUser) } returns true

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/corporate/user/$userId/status") {
            param("status", status)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success").value(true)
            jsonPath("$.payload").value(true)
        }
    }*/

    @Test
    fun `test delete corporate user`() {
        val userId = 1L

        every { corporateService.deleteCorporateUser(any()) } returns true

        mockMvc.delete("/api/${AppConstant.API_VERSION}/admin/corporate/user/$userId")
            .andExpect {
                status { isOk() }
            }
    }


    @Test
    fun `test resend verification email`() {
        val userId = 1L

        every { corporateUserService.sendVerificationEmail(any()) } returns true

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/corporate/user/$userId/verification-email")
            .andExpect {
                status { isOk() }
            }
    }
}