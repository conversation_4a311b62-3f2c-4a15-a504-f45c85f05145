package com.centuroglobal.controller

import com.centuroglobal.service.CorporateUserService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.payload.account.NotificationSettingRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateInfoRequest
import com.centuroglobal.shared.data.pojo.CorporateUserResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.put

@WebMvcTest(controllers = [AdminCorporateUserProfileController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminCorporateUserProfileControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var corporateUserService: CorporateUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test get user`() {
        val userId = 1L
        val corporateId = 1L
        
        val corporateUserResponse = CorporateUserResponse(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            bandId = 1L,
            corporateId = corporateId,
            countryCode = "US",
            accounts = listOf(),
            managerUserIds = listOf(),
            dialCode = "+1",
            contactNo = "**********",
            keepMeInformed = true,
            profilePhotoUrl = "https://example.com/photo.jpg",
            corporateName = "Test Corporate",
            isPrimary = true,
            notificationSettings = listOf(),
            corporateCountryCode = "",
            educationQualification = "",
            salary = "",
            relevantExperience = ""
        )


        every { corporateUserService.fetchUser(userId, corporateId) } returns corporateUserResponse

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/user-profile/$corporateId/profile/$userId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.firstName") { value("John") }
                jsonPath("$.payload.lastName") { value("Doe") }
                jsonPath("$.payload.email") { value("<EMAIL>") }
                jsonPath("$.payload.jobTitle") { value("Manager") }
                jsonPath("$.payload.bandId") { value(1) }
                jsonPath("$.payload.corporateId") { value(corporateId) }
                jsonPath("$.payload.countryCode") { value("US") }
                jsonPath("$.payload.dialCode") { value("+1") }
                jsonPath("$.payload.contactNo") { value("**********") }
                jsonPath("$.payload.keepMeInformed") { value(true) }
                jsonPath("$.payload.profilePhotoUrl") { value("https://example.com/photo.jpg") }
                jsonPath("$.payload.corporateName") { value("Test Corporate") }
                jsonPath("$.payload.isPrimary") { value(true) }
            }
    }

    @Test
    fun `test update user profile`() {
        val userId = 1L
        
        val updateRequest = UpdateCorporateInfoRequest(
            firstName = "John",
            lastName = "Doe",
            jobTitle = "Senior Manager",
            corporateName = "Test Corporate",
            countryCode = "US",
            keepMeInformed = true,
            dialCode = "+1",
            contactNo = "**********",
            corporateId = 1L,
            isPrimary = true,
            aiMessageCount = 10,
            notificationSettings = listOf(
                NotificationSettingRequest(
                    key = NotificationType.CASE_GCHAT_EMAIL,
                    value = true
                ),
                NotificationSettingRequest(
                    key = NotificationType.CASE_UPDATE_EMAIL,
                    value = false
                )
            ),
            profilePicS3Key = "profile-pic-key",
            educationQualification = "MBA",
            salary = "120000",
            relevantExperience = "7",
            bandId = 2L,
            managerUserIds = listOf(3L, 4L),
            accounts = listOf(1L, 2L),
            email = "<EMAIL>"
        )
        
        val updatedResponse = CorporateUserResponse(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Senior Manager",
            bandId = 2L,
            corporateId = 1L,
            countryCode = "US",
            accounts = listOf(1L, 2L),
            managerUserIds = listOf(3L, 4L),
            dialCode = "+1",
            contactNo = "**********",
            keepMeInformed = true,
            profilePhotoUrl = "https://example.com/updated-photo.jpg",
            corporateName = "Test Corporate",
            isPrimary = true,
            notificationSettings = listOf(),
            corporateCountryCode = "US",
            educationQualification = "MBA",
            salary = "120000",
            relevantExperience = "7"
        )

        every { corporateUserService.updateCorporateUserForAdmins(any(), any()) } returns updatedResponse

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/corporate/user-profile/$userId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(updateRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload.firstName") { value("John") }
            jsonPath("$.payload.lastName") { value("Doe") }
            jsonPath("$.payload.email") { value("<EMAIL>") }
            jsonPath("$.payload.jobTitle") { value("Senior Manager") }
            jsonPath("$.payload.bandId") { value(2) }
            jsonPath("$.payload.corporateId") { value(1) }
            jsonPath("$.payload.countryCode") { value("US") }
            jsonPath("$.payload.accounts[0]") { value(1) }
            jsonPath("$.payload.accounts[1]") { value(2) }
            jsonPath("$.payload.managerUserIds[0]") { value(3) }
            jsonPath("$.payload.managerUserIds[1]") { value(4) }
            jsonPath("$.payload.dialCode") { value("+1") }
            jsonPath("$.payload.contactNo") { value("**********") }
            jsonPath("$.payload.keepMeInformed") { value(true) }
            jsonPath("$.payload.profilePhotoUrl") { value("https://example.com/updated-photo.jpg") }
            jsonPath("$.payload.corporateName") { value("Test Corporate") }
            jsonPath("$.payload.isPrimary") { value(true) }
        }
    }

    /*@Test
    fun `test get user with invalid id`() {
        val userId = 999L
        val corporateId = 1L
        
        val errorMessage = "User not found with id: $userId"
        
        every { corporateUserService.fetchUser(userId, corporateId) } throws RuntimeException(errorMessage)

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/corporate/user-profile/$corporateId/profile/$userId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(false) }
                jsonPath("$.error.message") { value(errorMessage) }
            }
    }*/

    /*@Test
    fun `test update user profile with invalid data`() {
        val userId = 1L
        
        val updateRequest = UpdateCorporateInfoRequest(
            firstName = "",  // Invalid: empty first name
            lastName = "Doe",
            jobTitle = "Senior Manager",
            corporateName = "Test Corporate",
            countryCode = "US",
            keepMeInformed = true,
            dialCode = "+1",
            contactNo = "**********",
            corporateId = 1L,
            isPrimary = true,
            aiMessageCount = 10,
            notificationSettings = listOf(),
            profilePicS3Key = "",
            educationQualification = "",
            salary = "",
            relevantExperience = "",
            bandId = 2L,
            managerUserIds = listOf(),
            accounts = listOf(),
            email = "invalid-email"  // Invalid email format
        )
        
        val errorMessage = "Invalid user data: First name cannot be empty"
        
        every { corporateUserService.updateCorporateUserForAdmins(any(), any()) } throws RuntimeException(errorMessage)

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/corporate/user-profile/$userId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(updateRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(false) }
            jsonPath("$.error.message") { value(errorMessage) }
        }
    }*/
}