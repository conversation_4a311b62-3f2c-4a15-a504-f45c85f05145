package com.centuroglobal.controller

import com.centuroglobal.data.payload.event.CreateSpeakersResponse
import com.centuroglobal.data.payload.event.EditEventInviteesResponse
import com.centuroglobal.facade.AdminEventFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.CircleBannerUploadResponse
import com.centuroglobal.shared.data.pojo.event.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AdminEventController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminEventControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminEventFacade: AdminEventFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test change event status`() {
        val changeEventStatus = ChangeEventStatus(
            id = 1L,
            status = EventStatus.PUBLISHED
        )

        every { adminEventFacade.eventStatusUpdate(any(), any(), any()) } returns 
            CompletableFuture.completedFuture(true)

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/event/event-status-update") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(changeEventStatus)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test delete event`() {
        val eventId = 1L

        every { adminEventFacade.deleteEvent(eventId, authenticatedUser) } returns 
            CompletableFuture.completedFuture("Event deleted successfully")

        mockMvc.delete("/api/${AppConstant.API_VERSION}/admin/event/$eventId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test upload banner picture`() {
        val eventId = 1L
        val photo = MockMultipartFile(
            "bannerImage",
            "banner.jpg",
            "image/jpeg",
            "test image content".toByteArray()
        )

        val response = CircleBannerUploadResponse(
            "https://example.com/banner.jpg",
            "https://example.com/banner.jpg"
        )

        every { adminEventFacade.uploadCoverPicture(eventId, any(), authenticatedUser) } returns 
            CompletableFuture.completedFuture(response)

        mockMvc.perform(
            MockMvcRequestBuilders.multipart("/api/${AppConstant.API_VERSION}/admin/event/$eventId/banner-image")
                .file(photo)
                .with { request ->
                    request.method = "PUT"
                    request
                }
        ).andExpect (status().isOk)
    }

    @Test
    fun `test delete banner picture`() {
        val eventId = 1L

        every { adminEventFacade.deleteBannerPicture(eventId, authenticatedUser) } returns 
            CompletableFuture.completedFuture("Banner deleted successfully")

        mockMvc.delete("/api/${AppConstant.API_VERSION}/admin/event/$eventId/banner-image")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test add update speakers`() {
        val eventId = 1L
        val speakers = listOf(
            EventSpeaker(
                id = 4,
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_CORPORATE,
                internalMemberId = 34,
                isHost = true,
                profile = EventClientProfile(
                    id = 3,
                    internalId = "id",
                    userId = 2,
                    firstName = "abc",
                    lastName = "def",
                    about = "",
                    companyName = "",
                    jobTitle = "",
                    countryCode = "IN",
                    emailId = "<EMAIL>",
                    profilePictureKey = "",
                    profilePictureFullUrl = "",
                    isAttending = true
                ),
                expertProfile = ExpertProfileSummary(
                    id = 1L,
                    bio = "Bio",
                    companyProfile = ExpertCompanyProfile(
                        name = "name",
                        companyNumber = "*********",
                        companyAddress = "companyAddress",
                        aboutBusiness = "aboutBusiness",
                        effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                        effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                        feesCurrency = "feesCurrency",
                        feesAmount = "feesAmount",
                        specialTerms = "specialTerms",
                        membershipStatus = "membershipStatus",
                        services = "services",
                        territory = "territory",
                        size = CompanySize.Size0,
                        logoFullUrl = "logofullurl",
                        sizeName = "M",
                        summary = "summary"
                    ),
                    contactEmail = "<EMAIL>",
                    contactNumber = "*********",
                    contactWebsite = "mail.com",
                    countryCode = "US",
                    countryName = "US",
                    displayName = "Trial",
                    infoVideoUrl = "url.com",
                    jobTitle = "Manager",
                    profilePictureFullUrl = "url.com"
                ),
            ),
            EventSpeaker(
                id = 5,
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_CORPORATE,
                internalMemberId = 34,
                isHost = true,
                profile = EventClientProfile(
                    id = 3,
                    internalId = "id",
                    userId = 2,
                    firstName = "abc",
                    lastName = "def",
                    about = "",
                    companyName = "",
                    jobTitle = "",
                    countryCode = "IN",
                    emailId = "<EMAIL>",
                    profilePictureKey = "",
                    profilePictureFullUrl = "",
                    isAttending = true
                ),
                expertProfile = ExpertProfileSummary(
                    id = 1L,
                    bio = "Bio",
                    companyProfile = ExpertCompanyProfile(
                        name = "name",
                        companyNumber = "*********",
                        companyAddress = "companyAddress",
                        aboutBusiness = "aboutBusiness",
                        effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                        effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                        feesCurrency = "feesCurrency",
                        feesAmount = "feesAmount",
                        specialTerms = "specialTerms",
                        membershipStatus = "membershipStatus",
                        services = "services",
                        territory = "territory",
                        size = CompanySize.Size0,
                        logoFullUrl = "logofullurl",
                        sizeName = "M",
                        summary = "summary"
                    ),
                    contactEmail = "<EMAIL>",
                    contactNumber = "*********",
                    contactWebsite = "mail.com",
                    countryCode = "US",
                    countryName = "US",
                    displayName = "Trial",
                    infoVideoUrl = "url.com",
                    jobTitle = "Manager",
                    profilePictureFullUrl = "url.com"
                ),
            )
        )

        val createUpdateSpeakers = CreateUpdateSpeakers(
            id = eventId,
            speakers = listOf()
        )

        val response = CreateSpeakersResponse(
            speakers = listOf(
                EventSpeaker(
                    id = 7,
                    type = EventSpeakerType.EXTERNAL,
                    internalMemberRole = Role.ROLE_CORPORATE,
                    internalMemberId = 34,
                    isHost = true,
                    profile = EventClientProfile(
                        id = 3,
                        internalId = "id",
                        userId = 2,
                        firstName = "abc",
                        lastName = "def",
                        about = "",
                        companyName = "",
                        jobTitle = "",
                        countryCode = "IN",
                        emailId = "<EMAIL>",
                        profilePictureKey = "",
                        profilePictureFullUrl = "",
                        isAttending = true
                    ),
                    expertProfile = ExpertProfileSummary(
                        id = 1L,
                        bio = "Bio",
                        companyProfile = ExpertCompanyProfile(
                            name = "name",
                            companyNumber = "*********",
                            companyAddress = "companyAddress",
                            aboutBusiness = "aboutBusiness",
                            effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                            effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                            feesCurrency = "feesCurrency",
                            feesAmount = "feesAmount",
                            specialTerms = "specialTerms",
                            membershipStatus = "membershipStatus",
                            services = "services",
                            territory = "territory",
                            size = CompanySize.Size0,
                            logoFullUrl = "logofullurl",
                            sizeName = "M",
                            summary = "summary"
                        ),
                        contactEmail = "<EMAIL>",
                        contactNumber = "*********",
                        contactWebsite = "mail.com",
                        countryCode = "US",
                        countryName = "US",
                        displayName = "Trial",
                        infoVideoUrl = "url.com",
                        jobTitle = "Manager",
                        profilePictureFullUrl = "url.com"
                    ),
                ),
                EventSpeaker(
                    id = 6,
                    type = EventSpeakerType.EXTERNAL,
                    internalMemberRole = Role.ROLE_CORPORATE,
                    internalMemberId = 34,
                    isHost = true,
                    profile = EventClientProfile(
                        id = 3,
                        internalId = "id",
                        userId = 2,
                        firstName = "abc",
                        lastName = "def",
                        about = "",
                        companyName = "",
                        jobTitle = "",
                        countryCode = "IN",
                        emailId = "<EMAIL>",
                        profilePictureKey = "",
                        profilePictureFullUrl = "",
                        isAttending = true
                    ),
                    expertProfile = ExpertProfileSummary(
                        id = 1L,
                        bio = "Bio",
                        companyProfile = ExpertCompanyProfile(
                            name = "name",
                            companyNumber = "*********",
                            companyAddress = "companyAddress",
                            aboutBusiness = "aboutBusiness",
                            effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                            effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                            feesCurrency = "feesCurrency",
                            feesAmount = "feesAmount",
                            specialTerms = "specialTerms",
                            membershipStatus = "membershipStatus",
                            services = "services",
                            territory = "territory",
                            size = CompanySize.Size0,
                            logoFullUrl = "logofullurl",
                            sizeName = "M",
                            summary = "summary"
                        ),
                        contactEmail = "<EMAIL>",
                        contactNumber = "*********",
                        contactWebsite = "mail.com",
                        countryCode = "US",
                        countryName = "US",
                        displayName = "Trial",
                        infoVideoUrl = "url.com",
                        jobTitle = "Manager",
                        profilePictureFullUrl = "url.com"
                    ),
                )
            ),
            id = 123
        )

        every { adminEventFacade.addUpdateSpeakers(any(), any(), any()) } returns 
            CompletableFuture.completedFuture(response)

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/event/event-speakers") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(createUpdateSpeakers)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test add update sessions`() {
        val eventId = 1L
        val sessions = listOf(
            EventSession(
                id = 3,
                startTime = Date(),
                endTime = Date(),
                speakers = listOf(),
                name = "",
                about = "",
                date = 342,
                timeZone = "",
                duration = ""
            ),
            EventSession(
                id = 4,
                startTime = Date(),
                endTime = Date(),
                speakers = listOf(),
                name = "",
                about = "",
                date = 342,
                timeZone = "",
                duration = ""
            )
        )

        val createUpdateSessions = CreateUpdateSessions(
            id = eventId,
            sessions = listOf()
        )

        val pagedSessions = PagedResult(
            rows = listOf(
                EventSession(
                    id = 4,
                    startTime = Date(),
                    endTime = Date(),
                    speakers = listOf(),
                    name = "",
                    about = "",
                    date = 342,
                    timeZone = "",
                    duration = ""
                ),
                EventSession(
                    id = 4,
                    startTime = Date(),
                    endTime = Date(),
                    speakers = listOf(),
                    name = "",
                    about = "",
                    date = 342,
                    timeZone = "",
                    duration = ""
                )
            ),
            totalElements = 2,
            currentPage = 0,
            totalPages = 1
        )

        every { adminEventFacade.addUpdateSessions(any(), any(), any()) } returns 
            CompletableFuture.completedFuture(pagedSessions)

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/event/event-sessions") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(createUpdateSessions)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test add update invitees`() {
        val eventId = 1L
        val inviteesExternal = listOf(
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_CORPORATE,
                internalMemberId = 432,
                isHost = false,
                profile = ClientView(            1,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.CORPORATE,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            )
        )
        
        val inviteesInternal = listOf(
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_CORPORATE,
                internalMemberId = 324,
                isHost = false,
                profile = ClientView(            1,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.CORPORATE,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            )
        )

        val createUpdateInvitees = CreateUpdateInvitees(
            id = eventId,
            inviteesExternal = "",
            inviteesInternal = listOf()
        )

        val response = listOf(
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_CORPORATE,
                internalMemberId = 432,
                isHost = false,
                profile = ClientView(            1,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.CORPORATE,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            ),
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_CORPORATE,
                internalMemberId = 4332,
                isHost = false,
                profile = ClientView(            1,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.CORPORATE,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            )
        )

        every { 
            adminEventFacade.addUpdateInvitees(
                any(),
                any(),
                any(),
                any()
            ) 
        } returns CompletableFuture.completedFuture(response)

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/event/event-invitees") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(createUpdateInvitees)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test get invitees to edit`() {
        val eventId = 1L

        val response = EditEventInviteesResponse(
            internal = listOf(),
            external = ""
        )

        every { adminEventFacade.getInviteesToEdit(eventId, authenticatedUser) } returns 
            CompletableFuture.completedFuture(response)

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/event/invitee-edit/$eventId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test delete event with error`() {
        val eventId = 999L  // Non-existent event ID

        val errorMessage = "Event not found with ID: $eventId"
        val completableFuture = CompletableFuture<String>()
        completableFuture.completeExceptionally(RuntimeException(errorMessage))

        every { adminEventFacade.deleteEvent(eventId, authenticatedUser) } returns completableFuture

        mockMvc.delete("/api/${AppConstant.API_VERSION}/admin/event/$eventId")
            .andExpect {
                status { isOk() }
            }
    }
}