package com.centuroglobal.controller

import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.entity.ExpertContractEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.account.CompanyRequest
import com.centuroglobal.shared.data.payload.account.CreatePrimaryExpertUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime

@WebMvcTest(controllers = [AdminExpertController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminExpertControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var partnerService: PartnerService

    @MockkBean
    private lateinit var expertUserService: ExpertUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test retrieve secondary experts`() {
        val companyId = 1L
        
        val secondaryExperts = mutableListOf(
            Client(
                id = 1,
                email = "<EMAIL>",
                fullName = "a",
                status = AccountStatus.ACTIVE,
                company = "",
                subscription = true,
                userType = UserType.CORPORATE,
                createdDate = 12345,
                referredBy = "",
                referral = 4,
                userId = 4,
                countryCode = "IN",
                expertiseIds = "",
                isLinkedin = true,
                country = "IN",
                expertise = "",
                subscriptionType = "",
                expertType = "",
                corporateType = "",
                secondaryUserCount = 3,
                bandName = "",
                lastLoginTime = 1234,
                lastTermsViewDate = 1234
            ),
            Client(
                id = 122,
                email = "<EMAIL>",
                fullName = "a",
                status = AccountStatus.ACTIVE,
                company = "",
                subscription = true,
                userType = UserType.CORPORATE,
                createdDate = 12345,
                referredBy = "",
                referral = 4,
                userId = 4,
                countryCode = "IN",
                expertiseIds = "",
                isLinkedin = true,
                country = "IN",
                expertise = "",
                subscriptionType = "",
                expertType = "",
                corporateType = "",
                secondaryUserCount = 3,
                bandName = "",
                lastLoginTime = 1234,
                lastTermsViewDate = 1234
            )
        )

        every { expertUserService.retrieveSecondaryExpert(companyId, 4) } returns secondaryExperts

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/$companyId/secondary")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload[0].id") { value(1) }
            }
    }

    @Test
    fun `test update expert company`() {
        val companyId = 1L
        
        val request = ExpertCompanyProfile(

            name = "Test Name",
            aboutBusiness = "",
            companyAddress = "", companyNumber = "",
            effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            associatedPartners = mutableListOf(),
            logoFullUrl = "",
            sizeName = "",
            partnerId = 4,
            contract = "",
            aiMessageCount = 5
        )
        
        val updatedCompany = ExpertCompanyProfileEntity(
            id = 1,
            name = "Test Name",
            users = mutableListOf(),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        every { expertUserService.updateCompanyProfile(companyId, request) } returns updatedCompany

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/expert/$companyId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(companyId) }
        }
    }

    @Test
    fun `test retrieve expert company`() {
        val companyId = 1L
        
        val expertCompanyProfile = ExpertCompanyProfile(

            name = "Test Name",
            aboutBusiness = "",
            companyAddress = "", companyNumber = "",
            effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            associatedPartners = mutableListOf(),
            logoFullUrl = "",
            sizeName = "",
            partnerId = 4,
            contract = "",
            aiMessageCount = 5
        )

        every { expertUserService.retrieveExpertCompany(companyId) } returns expertCompanyProfile

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/$companyId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
            }
    }

    @Test
    fun `test retrieve primary experts`() {
        val primaryExperts = mutableListOf(
            ExpertUserSummary(
                id = 2L,
                email = "<EMAIL>",
                displayName = "",
            ),
            ExpertUserSummary(
                id = 23L,
                email = "<EMAIL>",
                displayName = "",
            )
        )

        every { expertUserService.retrievePrimaryExpert(4) } returns primaryExperts

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/primary")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload[0].id") { value(2) }
            }
    }

    @Test
    fun `test create expert company`() {
        val companyProfile = ExpertCompanyProfile(
            name = "name",
            companyNumber = "*********",
            companyAddress = "companyAddress",
            aboutBusiness = "aboutBusiness",
            effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            feesCurrency = "feesCurrency",
            feesAmount = "feesAmount",
            specialTerms = "specialTerms",
            membershipStatus = "membershipStatus",
            services = "services",
            territory = "territory",
            size = CompanySize.Size0,
            logoFullUrl = "logofullurl",
            sizeName = "M",
            summary = "summary"
        )
        
        val request = CreatePrimaryExpertUserRequest(
            "<EMAIL>",
            "test",
            "PJ",
            "Developer",
            "IN",
            "",
            CompanyRequest(
                "tet", "34", "Arora", "mxs", "qmw",
                "nl", "5", "small", 5L, 6L, "INR", "9000",
                "NA", "NA", "NA", "kl", 8L,
                6L, "abc", 0L
            ),
            aiMessageCount = 0
        )
        
        val newCompanyId = 5L

        every { expertUserService.createPrimaryExpertUser(any(), any()) } returns newCompanyId

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/expert") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(newCompanyId) }
        }
    }

    @Test
    fun `test expert users by company ids`() {
        val companyIds = "1,2,3"
        
        val expertUsers = listOf(
            ExpertUserReferenceData(1L, "<EMAIL>","John", "Expert"),
            ExpertUserReferenceData(2L, "<EMAIL>","John", "Expert"),
            ExpertUserReferenceData(3L, "<EMAIL>","John", "Expert")
        )

        every { 
            expertUserService.retrieveExpertUsersByCompanyIds(4, listOf(1L, 2L, 3L)) 
        } returns expertUsers

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/expert-users") {
            param("companyIds", companyIds)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload[0].id") { value(1) }
        }
    }

    @Test
    fun `test list expert companies`() {
        val isPartnerCompany = true
        
        val expertCompanies = listOf(
            ReferenceData(
                id = 1L,
                name = "Expert Company 1"
            ),
            ReferenceData(
                id = 2L,
                name = "Expert Company 2"
            ),
            ReferenceData(
                id = 3L,
                name = "Expert Company 3"
            )
        )

        every { 
            expertUserService.retrieveExpertCompany(any(), any())
        } returns expertCompanies

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/companies") {
            param("isPartnerCompany", isPartnerCompany.toString())
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload[0].id") { value(1) }
            jsonPath("$.payload[0].name") { value("Expert Company 1") }
            jsonPath("$.payload[1].id") { value(2) }
            jsonPath("$.payload[1].name") { value("Expert Company 2") }
            jsonPath("$.payload[2].id") { value(3) }
            jsonPath("$.payload[2].name") { value("Expert Company 3") }
        }
    }

    @Test
    fun `test retrieve secondary experts with empty result`() {
        val companyId = 999L  // Non-existent company ID
        
        every { expertUserService.retrieveSecondaryExpert(companyId, 4) } returns mutableListOf()

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/$companyId/secondary")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload") { isEmpty() }
            }
    }
}