package com.centuroglobal.controller

import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.payload.account.CreateSecondaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateExpertPersonalInfoRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime

@WebMvcTest(controllers = [AdminExpertUserController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminExpertUserControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var expertUserService: ExpertUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test retrieve expert user`() {
        val userId = 1L
        
        val expertProfile = ExpertProfile(
            id = userId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Senior Consultant",
            profilePictureFullUrl = "https://example.com/profile/john.jpg",
            countryCode = "US",
            contactEmail = "<EMAIL>",
            contactNumber = "+*********0",
            contactWebsite = "https://johndoe.com",
            status = AccountStatus.ACTIVE,
            infoVideoUrl = "https://example.com/john-intro.mp4",
            displayName = "",
            countryRegionId = 33,
            expertiseId = listOf(),
            bio = "",
            expertType = "",
            viewContract = true,
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            associations = listOf(),
        )

        every { expertUserService.retrieveProfile(userId) } returns expertProfile

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/user/$userId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.id") { value(userId) }
                jsonPath("$.payload.firstName") { value("John") }
                jsonPath("$.payload.lastName") { value("Doe") }
                jsonPath("$.payload.email") { value("<EMAIL>") }
            }
    }

    @Test
    fun `test update expert user`() {
        val userId = 1L
        
        val updateRequest = UpdateExpertPersonalInfoRequest(
            firstName = "John",
            lastName = "Doe",
            jobTitle = "Lead Expert",
            bio = "Updated bio with 15+ years of experience",
            countryCode = "UK",
            displayName = "",
            regionId = 23,
            expertiseId = listOf(),
            infoVideoUrl = "",
            profilePicS3Key = ""
        )
        
        val updatedProfile = ExpertProfile(
            id = userId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Senior Consultant",
            profilePictureFullUrl = "https://example.com/profile/john.jpg",
            countryCode = "US",
            contactEmail = "<EMAIL>",
            contactNumber = "+*********0",
            contactWebsite = "https://johndoe.com",
            status = AccountStatus.ACTIVE,
            infoVideoUrl = "https://example.com/john-intro.mp4",
            displayName = "",
            countryRegionId = 33,
            expertiseId = listOf(),
            bio = "",
            expertType = "",
            viewContract = true,
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            associations = listOf(),
        )

        every { expertUserService.updateProfileByAdmin(userId, 1L, updateRequest) } returns updatedProfile

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/expert/user/$userId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(updateRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload.id") { value(userId) }
            jsonPath("$.payload.firstName") { value("John") }
            jsonPath("$.payload.lastName") { value("Doe") }
        }
    }

    @Test
    fun `test create secondary expert user`() {
        val request = CreateSecondaryExpertUserRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            jobTitle = "Immigration Specialist",
            expertCompanyId = 1L,
            countryCode = "",
            expertType = "",
            partnerId = 3,
            profileImage = ""
        )

        every { expertUserService.createSecondaryExpertUser(any(), any()) } returns Unit

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/expert/user") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(request)
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success") { value(true) }
        }
    }

    @Test
    fun `test get expert user accounts`() {
        val userId = 1L
        
        val accounts = mapOf(
            "accounts" to listOf(
                ReferenceData(1L, "Account 1"),
                ReferenceData(2L, "Account 2"),
                ReferenceData(3L, "Account 3")
            ),
            "expertises" to listOf(
                ReferenceData(1L, "Finance"),
                ReferenceData(2L, "Legal")
            )
        )

        every { expertUserService.listAccounts(userId) } returns accounts

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/user/$userId/accounts")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.accounts[0].id") { value(1) }
                jsonPath("$.payload.accounts[0].name") { value("Account 1") }
                jsonPath("$.payload.accounts[1].id") { value(2) }
                jsonPath("$.payload.accounts[1].name") { value("Account 2") }
                jsonPath("$.payload.accounts[2].id") { value(3) }
                jsonPath("$.payload.accounts[2].name") { value("Account 3") }
                jsonPath("$.payload.expertises[0].id") { value(1) }
                jsonPath("$.payload.expertises[0].name") { value("Finance") }
                jsonPath("$.payload.expertises[1].id") { value(2) }
                jsonPath("$.payload.expertises[1].name") { value("Legal") }
            }
    }

    @Test
    fun `test list expert users`() {
        val search = "John"
        val countryCode = "US"
        val companyId = 1L
        val expertType = "PRIMARY"
        val status = "ACTIVE"
        val joinedFrom = 1640995200000L // 2022-01-01
        val joinedTo = 1672531200000L // 2023-01-01
        val createdFrom = 1640995200000L // 2022-01-01
        val createdTo = 1672531200000L // 2023-01-01
        val isPartnerCompany = true
        val pageIndex = 0
        val pageSize = 20
        
        val expertUserDetails = ExpertUserDetails(
            id = 1L,
            email = "<EMAIL>",
            status = "ACTIVE",
            createdDate = 1640995200000L,
            name = "John Doe",
            joinedDate = 1641081600000L,
            lastLogonDate = 1641168000000L,
            companyName = "Expert Company",
            countryCode = "US",
            expertType = "",
            associations = listOf()
        )
        
        val listingWithStats = ListingWithStatsDetails(
            data = PagedResult(listOf(expertUserDetails), 1, 0, 1),
            stats = ListingStats(
                total = 10,
                active = 8,
                suspended = 1,
                pending = 1
            )
        )

        every { 
            expertUserService.listExpertUser(
                any(),any(),any()
            ) 
        } returns listingWithStats

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/user/listing") {
            param("search", search)
            param("country", countryCode)
            param("companyId", companyId.toString())
            param("expertType", expertType)
            param("status", status)
            param("joinedFrom", joinedFrom.toString())
            param("joinedTo", joinedTo.toString())
            param("createdFrom", createdFrom.toString())
            param("createdTo", createdTo.toString())
            param("isPartnerCompany", isPartnerCompany.toString())
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
            param("sort", "DESC")
            param("sortBy", "createdDate")
            param("isDownload", "false")
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload.data.rows[0].id") { value(1) }
            jsonPath("$.payload.data.rows[0].email") { value("<EMAIL>") }
            jsonPath("$.payload.data.rows[0].status") { value("ACTIVE") }
            jsonPath("$.payload.data.rows[0].name") { value("John Doe") }
            jsonPath("$.payload.data.rows[0].companyName") { value("Expert Company") }
            jsonPath("$.payload.data.totalElements") { value(1) }
            jsonPath("$.payload.stats.total") { value(10) }
            jsonPath("$.payload.stats.active") { value(8) }
            jsonPath("$.payload.stats.suspended") { value(1) }
            jsonPath("$.payload.stats.pending") { value(1) }
        }
    }

    @Test
    fun `test list expert users with empty result`() {
        val search = "NonExistentUser"
        
        val emptyListingWithStats = ListingWithStatsDetails(
            data = PagedResult(listOf<ExpertUserDetails>(), 0, 0, 0),
            stats = ListingStats(
                total = 0,
                active = 0,
                suspended = 0,
                pending = 0
            )
        )

        every { 
            expertUserService.listExpertUser(
                any(),any(),any()
            ) 
        } returns emptyListingWithStats

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/expert/user/listing") {
            param("search", search)
            param("pageIndex", "0")
            param("pageSize", "20")
            param("sort", "DESC")
            param("sortBy", "createdDate")
            param("isDownload", "false")
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload.data.rows") { isEmpty() }
            jsonPath("$.payload.data.totalElements") { value(0) }
            jsonPath("$.payload.stats.total") { value(0) }
        }
    }
}