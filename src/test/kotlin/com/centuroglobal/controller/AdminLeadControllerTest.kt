package com.centuroglobal.controller

import com.centuroglobal.data.payload.lead.UpdateLeadRequest
import com.centuroglobal.facade.LeadFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.LeadType
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.put
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AdminLeadController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminLeadControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var leadFacade: LeadFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test update lead`() {
        val leadId = "lead123"
        val updateRequest = UpdateLeadRequest(
            title = "Updated Lead",
            description = "Updated Description",
            expertiseId = listOf(3, 4),
            countryCode = "UK",
            regionId = 5,
            leadStatus = LeadStatus.ACTIVE
        )
        
        val updatedLead = LeadDetail(
            id = "1",
            title = "Sample Lead",
            description = "Sample Description",
            expertiseId = listOf(1, 2),
            expertiseName = listOf("Expertise 1", "Expertise 2"),
            countryCode = "US",
            countryName = "United States",
            regionId = 4,
            regionName = "Region Name",
            status = LeadStatus.ACTIVE,
            createdDate = System.currentTimeMillis(),
            responses = listOf(),
            creator = Creator(
                userId = 1,
                displayName = "John Doe",
                profilePictureFullUrl = "https://example.com/profile.jpg",
                jobTitle = "Manager",
                companyName = "Test Company",
                countryName = "United States",
                regionName = "Region Name"
            )
        )

        val completableFuture = CompletableFuture.completedFuture(updatedLead)
        every { leadFacade.update(leadId, updateRequest, 1L) } returns completableFuture

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/lead/$leadId") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(updateRequest)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test search leads`() {
        val search = LeadsSearchCriteria(
            countryCode = "US",
            expertiseId = "",
            status = "ACTIVE",
            pageIndex = 0,
            pageSize = 20,
            sort = "DESC",
            sortBy = "createdDate"
        )
        
        val leadSummary = AdminLeadSummary(
            id = "1",
            title = "Sample Lead",
            expertiseName = listOf("Expertise 1", "Expertise 2"),
            countryName = "United States",
            regionName = "Region Name",
            status = LeadStatus.ACTIVE,
            createdDate = System.currentTimeMillis(),
            responseCount = 5,
            leadType = LeadType.EXPERT,
            createdByName = "",
            companyName = ""
        )
        
        val pagedResult = PagedResult(
            rows = listOf(leadSummary),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        val completableFuture = CompletableFuture.completedFuture( pagedResult)
        every { 
            leadFacade.searchLeadsForAdmin(
                any(),
                any()
            ) 
        } returns completableFuture

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/lead") {
            param("search", "")
            param("countryCode", "")
            param("expertiseId", search.expertiseId.toString())
            param("status", "")
            param("createdFrom", "")
            param("createdTo", "")
            param("pageIndex", search.pageIndex.toString())
            param("pageSize", search.pageSize.toString())
            param("sort", search.sort)
            param("sortBy", search.sortBy)
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `test retrieve lead`() {
        val leadId = "lead123"
        
        val leadDetail = LeadDetail(
            id = "1",
            title = "Sample Lead",
            description = "Sample Description",
            expertiseId = listOf(1, 2),
            expertiseName = listOf("Expertise 1", "Expertise 2"),
            countryCode = "US",
            countryName = "United States",
            regionId = 4,
            regionName = "Region Name",
            status = LeadStatus.ACTIVE,
            createdDate = System.currentTimeMillis(),
            responses = listOf(),
            creator = Creator(
                userId = 1,
                displayName = "John Doe",
                profilePictureFullUrl = "https://example.com/profile.jpg",
                jobTitle = "Manager",
                companyName = "Test Company",
                countryName = "United States",
                regionName = "Region Name"
            )
        )

        val completableFuture = CompletableFuture.completedFuture( leadDetail)
        every { leadFacade.retrieve(any(), any()) } returns completableFuture

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/lead/$leadId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test delete lead`() {
        val leadId = "lead123"
        val successMessage = "Lead deleted successfully"

        val completableFuture = CompletableFuture.completedFuture( successMessage)
        every { leadFacade.delete(leadId, 1L) } returns completableFuture

        mockMvc.delete("/api/${AppConstant.API_VERSION}/admin/lead/$leadId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test recent leads`() {
        val recentLeads = listOf(
            RecentLeadSummary(
                id = "1",
                title = "Sample Lead",
                description = "Sample Description",
                expertiseName = listOf("Expertise 1", "Expertise 2"),
                countryName = "United States",
                regionName = "Region Name",
                status = LeadStatus.ACTIVE,
                createdDate = System.currentTimeMillis()
            ),
            RecentLeadSummary(
                id = "2",
                title = "Sample Lead",
                description = "Sample Description",
                expertiseName = listOf("Expertise 1", "Expertise 2"),
                countryName = "United States",
                regionName = "Region Name",
                status = LeadStatus.ACTIVE,
                createdDate = System.currentTimeMillis()
            )
        )

        val completableFuture = CompletableFuture.completedFuture( recentLeads)
        every { leadFacade.recent() } returns completableFuture

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/lead/recent")
            .andExpect {
                status { isOk() }
            }
    }


    @Test
    fun `test search leads with empty result`() {
        val search = LeadsSearchCriteria(
            pageIndex = 0,
            pageSize = 20,
            sort = "DESC",
            sortBy = "createdDate"
        )
        
        val emptyPagedResult = PagedResult<AdminLeadSummary>(
            rows = listOf(),
            totalElements = 0,
            currentPage = 0,
            totalPages = 0
        )

        val completableFuture = CompletableFuture.completedFuture( emptyPagedResult)
        every { 
            leadFacade.searchLeadsForAdmin(
                any(),
                any()
            ) 
        } returns completableFuture

        mockMvc.get("/api/${AppConstant.API_VERSION}/admin/lead") {
            param("search", "")
            param("pageIndex", search.pageIndex.toString())
            param("pageSize", search.pageSize.toString())
            param("sort", search.sort)
            param("sortBy", search.sortBy)
        }.andExpect {
            status { isOk() }
        }
    }

}