package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.MigrationService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.repository.ExpertCompanyProfileRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDateTime

@WebMvcTest(controllers = [AdminMigrationController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminMigrationControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminUserService: AdminUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @MockkBean
    private lateinit var expertUserRepository: ExpertUserRepository

    @MockkBean
    private lateinit var expertCompanyProfileRepository: ExpertCompanyProfileRepository

    @MockkBean
    private lateinit var migrationService: MigrationService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test update blueprint`() {
        // Mock data
        val expertUser = mockk<ExpertUserEntity>()
        val companyProfile = mockk<ExpertCompanyProfileEntity>()
        
        // Mock repository behavior
        every { expertUserRepository.findAll() } returns listOf(expertUser)
        every { expertUser.companyProfile } returns companyProfile
        every { companyProfile.id } returns 1L
        every { expertCompanyProfileRepository.findById(1L) } returns java.util.Optional.of(companyProfile)
        every { expertUser.lastTermsViewDate } returns LocalDateTime.now()
        every { companyProfile.contractAcceptedDate = any() } returns Unit
        every { expertCompanyProfileRepository.save(any()) } returns companyProfile

        mockMvc.put("/api/${AppConstant.API_VERSION}/admin/migration") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(true) }
        }

        // Verify repository interactions
        verify { expertUserRepository.findAll() }
        verify { expertCompanyProfileRepository.findById(1L) }
        verify { expertCompanyProfileRepository.save(any()) }
    }



    @Test
    fun `test migrate db for case history actionFor`() {
        val file = MockMultipartFile(
            "file",
            "test.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "test content".toByteArray()
        )

        every { migrationService.caseHistoryActionForMigration(any()) } returns true

        mockMvc.perform(
            multipart("/api/${AppConstant.API_VERSION}/admin/migration/migration/case_history_actionFor")
                .file(file)
        ).andExpect (
            status().isOk
        )

        verify { migrationService.caseHistoryActionForMigration(any()) }
    }

    @Test
    fun `test migrate subscription`() {
        val planId = 1L
        val startDate = 1640995200000L
        val endDate = 1672531200000L

        every { migrationService.subscriptionPlansMigration(planId, startDate, endDate) } returns 10

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/migration/subscription") {
            contentType = MediaType.APPLICATION_JSON
            param("planId", planId.toString())
            param("startDate", startDate.toString())
            param("endDate", endDate.toString())
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(10) }
        }

        verify { migrationService.subscriptionPlansMigration(planId, startDate, endDate) }
    }

    @Test
    fun `test migrate client doc files`() {
        every { adminUserService.migrateClientDocsFiles(authenticatedUser) } returns true

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/migration/migrate-client-doc-file") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(true) }
        }

        verify { adminUserService.migrateClientDocsFiles(authenticatedUser) }
    }

    @Test
    fun `test migrate case group chats`() {
        every { migrationService.migrateCaseGroupChat() } returns Unit

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/migration/migrate-case-group-chat") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success") { value(true) }
        }

        verify { migrationService.migrateCaseGroupChat() }
    }

    @Test
    fun `test migrate task details`() {
        every { migrationService.migrateTaskCompanyAndCreatedDetails() } returns Unit

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/migration/migrate-task-details") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success") { value(true) }
        }

        verify { migrationService.migrateTaskCompanyAndCreatedDetails() }
    }

    @Test
    fun `test migrate case emails by category`() {
        every { migrationService.migarateCaseEmailsByCategory() } returns Unit

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/migration/migrate-case-emails-by-category") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isCreated() }
            jsonPath("$.success") { value(true) }
        }

        verify { migrationService.migarateCaseEmailsByCategory() }
    }

    @Test
    fun `test migrate subscription with invalid parameters`() {
        val planId = -1L  // Invalid plan ID
        val startDate = 1672531200000L  // 2023-01-01
        val endDate = 1640995200000L    // 2022-01-01 (before start date)

        every { migrationService.subscriptionPlansMigration(planId, startDate, endDate) } returns 0

        mockMvc.post("/api/${AppConstant.API_VERSION}/admin/migration/subscription") {
            contentType = MediaType.APPLICATION_JSON
            param("planId", planId.toString())
            param("startDate", startDate.toString())
            param("endDate", endDate.toString())
        }.andExpect {
            status { isOk() }
        }

        verify { migrationService.subscriptionPlansMigration(planId, startDate, endDate) }
    }
}