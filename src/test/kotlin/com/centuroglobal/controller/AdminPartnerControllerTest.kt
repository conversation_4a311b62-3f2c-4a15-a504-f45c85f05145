package com.centuroglobal.controller

import com.centuroglobal.data.pojo.UpdatePartnerRequest
import com.centuroglobal.data.pojo.UpdatePartnerUserDetails
import com.centuroglobal.service.CorporateService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.time.LocalDateTime


@WebMvcTest(controllers = [AdminPartnerController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminPartnerControllerTest(@Autowired val mockMvc: MockMvc){

    @MockkBean
    lateinit var partnerService: PartnerService

    @MockkBean
    lateinit var corporateService: CorporateService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private var partnerEntity = PartnerEntity(
        id = 1,
        name = "Test Name",
        country = "IN",
        status = CorporateStatus.ACTIVE,
        rootUserId = 0,
        corporates = emptyList(),
        casesManaged = PartnerCaseType.CG,
        queriesManaged = PartnerCaseType.CG,
        contractToDate = LocalDateTime.now(),
        contractFromDate = LocalDateTime.now(),
        band = null,
        createdFrom = PartnerType.EXPERT,
        referenceId = 0L,
        companyLogo = "string",
        corporateAccess = "string"
    )

    private val authenticatedUser: AuthenticatedUser = mockk()

    private val mapper = jacksonObjectMapper()

    @BeforeEach
    fun setup(){
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `get all partners from controller`() {
        val search = "Test"
        val country = "US"
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val status = CorporateStatus.ACTIVE
        val partnerSearchFilter = PartnerSearchFilter.Builder.build(search, country, null, null, status)

        val pageRequest = PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
        val totalElements = 1L


        val partners = PagedResult<PartnerList?>(listOf(
            PartnerList(
                id = 1,
                name = "Test Name",
                country = "IN",
                status = CorporateStatus.ACTIVE,
                rootUserId = 0,
                rootUserEmail = "Test email",
                rootUserName = "Test Name",
                corporates = 0,
                experts = 0,
                users = 0,
                createdDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                features = listOf()
            )
        ), totalElements, 0, 1)

        every { partnerService.listPartner(partnerSearchFilter, pageRequest,  authenticatedUser) } returns partners


        mockMvc.perform(
            MockMvcRequestBuilders.get("/api/${AppConstant.API_VERSION}/admin/partner/listing")
                .param("search", search)
                .param("country", country)
                .param("status", status.toString())
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").isMap)
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").isNotEmpty)
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload.totalElements").value(totalElements))
    }

    @Test
    fun `update partner from controller`(){

        val userId = 12L

        val updatePartnerRequest = UpdatePartnerRequest(
            name = "Test Name",
            country = "IN",
            startDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            endDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            primaryColor = "color1",
            secondaryColor = "color2",
            rootUserDetails = UpdatePartnerUserDetails(
                firstName = "Test",
                lastName = "Name",
                jobTitle = "test",
                country = "IN",
                contactNumber = "123",
                dialCode = "91",
                profilePicS3Key = "string"
            ),
            features = emptyList(),
            companyLogo = "string",
            corporateFeatures = listOf()

        )



        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns userId

        every { partnerService.updatePartner(userId, updatePartnerRequest, authenticatedUser) } returns partnerEntity

        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(
            MockMvcRequestBuilders.put("/api/${AppConstant.API_VERSION}/admin/partner/$userId")
            .content(mapper.writeValueAsString(updatePartnerRequest))
            .contentType(
                MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8"))
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
    }

    /*@Test
    fun `Partner From Corporate From Controller`(){

        val partnerId = 1L
        val corporates =listOf(CorporateEntity(
            id = 1,
            name = "Test Name",
            countryCode = "IN",
            status = CorporateStatus.ACTIVE,
            rootUserId = 0,
            users = emptyList(),
            lastUpdatedBy = 0,
            subscriptionActive = true

        ))
        every { authenticatedUser.companyId } returns 1
        every { partnerService.retrieveCorporates(1) } returns  corporates.filter { it.id != -1L }
            .map { ReferenceData(it.id, it.name) }

        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(MockMvcRequestBuilders.get("/api/${AppConstant.API_VERSION}/admin/partner/$partnerId/corporates"))
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").isNotEmpty())
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload[0].id").value(partnerEntity.id))
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload[0].name").value(partnerEntity.name))
    }*/

    /*@Test
    fun `Partner From expertCompany From Controller`(){

        val partnerId = 1L

        val expertCompany =listOf(
            ExpertCompanyProfileEntity(
                id = 1,
                name = "Test Name",
                users = mutableListOf(),
                lastUpdatedBy = 0,
                aboutBusiness = "",
                account = AccountEntity(
                    name = "Test Name",
                    corporate = null
                ),
                companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
                effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
                feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
                companyType = ExpertCompanyType.EXPERT
        )
        )
        every { authenticatedUser.companyId } returns 1
        every { partnerService.retrieveExpertCompany(1) } returns  expertCompany.filter { it.id != -1L }
            .map { ReferenceData(it.id, it.name) }

        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(MockMvcRequestBuilders.get("/api/${AppConstant.API_VERSION}/admin/partner/$partnerId/expert-companies"))
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").isNotEmpty())
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload[0].id").value(partnerEntity.id))
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload[0].name").value(partnerEntity.name))
    }*/


    @Test
    fun `corporate root user details From Controller`(){

        val companyId = 1L
        val companyType = "CORPORATE"

        val rootUser = CorporateUserEntity()


        every { authenticatedUser.companyId } returns 1
        every { partnerService.retrieveRootUserDetails(companyType,companyId) } returns  mapOf(
            "firstName" to rootUser.firstName, "lastName" to rootUser.lastName,
            "jobTitle" to rootUser.jobTitle, "email" to rootUser.email, "country" to rootUser.countryCode
        )

        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(MockMvcRequestBuilders.get("/api/${AppConstant.API_VERSION}/admin/partner/company")
            .param("companyId", companyId.toString())
            .param("companyType", companyType)
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").isNotEmpty())
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload.firstName").value(rootUser.firstName))

    }

    @Test
    fun `get all partners details from controller`() {

        val partnerId = 1L
        val allPartnerList =
            AllPartnerList(
                id = 1,
                name = "Test Name",
                country = "IN",
                status = CorporateStatus.ACTIVE,
                rootUserId = 0,
                rootUserEmail = "",
                rootUserLastLoginDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                rootUserCreatedDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                rootUserFirstName = "Test",
                rootUserLastName = "Name",
                rootUserJobTitle = "",
                rootUserStatus = AccountStatus.ACTIVE,
                casesManagedBy = PartnerCaseType.CG,
                queriesManagedBy = PartnerCaseType.CG,
                contractToDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                contractFromDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                createdFrom = PartnerType.EXPERT,
                createReferenceId = 0L,
                features = listOf(),
                corporateFeatures = listOf()
            )


        every { authenticatedUser.companyId } returns 1
        every { partnerService.retrieveAllPartnerDetails(partnerId) } returns allPartnerList


        mockMvc.perform(
            MockMvcRequestBuilders.get("/api/${AppConstant.API_VERSION}/admin/partner/$partnerId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").isMap)
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").isNotEmpty)
    }

    /*@Test
    fun `update company status from controller`(){

        val companyId = 12L

        val updatePartnerRequest = UpdateCompanyStatusRequest(
            id = companyId,
            type = UserType.PARTNER,
            status = AccountStatus.ACTIVE
        )

        every { partnerService.updateCompanyStatus(updatePartnerRequest, authenticatedUser) } returns true

        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(
            MockMvcRequestBuilders.put("/api/${AppConstant.API_VERSION}/admin/partner/company-status")
                .content(mapper.writeValueAsString(updatePartnerRequest))
                .contentType(
                    MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8"))
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
    }*/


    /*@Test
    fun `should sign up corporate with partner`() {
        // Mock the necessary dependencies and inputs
        val signUpRequest = SignUpRequest(
            email = "<EMAIL>",
            corporateName = "Test",
            countryCode = "IN",
            firstName = "Test",
            lastName = "Test",
            jobTitle = "Tester",
            referralCode = "test"
        )
        val partnerId = 1L
        //val expectedResponse = Response(true, 123L)

        every { corporateService.createCorporateWithPartner(signUpRequest, partnerId) } returns 1L

        // Invoke the method being tested
        SecurityContextHolder.getContext().authentication = authenticatedUser
        mockMvc.perform(MockMvcRequestBuilders.post("/api/${AppConstant.API_VERSION}/admin/partner/sign-up/$partnerId")
            .contentType(MediaType.APPLICATION_JSON)
            .content(mapper.writeValueAsString(signUpRequest)))
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").isNotEmpty())
            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").value(1L))

        // Verify that the mock method was called with the correct arguments
        verify { corporateService.createCorporateWithPartner(signUpRequest, partnerId) }
    }*/



}