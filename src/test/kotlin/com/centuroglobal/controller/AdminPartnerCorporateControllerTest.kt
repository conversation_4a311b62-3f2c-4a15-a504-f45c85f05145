//package com.centuroglobal.controller
//
//
//import com.centuroglobal.service.CorporateService
//import com.centuroglobal.service.PartnerService
//import com.centuroglobal.service.metrics.AccessLogService
//import com.centuroglobal.shared.data.AppConstant
//import com.centuroglobal.shared.data.SearchConstant
//import com.centuroglobal.shared.data.enums.CorporateStatus
//import com.centuroglobal.shared.data.payload.Response
//import com.centuroglobal.shared.data.payload.account.UpdatePartnerCorporateRequest
//import com.centuroglobal.shared.data.pojo.CorporateList
//import com.centuroglobal.shared.data.pojo.PagedResult
//import com.centuroglobal.shared.data.pojo.PartnerCorporateSearchFilter
//import com.centuroglobal.shared.security.AuthenticatedUser
//import com.centuroglobal.shared.util.TimeUtil
//import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
//import com.ninjasquad.springmockk.MockkBean
//import io.mockk.every
//import io.mockk.mockk
//import io.swagger.v3.oas.annotations.Operation
//import io.swagger.v3.oas.annotations.Parameter
//import io.swagger.v3.oas.annotations.media.Content
//import io.swagger.v3.oas.annotations.responses.ApiResponse
//import io.swagger.v3.oas.annotations.security.SecurityRequirement
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
//import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
//import org.springframework.data.domain.PageRequest
//import org.springframework.http.MediaType
//import org.springframework.security.core.annotation.AuthenticationPrincipal
//import org.springframework.security.core.context.SecurityContextHolder
//import org.springframework.test.web.servlet.MockMvc
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
//import org.springframework.test.web.servlet.result.MockMvcResultMatchers
//import org.springframework.web.bind.annotation.GetMapping
//import org.springframework.web.bind.annotation.PathVariable
//import java.time.LocalDateTime
//
//@WebMvcTest(controllers = [AdminPartnerCorporateController::class])
//@AutoConfigureMockMvc(addFilters = false)
//class AdminPartnerCorporateControllerTest(@Autowired val mockMvc: MockMvc){
//
//    @MockkBean
//    lateinit var partnerService: PartnerService
//
//    @MockkBean
//    lateinit var corporateService: CorporateService
//
//    @MockkBean
//    lateinit var accessLogService: AccessLogService
//
//    private val authenticatedUser: AuthenticatedUser = mockk()
//    private val mapper = jacksonObjectMapper()
//
//    @BeforeEach
//    fun setup(){
//        SecurityContextHolder.getContext().authentication = authenticatedUser
//        every { authenticatedUser.principal } returns authenticatedUser
//    }
//
//    @Test
//    fun `expert company list`() {
//        val name = "Test"
//        val country = "US"
//        val pageIndex = 0
//        val pageSize = 20
//        val sort = "DESC"
//        val sortBy = "createdDate"
//        val status = CorporateStatus.ACTIVE
//        val partnerId= 2L
//        val accountId= 2L
//        val partnerSearchFilter = PartnerCorporateSearchFilter.Builder.build(
//            name,
//            country,
//            accountId,
//            status,
//            null,
//            null,
//            null
//
//        )
//        val pageRequest = PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
//        val totalElements = 1L
//
//
//        val partners = PagedResult<CorporateList?>(listOf(
//            CorporateList(
//                id = 2,
//                name = name,
//                country = country,
//                status = CorporateStatus.ACTIVE.toString(),
//                rootUserId = "0",
//                accounts = 2L,
//                corporateUsers= 0,
//                createdOn = TimeUtil.toEpochMillis(LocalDateTime.now()),
//            )
//        ), totalElements, 0, 1)
//
//        every { partnerService.listPartnerCorporate(partnerId,partnerSearchFilter, pageRequest,  authenticatedUser) } returns partners
//
//
//        mockMvc.perform(
//            MockMvcRequestBuilders.get("/api/${AppConstant.API_VERSION}/admin/partner/${partnerId}/corporates")
//                .param("name", name)
//                .param("country", country)
//                .param("status", status.toString())
//                .param("account id", accountId.toString())
//                .param("pageIndex", pageIndex.toString())
//                .param("pageSize", pageSize.toString())
//                .param("sort", sort)
//                .param("sortBy", sortBy)
//                .contentType(MediaType.APPLICATION_JSON)
//                .characterEncoding("utf-8")
//        )
//            .andExpect(MockMvcResultMatchers.status().isOk)
//            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
//            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").isMap)
//            .andExpect(MockMvcResultMatchers.jsonPath("$.payload").isNotEmpty)
//            .andExpect(MockMvcResultMatchers.jsonPath("$.payload.totalElements").value(totalElements))
//    }
//
//
//
//    @Test
//    fun `update corporate partner from controller`(){
//
//        val userId = 12L
//        val partnerId=4L
//        val userType="abc"
//
//        val corporateId=1L
//        val updatePartnerCorporateRequest = UpdatePartnerCorporateRequest(
//        corporateName = "test",
//        countryCode = "US",
//        primaryColor= "Purple",
//        secondaryColor= "Orange",
//        companyLogoId= "string"
//
//        )
//
//        every { authenticatedUser.principal } returns authenticatedUser
//        every { authenticatedUser.userId } returns userId
//        every { authenticatedUser.userType } returns  userType
//
//        every { corporateService.updatePartnerCorporate(corporateId,userId, userType,partnerId,updatePartnerCorporateRequest) } returns corporateId
//
//        SecurityContextHolder.getContext().authentication = authenticatedUser
//
//        mockMvc.perform(
//            MockMvcRequestBuilders.put("/api/${AppConstant.API_VERSION}/admin/partner/${partnerId}/corporates/${corporateId}")
//                .content(mapper.writeValueAsString(updatePartnerCorporateRequest))
//                .contentType(
//                    MediaType.APPLICATION_JSON)
//                .characterEncoding("utf-8"))
//            .andExpect(MockMvcResultMatchers.status().isOk)
//            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
//    }
//
//    @GetMapping("/{partnerId}/corporates")
//    @Operation(
//        summary = "list of corporates associated with partner",
//        responses = [ApiResponse(content = [Content(mediaType = "application/json")])],
//        security = [SecurityRequirement(name =  "jwt")]
//    )
//    fun listCorporate(
//        @PathVariable("partnerId") partnerId: Long,
//        @AuthenticationPrincipal @Parameter(hidden = true) authenticatedUser: AuthenticatedUser
//    ): Response<List<Map<String, String>>> {
//
//        val corporate = partnerService.retrieveCorporates(partnerId)
//        return Response(true,corporate)
//    }
//
//}