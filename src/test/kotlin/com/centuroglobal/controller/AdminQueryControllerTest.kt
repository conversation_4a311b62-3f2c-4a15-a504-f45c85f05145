package com.centuroglobal.controller

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.query.QueryStatus
import com.centuroglobal.data.payload.query.QueryRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.query.QueryCardDetails
import com.centuroglobal.shared.data.pojo.query.QuerySearchFilter
import com.centuroglobal.shared.data.pojo.query.QueryStats
import com.centuroglobal.shared.data.pojo.query.QueryStatsResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.service.QueryService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime


@WebMvcTest(controllers = [AdminQueryController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminQueryControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var queryService: QueryService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    private val mapper = jacksonObjectMapper()

    private val userProfile = CorporateUserProfile(
        2, "<EMAIL>", "firstName", "lastName", role = Role.ROLE_CORPORATE, status = AccountStatus.ACTIVE,
        countryCode = "AU",
        profilePictureFullUrl = "",
        companyName = "abc",
        userType = PartnerCaseType.CG,
        bandName = "abc"
    )

    private val queryCardDetails = QueryCardDetails(
        id = 1,
        heading = "Test Heading",
        description = "Test Description",
        category = listOf("Category1"),
        country = "IN",
        isProposalApproved = false,
        status = QueryStatus.OPEN.name,
        raisedBy = userProfile,
        submittedOn = TimeUtil.toEpochMillis(LocalDateTime.now())
    )

    @BeforeEach
    fun setup(){
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `get all queries from controller`() {
        val search = "Test"
        val categories = "TestCategories"
        val country = "US"
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val status = QueryStatus.OPEN
        val querySearchFilter = QuerySearchFilter.Builder.build(
            search,
            null,
            null,
            null,
            categories,
            country,
            listOf(QueryStatus.OPEN),
            null,
            null,
            null,
            null,
            true,
            true
        )
        val pageRequest = PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort))
        val totalElements = 1L


        val queries = PagedResult(listOf(QueryCardDetails(
            id=1,
            heading = "Test Heading",
            description = "Test Description",
            category = listOf("Category1"),
            country = "IN",
            isProposalApproved = false,
            status = QueryStatus.OPEN.name,
            raisedBy = userProfile,
            submittedOn = TimeUtil.toEpochMillis(LocalDateTime.now())
        )), totalElements, 0, 1)

        val queryStatsResponse = QueryStatsResponse(
            queries = queries,
            stats = QueryStats(2, 1, 1, 1, 0)
        )
        every { queryService.listQueriesForAdmin(querySearchFilter, pageRequest, authenticatedUser) } returns queryStatsResponse

        every { authenticatedUser.partnerId } returns 3

        every { queryService.listQueriesForAdmin(any(), any(), any()) } returns queryStatsResponse

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/queries")
                .param("search", search)
                .param("categories", categories)
                .param("country", country)
                .param("status", status.toString())
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isMap)
            .andExpect(jsonPath("$.payload.queries").isNotEmpty)
            .andExpect(jsonPath("$.payload.queries.totalElements").value(totalElements))
    }

    @Test
    fun `get query by id test`() {
        val queryId = 1L
        every { queryService.getQueryById(queryId, authenticatedUser) } returns queryCardDetails
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/admin/queries/$queryId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isMap)
            .andExpect(jsonPath("$.payload.id").value(queryId))
            .andExpect(jsonPath("$.payload.heading").value(queryCardDetails.heading))
    }
    @Test
    fun `update query status test`() {
        val queryId = 1L
        val status = QueryStatus.RESOLVED
        every { queryService.updateStatus(queryId, status, authenticatedUser) } returns true
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/queries/$queryId")
                .param("status", status.name)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `edit query test`() {
        val queryId = 1L

        val editQueryRequest = QueryRequest(
            heading = "Test Heading",
            description = "Test Description",
            queryCategory = listOf("CATEGORY1"),
            assignedExperts = listOf(10),
            country = "US"
        )

        every { queryService.editQuery(queryId, editQueryRequest, authenticatedUser) } returns true
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/admin/queries/edit/$queryId")
                .content(mapper.writeValueAsString(editQueryRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    /*@Test
    fun `create query proposal test`() {
        val queryId = 1L

        val fileName = "proposal-1.docx"
        val fileType = "docx"
        val fileSize = 1024L
        val file = MockMultipartFile(
            fileName, null, "text/plain", "Sample content".toByteArray(
                StandardCharsets.UTF_8
            )
        )
        every { queryService.createProposal(queryId, fileName, fileType, fileSize, any(), authenticatedUser) } returns 1L
        mockMvc.perform(
            multipart("/api/${AppConstant.API_VERSION}/admin/queries/$queryId/proposal")
                .file("file", file.bytes)
                .param("fileName", fileName)
                .param("fileType", fileType)
                .param("fileSize", fileSize.toString())
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(1))
    }*/

    /*@Test
    fun `delete query proposal test`() {
        val queryId = 1L
        val proposalId = 2L
        every { queryService.deleteProposal(queryId, proposalId, authenticatedUser) } returns true
        mockMvc.perform(
            delete("/api/${AppConstant.API_VERSION}/admin/queries/$queryId/proposals/$proposalId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }*/

}