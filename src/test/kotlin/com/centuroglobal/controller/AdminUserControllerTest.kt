package com.centuroglobal.controller

import com.centuroglobal.facade.BackofficeFacade
import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.HubspotResponse
import com.centuroglobal.shared.data.payload.account.CreateAdminUserRequest
import com.centuroglobal.shared.data.pojo.AdminAuthorities
import com.centuroglobal.shared.data.pojo.BackofficeUser
import com.centuroglobal.shared.data.pojo.BackofficeUserList
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.SharedAdminUserService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [AdminUserController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminUserControllerTest(@Autowired val mockMvc: MockMvc) {

    private val objectMapper = ObjectMapper()

    @MockkBean
    lateinit var sharedAdminUserService: SharedAdminUserService

    @MockkBean
    lateinit var adminUserService: AdminUserService

    @MockkBean
    lateinit var backofficeFacade: BackofficeFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `list should return list of BackofficeUserList`() {
        val search = "John"
        val role = "ROLE_ADMIN"
        val status = "ACTIVE"
        val responsibility = "USERS"
        val userList = listOf<BackofficeUserList>()

        every { backofficeFacade.retrieveBackofficeUsers(any()) } returns CompletableFuture.completedFuture(userList)

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user")
                .param("search", search)
                .param("role", role)
                .param("status", status)
                .param("responsibility", responsibility)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }


    @Test
    fun `retrieve should return BackofficeUser`() {
        val userId = 1L
        val backofficeUser = mockk<BackofficeUser>()

        every { backofficeFacade.retrieveBackofficeUser(userId) } returns CompletableFuture.completedFuture(backofficeUser)

        mockMvc.perform(
            MockMvcRequestBuilders.get("/api/v1/admin/user/$userId")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }


    @Test
    fun `create should return created BackofficeUser`() {
        val request = CreateAdminUserRequest(
            "<EMAIL>",
            "John",
            "Doe",
            Role.ROLE_SUPER_ADMIN.name,
            listOf(AdminAuthorities("READ_WRITE", true, "READ_WRITE"))
        )
        val backofficeUser = mockk<BackofficeUser>()

        every { backofficeFacade.createBackofficeUser(any(), any()) } returns CompletableFuture.completedFuture(backofficeUser)

        mockMvc.perform(
            MockMvcRequestBuilders.post("/api/v1/admin/user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
    }

    @Test
    fun `test delete user`() {
        val userId = 1L
        every { adminUserService.deleteUser(userId, authenticatedUser) } returns CompletableFuture.completedFuture("User deleted successfully")

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/v1/admin/user/$userId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }


    @Test
    fun `test retrieve client users`() {
        every { adminUserService.getClientUsers() } returns listOf()

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user/client-users")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test create hubspot user`() {
        val hubspotResponses = mutableListOf<HubspotResponse>()
        every { sharedAdminUserService.createHubspotUsers() } returns hubspotResponses

        mockMvc.perform(MockMvcRequestBuilders.post("/api/v1/admin/user/hubspot")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isCreated)
    }

    @Test
    fun `test delete profile picture`() {
        val userId = 1L
        every { adminUserService.deleteProfilePicture(any(), any()) } returns "Profile picture deleted successfully"

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/v1/admin/user/photo/$userId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

}

