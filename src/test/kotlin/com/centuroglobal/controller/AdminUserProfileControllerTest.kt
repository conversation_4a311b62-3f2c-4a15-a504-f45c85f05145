package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.payload.ExtractDocDetailsRequest
import com.centuroglobal.shared.data.payload.PassportDocumentRequest
import com.centuroglobal.shared.data.payload.UserDocumentRequest
import com.centuroglobal.shared.data.payload.VisaDocumentRequest
import com.centuroglobal.shared.data.pojo.CorporateDocumentResponse
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.passportvisa.DocumentMetadataGeneric
import com.centuroglobal.shared.data.pojo.passportvisa.GetPassportResponse
import com.centuroglobal.shared.data.pojo.passportvisa.GetVisaResponse
import com.centuroglobal.shared.data.pojo.passportvisa.PassportVisaResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@WebMvcTest(controllers = [AdminUserProfileController::class])
@AutoConfigureMockMvc(addFilters = false)
class AdminUserProfileControllerTest(@Autowired val mockMvc: MockMvc) {

    private val objectMapper = ObjectMapper()

    @MockkBean
    lateinit var adminUserService: AdminUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `uploadDocs should return Response with true when document is uploaded successfully`() {
        val userDocumentRequest = UserDocumentRequest(
            docName = "abc",
            fileData = mutableListOf(),
            expiryDate = 123456,
            docSubType = "",
            issueCountry = "IN",
            userId = 1
        )
        val userId = 1L

        every { adminUserService.uploadDocumentUser(userDocumentRequest, authenticatedUser, userId) } returns true

        mockMvc.perform(
            post("/api/v1/admin/user-profile/doc/$userId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(userDocumentRequest)))
            .andExpect(status().isCreated)
    }

    @Test
    fun `updatePassport should return Response with true when passport is updated successfully`() {
        val passportDocumentRequest = PassportDocumentRequest(
            birthDate = "",
            birthPlace = "",
            nationality = "IN",
            issueDate = "123456",
            issuePlace = "PN",
            expiryDate = "",
            gender = "M",
            docFiles = mutableListOf(),
            userId = 1234
        )
        val userId = 1L

        every { adminUserService.updatePassport(passportDocumentRequest, authenticatedUser, userId) } returns true

        mockMvc.perform(
            post("/api/v1/admin/user-profile/passport/$userId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(passportDocumentRequest)))
            .andExpect(status().isCreated)
    }

    @Test
    fun `updateVisa should return Response with true when visa is updated successfully`() {
        val visaDocumentRequest =VisaDocumentRequest(
            name = "avc",
            visaType = "",
            country = "IN",
            issueDate = "",
            expiryDate = "",
            docFiles = mutableListOf(),
            userId = 1
        )
        val userId = 1L

        every { adminUserService.updateVisa(visaDocumentRequest, authenticatedUser, userId) } returns true

        mockMvc.perform(
            post("/api/v1/admin/user-profile/visa/$userId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(visaDocumentRequest)))
            .andExpect(status().isCreated)
    }

    @Test
    fun `extractDocsData should return Response with DocumentMetadataGeneric when extraction is successful`() {
        val extractDocDetailsRequest = ExtractDocDetailsRequest(
            doc_type = "pdf",
            bucket = "",
            s3Key = mutableListOf()
        )

        every { adminUserService.extractDocumentMetadataResponse(any()) } returns DocumentMetadataGeneric()

        mockMvc.perform(
            post("/api/v1/admin/user-profile/extract-doc")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(extractDocDetailsRequest)))
            .andExpect(status().isOk)

    }

    @Test
    fun `docList should return Response with PassportVisaResponse when documents are listed successfully`() {
        val userId = 1L

        every { adminUserService.listPassportVisa(any(), userId) } returns PassportVisaResponse(
            passportList = listOf(),
            visaList = listOf()
        )

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/passport-visa/$userId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test retrievePassport`() {
        val passportId = 1L
        val userId = 1L

        every { adminUserService.retrievePassport(authenticatedUser, passportId, userId) } returns GetPassportResponse(
            id = 2,
            nationality = "IN",
            birthPlace = "",
            birthDate = "",
            issuePlace = "",
            gender = "",
            issueDate = "",
            expiryDate = "",
            updatedBy = "",
            updatedDate = 1234,
            filesUploaded = listOf()
        )

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/$userId/passport/$passportId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test retrieveVisa`() {
        val visaId = 1L
        val userId = 1L
        val getVisaResponse = GetVisaResponse(
            id = 2,
            visaType = "",
            country = "",
            issueDate = "",
            expiryDate = "",
            updatedBy = "",
            updatedDate = 123,
            filesUploaded = listOf()
        )
        every { adminUserService.retrieveVisa(authenticatedUser, visaId, userId) } returns getVisaResponse

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/$userId/visa/$visaId"))
            .andExpect(status().isOk)
    }


    @Test
    fun `test docList`() {
        val userId = 1L
        val response = PassportVisaResponse(passportList = listOf(), visaList = listOf())
        every { adminUserService.listPassportVisa(authenticatedUser, userId) } returns response

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/passport-visa/$userId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test deleteDoc`() {
        val userId = 1L
        val docId = 1L
        every { adminUserService.deleteDocumentUser(docId, authenticatedUser, userId) } returns true

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/v1/admin/user-profile/$userId/doc/$docId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test deletePasssportVisa`() {
        val userId = 1L
        val docId = 1L
        every { adminUserService.deletePassportVisa(docId, authenticatedUser, userId) } returns true

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/v1/admin/user-profile/$userId/passport-visa/$docId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test viewUrl`() {
        val userId = 1L
        val fileId = 1L
        val url = "http://example.com/document"
        every { adminUserService.getDocUrlUser(fileId, authenticatedUser, userId) } returns url

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/$userId/view-doc/$fileId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test viewPassportVisa`() {
        val s3key = "some-key"
        val expectedUrl = "http://example.com/passport-visa"

        every { adminUserService.viewPassportVisa(s3key) } returns expectedUrl

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/view-passport-visa")
            .param("s3key", s3key))
            .andExpect(status().isOk)
    }

    @Test
    fun `test downloadPassportVisa`() {
        val docId = 1L
        val userId = 1L
        val streamingResponseBody = StreamingResponseBody { outputStream -> outputStream.write("content".toByteArray()) }

        every { adminUserService.downloadPassportVisaFiles(docId, authenticatedUser, userId) } returns ResponseEntity.ok(streamingResponseBody)

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/$userId/passport-visa/$docId"))
            .andExpect(status().isOk)
    }

    @Test
    fun `test downloadDoc`() {
        val fileId = 1L
        val userId = 1L
        val streamingResponseBody = StreamingResponseBody { outputStream -> outputStream.write("content".toByteArray()) }

        every { adminUserService.downloadDocUser(fileId, authenticatedUser, userId) } returns ResponseEntity.ok(streamingResponseBody)

        mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/admin/user-profile/download-doc/$userId")
            .param("fileId", fileId.toString()))
            .andExpect(status().isOk)
    }

    @Test
    fun `docList should return Response with PagedResult when documents are listed successfully`() {
        val reqUserId = 1L
        val search = "document"
        val country = "IN"
        val uploadedType = "pdf"
        val createdBy: Long? = null
        val userId: Long? = null
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = false

        val pagedResult = PagedResult<CorporateDocumentResponse>(
            listOf(), 0, 0, 0
        )

        every {
            adminUserService.listDocuments(
                any(), any(), any(), any()
            )
        } returns pagedResult

        mockMvc.perform(
            MockMvcRequestBuilders.get("/api/v1/admin/user-profile/docs/$reqUserId")
                .param("search", search)
                .param("country", country)
                .param("uploadedType", uploadedType)
                .param("createdBy", createdBy?.toString() ?: "")
                .param("userId", userId?.toString() ?: "")
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .param("isDownload", isDownload.toString())
        )
            .andExpect(status().isOk)
    }


}
