package com.centuroglobal.controller

import com.centuroglobal.service.CaseDashboardService
import com.centuroglobal.service.CaseService
import com.centuroglobal.service.TaskService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.CaseCategoryEntity
import com.centuroglobal.shared.data.entity.CaseEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.TrackingType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.payload.case.*
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.case.ApplicantCaseReferenceData
import com.centuroglobal.shared.data.pojo.case.CaseNotesDetails
import com.centuroglobal.shared.data.pojo.case.CaseViewDetails
import com.centuroglobal.shared.data.pojo.case.CaseViewForDownload
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [CaseController::class])
@AutoConfigureMockMvc(addFilters = false)
class CaseControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var caseService: CaseService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @MockkBean
    private lateinit var caseDashboardService: CaseDashboardService

    @MockkBean
    private lateinit var taskService: TaskService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test update case`() {
        val caseId = 1L
        val updateCaseRequest=UpdateCaseRequest(

            status="IN_PROGRESS",
            statusUpdate="",
            startDate=null,
            assignCompany=1,
            notifyPrimaryExpert=false,
            isPriorityCase=false,
            visaType=null,
            visaIssueDate=null,
            visaExpiryDate=null,
            estimatedTimeline=null,
            caseOwner=authenticatedUser.userId,
            accountId=3,
            isStatusChange=true,
            actionFor="CENTURO",
            experts=mutableListOf(),
            managers=mutableListOf(),
            accountManager=null,
            notes=null,
            notifyCaseOwner=true,
            notifyApplicant=true
        )
        every { caseService.update(any(), any(), any(),any()) } returns true

        mockMvc.perform(put("/api/v1/case/$caseId")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(updateCaseRequest)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test get case data`() {
        val caseId = "1"

        val caseViewDetails = CaseViewDetails(
            caseId = 1L,
            initiatedDate = System.currentTimeMillis(),
            startDate = System.currentTimeMillis(),
            country = "USA",
            companyName = "Test Company",
            initiatedFor = "Test User",
            email = "<EMAIL>",
            category = "IMMIGRATION",
            initiatedBy = null,
            status = "IN_PROGRESS",
            caseDetails = null,
            statusUpdate = "Status updated",
            actionFor = "CENTURO",
            isPriorityCase = false,
            documents = listOf(),
            statusHistory = listOf(),
            milestones = listOf(),
            assigneeList = listOf(),
            managersList = listOf(),
            accountManager = null,
            archive = false,
            notifyCaseOwner = true,
            lastUpdatedDate = System.currentTimeMillis(),
            caseFees = null,
            percentCompletion = 50,
            account = null,
            taskPending = listOf(),
            recentUpdates = listOf(),
            isCaseMember = true,
            aliasName = "Alias",
            partnerName = "Partner",
            taskWorkflow = null,
            milestoneChart = null
        )
        
        every { caseService.getCaseDetailsById(caseId, authenticatedUser) } returns Response(true, caseViewDetails)

        mockMvc.perform(get("/api/v1/case/$caseId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test list case notes`() {
        val caseId = 1L
        val caseNotesDetails = listOf(CaseNotesDetails(
            id = 3,
            notes = "",
            updatedBy = UserProfile(
                1,
                "<EMAIL>",
                "String",
                "String",
                AccountStatus.ACTIVE,
                Role.ROLE_EXPERT,
                "String",
                "String",
                "String"),
            createdOn = 1234,
            updatedOn = 1234
        ))
        every { caseService.listCaseNotes(caseId, authenticatedUser) } returns caseNotesDetails

        mockMvc.perform(get("/api/v1/case/$caseId/case-notes")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isArray)
    }

    @Test
    fun `test list cases`() {
        val pagedResult = PagedResult<CaseViewDetails>(
            rows = listOf(),
            totalElements = 11,
            currentPage = 11,
            totalPages = 11
        )

        val pageRequest = PageRequest.of(0, 20)

        every { caseService.getPageRequest(any(), any(), any(), any(), any(), any()) } returns pageRequest

        every { caseService.listCases(
            any(),any(),any(),any(),any()
        ) } returns pagedResult

        mockMvc.perform(get("/api/v1/case/listing")
            .param("pageIndex", "0")
            .param("pageSize", "20")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `case download test`() {
        val caseViewForDownload = CaseViewForDownload(
            caseId = 1L,
            initiatedDate = System.currentTimeMillis(),
            startDate = System.currentTimeMillis(),
            country = "USA",
            companyName = "Test Company",
            initiatedFor = "Test User",
            email = "<EMAIL>",
            category = "IMMIGRATION",
            initiatedBy = null,
            status = "IN_PROGRESS",
            statusUpdate = "Status updated",
            actionFor = "CENTURO",
            isPriorityCase = false,
            accountManager = null,
            archive = false,
            notifyCaseOwner = true,
            notes = "Some notes",
            lastUpdatedDate = System.currentTimeMillis(),
            fromCountry = "India",
            percentCompletion = 50,
            managers = listOf("Manager1", "Manager2"),
            aliasName = "Alias",
            partnerName = "Partner"
        )
        every { caseService.downloadCases(any(), any(), any(), any()) } returns listOf(caseViewForDownload)

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/case/download")
            .param("search", "test")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `create generic case test`() {
        val caseRequest = GenericCaseRequest(
            caseEntityData = objectMapper.readTree("{ \"a\": \"a\"}"),
            additionalData = objectMapper.readTree("{ \"a\": \"a\"}"),
            userData = objectMapper.readTree("{ \"a\": \"a\"}")
        )
        val caseCategory = "SINGLE_VISA"
        every { caseService.createCase(caseCategory, caseRequest, authenticatedUser) } returns Pair(listOf(1L), true)

        mockMvc.perform(post("/api/${AppConstant.API_VERSION}/case/$caseCategory")
            .content(objectMapper.writeValueAsString(caseRequest))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isCreated)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }


    @Test
    fun `update generic case test`() {
        val caseId = 1L
        val caseRequest = GenericCaseRequest(
            caseEntityData = objectMapper.readTree("{ \"a\": \"a\"}"),
            additionalData = objectMapper.readTree("{ \"a\": \"a\"}"),
            userData = objectMapper.readTree("{ \"a\": \"a\"}")
        )

        val case : CaseEntity = mockk()

        every { caseService.updateCase(caseId, caseRequest, authenticatedUser) } returns case

        every { case.id } returns 3

        mockMvc.perform(put("/api/${AppConstant.API_VERSION}/case/edit/$caseId")
            .content(objectMapper.writeValueAsString(caseRequest))
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `approve case fees test`() {
        val caseId = 1L
        val approverDetailsRequest = ApproverDetailsRequest()
        every { caseService.caseFeesApprovalForCaseOwner(caseId, authenticatedUser) } returns approverDetailsRequest

        mockMvc.perform(put("/api/${AppConstant.API_VERSION}/case/$caseId/approve")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `cases id listing test`() {
        val applicantCaseReferenceData = ApplicantCaseReferenceData(
            id = 3,
            country = "IN",
            category = CaseCategoryEntity(
                id = 3,
                subCategoryId = "",
                subCategoryName = "",
                parentCategoryId = "",
                parentCategoryName = ""
            ),
        )
        every { caseService.listCaseIds(any<Long>()) } returns mutableListOf(applicantCaseReferenceData)

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/case/ids")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test applicantCaseDetails`() {
        val caseId = "123"
        val caseViewDetails = CaseViewDetails(
            caseId = 1L,
            initiatedDate = System.currentTimeMillis(),
            startDate = System.currentTimeMillis(),
            country = "USA",
            companyName = "Test Company",
            initiatedFor = "Test User",
            email = "<EMAIL>",
            category = "IMMIGRATION",
            initiatedBy = null,
            status = "IN_PROGRESS",
            caseDetails = null,
            statusUpdate = "Status updated",
            actionFor = "CENTURO",
            isPriorityCase = false,
            documents = listOf(),
            statusHistory = listOf(),
            milestones = listOf(),
            assigneeList = listOf(),
            managersList = listOf(),
            accountManager = null,
            archive = false,
            notifyCaseOwner = true,
            lastUpdatedDate = System.currentTimeMillis(),
            caseFees = null,
            percentCompletion = 50,
            account = null,
            taskPending = listOf(),
            recentUpdates = listOf(),
            isCaseMember = true,
            aliasName = "Alias",
            partnerName = "Partner",
            taskWorkflow = null,
            milestoneChart = null
        )
        val caseResponse = Response(true, caseViewDetails)
        val applicantCaseDetails = mockk<CaseViewDetails>()

        every { caseService.getCaseDetailsById(caseId, authenticatedUser) } returns caseResponse
        every { caseDashboardService.getApplicantCaseDetails(authenticatedUser, caseViewDetails) } returns caseViewDetails

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/case/applicant-dashboard-details/$caseId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test addCaseReminders`() {
        val caseId = 123L
        val caseTracking = CaseTracking(
            trackingType = TrackingType.RENEWAL,
            reminders = listOf(),
            visaExpiry = 1234,
            visaRenewalDeadline = 1234
        )
        val requestJson = objectMapper.writeValueAsString(caseTracking)

        every { caseService.createReminders(caseId, caseTracking, authenticatedUser) } returns true

        mockMvc.perform(post("/api/${AppConstant.API_VERSION}/case/reminder/$caseId")
            .content(requestJson)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test getCaseReminders`() {
        val caseId = 123L
        val reminders = listOf(mockk<CaseReminder>())

        every { caseService.getReminders(caseId, authenticatedUser) } returns CaseTracking(
            trackingType = TrackingType.RENEWAL,
            reminders = listOf(),
            visaExpiry = 123,
            visaRenewalDeadline = 123
        )

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/case/reminder/$caseId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test linkedCaseList`() {

        val caseId = 123L

        every { caseService.getLinkedCases(any()) } returns PagedResult(listOf(
            CaseViewDetails(
                caseId = 4,
                initiatedDate = 1234,
                startDate = 1234,
                country = "IN",
                companyName = "",
                initiatedFor = "",
                email = "<EMAIL>",
                category = "",
                initiatedBy = null,
                status = "",
                caseDetails = null,
                statusUpdate = "",
                actionFor = "",
                isPriorityCase = true,
                documents = listOf(),
                statusHistory = listOf(),
                milestones = listOf(),
                assigneeList = listOf(),
                managersList = listOf(),
                accountManager = null,
                archive = false,
                notifyCaseOwner = false,
                lastUpdatedDate = 123,
                caseFees = null,
                percentCompletion = 112,
                account = null,
                taskPending = listOf(),
                recentUpdates = listOf(),
                isCaseMember = false,
                aliasName = "",
                partnerName = "",
                taskWorkflow = null,
                milestoneChart = null
            )
        ), 0, 0, 0)

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/case/$caseId/linked")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

}
