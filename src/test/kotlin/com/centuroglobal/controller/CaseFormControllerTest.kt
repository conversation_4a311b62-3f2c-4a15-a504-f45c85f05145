package com.centuroglobal.controller

import com.centuroglobal.service.CaseFormService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.CaseFormStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.case.CaseFormDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(controllers = [CaseFormController::class])
@AutoConfigureMockMvc(addFilters = false)
class CaseFormControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var caseFormService: CaseFormService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "CORPORATE"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test get case form by id`() {
        val caseFormId = 1L
        val caseFormDetails = CaseFormDetails(
            id = caseFormId,
            name = "Test Case Form",
            description = "This is a test case form",
            countries = listOf("US", "UK", "CA"),
            category = "Legal",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 10,
            defaultDocumentList = listOf(),
            isDefault = false,
            fields = null,
            updatedBy = null,
            updatedOn = 123,
            mapping = null
        )

        every { caseFormService.get(caseFormId, authenticatedUser) } returns caseFormDetails

        mockMvc.get("/api/${AppConstant.API_VERSION}/case-form/$caseFormId")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.id") { value(caseFormId) }
                jsonPath("$.payload.name") { value("Test Case Form") }
                jsonPath("$.payload.description") { value("This is a test case form") }
                jsonPath("$.payload.countries[0]") { value("US") }
                jsonPath("$.payload.countries[1]") { value("UK") }
                jsonPath("$.payload.countries[2]") { value("CA") }
                jsonPath("$.payload.category") { value("Legal") }
                jsonPath("$.payload.visibility") { value("PUBLIC") }
                jsonPath("$.payload.status") { value("ACTIVE") }
                jsonPath("$.payload.fieldCount") { value(10) }
                jsonPath("$.payload.isDefault") { value(false) }
            }

        verify { caseFormService.get(caseFormId, authenticatedUser) }
    }

    @Test
    fun `test list case forms with no filters`() {
        val caseFormDetails1 = CaseFormDetails(
            id = 32,
            name = "Test Case Form",
            description = "This is a test case form",
            countries = listOf("US", "UK", "CA"),
            category = "Legal",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 10,
            defaultDocumentList = listOf(),
            isDefault = false,
            fields = null,
            updatedBy = null,
            updatedOn = 123,
            mapping = null
        )

        val caseFormDetails2 = CaseFormDetails(
            id = 44,
            name = "Test Case Form",
            description = "This is a test case form",
            countries = listOf("US", "UK", "CA"),
            category = "Legal",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 10,
            defaultDocumentList = listOf(),
            isDefault = false,
            fields = null,
            updatedBy = null,
            updatedOn = 123,
            mapping = null
        )

        val pagedResult = PagedResult(
            rows = listOf(caseFormDetails1, caseFormDetails2),
            totalElements = 2,
            currentPage = 0,
            totalPages = 1
        )

        every { 
            caseFormService.list(
                any(),
                any(),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.get("/api/${AppConstant.API_VERSION}/case-form") {
            param("pageIndex", "0")
            param("pageSize", "20")
            param("sort", "DESC")
            param("sortBy", "lastUpdatedDate")
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
        }

        verify { 
            caseFormService.list(
                match { filter ->
                    filter.search == null &&
                    filter.category == null &&
                    filter.country == null &&
                    filter.status == null &&
                    filter.visibility == null &&
                    filter.updatedBy == null &&
                    filter.partnerId == 4L &&
                    filter.isPartner == null
                },
                any(),
                authenticatedUser
            ) 
        }
    }

    @Test
    fun `test list case forms with filters`() {
        val search = "Legal"
        val category = "Tax"
        val country = "US"
        val status = CaseFormStatus.ACTIVE
        val visibility = TaskVisibility.PUBLIC
        val updatedBy = 1L
        val partnerId = 4L
        val isPartner = true
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "lastUpdatedDate"
        val isDownload = false

        val caseFormDetails = CaseFormDetails(
            id = 212,
            name = "Test Case Form",
            description = "This is a test case form",
            countries = listOf("US", "UK", "CA"),
            category = "Legal",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 10,
            defaultDocumentList = listOf(),
            isDefault = false,
            fields = null,
            updatedBy = null,
            updatedOn = 123,
            mapping = null
        )

        val pagedResult = PagedResult(
            rows = listOf(caseFormDetails),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        every { 
            caseFormService.list(
                any(),
                any(),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.get("/api/${AppConstant.API_VERSION}/case-form") {
            param("search", search)
            param("category", category)
            param("country", country)
            param("status", status.toString())
            param("visibility", visibility.toString())
            param("updatedBy", updatedBy.toString())
            param("partnerId", partnerId.toString())
            param("isPartner", isPartner.toString())
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
            param("sort", sort)
            param("sortBy", sortBy)
            param("isDownload", isDownload.toString())
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
        }

    }


    @Test
    fun `test list case forms with empty result`() {
        val search = "NonExistentForm"
        
        val emptyPagedResult = PagedResult<CaseFormDetails>(
            rows = listOf(),
            totalElements = 0,
            currentPage = 0,
            totalPages = 0
        )

        every { 
            caseFormService.list(
                any(),
                any(),
                authenticatedUser
            ) 
        } returns emptyPagedResult

        mockMvc.get("/api/${AppConstant.API_VERSION}/case-form") {
            param("search", search)
            param("pageIndex", "0")
            param("pageSize", "20")
            param("sort", "DESC")
            param("sortBy", "lastUpdatedDate")
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload.rows") { isEmpty() }
            jsonPath("$.payload.totalElements") { value(0) }
        }
    }

    @Test
    fun `test list case forms with download option`() {
        val isDownload = true
        
        val caseFormDetails = CaseFormDetails(
            id = 432,
            name = "Test Case Form",
            description = "This is a test case form",
            countries = listOf("US", "UK", "CA"),
            category = "Legal",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 10,
            defaultDocumentList = listOf(),
            isDefault = false,
            fields = null,
            updatedBy = null,
            updatedOn = 123,
            mapping = null
        )

        val pagedResult = PagedResult(
            rows = listOf(caseFormDetails),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        every { 
            caseFormService.list(
                any(),
                any(),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.get("/api/${AppConstant.API_VERSION}/case-form") {
            param("pageIndex", "0")
            param("pageSize", "20")
            param("sort", "DESC")
            param("sortBy", "lastUpdatedDate")
            param("isDownload", isDownload.toString())
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
        }

        verify { 
            caseFormService.list(
                any(),
                match { pageRequest -> 
                    pageRequest.pageSize == Int.MAX_VALUE 
                },
                authenticatedUser
            ) 
        }
    }

    @Test
    fun `test list case forms with different user role`() {
        // Setup a different user role
        every { authenticatedUser.role } returns "EXPERT"
        
        val caseFormDetails = CaseFormDetails(
            id = 55,
            name = "Test Case Form",
            description = "This is a test case form",
            countries = listOf("US", "UK", "CA"),
            category = "Legal",
            visibility = TaskVisibility.PUBLIC,
            status = CaseFormStatus.ACTIVE,
            fieldCount = 10,
            defaultDocumentList = listOf(),
            isDefault = false,
            fields = null,
            updatedBy = null,
            updatedOn = 123,
            mapping = null
        )

        val pagedResult = PagedResult(
            rows = listOf(caseFormDetails),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )

        every { 
            caseFormService.list(
                any(),
                any(),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.get("/api/${AppConstant.API_VERSION}/case-form")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
            }

        verify { 
            caseFormService.list(
                any(),
                any(),
                authenticatedUser
            ) 
        }
    }
}