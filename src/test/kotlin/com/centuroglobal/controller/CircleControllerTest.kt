package com.centuroglobal.controller

import com.centuroglobal.facade.ExpertCircleFacade
import com.centuroglobal.service.CircleService
import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.service.ExpertiseService
import com.centuroglobal.shared.data.entity.CircleEntity
import com.centuroglobal.shared.data.entity.CircleMemberEntity
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.circle.CircleMessageTrailRequest
import com.centuroglobal.shared.data.pojo.circle.CircleResponseTrail
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.view.CircleViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import junit.framework.TestCase.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.context.request.async.DeferredResult
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.CompletableFuture

@DisplayName("Circle Controller")
class CircleControllerTest {

    private val expertCircleFacade: ExpertCircleFacade = mockk()


    private val circleController = CircleController(
        expertCircleFacade
    )

    private val circleRepository: CircleRepository = mockk()
    private val expertiseService: ExpertiseService = mockk<ExpertiseService>()
    private val circleMemberRepository: CircleMemberRepository = mockk<CircleMemberRepository>()
    private val circleRequestRepository: CircleRequestRepository = mockk<CircleRequestRepository>()
    private val circleResponseTrailRepository : CircleResponseTrailRepository = mockk()
    private val circleViewRepository: CircleViewRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val expertUserService: ExpertUserService = mockk()
    private val expertUserRepository: ExpertUserRepository = mockk()
    private val awsS3Service: AwsS3Service = mockk()
    private val circleBannerFolder: String = ""
    private val circleService = CircleService(
        circleBannerFolder,
        circleRepository,
        expertiseService,
        circleMemberRepository,
        circleRequestRepository,
        circleResponseTrailRepository,
        circleViewRepository,
        loginAccountRepository,
        expertUserService,
        expertUserRepository,
        awsS3Service
    )

    private val circleId =10L
    private val circleEntity = CircleEntity(
        id = circleId,
        name = "Test Circle",
        members = mutableListOf(),
        about = "This is a private circle",
        circleType = CircleType.PUBLIC,
        status = CircleStatus.ACTIVE,
        lastUpdatedBy = circleId,
        createdBy=circleId
    )
    private val authenticatedUser: AuthenticatedUser = mockk {
        every { userId } returns 10L
        every { companyId } returns 5L
        every { email } returns "<EMAIL>"
        every { userType } returns "EXPERT"
    }

    private val lastMessageDateTime = TimeUtil.toEpochMillis(LocalDateTime.now())

    @BeforeEach
    fun setup() {
        every { circleRepository.save(circleEntity) } returns circleEntity
        every { circleEntity.id?.let { circleRepository.findById(it) } } returns Optional.of(circleEntity)

        every { authenticatedUser.userId } returns 10L
        every { authenticatedUser.companyId } returns 5L
        every { authenticatedUser.email } returns "<EMAIL>"
        every { circleRepository.findByIdOrNull(circleId) } returns circleEntity
        every { expertiseService.retrieveExpertiseByIds(any()) } returns emptyList()
        every {
            circleMemberRepository.findTopByCircleAndUserId(
                any(),
                any()
            )
        } returns CircleMemberEntity(circle = circleEntity, userId = 10L, lastUpdatedBy = 10L)
        every { circleMemberRepository.deleteAll(emptyList<CircleMemberEntity>()) } returns Unit
        every { authenticatedUser.userType } returns "EXPERT"

    }
    @Test
    @DisplayName("Should throw an exception when the authenticated user is not authorized to access the circle")
    fun getMessageTrailThrowsExceptionWhenUserNotAuthorized() {
        // Mocking the required objects
        //val authenticatedUser = AuthenticatedUser()
        val circleId = 10L
        val lastMessageDateTime = 123456789L
        val deferredResult = DeferredResult<Response<List<List<CircleResponseTrail>>>>()
        val response = Response<List<List<CircleResponseTrail>>>(true, emptyList())


        // Calling the method under test

        every { expertCircleFacade.getMessageTrail(10, 123456789, authenticatedUser) } returns CompletableFuture.completedFuture(circleService.getCircleResponseTrail(circleId,authenticatedUser.userId,lastMessageDateTime,
            UserType.valueOf(authenticatedUser.userType)))

        val result = circleController.getMessageTrail(authenticatedUser, circleId, lastMessageDateTime)

        // Verifying the expected behavior
        assertNotNull(result)
        assertTrue(result is DeferredResult<Response<List<List<CircleResponseTrail>>>>)

        val deferredResultans = result as DeferredResult<Response<List<List<CircleResponseTrail>>>>


        assertEquals(response, deferredResultans.result)

    }



    @Test
    fun saveMessageTrailReturnsExpectedResult() {
        // Arrange
        //val authenticatedUser = AuthenticatedUser(1L, "testuser")
        val circleId = 10L
        val circleMessageTrailRequest = CircleMessageTrailRequest(10L, 10L,
            10L,
            "Test message",
            UserType.EXPERT,
            TimeUtil.toEpochMillis(LocalDateTime.now()))

        val response = Response<List<List<CircleResponseTrail>>>(true, emptyList())

        every { expertCircleFacade.saveMessageTrail(circleId, circleMessageTrailRequest, authenticatedUser) } returns CompletableFuture.completedFuture(circleService.getCircleResponseTrail(circleId,authenticatedUser.userId,lastMessageDateTime,
            UserType.valueOf(authenticatedUser.userType)))

        // Act
        val result = circleController.saveMessageTrail(authenticatedUser, circleId, circleMessageTrailRequest)

        assertNotNull(result)
        assertTrue(result is DeferredResult<Response<List<List<CircleResponseTrail>>>>)

        val deferredResult = result as DeferredResult<Response<List<List<CircleResponseTrail>>>>

        // Verifying the deferred result
        assertTrue(deferredResult.hasResult())
        // Assert
        assertEquals(response, deferredResult.result)
        verify { expertCircleFacade.saveMessageTrail(circleId, circleMessageTrailRequest, authenticatedUser) }
    }
}