package com.centuroglobal.controller

import com.centuroglobal.facade.ConciergeFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.ConciergeRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [ConciergeController::class])
@AutoConfigureMockMvc(addFilters = false)
class ConciergeControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var conciergeFacade: ConciergeFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "ADMIN"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test post concierge request`() {
        // Arrange
        val conciergeRequest = ConciergeRequest(
            query = "I need assistance with my visa application"
        )
        
        val successResponse = "Request submitted successfully"
        
        every { 
            conciergeFacade.post(any(), any())
        } returns CompletableFuture.completedFuture(successResponse)

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test post concierge request with empty query`() {
        // Arrange
        val conciergeRequest = ConciergeRequest(
            query = ""
        )
        
        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `test post concierge request with null query`() {
        // Arrange
        val requestJson = """
            {
                "query": null
            }
        """.trimIndent()
        
        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson)
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `test post concierge request with corporate role`() {
        // Arrange
        val conciergeRequest = ConciergeRequest(
            query = "I need assistance with my visa application"
        )
        
        val successResponse = "Request submitted successfully"
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        
        every { 
            conciergeFacade.post(any(), any())
        } returns CompletableFuture.completedFuture(successResponse)

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test post concierge request with expert role`() {
        // Arrange
        val conciergeRequest = ConciergeRequest(
            query = "I need assistance with my visa application"
        )
        
        val successResponse = "Request submitted successfully"
        
        every { authenticatedUser.role } returns Role.ROLE_EXPERT.name
        
        every { 
            conciergeFacade.post(any(), any())
        } returns CompletableFuture.completedFuture(successResponse)

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test post concierge request with partner role`() {
        // Arrange
        val conciergeRequest = ConciergeRequest(
            query = "I need assistance with my visa application"
        )
        
        val successResponse = "Request submitted successfully"
        
        every { authenticatedUser.role } returns Role.ROLE_PARTNER.name
        
        every { 
            conciergeFacade.post(any(), any())
        } returns CompletableFuture.completedFuture(successResponse)

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test post concierge request with supplier role`() {
        // Arrange
        val conciergeRequest = ConciergeRequest(
            query = "I need assistance with my visa application"
        )
        
        val successResponse = "Request submitted successfully"
        
        every { authenticatedUser.role } returns Role.ROLE_SUPPLIER.name
        
        every { 
            conciergeFacade.post(any(), any())
        } returns CompletableFuture.completedFuture(successResponse)

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test post concierge request with long query`() {
        // Arrange
        val longQuery = "a".repeat(1000)
        val conciergeRequest = ConciergeRequest(
            query = longQuery
        )
        
        val successResponse = "Request submitted successfully"
        
        every { 
            conciergeFacade.post(any(), any())
        } returns CompletableFuture.completedFuture(successResponse)

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/concierge")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(conciergeRequest))
        )
            .andExpect(status().isOk)
    }
}