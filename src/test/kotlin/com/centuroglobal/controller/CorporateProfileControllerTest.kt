package com.centuroglobal.controller

import com.centuroglobal.facade.CorporateFacade
import com.centuroglobal.service.SubscriptionService
import com.centuroglobal.service.WorkLogService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.subscription.SubscriptionPaymentStatus
import com.centuroglobal.shared.data.payload.account.UpdateCorporateInfoRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateProfileRequest
import com.centuroglobal.shared.data.payload.account.UpdateCorporateStatusRequest
import com.centuroglobal.shared.data.payload.corporate.CorporateUserInfoRequest
import com.centuroglobal.shared.data.pojo.Corporate
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.subscription.SubscriptionUsageResponse
import com.centuroglobal.shared.data.pojo.subscription.WorkLogResponse
import com.centuroglobal.shared.data.pojo.subscription.WorkLogSearchFilter
import com.centuroglobal.shared.data.pojo.subscription.usage.AbstractUsageResponse
import com.centuroglobal.shared.data.pojo.subscription.usage.ExpertSupportUsageResponse
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [CorporateProfileController::class, CorporateSubscriptionController::class])
@AutoConfigureMockMvc(addFilters = false)
class CorporateProfileControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var corporateFacade: CorporateFacade

    @MockkBean
    private lateinit var subscriptionService: SubscriptionService

    @MockkBean
    private lateinit var workLogService: WorkLogService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "CORPORATE"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.companyId } returns 4L
        every { authenticatedUser.userType } returns "CORPORATE"
    }

    @Test
    fun `test retrieve corporate profile`() {
        val corporateId = 4L
        
        val corporate = Corporate(
            id = corporateId,
            countryCode = "US",
            status = AccountStatus.ACTIVE,
            email = "<EMAIL>",
            contactNo = "**********",
            dialCode = "+1",
            firstName = "",
            lastName = "",
            jobTitle = "",
            corporateName = "",
            onBoardingInfo = null,
            keepMeInformed = true,
            bandId = 4,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            isPrimary = false,
            aiMessageCount = 5,
            notificationSettings = listOf(),
            corporateCountryCode = "IN",
            partnerName = "",
            profilePhotoUrl = "",
        )
        
        every { corporateFacade.retrieve(corporateId) } returns CompletableFuture.completedFuture( corporate)

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/$corporateId/profile")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test update corporate profile`() {
        val corporateId = 4L
        
        val updateRequest = UpdateCorporateProfileRequest(
            corporateInfo = UpdateCorporateInfoRequest(
                firstName = "John",
                lastName = "Doe",
                jobTitle = "Manager",
                corporateName = "ABC Corporation",
                countryCode = "US",
                keepMeInformed = true,
                dialCode = "+1",
                contactNo = "**********",
                corporateId = 1L,
                isPrimary = true,
                aiMessageCount = 10,
                notificationSettings = listOf(),
                profilePicS3Key = "",
                educationQualification = "",
                salary = "12345",
                relevantExperience = "4",
                bandId = 2L,
                managerUserIds = listOf(),
                accounts = listOf(),
                email = "<EMAIL>"
            ),
            status = UpdateCorporateStatusRequest(
                status = "ACTIVE"
            ),
            userInfo = CorporateUserInfoRequest(
                bandId = 123,
                accounts = listOf(),
                managerUserIds = listOf()
            )
        )
        
        val updatedCorporate = Corporate(
            id = corporateId,
            countryCode = "US",
            status = AccountStatus.ACTIVE,
            email = "<EMAIL>",
            contactNo = "**********",
            dialCode = "+1",
            firstName = "",
            lastName = "",
            jobTitle = "",
            corporateName = "",
            onBoardingInfo = null,
            keepMeInformed = true,
            bandId = 4,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            isPrimary = false,
            aiMessageCount = 5,
            notificationSettings = listOf(),
            corporateCountryCode = "IN",
            partnerName = "",
            profilePhotoUrl = "",
        )
        
        every { 
            corporateFacade.update(
                any(),
                any(),
                any(),
                any()
            ) 
        } returns CompletableFuture.completedFuture( updatedCorporate)

        mockMvc.perform(put("/api/${AppConstant.API_VERSION}/corporate/$corporateId/profile")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(updateRequest)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test list subscription usage`() {
        val corporateId = 4L
        
        val usageResponses = listOf(
            SubscriptionUsageResponse(
                id = 4,
                name = "",
                amount = 11.11F,
                billingDate = 1234,
                status = SubscriptionPaymentStatus.PAID,
                currency = "",
                startDate = 1234,
                endDate = 2345,
                modules = null
            ),
            SubscriptionUsageResponse(
                id = 5,
                name = "",
                amount = 11.11F,
                billingDate = 1234,
                status = SubscriptionPaymentStatus.PAID,
                currency = "",
                startDate = 1234,
                endDate = 2345,
                modules = null
            )
        )
        
        every { subscriptionService.listSubscriptionUsage(corporateId) } returns usageResponses

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/$corporateId/subscription/listing")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test list subscription usage details by month`() {
        val corporateId = 4L
        val monthYear = "2023-02"
        
        val usageResponse = SubscriptionUsageResponse(
            id = 4,
            name = "",
            amount = 11.11F,
            billingDate = 1234,
            status = SubscriptionPaymentStatus.PAID,
            currency = "",
            startDate = 1234,
            endDate = 2345,
            modules = null
        )
        
        every { subscriptionService.listSubscriptionUsageDetails(corporateId, monthYear) } returns usageResponse

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/$corporateId/subscription/$monthYear")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test feature usage`() {
        val corporateId = 4L
        val featureKey = "CASES"
        
        val usageResponses : List<AbstractUsageResponse> = listOf<AbstractUsageResponse>(
            ExpertSupportUsageResponse(
                logType = ReferenceType.CASE,
                logId = 1,
                submittedBy = "",
                band = "",
                createdAt = 1,
                supportTime = 1234
            )
        )
        
        every { subscriptionService.getFeatureUsage(null, featureKey, corporateId) } returns usageResponses

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/$corporateId/subscription/feature-usage")
            .param("featureKey", featureKey)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test request receipt`() {
        val corporateId = 4L
        val monthYear = "2023-02"
        
        every { subscriptionService.requestReceipt(corporateId, monthYear, authenticatedUser) } returns true

        mockMvc.perform(post("/api/${AppConstant.API_VERSION}/corporate/$corporateId/subscription/$monthYear/receipt")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `test get work logs`() {
        val corporateId = 4L
        val monthYear = "02-2023"
        val referenceId = 123L
        val referenceType = ReferenceType.CASE
        
        val workLogs = listOf(
            WorkLogResponse(
                id = 1L,
                referenceId = referenceId,
                referenceType = referenceType,
                description = "Work log description",
                corporateId = 5,
                corporateName = "",
                eventDate = 456,
                timeSpent = 12,
                loggedBy = UserProfile(
                    id = 1L,
                    email = "<EMAIL>",
                    firstName = "John",
                    lastName = "Doe",
                    status = AccountStatus.ACTIVE,
                    role = Role.ROLE_EXPERT,
                    countryCode = "US",
                    profilePictureFullUrl = ""
                ),
                isDeleted = true,
                isDeleteAllowed = true
            )
        )
        
        val searchFilter = WorkLogSearchFilter.Builder.build(
            corporateId,
            referenceId,
            referenceType,
            2,
            2023,
            null
        )
        
        val pagedResult = PagedResult(
            rows = workLogs,
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            workLogService.list(
                searchFilter,
                PageRequest.of(0, Int.MAX_VALUE, SearchConstant.SORT_ORDER("lastUpdatedDate", "DESC")),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/$corporateId/subscription/$monthYear/work-log/$referenceId/$referenceType")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }
}