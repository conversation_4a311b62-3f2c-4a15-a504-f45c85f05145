package com.centuroglobal.controller

import com.centuroglobal.service.*
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.SearchConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.subscription.response.SubscriptionUsageDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [CorporateUserController::class])
@AutoConfigureMockMvc(addFilters = false)
class CorporateUserControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var adminUserService: AdminUserService

    @MockkBean
    private lateinit var corporateService: CorporateService

    @MockkBean
    private lateinit var corporateUserService: CorporateUserService

    @MockkBean
    private lateinit var contentLibraryService: ContentLibraryService

    @MockkBean
    private lateinit var clientService: ClientService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "CORPORATE"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.companyId } returns 4L
    }

    @Test
    fun `test create corporate user`() {
        val request = CorporateUserRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            bandId = 1L,
            corporateId = 4L,
            countryCode = "US",
            referralCode = "",
            keepMeInformed = true,
            accounts = listOf(),
            managerUserIds = listOf(),
            dialCode = "",
            contactNo = "12345",
            isDraft = true
        )

        every { corporateUserService.createCorporateUser(any(), any(), any()) } returns CorporateUser(
            id = 1L,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            status = AccountStatus.ACTIVE,
            corporateId = 4L,
            role = Role.ROLE_EXPERT,
            createdDate = 1234,
        )

        mockMvc.perform(post("/api/${AppConstant.API_VERSION}/corporate/user")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test get subscription usage details`() {
        val token = "usage-token"
        val corporateId = 4L
        
        val usageDetails = SubscriptionUsageDetails(
            threshold = 4,
            overageRate = 4.4F,
            overageCharge = 1.3F,
            totalUsage = 56
        )
        
        every { corporateUserService.getCorporateUsageDetails(token, authenticatedUser, corporateId) } returns usageDetails

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/user/usage-details")
            .param("token", token)
            .param("corporateId", corporateId.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test list corporate users`() {
        val search = "john"
        val accountId = 1L
        val status = "ACTIVE"
        val bandId = 1L
        val countryCode = "US"
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = false
        
        val userCardDetails = UserCardDetails(
            id = 1L,
            email = "<EMAIL>",
            status = AccountStatus.ACTIVE.toString(),
            jobTitle = "Manager",
            lastSeen = 1234,
            bandColor = "",
            bandLabel = "",
            fullName = "",
            country = "IN",
            casesCreated = 12,
            reportees = 12,
            profilePhotoUrl = "",
        )
        
        val pagedResult = PagedResult(
            rows = listOf(userCardDetails),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        val searchFilter = CorporateUserSearchFilter.Builder.build(search, accountId, status, bandId, countryCode)
        
        every { 
            corporateUserService.listUsers(
                searchFilter,
                PageRequest.of(pageIndex, pageSize, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/user/listing")
            .param("search", search)
            .param("account", accountId.toString())
            .param("status", status)
            .param("band", bandId.toString())
            .param("country", countryCode)
            .param("pageIndex", pageIndex.toString())
            .param("pageSize", pageSize.toString())
            .param("sort", sort)
            .param("sortBy", sortBy)
            .param("isDownload", isDownload.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows[0].id").value(1))
    }

    @Test
    fun `test update corporate user`() {
        val userId = 2L
        val request = CorporateUserRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            jobTitle = "Director",
            bandId = 2L,
            corporateId = 4L,
            countryCode = "UK",
            referralCode = "",
            keepMeInformed = true,
            accounts = listOf(1L, 2L),
            managerUserIds = listOf(1L),
            dialCode = "+44",
            contactNo = "98765",
            isDraft = false
        )
        
        every { corporateService.canUpdateUser(userId, authenticatedUser) } returns true
        every { corporateUserService.updateCorporateUser(userId, request, authenticatedUser) } returns mockk()

        mockMvc.perform(put("/api/${AppConstant.API_VERSION}/corporate/user/$userId")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value("SUCCESS"))
    }

    @Test
    fun `test get corporate user`() {
        val userId = 2L
        
        val corporateUserResponse = CorporateUserResponse(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            jobTitle = "Director",
            bandId = 2L,
            corporateId = 4L,
            countryCode = "UK",
            accounts = listOf(),
            managerUserIds = listOf(1L),
            dialCode = "+44",
            contactNo = "98765",
            keepMeInformed = true,
            profilePhotoUrl = "",
            corporateName = "",
            isPrimary = true,
            notificationSettings = listOf(),
            corporateCountryCode = "IN",
            educationQualification = "",
            salary = "1234",
            relevantExperience = "3",
        )
        
        every { corporateService.canUpdateUser(userId, authenticatedUser) } returns true
        every { corporateUserService.fetchUser(userId, 4L) } returns corporateUserResponse

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/user/$userId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test list corporate users with download flag`() {
        val search = "john"
        val accountId = 1L
        val status = "ACTIVE"
        val bandId = 1L
        val countryCode = "US"
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = true
        
        val userCardDetails = UserCardDetails(
            id = 1L,
            email = "<EMAIL>",
            status = AccountStatus.ACTIVE.toString(),
            jobTitle = "Manager",
            lastSeen = 234,
            bandColor = "",
            bandLabel = "",
            fullName = "",
            country = "IN",
            casesCreated = 123,
            reportees = 345,
            profilePhotoUrl = "",
        )
        
        val pagedResult = PagedResult(
            rows = listOf(userCardDetails),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        val searchFilter = CorporateUserSearchFilter.Builder.build(search, accountId, status, bandId, countryCode)
        
        every { 
            corporateUserService.listUsers(
                searchFilter,
                PageRequest.of(pageIndex, Int.MAX_VALUE, SearchConstant.SORT_ORDER(sortBy, sort)),
                authenticatedUser
            ) 
        } returns pagedResult

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/corporate/user/listing")
            .param("search", search)
            .param("account", accountId.toString())
            .param("status", status)
            .param("band", bandId.toString())
            .param("country", countryCode)
            .param("pageIndex", pageIndex.toString())
            .param("pageSize", pageSize.toString())
            .param("sort", sort)
            .param("sortBy", sortBy)
            .param("isDownload", isDownload.toString())
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows[0].id").value(1))
    }
}