package com.centuroglobal.controller

import com.centuroglobal.service.CountryTaxService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.entity.CountryTaxEntity
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [CountryTaxController::class])
@AutoConfigureMockMvc(addFilters = false)
class CountryTaxControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var countryTaxService: CountryTaxService

    private val mapper = ObjectMapper()

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1
    }

    @Test
    fun `test getCountryTax`() {
        val countryTaxList = listOf(CountryTaxEntity(
            id = 3,
            country = "IN",
            countryCode = "IN",
            corporateTaxRate = 4,
            employerTaxRate = 5,
            employeeTaxRate = 6,
            infoText = ""
        ))
        every { countryTaxService.getCountryTax() } returns countryTaxList

        mockMvc.perform(get("/api/v1/countryTax/")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }
}
