package com.centuroglobal.controller

import com.centuroglobal.data.payload.dashboard.CountryGdp
import com.centuroglobal.service.DashboardService
import com.centuroglobal.service.RfpService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.CountryHighlightsEntity
import com.centuroglobal.shared.data.entity.CountryIndicesCategoryEntity
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@WebMvcTest(controllers = [DashboardController::class])
@AutoConfigureMockMvc(addFilters = false)
class DashboardControllerTest(@Autowired val mockMvc:MockMvc){

    @MockkBean
    lateinit var dashboardService:DashboardService

    @MockkBean
    lateinit var accessLogService:AccessLogService

    private val authenticatedUser:AuthenticatedUser= mockk()
    private val mapper = jacksonObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `get aggregate data test`(){

        every { authenticatedUser.principal } returns authenticatedUser
        every { dashboardService.generateAggregateData(authenticatedUser) } returns listOf()
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/aggregate-data")
            .contentType(
                MediaType.APPLICATION_JSON)
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
       }

    @Test
    fun `get country gdp test`() {

        val filter = "US"
        val countryGdp=CountryGdp(
            "US",
            "5.5",
            "12345",
            "5.5",
            2.3)

        every { authenticatedUser.principal } returns authenticatedUser
        every { dashboardService.getTopCountryDataByFilter(filter) } returns listOf(countryGdp)
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/country-data")
            .param("filter", filter)
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `get country indices category list test`() {

        val countryIndicesCategoryEntity = CountryIndicesCategoryEntity(
            "test",
            "test")

        every { authenticatedUser.principal } returns authenticatedUser
        every { dashboardService.getIndicesCategory() } returns listOf(countryIndicesCategoryEntity)
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/rating-categories")
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `get did you know country facts test`() {

        val countryHighlightsEntity = CountryHighlightsEntity(1,
            "test")

        every { authenticatedUser.principal } returns authenticatedUser
        every { dashboardService.getCountryHighlights() } returns listOf(countryHighlightsEntity)
        SecurityContextHolder.getContext().authentication = authenticatedUser

        mockMvc.perform(get("/api/${AppConstant.API_VERSION}/dashboard/did-you-know")
            .characterEncoding("utf-8"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }
}