package com.centuroglobal.controller

import com.centuroglobal.data.payload.lead.LeadResponseRequest
import com.centuroglobal.facade.LeadFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.entity.ExpertCompanyProfileEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.lead.ExpertLeadSummary
import com.centuroglobal.shared.data.pojo.lead.LeadDetail
import com.centuroglobal.shared.data.pojo.lead.LeadsSearchCriteria
import com.centuroglobal.shared.data.pojo.lead.RecentLeadSummary
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.Instant
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [ExpertLeadController::class])
@AutoConfigureMockMvc(addFilters = false)
class ExpertLeadControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var leadFacade: LeadFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @MockkBean
    private lateinit var expertUserRepository: ExpertUserRepository

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "EXPERT"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test search leads`() {
        val userId = 1L
        val search = LeadsSearchCriteria(
            status = "OPEN",
            pageIndex = 0,
            pageSize = 20,
            sort = "DESC",
            sortBy = "createdDate"
        )
        
        val expertUser = ExpertUserEntity(
            id= 134,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        expertUser.id = userId
        
        val leadSummary = ExpertLeadSummary(
            id = "lead123",
            title = "Test Lead",
            description = "This is a test lead",
            status = LeadStatus.ACTIVE,
            createdDate = Instant.now().toEpochMilli(),
            expertiseName = listOf(),
            countryName = "",
            regionName = "",
            hasResponded = false
        )
        
        val pagedResult = PagedResult(
            rows = listOf(leadSummary),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        /*val deferredResult = DeferredResult<Any>()
        deferredResult.setResult(ResponseUtil.success(pagedResult))*/
        
        every { expertUserRepository.findById(userId) } returns Optional.of(expertUser)
        
        every { 
            leadFacade.searchLeadsForExpert(
                any(),
                eq(userId),
                any()
            ) 
        } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert/$userId/lead") {
            param("search", "")
            param("country", "")
            param("category", "")
            param("status", "")
            param("pageIndex", search.pageIndex.toString())
            param("pageSize", search.pageSize.toString())
            param("sort", search.sort)
            param("sortBy", search.sortBy)
        }.andExpect {
            status { isOk() }
        }

        verify { 
            leadFacade.searchLeadsForExpert(
                any(),
                userId,
                any()
            ) 
        }
    }

    @Test
    fun `test retrieve lead`() {
        val userId = 1L
        val leadId = "lead123"
        
        val leadDetail = LeadDetail(
            id = leadId,
            title = "Test Lead",
            description = "This is a test lead with detailed information",
            status = LeadStatus.ACTIVE,
            createdDate = Instant.now().toEpochMilli(),
            expertiseId = listOf(),
            expertiseName = listOf(),
            countryCode = "",
            countryName = "",
            regionId = 123,
            regionName = "",
            responses = listOf(),
            creator = null,
        )
        
        every { 
            leadFacade.retrieveLeadForExpert(userId, leadId) 
        } returns CompletableFuture.completedFuture(leadDetail)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert/$userId/lead/$leadId")
            .andExpect {
                status { isOk() }
            }

        verify { leadFacade.retrieveLeadForExpert(userId, leadId) }
    }

   /* @Test
    fun `test respond to lead`() {
        val userId = 1L
        val leadId = "lead123"
        val responseId = "resp456"
        
        val responseRequest = LeadResponseRequest(
            description = ""
        )
        
        every { 
            leadFacade.respond(userId, leadId, responseRequest) 
        } returns CompletableFuture.completedFuture(responseId)

        mockMvc.post("/api/${AppConstant.API_VERSION}/expert/$userId/lead/$leadId/response") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(responseRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(responseId) }
        }

        verify { leadFacade.respond(userId, leadId, responseRequest) }
    }*/

    /*@Test
    fun `test update lead response`() {
        val userId = 1L
        val leadId = "lead123"
        val responseId = "resp456"
        
        val responseRequest = LeadResponseRequest(
            description = ""
        )
        
        every { 
            leadFacade.updateResponse(userId, leadId, responseRequest) 
        } returns CompletableFuture.completedFuture(responseId)

        mockMvc.put("/api/${AppConstant.API_VERSION}/expert/$userId/lead/$leadId/response") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(responseRequest)
        }.andExpect {
            status { isOk() }
            jsonPath("$.success") { value(true) }
            jsonPath("$.payload") { value(responseId) }
        }

        verify { leadFacade.updateResponse(userId, leadId, responseRequest) }
    }*/

    @Test
    fun `test get recent leads`() {
        val userId = 1L
        
        val recentLeads = listOf(
            RecentLeadSummary(
                id = "lead123",
                title = "Recent Lead 1",
                createdDate = Instant.now().toEpochMilli(),
                description = "",
                expertiseName = listOf(),
                countryName = "",
                regionName = "",
                status = LeadStatus.ACTIVE
            ),
            RecentLeadSummary(
                id = "lead122",
                title = "Recent Lead 1",
                createdDate = Instant.now().toEpochMilli(),
                description = "",
                expertiseName = listOf(),
                countryName = "",
                regionName = "",
                status = LeadStatus.ACTIVE
            )
        )
        
        every { 
            leadFacade.recent() 
        } returns CompletableFuture.completedFuture(recentLeads)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert/$userId/lead/recent")
            .andExpect {
                status { isOk() }
            }

        verify { leadFacade.recent() }
    }

    @Test
    fun `test search leads with empty result`() {
        val userId = 1L
        val search = LeadsSearchCriteria(
            status = null,
            pageIndex = 0,
            pageSize = 20,
            sort = "DESC",
            sortBy = "createdDate"
        )
        
        val expertUser = ExpertUserEntity(
            id = 1,
            jobTitle = "SDE",
            countryRegionId = 2,
            expertiseId = 3,
            expertises = listOf(),
            bio = "",
            displayName = "",
            infoVideoUrl = "",
            contactNumber = "123456",
            contactEmail = "<EMAIL>",
            contactWebsite = "a.com",
            companyProfile = ExpertCompanyProfileEntity(
                id = 5,
                name = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                users = mutableListOf(),
                lastUpdatedBy = 1,
                companyNumber = "123",
                companyAddress = "",
                aboutBusiness = "",
                effectiveDate = LocalDateTime.now(),
                effectiveEndDate = LocalDateTime.now(),
                contractAcceptedDate = LocalDateTime.now(),
                feesCurrency = "",
                feesAmount = "",
                specialTerms = "",
                membershipStatus = "",
                territory = "",
                renewContract = false,
                services = "",
                account = AccountEntity(
                    id = 6,
                    name = "",
                    status = AccountStatus.ACTIVE,
                    description = "",
                    companyName = "",
                    corporate = null,
                    corporateUsers = listOf(),
                    cases = mutableListOf()
                ),
                profileImage = "",
                expertContract =  null,
                status = AccountStatus.ACTIVE,
                questionsQuota = 13,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 6
            ),
            expertType = "",
            viewContract = false,
            case = mutableListOf(),
            profileImage = ""
        )
        expertUser.id = userId
        
        val emptyPagedResult = PagedResult<ExpertLeadSummary>(
            rows = listOf(),
            totalElements = 0,
            currentPage = 0,
            totalPages = 0
        )
        
        every { expertUserRepository.findById(userId) } returns Optional.of(expertUser)
        
        every { 
            leadFacade.searchLeadsForExpert(
                any(),
                eq(userId),
                any()
            ) 
        } returns CompletableFuture.completedFuture(emptyPagedResult)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert/$userId/lead") {
            param("search", "")
            param("pageIndex", search.pageIndex.toString())
            param("pageSize", search.pageSize.toString())
            param("sort", search.sort)
            param("sortBy", search.sortBy)
        }.andExpect {
            status { isOk() }
        }

        verify { 
            leadFacade.searchLeadsForExpert(
                any(),
                userId,
                any()
            ) 
        }
    }


    @Test
    fun `test respond to lead with invalid data`() {
        val userId = 1L
        val leadId = "lead123"
        val errorMessage = "Invalid response data: Budget must be positive"
        
        val invalidResponseRequest = LeadResponseRequest(
            description = ""
        )
        
        every { 
            leadFacade.respond(userId, leadId, invalidResponseRequest) 
        } returns CompletableFuture.failedFuture(IllegalArgumentException(errorMessage))

        mockMvc.post("/api/${AppConstant.API_VERSION}/expert/$userId/lead/$leadId/response") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(invalidResponseRequest)
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.success") { value(false) }
        }

    }

    @Test
    fun `test update lead response for already accepted lead`() {
        val userId = 1L
        val leadId = "lead123"
        val errorMessage = "Cannot update response for an already accepted lead"
        
        val responseRequest = LeadResponseRequest(
            description = ""
        )
        
        every { 
            leadFacade.updateResponse(userId, leadId, responseRequest) 
        } returns CompletableFuture.failedFuture(IllegalStateException(errorMessage))

        mockMvc.put("/api/${AppConstant.API_VERSION}/expert/$userId/lead/$leadId/response") {
            contentType = MediaType.APPLICATION_JSON
            content = objectMapper.writeValueAsString(responseRequest)
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.success") { value(false) }
        }

    }
}