package com.centuroglobal.controller

import com.centuroglobal.facade.ExpertFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.pojo.ExpertCompanyProfile
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import java.time.LocalDateTime
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [ExpertSearchController::class])
@AutoConfigureMockMvc(addFilters = false)
class ExpertSearchControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var expertFacade: ExpertFacade

    private val authenticatedUser: AuthenticatedUser = mockk()

    @MockkBean
    lateinit var accessLogService: AccessLogService


    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "CORPORATE"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test search experts`() {
        val search = "legal"
        val countryCode = "US"
        val expertiseId = "1"
        val pageIndex = 0
        val pageSize = 20
        
        val expertProfileSummary = ExpertProfileSummary(
            id = 1L,
            bio = "Expert bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********0",
            contactWebsite = "example.com",
            countryCode = "US",
            countryName = "United States",
            displayName = "Test Expert",
            infoVideoUrl = "https://example.com/video.mp4",
            jobTitle = "Senior Expert",
            profilePictureFullUrl = "https://example.com/profile.jpg"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(expertProfileSummary),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        val sortOrder = Sort.by(Sort.Order(Sort.Direction.ASC, "displayName"))
        val pageRequest = PageRequest.of(pageIndex, pageSize, sortOrder)
        val searchFilter = ExpertSearchFilter.Builder.build(search, countryCode, expertiseId)
        
        every { 
            expertFacade.retrieveActiveProfileSummary(searchFilter, pageRequest) 
        } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search") {
            param("search", search)
            param("countryCode", countryCode)
            param("expertiseId", expertiseId)
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
        }.andExpect {
            status { isOk() }
        }

    }

    @Test
    fun `test search experts with minimal parameters`() {
        val pageIndex = 0
        val pageSize = 20
        
        val expertProfileSummary = ExpertProfileSummary(
            id = 1L,
            bio = "Expert bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********0",
            contactWebsite = "example.com",
            countryCode = "US",
            countryName = "United States",
            displayName = "Test Expert",
            infoVideoUrl = "https://example.com/video.mp4",
            jobTitle = "Senior Expert",
            profilePictureFullUrl = "https://example.com/profile.jpg"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(expertProfileSummary),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        val sortOrder = Sort.by(Sort.Order(Sort.Direction.ASC, "displayName"))
        val pageRequest = PageRequest.of(pageIndex, pageSize, sortOrder)
        val searchFilter = ExpertSearchFilter.Builder.build(null, null, null)
        
        every { 
            expertFacade.retrieveActiveProfileSummary(searchFilter, pageRequest) 
        } returns CompletableFuture.completedFuture(pagedResult)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search") {
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
        }.andExpect {
            status { isOk() }
        }

    }

    @Test
    fun `test search experts with empty result`() {
        val search = "nonexistent"
        val pageIndex = 0
        val pageSize = 20
        
        val emptyPagedResult = PagedResult<ExpertProfileSummary>(
            rows = listOf(),
            totalElements = 0,
            currentPage = 0,
            totalPages = 0
        )
        
        val sortOrder = Sort.by(Sort.Order(Sort.Direction.ASC, "displayName"))
        val pageRequest = PageRequest.of(pageIndex, pageSize, sortOrder)
        val searchFilter = ExpertSearchFilter.Builder.build(search, null, null)
        
        every { 
            expertFacade.retrieveActiveProfileSummary(searchFilter, pageRequest) 
        } returns CompletableFuture.completedFuture(emptyPagedResult)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search") {
            param("search", search)
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
        }.andExpect {
            status { isOk() }
        }

    }

    @Test
    fun `test retrieve expert details`() {
        val userId = 1L
        
        val expertProfileSummary = ExpertProfileSummary(
            id = 1L,
            bio = "Expert bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********0",
            contactWebsite = "example.com",
            countryCode = "US",
            countryName = "United States",
            displayName = "Test Expert",
            infoVideoUrl = "https://example.com/video.mp4",
            jobTitle = "Senior Expert",
            profilePictureFullUrl = "https://example.com/profile.jpg"
        )
        
        every { 
            expertFacade.retrieveProfileSummary(userId, false) 
        } returns CompletableFuture.completedFuture(expertProfileSummary)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search/$userId")
            .andExpect {
                status { isOk() }
            }

    }

    @Test
    fun `test retrieve expert details with admin role`() {
        val userId = 1L
        
        // Change role to ADMIN
        every { authenticatedUser.role } returns "ADMIN"
        
        val expertProfileSummary = ExpertProfileSummary(
            id = 1L,
            bio = "Expert bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********0",
            contactWebsite = "example.com",
            countryCode = "US",
            countryName = "United States",
            displayName = "Test Expert",
            infoVideoUrl = "https://example.com/video.mp4",
            jobTitle = "Senior Expert",
            profilePictureFullUrl = "https://example.com/profile.jpg"
        )
        
        every { 
            expertFacade.retrieveProfileSummary(any(), any())
        } returns CompletableFuture.completedFuture(expertProfileSummary)

        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search/$userId")
            .andExpect {
                status { isOk() }
            }
    }

    @Test
    fun `test search experts with invalid country code`() {
        val search = "legal"
        val countryCode = "INVALID" // Invalid country code (should be 2 letters)
        val pageIndex = 0
        val pageSize = 20
        
        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search") {
            param("search", search)
            param("countryCode", countryCode)
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    fun `test search experts with negative page index`() {
        val search = "legal"
        val pageIndex = -1 // Invalid page index
        val pageSize = 20
        
        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search") {
            param("search", search)
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    fun `test search experts with too small page size`() {
        val search = "legal"
        val pageIndex = 0
        val pageSize = 3 // Invalid page size (should be >= 5)
        
        mockMvc.get("/api/${AppConstant.API_VERSION}/expert-search") {
            param("search", search)
            param("pageIndex", pageIndex.toString())
            param("pageSize", pageSize.toString())
        }.andExpect {
            status { isBadRequest() }
        }
    }
}