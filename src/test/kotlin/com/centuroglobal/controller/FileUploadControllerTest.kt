package com.centuroglobal.controller

import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(controllers = [FileUploadController::class])
@AutoConfigureMockMvc(addFilters = false)
class FileUploadControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var awsS3Service: AwsS3Service

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns "CORPORATE"
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

    @Test
    fun `test get pre-signed upload URL for profile photo`() {
        val uploadType = UploadType.PROFILE_PHOTO
        val preSignedUrl = "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        
        val urlMap = mapOf(
            "url" to preSignedUrl,
            "key" to "uploads/profile-photos/user-1-123456789.jpg"
        )
        
        every { awsS3Service.getPreSignedUploadUrl() } returns urlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.url") { value(preSignedUrl) }
                jsonPath("$.payload.key") { value("uploads/profile-photos/user-1-123456789.jpg") }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }

    @Test
    fun `test get pre-signed upload URL for onboarding document`() {
        val uploadType = UploadType.ONBOARDING_DOC
        val preSignedUrl = "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        
        val urlMap = mapOf(
            "url" to preSignedUrl,
            "key" to "uploads/onboarding-docs/doc-1-123456789.pdf"
        )
        
        every { awsS3Service.getPreSignedUploadUrl() } returns urlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.url") { value(preSignedUrl) }
                jsonPath("$.payload.key") { value("uploads/onboarding-docs/doc-1-123456789.pdf") }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }

    @Test
    fun `test get pre-signed upload URL for corporate document`() {
        val uploadType = UploadType.CORPORATE_DOC
        val preSignedUrl = "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        
        val urlMap = mapOf(
            "url" to preSignedUrl,
            "key" to "uploads/corporate-docs/corp-1-123456789.pdf"
        )
        
        every { awsS3Service.getPreSignedUploadUrl() } returns urlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.url") { value(preSignedUrl) }
                jsonPath("$.payload.key") { value("uploads/corporate-docs/corp-1-123456789.pdf") }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }

    @Test
    fun `test get pre-signed upload URL with admin role`() {
        // Change role to ADMIN
        every { authenticatedUser.role } returns "ADMIN"
        
        val uploadType = UploadType.PROFILE_PHOTO
        val preSignedUrl = "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        
        val urlMap = mapOf(
            "url" to preSignedUrl,
            "key" to "uploads/profile-photos/admin-1-123456789.jpg"
        )
        
        every { awsS3Service.getPreSignedUploadUrl() } returns urlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.url") { value(preSignedUrl) }
                jsonPath("$.payload.key") { value("uploads/profile-photos/admin-1-123456789.jpg") }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }

    @Test
    fun `test get pre-signed upload URL with expert role`() {
        // Change role to EXPERT
        every { authenticatedUser.role } returns "EXPERT"
        
        val uploadType = UploadType.PROFILE_PHOTO
        val preSignedUrl = "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        
        val urlMap = mapOf(
            "url" to preSignedUrl,
            "key" to "uploads/profile-photos/expert-1-123456789.jpg"
        )
        
        every { awsS3Service.getPreSignedUploadUrl() } returns urlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.url") { value(preSignedUrl) }
                jsonPath("$.payload.key") { value("uploads/profile-photos/expert-1-123456789.jpg") }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }

    @Test
    fun `test get pre-signed upload URL with partner role`() {
        // Change role to PARTNER
        every { authenticatedUser.role } returns "PARTNER"
        
        val uploadType = UploadType.PROFILE_PHOTO
        val preSignedUrl = "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        
        val urlMap = mapOf(
            "url" to preSignedUrl,
            "key" to "uploads/profile-photos/partner-1-123456789.jpg"
        )
        
        every { awsS3Service.getPreSignedUploadUrl() } returns urlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload.url") { value(preSignedUrl) }
                jsonPath("$.payload.key") { value("uploads/profile-photos/partner-1-123456789.jpg") }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }

    @Test
    fun `test get pre-signed upload URL with empty response`() {
        val uploadType = UploadType.PROFILE_PHOTO
        
        // Empty map to simulate a case where no URL is generated
        val emptyUrlMap = mapOf<String, String>()
        
        every { awsS3Service.getPreSignedUploadUrl() } returns emptyUrlMap

        mockMvc.get("/api/${AppConstant.API_VERSION}/uploads/${uploadType.name}")
            .andExpect {
                status { isOk() }
                jsonPath("$.success") { value(true) }
                jsonPath("$.payload") { isEmpty() }
            }

        verify { awsS3Service.getPreSignedUploadUrl() }
    }
}