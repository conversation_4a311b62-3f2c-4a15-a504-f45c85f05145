package com.centuroglobal.controller

import com.centuroglobal.data.payload.ImmigrationDetails
import com.centuroglobal.data.payload.ImmigrationRequirement
import com.centuroglobal.service.ImmigrationService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [ImmigrationController::class])
@AutoConfigureMockMvc(addFilters = false)
class ImmigrationControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var immigrationService: ImmigrationService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns "ADMIN"
    }

    @Test
    fun `test getVisaDetails with source and destination countries`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "UK"
        
        val immigrationRequirement = ImmigrationRequirement(
            businessVisa = true,
            workVisa = false,
            eVisa = false,
            validDays = 90
        )
        
        every { 
            immigrationService.getVisaDetails(sourceCountry, destinationCountry) 
        } returns immigrationRequirement

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/immigration/$sourceCountry/$destinationCountry")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test getVisaDetails with source, destination countries and visa type`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "UK"
        val visaType = "BUSINESS"
        
        val immigrationDetails = ImmigrationDetails(
            isVisaRequired = true,
            noOfDays = 90,
            sessionId = "session-123"
        )
        
        every { 
            immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType) 
        } returns immigrationDetails

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/immigration/$sourceCountry/$destinationCountry/$visaType")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test getVisaDetails with work visa type`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "DE"
        val visaType = "WORK"
        
        val immigrationDetails = ImmigrationDetails(
            isVisaRequired = true,
            noOfDays = 180,
            sessionId = "session-456"
        )
        
        every { 
            immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType) 
        } returns immigrationDetails

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/immigration/$sourceCountry/$destinationCountry/$visaType")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test getVisaDetails with e-visa type`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "AU"
        val visaType = "EVISA"
        
        val immigrationDetails = ImmigrationDetails(
            isVisaRequired = true,
            noOfDays = 30,
            sessionId = "session-789"
        )
        
        every { 
            immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType) 
        } returns immigrationDetails

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/immigration/$sourceCountry/$destinationCountry/$visaType")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test getVisaDetails with visa not required`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "CA"
        
        val immigrationRequirement = ImmigrationRequirement(
            businessVisa = false,
            workVisa = false,
            eVisa = false,
            validDays = 180
        )
        
        every { 
            immigrationService.getVisaDetails(sourceCountry, destinationCountry) 
        } returns immigrationRequirement

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/immigration/$sourceCountry/$destinationCountry")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
    }
}