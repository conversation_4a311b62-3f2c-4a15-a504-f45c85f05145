package com.centuroglobal.controller

import com.centuroglobal.service.metrics.AccessLogService
import com.ninjasquad.springmockk.MockkBean
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.redirectedUrl
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [IndexController::class])
@AutoConfigureMockMvc(addFilters = false)
class IndexControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var accessLogService: AccessLogService

    @Test
    fun `test redirectToSwagger redirects to swagger-ui html page`() {
        mockMvc.perform(get("/"))
            .andExpect(status().is3xxRedirection)
            .andExpect(redirectedUrl("swagger-ui.html"))
    }
}