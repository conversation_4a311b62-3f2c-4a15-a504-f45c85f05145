package com.centuroglobal.controller

import com.centuroglobal.service.ClientService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.account.signup.UpdateAccountStatusRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [PartnerClientController::class])
@AutoConfigureMockMvc(addFilters = false)
class PartnerClientControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var partnerService: PartnerService

    @MockkBean
    private lateinit var clientService: ClientService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.role } returns Role.ROLE_PARTNER.name
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 4
    }

   /* @Test
    fun `test updateUserStatus`() {
        // Arrange
        val userId = 123L
        val partnerId = 456L
        val request = UpdateAccountStatusRequest(
            userId = 2,
            accountStatus = AccountStatus.ACTIVE
        )
        
        val deferredResult = DeferredResult<Response<Any>>()
        deferredResult.setResult(Response(true, "Status updated successfully"))
        
        every { 
            clientService.updateUserStatus(any(), any())
        } returns Unit
        
        // Act & Assert
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/partner/$userId/$partnerId/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test getUserProfiles`() {
        // Arrange
        val partnerId = 456L
        val userIds = "123,456,789"
        val userIdsList = listOf(123L, 456L, 789L)
        
        val userProfiles = listOf(
            UserProfile(
                id = 123L,
                email = "<EMAIL>",
                firstName = "John",
                lastName = "Doe",
                status = AccountStatus.ACTIVE,
                role = Role.ROLE_CORPORATE,
                companyName = "Company A",
            ),
            UserProfile(
                id = 456L,
                email = "<EMAIL>",
                firstName = "Jane",
                lastName = "Smith",
                status = AccountStatus.ACTIVE,
                role = Role.ROLE_EXPERT,
                companyName = "Company B",
            ),
            UserProfile(
                id = 789L,
                email = "<EMAIL>",
                firstName = "Bob",
                lastName = "Johnson",
                status = AccountStatus.SUSPENDED,
                role = Role.ROLE_CORPORATE,
                companyName = "Company C",
            )
        )
        
        every { 
            partnerService.getUserProfiles(partnerId, userIdsList) 
        } returns userProfiles
        
        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$partnerId/profiles")
                .param("userIds", userIds)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)

        
        verify { partnerService.getUserProfiles(partnerId, userIdsList) }
    }*/

    @Test
    fun `test updateUserStatus with invalid request`() {
        // Arrange
        val userId = 123L
        val partnerId = 456L
        val request = UpdateAccountStatusRequest(
            userId = 2,
            accountStatus = AccountStatus.ACTIVE
        )
        
        // Act & Assert
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/partner/$userId/$partnerId/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().is4xxClientError)
    }

   /* @Test
    fun `test getUserProfiles with empty user ids`() {
        // Arrange
        val partnerId = 456L
        val userIds = ""
        
        every { 
            partnerService.getUserProfiles(partnerId, emptyList()) 
        } returns emptyList()
        
        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$partnerId/profiles")
                .param("userIds", userIds)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)

        verify { partnerService.getUserProfiles(partnerId, emptyList()) }
    }

    @Test
    fun `test getUserProfiles with invalid user ids format`() {
        // Arrange
        val partnerId = 456L
        val userIds = "123,abc,456"  // Contains non-numeric value
        
        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$partnerId/profiles")
                .param("userIds", userIds)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isBadRequest)
    }*/
}