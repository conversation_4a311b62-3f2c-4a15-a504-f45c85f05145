package com.centuroglobal.controller

import com.centuroglobal.service.AdminUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.AllPartnerList
import com.centuroglobal.shared.data.pojo.PartnerDashboardCompanyInfo
import com.centuroglobal.shared.data.pojo.PartnerUserCounts
import com.centuroglobal.shared.data.pojo.UpdateCompanyStatusRequest
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@WebMvcTest(controllers = [PartnerController::class])
@AutoConfigureMockMvc(addFilters = false)
class PartnerControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var partnerService: PartnerService

    @MockkBean
    private lateinit var adminUserService: AdminUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.isAuthenticated } returns true
        every { authenticatedUser.partnerId } returns 3
        every { authenticatedUser.name } returns "a"
    }

    @Test
    fun `test getAllPartnerDetails`() {
        val partnerId = 1L

        val allPartnerList = AllPartnerList(
            id = 1L,
            name = "Partner Name",
            createdFrom = PartnerType.EXPERT,
            contractFromDate = *************,
            contractToDate = *************,
            casesManagedBy = PartnerCaseType.CG,
            queriesManagedBy = PartnerCaseType.SELF,
            companyLogo = "logo.png",
            primaryColor = "#FFFFFF",
            secondaryColor = "#000000",
            status = CorporateStatus.ACTIVE,
            createReferenceId = 2L,
            country = "US",
            features = listOf("Feature1", "Feature2"),
            corporateFeatures = listOf("CorporateFeature1", "CorporateFeature2"),
            rootUserId = 3L,
            rootUserEmail = "<EMAIL>",
            rootUserFirstName = "Root",
            rootUserLastName = "User",
            rootUserJobTitle = "CEO",
            rootUserStatus = AccountStatus.ACTIVE,
            rootUserCreatedDate = *************,
            rootUserLastLoginDate = *************,
            rootUserProfilePictureFullUrl = "http://example.com/profile.jpg",
            rootUserContactNumber = "+**********",
            onboardingDocs = listOf(),
            aiMessageCount = 10
        )

        every { partnerService.retrieveAllPartnerDetails(partnerId) } returns allPartnerList

        mockMvc.perform(get("/api/v1/partner/$partnerId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test updateStatus`() {
        val partnerId = 1L
        val updateCompanyStatusRequest = UpdateCompanyStatusRequest(
            id = 3,
            type = UserType.EXPERT,
            status = AccountStatus.ACTIVE
        )
        every { partnerService.updatePartnerCompanyStatus(partnerId, updateCompanyStatusRequest, authenticatedUser) } returns true

        mockMvc.perform(put("/api/v1/partner/$partnerId/company-status")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(updateCompanyStatusRequest)))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `test getCompanyInfo`() {
        val companyInfo = PartnerDashboardCompanyInfo(
            docs = listOf(),
            stats = PartnerUserCounts(
                admins = 3,
                corporates = 2,
                suppliers = 12
            ),
            countryCode = "IN",
            companyName = ""
        )
        every { partnerService.getDashboardInfo(authenticatedUser) } returns companyInfo

        mockMvc.perform(get("/api/v1/partner/company-info")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isNotEmpty)
    }

    @Test
    fun `test viewUrl`() {
        val fileId = 1L
        val docUrl = "http://example.com/doc"
        every { adminUserService.getDocUrlCorporate(any(), any(), any()) } returns docUrl

        mockMvc.perform(get("/api/v1/partner/view-doc/$fileId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(docUrl))
    }

    @Test
    fun `test downloadDoc`() {
        val fileId = 1L
        val streamingResponseBody = mockk<StreamingResponseBody>()
        every { adminUserService.downloadDocCorporate(any(), any(), any()) }returns ResponseEntity.ok(streamingResponseBody)

        mockMvc.perform(get("/api/v1/partner/download-doc/$fileId")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
    }

    @Test
    fun `test getPartnerBandAccesses`() {
        val accesses = listOf("Access1", "Access2")
        every { partnerService.retrievePartnerBandAccesses(any()) } returns accesses

        mockMvc.perform(get("/api/v1/partner/access")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").isArray)
    }
}

