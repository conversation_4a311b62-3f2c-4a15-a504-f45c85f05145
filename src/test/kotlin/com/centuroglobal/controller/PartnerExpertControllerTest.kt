package com.centuroglobal.controller

import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.service.PartnerService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ExpertCompanyType
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.account.CompanyRequest
import com.centuroglobal.shared.data.payload.account.CreatePrimaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.CreateSecondaryExpertUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateExpertUserRequest
import com.centuroglobal.shared.data.payload.partner.UpdateExpertPartnerCompanyRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [PartnerExpertController::class])
@AutoConfigureMockMvc(addFilters = false)
class PartnerExpertControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var partnerService: PartnerService

    @MockkBean
    private lateinit var expertUserService: ExpertUserService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.isAuthenticated } returns true
        every { authenticatedUser.partnerId } returns 3
        every { authenticatedUser.name } returns "a"
    }

    @Test
    fun `test listPartnerExperts`() {
        // Arrange
        val search = "Test Expert"
        val country = "US"
        val status = "ACTIVE"
        val pageIndex = 0
        val pageSize = 20
        val sort = "DESC"
        val sortBy = "createdDate"
        val isDownload = false
        
        val expertList = ExpertList(
            id = 1L,
            name = "Test Expert",
            country = "US",
            status = AccountStatus.ACTIVE.toString(),
            expertUser = 333,
            primaryExpertUserId = "",
            createdOn = 123455,
            type = ExpertCompanyType.EXPERT,
            associations = listOf()
        )
        
        val listingWithStats = ListingWithStatsDetails(
            stats = ListingStats(
                total = 12,
                active = 3,
                suspended = 5,
                pending = 4
            ),
            data = PagedResult(
                listOf(expertList),
                totalElements = 1,
                currentPage = 0,
                totalPages = 1
            )
        )
        
        every { 
            partnerService.listExpertStats(
                any(),
                any(),
                any(),
                any()
            ) 
        } returns listingWithStats

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/experts")
                .param("search", search)
                .param("country", country)
                .param("status", status)
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .param("sort", sort)
                .param("sortBy", sortBy)
                .param("isDownload", isDownload.toString())
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test updateExpertUserPartner`() {
        // Arrange
        val partnerId = 3L
        val expertId = 5L
        
        val updateRequest = UpdateExpertUserRequest(
            firstName = "Updated",
            lastName = "Expert",
            jobTitle = "Senior Expert",
            country = "UK",
        )
        
        every { 
            expertUserService.updateExpertUserInfo(any(), any(), any())
        } returns expertId

        // Act & Assert
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/partner/$partnerId/expert/$expertId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(expertId))
    }

    @Test
    fun `test inviteExpert`() {
        // Arrange
        val partnerId = 3L
        
        val request = CreatePrimaryExpertUserRequest(
            firstName = "New",
            lastName = "Expert",
            email = "<EMAIL>",
            jobTitle = "Expert Consultant",
            countryCode = "IN",
            profileImage = "",
            companyProfile = CompanyRequest(
                "tet", "34", "Arora", "mxs", "qmw",
                "nl", "5", "small", 5L, 6L, "INR", "9000",
                "NA", "NA", "NA", "kl", 8L,
                6L, "abc", 0L
            ),
            aiMessageCount = 123
        )
        
        val responseMessage = "Invitation sent successfully"
        
        every { 
            expertUserService.invitePartnerExpert(any(), any(), any())
        } returns Unit

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/partner/$partnerId/experts/sign-up")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
    }

    @Test
    fun `test listExpertCompany`() {
        // Arrange
        val partnerId = 3L
        
        val expertCompanies = listOf(
            ReferenceData(1L, "Expert Company 1"),
            ReferenceData(2L, "Expert Company 2"),
            ReferenceData(3L, "Expert Company 3")
        )
        
        every { 
            partnerService.retrieveExpertCompany(partnerId)
        } returns expertCompanies

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$partnerId/expert-companies")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload[0].id").value(1))
            .andExpect(jsonPath("$.payload[0].name").value("Expert Company 1"))
            .andExpect(jsonPath("$.payload[1].id").value(2))
            .andExpect(jsonPath("$.payload[2].id").value(3))
    }

    @Test
    fun `test createSecondaryExpert`() {
        // Arrange
        val partnerId = 3L
        
        val request = CreateSecondaryExpertUserRequest(
            firstName = "Secondary",
            lastName = "Expert",
            email = "<EMAIL>",
            jobTitle = "Junior Expert",
            countryCode = "IN",
            expertType = "",
            partnerId = 123,
            profileImage = "",
            expertCompanyId = 123
        )
        
        every { 
            expertUserService.createSecondaryExpertUser(any(), any())
        } returns Unit

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/partner/$partnerId/experts/secondary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        )
            .andExpect(status().isCreated)
    }

    @Test
    fun `test retrieveExpertUser`() {
        // Arrange
        val partnerId = 3L
        val expertUserId = 5L
        
        val expertUserResponse = ExpertUserResponse(
            firstName = "John",
            lastName = "Expert",
            email = "<EMAIL>",
            jobTitle = "Senior Expert",
            companyName = "Expert Company",
            countryCode = "IN",
            companyNumber = "",
            companyAddress = "",
            aboutBusiness = "",
            territory = "",
            services = "",
            effectiveDate = 432,
            effectiveEndDate = 333,
            contract = "",
            profileImage = ""
        )
        
        every { 
            expertUserService.retrieveExpertUserDetails(any())
        } returns expertUserResponse

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$partnerId/expert-user/$expertUserId")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test expertUsersByCompanyIds`() {
        // Arrange
        val partnerId = 3L
        val companyIds = "10,11,12"
        
        val expertUsers = listOf(
            ExpertUserReferenceData(1L, "<EMAIL>","John", "Expert"),
            ExpertUserReferenceData(2L, "John", "<EMAIL>","Expert")
        )
        
        every { 
            expertUserService.retrieveExpertUsersByCompanyIds(any(), any())
        } returns expertUsers

        every { partnerService.retrieveExpertUsersByCompanyIds(any(), any()) } returns expertUsers

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$partnerId/expert-users")
                .param("companyIds", companyIds)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload[0].id").value(1))
            .andExpect(jsonPath("$.payload[0].name").value("John Expert"))
            .andExpect(jsonPath("$.payload[1].id").value(2))
    }

    @Test
    fun `test updateExpertCompany`() {
        // Arrange
        val partnerId = 3L
        val expertCompanyId = 10L
        
        val updateRequest = UpdateExpertPartnerCompanyRequest(
            companyLogoId = "logo123",
            name = "",
            companyNumber = "",
            territory = "",
            companyAddress = "",
            aboutBusiness = "",
            services = "",
            effectiveDate = 1234,
            effectiveEndDate = 3456
        )
        
        every { 
            expertUserService.updateExpertCompany(any(), any(), any())
        } returns expertCompanyId

        // Act & Assert
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/partner/$partnerId/expert-company/$expertCompanyId")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(expertCompanyId))
    }

    @Test
    fun `test retrieveSecondaryExpertList`() {
        // Arrange
        val companyId = 10L
        
        val secondaryExperts = mutableListOf(
            Client(
                id = 1L,
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE,
                fullName = "",
                company = "",
                subscription = false,
                userType = UserType.EXPERT,
                createdDate = 1234,
                referredBy = "",
                referral = 22,
                userId = 1234,
                countryCode = "",
                expertiseIds = "",
                isLinkedin = false,
                country = "",
                expertise = "",
                subscriptionType = "",
                expertType = "",
                corporateType = "",
                secondaryUserCount = 212,
                bandName = "",
                lastLoginTime = 432,
                lastTermsViewDate = 1234
            ),
            Client(
                id = 2L,
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE,
                fullName = "",
                company = "",
                subscription = false,
                userType = UserType.EXPERT,
                createdDate = 1234,
                referredBy = "",
                referral = 22,
                userId = 1234,
                countryCode = "",
                expertiseIds = "",
                isLinkedin = false,
                country = "",
                expertise = "",
                subscriptionType = "",
                expertType = "",
                corporateType = "",
                secondaryUserCount = 212,
                bandName = "",
                lastLoginTime = 432,
                lastTermsViewDate = 1234
            )
        )
        
        every { 
            expertUserService.retrieveSecondaryExpert(any(), any())
        } returns secondaryExperts

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/partner/$companyId/secondary")
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload[0].id").value(1))
    }
}