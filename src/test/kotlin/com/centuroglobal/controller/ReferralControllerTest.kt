package com.centuroglobal.controller

import com.centuroglobal.data.payload.referral.CreateReferralCodeRequest
import com.centuroglobal.facade.AdminClientFacade
import com.centuroglobal.facade.ReferralFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.Response
import com.centuroglobal.shared.data.pojo.Client
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.Referral
import com.centuroglobal.shared.data.pojo.ReferralResponse
import com.centuroglobal.shared.data.pojo.client.ClientListing
import com.centuroglobal.shared.data.pojo.client.ClientStats
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.context.request.async.DeferredResult
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [ReferralController::class])
@AutoConfigureMockMvc(addFilters = false)
class ReferralControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var referralFacade: ReferralFacade

    @MockkBean
    private lateinit var adminClientFacade: AdminClientFacade

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
    }

    @Test
    fun `test getUserReferralCode`() {
        val request = CreateReferralCodeRequest(userId = 1L)
        val referralResponse = ReferralResponse(
            data = Referral(
                referralCode = "code",
                referralLink = "",
                referrerName = "name"
            )
        )
        val deferredResult = DeferredResult<Response<ReferralResponse>>()
        deferredResult.setResult(Response(true, referralResponse))

        every { referralFacade.getReferralCode(any(), any()) } returns CompletableFuture.completedFuture(referralResponse)

        mockMvc.perform(post("/api/v1/referral/user-referral-code")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk)
    }

    @Test
    fun `test list`() {

        val client = Client(
            id = 1,
            email = "<EMAIL>",
            fullName = "a",
            status = AccountStatus.ACTIVE,
            company = "",
            subscription = true,
            userType = UserType.CORPORATE,
            createdDate = 12345,
            referredBy = "",
            referral = 4,
            userId = 4,
            countryCode = "IN",
            expertiseIds = "",
            isLinkedin = true,
            country = "IN",
            expertise = "",
            subscriptionType = "",
            expertType = "",
            corporateType = "",
            secondaryUserCount = 3,
            bandName = "",
            lastLoginTime = 1234,
            lastTermsViewDate = 1234
        )

        val clientListing = ClientListing(
            data = PagedResult(listOf(client), 1, 0, 1),
            stats = ClientStats(
                users = 4,
                corporate = 3,
                expert = 5,
                active = 2,
                subscribed = 1
            )
        )
        val deferredResult = DeferredResult<Response<ClientListing>>()
        deferredResult.setResult(Response(true, clientListing))

        every { adminClientFacade.listClientsReferredBy(1L, any()) } returns CompletableFuture.completedFuture(clientListing)

        mockMvc.perform(get("/api/v1/referral/referral-listing/1")
            .param("pageIndex", "0")
            .param("pageSize", "20"))
            .andExpect(status().isOk)
    }
}
