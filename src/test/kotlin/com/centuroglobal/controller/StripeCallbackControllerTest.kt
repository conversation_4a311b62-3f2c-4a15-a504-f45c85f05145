package com.centuroglobal.controller

import com.centuroglobal.facade.stripe.StripeEventLogFacade
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.concurrent.CompletableFuture

@WebMvcTest(controllers = [StripeCallbackController::class])
@AutoConfigureMockMvc(addFilters = false)
class StripeCallbackControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var stripeEventLogFacade: StripeEventLogFacade
    
    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val stripeSignature = "whsec_test_signature"
    private val eventPayload = """
        {
          "id": "evt_test123",
          "object": "event",
          "type": "payment_intent.succeeded",
          "data": {
            "object": {
              "id": "pi_test123",
              "object": "payment_intent",
              "amount": 1000,
              "currency": "usd"
            }
          }
        }
    """.trimIndent()

    @Test
    fun `test paymentIntent endpoint`() {
        // Arrange
        every { 
            stripeEventLogFacade.logPayment(stripeSignature, eventPayload) 
        } returns CompletableFuture.completedFuture("Payment processed successfully")

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/payment")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test invoice endpoint`() {
        // Arrange
        every { 
            stripeEventLogFacade.logInvoice(stripeSignature, eventPayload) 
        } returns CompletableFuture.completedFuture("Invoice processed successfully")

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/invoice")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test subscription endpoint`() {
        // Arrange
        every { 
            stripeEventLogFacade.logSubscription(stripeSignature, eventPayload) 
        } returns CompletableFuture.completedFuture("Subscription processed successfully")

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/subscription")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test checkoutSession endpoint`() {
        // Arrange
        every { 
            stripeEventLogFacade.logCheckoutSession(stripeSignature, eventPayload) 
        } returns CompletableFuture.completedFuture("Checkout session processed successfully")

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/checkout-session")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test paymentIntent endpoint with error`() {
        // Arrange
        val errorMessage = "Invalid Stripe signature"
        val completableFuture = CompletableFuture<String>()
        completableFuture.completeExceptionally(RuntimeException(errorMessage))
        
        every { 
            stripeEventLogFacade.logPayment(stripeSignature, eventPayload) 
        } returns completableFuture

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/payment")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test invoice endpoint with error`() {
        // Arrange
        val errorMessage = "Invalid invoice data"
        val completableFuture = CompletableFuture<String>()
        completableFuture.completeExceptionally(RuntimeException(errorMessage))
        
        every { 
            stripeEventLogFacade.logInvoice(stripeSignature, eventPayload) 
        } returns completableFuture

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/invoice")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test subscription endpoint with error`() {
        // Arrange
        val errorMessage = "Invalid subscription data"
        val completableFuture = CompletableFuture<String>()
        completableFuture.completeExceptionally(RuntimeException(errorMessage))
        
        every { 
            stripeEventLogFacade.logSubscription(stripeSignature, eventPayload) 
        } returns completableFuture

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/subscription")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `test checkoutSession endpoint with error`() {
        // Arrange
        val errorMessage = "Invalid checkout session data"
        val completableFuture = CompletableFuture<String>()
        completableFuture.completeExceptionally(RuntimeException(errorMessage))
        
        every { 
            stripeEventLogFacade.logCheckoutSession(stripeSignature, eventPayload) 
        } returns completableFuture

        // Act & Assert
        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/stripe-callback/checkout-session")
                .header("Stripe-Signature", stripeSignature)
                .contentType(MediaType.APPLICATION_JSON)
                .content(eventPayload)
        )
            .andExpect(status().isOk)
    }
}