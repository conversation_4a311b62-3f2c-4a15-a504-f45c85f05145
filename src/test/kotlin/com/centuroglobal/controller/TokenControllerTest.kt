package com.centuroglobal.controller

import com.centuroglobal.service.TokenVerificationService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(controllers = [TokenController::class])
@AutoConfigureMockMvc(addFilters = false)
class TokenControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var tokenVerificationService: TokenVerificationService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val objectMapper = ObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
    }

    @Test
    fun `test switch role to CORPORATE`() {
        val role = Role.ROLE_CORPORATE

        val tokenResult = TokenResult(
            tokenType = "Bearer",
            refreshToken = "refresh-token-123",
            accessToken = "access-token-456",
            expiresIn = 3600,
            scope = "read write",
            onboard = false,
            lastLoginDate = System.currentTimeMillis(),
            validationToken = "validation-token-789",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "Test Company",
            bandName = "Senior Manager",
            profilePhotoUrl = "https://example.com/photo.jpg",
            aiMessageCount = 100,
            userRoles = listOf("CORPORATE"),
            loginToken = "login-token-abc",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = true,
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            companyLogo = "https://example.com/logo.png",
            isPartnerUser = false
        )

        every { tokenVerificationService.generateLoginTokenForRole(authenticatedUser, role) } returns tokenResult

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/switch-role")
                .param("role", role.name)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test switch role to EXPERT`() {
        val role = Role.ROLE_EXPERT

        val tokenResult = TokenResult(
            tokenType = "Bearer",
            refreshToken = "refresh-token-expert",
            accessToken = "access-token-expert",
            expiresIn = 3600,
            scope = "read write",
            onboard = false,
            lastLoginDate = System.currentTimeMillis(),
            validationToken = "validation-token-expert",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "Expert Company",
            bandName = "Expert",
            profilePhotoUrl = "https://example.com/expert-photo.jpg",
            aiMessageCount = 50,
            userRoles = listOf("EXPERT"),
            loginToken = "login-token-expert",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = true,
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            companyLogo = "https://example.com/expert-logo.png",
            isPartnerUser = false
        )

        every { tokenVerificationService.generateLoginTokenForRole(authenticatedUser, role) } returns tokenResult

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/switch-role")
                .param("role", role.name)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test switch role to PARTNER`() {
        val role = Role.ROLE_PARTNER

        val tokenResult = TokenResult(
            tokenType = "Bearer",
            refreshToken = "refresh-token-partner",
            accessToken = "access-token-partner",
            expiresIn = 3600,
            scope = "read write",
            onboard = false,
            lastLoginDate = System.currentTimeMillis(),
            validationToken = "validation-token-partner",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "Partner Company",
            bandName = "Partner",
            profilePhotoUrl = "https://example.com/partner-photo.jpg",
            aiMessageCount = 75,
            userRoles = listOf("PARTNER"),
            loginToken = "login-token-partner",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = true,
            casesManagedBy = PartnerCaseType.PARTNER,
            queryManagedBy = PartnerCaseType.PARTNER,
            companyLogo = "https://example.com/partner-logo.png",
            isPartnerUser = true
        )

        every { tokenVerificationService.generateLoginTokenForRole(authenticatedUser, role) } returns tokenResult

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/switch-role")
                .param("role", role.name)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test switch role to ADMIN`() {
        val role = Role.ROLE_ADMIN

        val tokenResult = TokenResult(
            tokenType = "Bearer",
            refreshToken = "refresh-token-admin",
            accessToken = "access-token-admin",
            expiresIn = 3600,
            scope = "read write",
            onboard = false,
            lastLoginDate = System.currentTimeMillis(),
            validationToken = "validation-token-admin",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "Centuro Global",
            bandName = "Admin",
            profilePhotoUrl = "https://example.com/admin-photo.jpg",
            aiMessageCount = 200,
            userRoles = listOf("ADMIN"),
            loginToken = "login-token-admin",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = true,
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            companyLogo = "https://example.com/admin-logo.png",
            isPartnerUser = false
        )

        every { tokenVerificationService.generateLoginTokenForRole(authenticatedUser, role) } returns tokenResult

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/switch-role")
                .param("role", role.name)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `test switch role to SUPER_ADMIN`() {
        val role = Role.ROLE_SUPER_ADMIN

        val tokenResult = TokenResult(
            tokenType = "Bearer",
            refreshToken = "refresh-token-super-admin",
            accessToken = "access-token-super-admin",
            expiresIn = 3600,
            scope = "read write",
            onboard = false,
            lastLoginDate = System.currentTimeMillis(),
            validationToken = "validation-token-super-admin",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "Centuro Global",
            bandName = "Super Admin",
            profilePhotoUrl = "https://example.com/super-admin-photo.jpg",
            aiMessageCount = 500,
            userRoles = listOf("SUPER_ADMIN"),
            loginToken = "login-token-super-admin",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = true,
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            companyLogo = "https://example.com/super-admin-logo.png",
            isPartnerUser = false
        )

        every { tokenVerificationService.generateLoginTokenForRole(authenticatedUser, role) } returns tokenResult

        mockMvc.perform(
            post("/api/${AppConstant.API_VERSION}/switch-role")
                .param("role", role.name)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
    }
}