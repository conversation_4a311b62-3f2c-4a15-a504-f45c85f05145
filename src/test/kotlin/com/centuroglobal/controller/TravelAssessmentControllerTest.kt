package com.centuroglobal.controller

import com.centuroglobal.service.TravelAssessmentService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.travel.TravelAssessmentDetails
import com.centuroglobal.shared.data.pojo.travel.TravelAssessmentLogsResponse
import com.centuroglobal.shared.data.pojo.travel.TravelAssessmentTrackingDetails
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant
import java.util.*

@WebMvcTest(controllers = [TravelAssessmentController::class])
@AutoConfigureMockMvc(addFilters = false)
class TravelAssessmentControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    lateinit var travelAssessmentService: TravelAssessmentService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val mapper = jacksonObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.displayName } returns "John Doe"
    }

    @Test
    fun `get travel assessment listing`() {
        val assessmentDetails = TravelAssessmentDetails(
            id = 1L,
            originCountry = "US",
            destinationCountry = "UK",
            purpose = listOf("BUSINESS_TRAVEL"),
            createdBy = "Test User",
            periodOfStay = 10,
            stayUnit = "DAYS",
            createdOn = System.currentTimeMillis(),
            applicant = UserProfile(1, "<EMAIL>", "test", "user", AccountStatus.ACTIVE, Role.ROLE_CORPORATE, null, null)
        )

        val pagedResult = PagedResult(
            listOf(assessmentDetails),
            1L,
            0,
            1
        )

        every { 
            travelAssessmentService.listTravelAssessments(any(), any(), authenticatedUser)
        } returns pagedResult

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/travel-assessment/listing")
                .param("originCountry", "US")
                .param("destinationCountry", "UK")
                .param("pageIndex", "0")
                .param("pageSize", "20")
                .param("sort", "DESC")
                .param("sortBy", "createdDate")
                .param("assessmentType", "BUSINESS")
                .param("purpose", "BUSINESS_TRAVEL")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows[0].id").value(assessmentDetails.id))
            .andExpect(jsonPath("$.payload.rows[0].originCountry").value(assessmentDetails.originCountry))
            .andExpect(jsonPath("$.payload.totalElements").value(pagedResult.totalElements))
    }

    @Test
    fun `get travel assessment tracking`() {
        val assessmentId = 1L
        val trackingDetails = TravelAssessmentTrackingDetails(
            id = 1L,
            createdDate = System.currentTimeMillis(),
            step = "STEP_1",
            request = "test request",
            response = "test response",
            updatedDate = System.currentTimeMillis(),
            createdBy =  UserProfile(1, "<EMAIL>", "test", "user", AccountStatus.ACTIVE, Role.ROLE_CORPORATE, null, null),
            updatedBy =  UserProfile(1, "<EMAIL>", "test", "user", AccountStatus.ACTIVE, Role.ROLE_CORPORATE, null, null)
        )

        every { 
            travelAssessmentService.listTravelAssessmentTracking(assessmentId)
        } returns listOf(trackingDetails)

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/travel-assessment/list-tracking/$assessmentId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload[0].id").value(trackingDetails.id))
    }

    @Test
    fun `get travel assessment by id`() {
        val assessmentId = 1L
        val assessmentDetails = mapOf(
            "id" to assessmentId,
            "status" to "PENDING",
            "createdDate" to System.currentTimeMillis()
        )

        every { 
            travelAssessmentService.get(assessmentId, authenticatedUser, null)
        } returns assessmentDetails

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/travel-assessment/$assessmentId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.id").value(assessmentId))
            .andExpect(jsonPath("$.payload.status").value("PENDING"))
    }

    @Test
    fun `delete travel assessment`() {
        val assessmentId = 1L

        every { 
            travelAssessmentService.delete(assessmentId, authenticatedUser)
        } returns true

        mockMvc.perform(
            delete("/api/${AppConstant.API_VERSION}/travel-assessment/$assessmentId")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `get travel assessment logs`() {
        val pagedResult = PagedResult<TravelAssessmentLogsResponse>(
            emptyList(),
            0L,
            0,
            0
        )

        every { authenticatedUser.partnerId } returns 1L

        every { 
            travelAssessmentService.logs(any(), any())
        } returns pagedResult

        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/travel-assessment/logs-listing")
                .param("destination", "UK")
                .param("from", Instant.now().minusSeconds(3600).toEpochMilli().toString())
                .param("to", Instant.now().toEpochMilli().toString())
                .param("corporate", "123")
                .param("pageIndex", "0")
                .param("pageSize", "20")
                .param("sort", "DESC")
                .param("sortBy", "createdDate")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.totalElements").value(0))
    }

    @Test
    fun `generate dossier should return dossier content`() {
        // Arrange
        val assessmentId = 1L
        val dossierContent = "Generated dossier content"

        every {
            travelAssessmentService.generateDossier(assessmentId, authenticatedUser)
        } returns dossierContent

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/travel-assessment/$assessmentId/dossier")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(dossierContent))
    }

    @Test
    fun `generate dossier should handle service errors`() {
        // Arrange
        val assessmentId = 1L

        every {
            travelAssessmentService.generateDossier(assessmentId, authenticatedUser)
        } throws ApplicationException(ErrorCode.NOT_FOUND)

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/travel-assessment/$assessmentId/dossier")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isNotFound)
            .andExpect(jsonPath("$.success").value(false))
    }
}