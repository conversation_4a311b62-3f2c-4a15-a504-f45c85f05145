package com.centuroglobal.controller

import com.centuroglobal.service.BlueprintService
import com.centuroglobal.service.UsageService
import com.centuroglobal.service.metrics.AccessLogService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.security.AuthenticatedUser
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.UUID

@WebMvcTest(controllers = [UsageController::class])
@AutoConfigureMockMvc(addFilters = false)
class UsageControllerTest(@Autowired val mockMvc: MockMvc) {

    @MockkBean
    private lateinit var usageService: UsageService

    @MockkBean
    private lateinit var blueprintService: BlueprintService

    @MockkBean
    lateinit var accessLogService: AccessLogService

    private val authenticatedUser: AuthenticatedUser = mockk()
    private val mapper = jacksonObjectMapper()

    @BeforeEach
    fun setup() {
        SecurityContextHolder.getContext().authentication = authenticatedUser
        every { authenticatedUser.principal } returns authenticatedUser
    }

    @Test
    fun `closeSession should return success response when session is closed`() {
        // Arrange
        val sessionId = "test-session-123"
        every { usageService.closeSession(sessionId, authenticatedUser) } returns true

        // Act & Assert
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/usage/$sessionId/close")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `closeSessionByType should return success response when feature session is closed`() {
        // Arrange
        val feature = "AI_CHAT"
        every { usageService.closeSessionByType(feature, any(), authenticatedUser) } returns true

        // Act & Assert
        mockMvc.perform(
            put("/api/${AppConstant.API_VERSION}/usage/close")
                .param("feature", feature)
                .param("sessionId", UUID.randomUUID().toString())
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload").value(true))
    }

    @Test
    fun `blueprint should return paged results with default parameters`() {
        // Arrange
        val pageIndex = 0
        val pageSize = 20
        
        every { 
            blueprintService.listUsage(any(), any()) 
        } returns PagedResult(emptyList(), 0, 0, 0)

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/usage/blueprint")
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows").isArray)
    }

    @Test
    fun `blueprint should handle optional parameters correctly`() {
        // Arrange
        val country = "US"
        val corporate = 1L
        val partnerId = 2L
        val from = 1234567890L
        val to = 1234567899L
        val isPartner = true

        every { 
            blueprintService.listUsage(any(), any()) 
        } returns PagedResult(emptyList(), 0, 0, 0)

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/usage/blueprint")
                .param("country", country)
                .param("corporate", corporate.toString())
                .param("partnerId", partnerId.toString())
                .param("from", from.toString())
                .param("to", to.toString())
                .param("isPartner", isPartner.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
    }

    @Test
    fun `aiChat should return paged results with default parameters`() {
        // Arrange
        val pageIndex = 0
        val pageSize = 20
        
        every { 
            usageService.aiChat(any(), any())
        } returns PagedResult(emptyList(), 0, 0, 0)

        // Act & Assert
        mockMvc.perform(
            get("/api/${AppConstant.API_VERSION}/usage/ai-chat")
                .param("pageIndex", pageIndex.toString())
                .param("pageSize", pageSize.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("utf-8")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.payload.rows").isArray)
    }
}