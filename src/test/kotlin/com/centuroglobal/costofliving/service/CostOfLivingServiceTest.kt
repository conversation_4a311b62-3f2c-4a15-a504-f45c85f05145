package com.centuroglobal.costofliving.service

import com.centuroglobal.costofliving.client.NumbeoClient
import com.centuroglobal.costofliving.data.constant.Constant
import com.centuroglobal.costofliving.data.entity.CityEntity
import com.centuroglobal.costofliving.data.entity.CityPriceEntity
import com.centuroglobal.costofliving.data.entity.ExchangeRateEntity
import com.centuroglobal.costofliving.data.entity.ItemEntity
import com.centuroglobal.costofliving.data.payload.ExchangeRate
import com.centuroglobal.costofliving.data.payload.ExchangeRateList
import com.centuroglobal.costofliving.repository.CityPriceRepository
import com.centuroglobal.costofliving.repository.CityRepository
import com.centuroglobal.costofliving.repository.ExchangeRatesRepository
import com.centuroglobal.costofliving.repository.ItemsRepository
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class CostOfLivingServiceTest {
    private val cityRepository: CityRepository = mockk()
    private val exchangeRatesRepository: ExchangeRatesRepository = mockk()
    private val cityPriceRepository: CityPriceRepository = mockk()
    private val itemsRepository: ItemsRepository = mockk()


    private val costOfLivingService = CostOfLivingService(
        cityRepository,
        exchangeRatesRepository,
        cityPriceRepository,
        itemsRepository,
        "api-key",
        10,
        10
    )

    private val cityEntity = CityEntity(
        1,
        "US",
        "America",
        "New York",
        1,
        0.0,
        0.0
    )

    private val itemEntity = ItemEntity(
        1,
        1,
        "itemName",
        "Category"
    )

    private val exchangeRateEntity = ExchangeRateEntity(
        "ABC",
        "ABC"
    )

    private val cityPriceEntity = CityPriceEntity(
        1,
        cityEntity.countryCode,
        cityEntity.country,
        cityEntity.city,
        cityEntity.cityId,
        "ABC",
        1,
        "ABC",
        0.0,
        0.0,
        0.0,
        1,
        1
    )

    private val exchangeRate = ExchangeRate()
    private val exchangeRateList = ExchangeRateList()
//    private val numbeoClient: NumbeoClient = mockk()

    private var numbeoClient = mockk<NumbeoClient>()

    @Test
    fun getCityListTest() {
        every { cityRepository.findAllByCountryCodeAndCreatedDateGreaterThan("US", any()) } returns listOf(cityEntity)
        every { cityRepository.deleteByCityId(1) }
        every { cityRepository.save(any()) }
        val result = costOfLivingService.getCityList("US")
        assertNotNull(result)
    }

//    @Test
//    fun updateExchangeRatesListTest() {
//        every { numbeoClient.findAllExchangeRates("api-key") } returns exchangeRateList
//        val result = costOfLivingService.updateExchangeRatesList()
//        assertNotNull(result)
//    }

    @Test
    fun getCityPriceTest() {
        every { itemsRepository.findAll() } returns mutableListOf()
        every { exchangeRatesRepository.findAllByCurrencyType(any()) } returns exchangeRateEntity
        every {
            cityPriceRepository.findAllByCityIdAndCountryCodeAndCreatedDateGreaterThan(
                any(),
                "US",
                any()
            )
        } returns listOf(cityPriceEntity)
        val result = costOfLivingService.getCityPrice("US", 1, Constant.CURRENCY_USD, "US", 2)
        assertNotNull(result)
    }

    @Test
    fun getCurrenciesTest() {
        every { exchangeRatesRepository.findAll() } returns mutableListOf(exchangeRateEntity)
        val result = costOfLivingService.getCurrencies()
        assertNotNull(result)
    }
}