package com.centuroglobal.facade

import com.centuroglobal.service.CircleService
import com.centuroglobal.shared.data.enums.CircleStatus
import com.centuroglobal.shared.data.enums.CircleType
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.circle.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.mock.web.MockMultipartFile
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

class AdminCircleFacadeTest {

    private lateinit var circleService: CircleService
    private lateinit var adminCircleFacade: AdminCircleFacade
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        circleService = mockk()
        adminCircleFacade = AdminCircleFacade(circleService)
        authenticatedUser = mockk()
        
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns "ADMIN"
    }

    @Test
    fun `test createCircle returns created circle`() {
        // Arrange
        val request = CreateUpdateCircleRequest(
            name = "Test Circle",
            about = "This is a test circle",
            circleAccessType = CircleType.PUBLIC,
            countryCode = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            status = CircleStatus.ACTIVE,
            members = listOf()
        )
        
        val createdCircle = Circle(
            id = 1,
            name = "Test Circle",
            about = "This is a test circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/banner.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 0,
            invitee = 0,
            request = 0,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf()
        )
        
        every { circleService.createCircle(request, authenticatedUser) } returns createdCircle
        
        // Act
        val result = adminCircleFacade.createCircle(request, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.id)
        assertEquals("Test Circle", result.name)
        assertEquals("This is a test circle", result.about)
        assertEquals(CircleStatus.ACTIVE, result.status)
        assertEquals(CircleType.PUBLIC, result.circleAccessType)
        assertEquals(listOf("US", "UK"), result.countryCodes)
        assertEquals(listOf(1, 2), result.expertiseIds)
        
        verify { circleService.createCircle(request, authenticatedUser) }
    }

    @Test
    fun `test updateCircle returns updated circle`() {
        // Arrange
        val circleId = 1L
        val request = CreateUpdateCircleRequest(
            name = "Updated Circle",
            about = "This is an updated circle",
            circleAccessType = CircleType.PRIVATE,
            countryCode = listOf("US", "CA"),
            expertiseIds = listOf(1, 3),
            status = CircleStatus.ACTIVE,
            members = listOf()
        )
        
        val updatedCircle = Circle(
            id = 1,
            name = "Updated Circle",
            about = "This is an updated circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/banner.jpg",
            circleAccessType = CircleType.PRIVATE,
            createdDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            countryCodes = listOf("US", "CA"),
            expertiseIds = listOf(1, 3),
            member = 2,
            invitee = 1,
            request = 0,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf()
        )
        
        every { circleService.updateCircle(circleId, request, authenticatedUser) } returns updatedCircle
        
        // Act
        val result = adminCircleFacade.updateCircle(circleId, request, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.id)
        assertEquals("Updated Circle", result.name)
        assertEquals("This is an updated circle", result.about)
        assertEquals(CircleStatus.ACTIVE, result.status)
        assertEquals(CircleType.PRIVATE, result.circleAccessType)
        assertEquals(listOf("US", "CA"), result.countryCodes)
        assertEquals(listOf(1, 3), result.expertiseIds)
        
        verify { circleService.updateCircle(circleId, request, authenticatedUser) }
    }

    @Test
    fun `test deleteCircle returns success`() {
        // Arrange
        val circleId = 1L
        val deleteResult = Unit
        
        every { circleService.deleteCircle(circleId, authenticatedUser) } returns Unit
        
        // Act
        val result = adminCircleFacade.deleteCircle(circleId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(deleteResult, result)
        verify { circleService.deleteCircle(circleId, authenticatedUser) }
    }

    @Test
    fun `test circleStatusUpdate returns success`() {
        // Arrange
        val circleId = 1L
        val circleStatus = CircleStatus.INACTIVE
        val updateResult = Unit
        
        every { circleService.circleStatusUpdate(circleId, circleStatus, authenticatedUser) } returns Unit
        
        // Act
        val result = adminCircleFacade.circleStatusUpdate(circleId, circleStatus, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(updateResult, result)
        verify { circleService.circleStatusUpdate(circleId, circleStatus, authenticatedUser) }
    }

    @Test
    fun `test approveMember returns success`() {
        // Arrange
        val approve = true
        val circleId = 1L
        val memberId = 2L
        val approveResult = Unit
        
        every { circleService.approveMember(approve, circleId, memberId, authenticatedUser) } returns Unit
        
        // Act
        val result = adminCircleFacade.approveMember(approve, circleId, memberId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(approveResult, result)
        verify { circleService.approveMember(approve, circleId, memberId, authenticatedUser) }
    }

    @Test
    fun `test listCircle returns circle listing`() {
        // Arrange
        val filter = CircleSearchFilter.Builder.build(
            status = CircleStatus.ACTIVE.toString(),
            countryCode = "US",
            expertiseId = "1",
            circleType = "",
            from = 3,
            to = 2,
            search = ""
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val circle = Circle(
            id = 1,
            name = "Test Circle",
            about = "This is a test circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/banner.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 5,
            invitee = 2,
            request = 1,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf()
        )
        
        val circleListing = CircleListing(
            data = PagedResult(
                rows = listOf(circle),
                totalElements = 1,
                currentPage = 0,
                totalPages = 1
            ),
            stats = CircleStats(
                active = 1,
                inactive = 0,
                circles = 2,
                public = 2,
                private = 1
            )
        )
        
        every { circleService.listCircle(filter, pageRequest, authenticatedUser) } returns circleListing
        
        // Act
        val result = adminCircleFacade.listCircle(filter, pageRequest, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.data.rows.size)
        assertEquals(1, result.data.rows[0].id)
        assertEquals("Test Circle", result.data.rows[0].name)
        assertEquals(CircleStatus.ACTIVE, result.data.rows[0].status)
        assertEquals(1, result.stats?.active)
        assertEquals(0, result.stats?.inactive)
        
        verify { circleService.listCircle(filter, pageRequest, authenticatedUser) }
    }

    @Test
    fun `test circleMemberSearch returns paged result`() {
        // Arrange
        val circleId = 1L
        val filter = ExpertSearchFilter.Builder.build(
            search = "John",
            countryCode = "US",
            expertiseId = "1"
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val expertProfile = ExpertProfileSummary(
            id = 2L,
            displayName = "John Doe",
            jobTitle = "Software Engineer",
            contactEmail = "<EMAIL>",
            contactNumber = "1234567890",
            contactWebsite = "johndoe.com",
            bio = "Experienced software engineer",
            profilePictureFullUrl = "https://example.com/profile.jpg",
            countryName = "United States",
            countryCode = "US",
            companyProfile = ExpertCompanyProfile(
                name = "Tech Company",
                aboutBusiness = "Technology solutions",
                companyAddress = "123 Tech St",
                companyNumber = "TC12345",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now().plusYears(1)),
                feesAmount = "1000",
                feesCurrency = "USD",
                membershipStatus = "Active",
                services = "Software development",
                specialTerms = "None",
                territory = "US",
                size = CompanySize.Size1,
                summary = "Leading tech company",
                renewContract = true,
                profileImage = "profile.jpg",
                associatedPartners = mutableListOf(),
                logoFullUrl = "https://example.com/logo.jpg",
                sizeName = "Small",
                partnerId = 1,
                contract = "Standard",
                aiMessageCount = 10
            ),
            infoVideoUrl = "https://example.com/video.mp4"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(expertProfile),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            circleService.circleMemberSearch(circleId, filter, pageRequest, authenticatedUser) 
        } returns pagedResult
        
        // Act
        val result = adminCircleFacade.circleMemberSearch(circleId, filter, pageRequest, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.rows.size)
        assertEquals(2L, result.rows[0].id)
        assertEquals("John Doe", result.rows[0].displayName)
        assertEquals("Software Engineer", result.rows[0].jobTitle)
        assertEquals("US", result.rows[0].countryCode)
        
        verify { circleService.circleMemberSearch(circleId, filter, pageRequest, authenticatedUser) }
    }

    @Test
    fun `test uploadCoverPicture returns upload response`() {
        // Arrange
        val circleId = 1L
        val photo = MockMultipartFile(
            "bannerImage",
            "banner.jpg",
            "image/jpeg",
            "test image content".toByteArray()
        )
        
        val uploadResponse = CircleBannerUploadResponse(
            bannerPhotoUrl = "https://example.com/banner.jpg",
            bannerPhotoKey =""
        )
        
        every { 
            circleService.uploadCoverPicture(
                any(), any(),
                any()
            )
        } returns uploadResponse
        
        // Act
        val result = adminCircleFacade.uploadCoverPicture(circleId, photo, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("https://example.com/banner.jpg", result.bannerPhotoUrl)
        
    }

    @Test
    fun `test circleRequestSearch returns paged result`() {
        // Arrange
        val circleId = 1L
        val filter = ExpertSearchFilter.Builder.build(
            search = "Jane",
            countryCode = "UK",
            expertiseId = ""
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val expertProfile = ExpertProfileSummary(
            id = 3L,
            displayName = "Jane Smith",
            jobTitle = "Product Manager",
            contactEmail = "<EMAIL>",
            contactNumber = "9876543210",
            contactWebsite = "janesmith.com",
            bio = "Experienced product manager",
            profilePictureFullUrl = "https://example.com/jane-profile.jpg",
            countryName = "United Kingdom",
            countryCode = "UK",
            companyProfile = ExpertCompanyProfile(
                name = "Product Co",
                aboutBusiness = "Product solutions",
                companyAddress = "456 Product St",
                companyNumber = "PC67890",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now().plusYears(1)),
                feesAmount = "2000",
                feesCurrency = "GBP",
                membershipStatus = "Active",
                services = "Product management",
                specialTerms = "None",
                territory = "UK",
                size = CompanySize.Size2,
                summary = "Leading product company",
                renewContract = true,
                profileImage = "jane-profile.jpg",
                associatedPartners = mutableListOf(),
                logoFullUrl = "https://example.com/product-logo.jpg",
                sizeName = "Medium",
                partnerId = 2,
                contract = "Premium",
                aiMessageCount = 15
            ),
            infoVideoUrl = "https://example.com/jane-video.mp4"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(expertProfile),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            circleService.circleRequestSearch(circleId, filter, pageRequest) 
        } returns pagedResult
        
        // Act
        val result = adminCircleFacade.circleRequestSearch(circleId, filter, pageRequest)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.rows.size)
        assertEquals(3L, result.rows[0].id)
        assertEquals("Jane Smith", result.rows[0].displayName)
        assertEquals("Product Manager", result.rows[0].jobTitle)
        assertEquals("UK", result.rows[0].countryCode)
        
        verify { circleService.circleRequestSearch(circleId, filter, pageRequest) }
    }

    @Test
    fun `test circleDetails returns circle details`() {
        // Arrange
        val circleId = 1L
        
        val circle = Circle(
            id = 1,
            name = "Test Circle",
            about = "This is a test circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/banner.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 5,
            invitee = 2,
            request = 1,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf()
        )
        
        every { 
            circleService.circleDetails(circleId, authenticatedUser) 
        } returns circle
        
        // Act
        val result = adminCircleFacade.circleDetails(circleId, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.id)
        assertEquals("Test Circle", result.name)
        assertEquals("This is a test circle", result.about)
        assertEquals(CircleStatus.ACTIVE, result.status)
        assertEquals(CircleType.PUBLIC, result.circleAccessType)
        assertEquals(listOf("US", "UK"), result.countryCodes)
        assertEquals(listOf(1, 2), result.expertiseIds)
        assertEquals(5, result.member)
        assertEquals(2, result.invitee)
        assertEquals(1, result.request)
        
        verify { circleService.circleDetails(circleId, authenticatedUser) }
    }
}