package com.centuroglobal.facade

import com.centuroglobal.service.ClientService
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.Client
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.client.ClientListing
import com.centuroglobal.shared.data.pojo.client.ClientSearchFilter
import com.centuroglobal.shared.data.pojo.client.ClientStats
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.util.concurrent.TimeUnit

class AdminClientFacadeTest {

    private lateinit var clientService: ClientService
    private lateinit var adminClientFacade: AdminClientFacade

    @BeforeEach
    fun setup() {
        clientService = mockk()
        adminClientFacade = AdminClientFacade(clientService)
    }

    @Test
    fun `test listClients returns client listing`() {
        // Arrange
        val filter = ClientSearchFilter.Builder.build(
            search = "test",
            status = AccountStatus.ACTIVE.toString(),
            from = 1234567890L,
            to = 1234567899L,
            userType = "",
            countryCodes = "",
            expertiseIds = "",
            corporateId = 4,
            partnerId = 2,
            expertCompanyId = 6
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val client = Client(
            id = 1,
            email = "<EMAIL>",
            fullName = "a",
            status = AccountStatus.ACTIVE,
            company = "",
            subscription = true,
            userType = UserType.CORPORATE,
            createdDate = 12345,
            referredBy = "",
            referral = 4,
            userId = 4,
            countryCode = "IN",
            expertiseIds = "",
            isLinkedin = true,
            country = "IN",
            expertise = "",
            subscriptionType = "",
            expertType = "",
            corporateType = "",
            secondaryUserCount = 3,
            bandName = "",
            lastLoginTime = 1234,
            lastTermsViewDate = 1234
        )
        
        val clientListing = ClientListing(
            data = PagedResult(
                rows = listOf(client),
                totalElements = 1,
                currentPage = 0,
                totalPages = 1
            ),
            stats = ClientStats(
                active = 1,
                users = 4,
                corporate = 5,
                expert = 1,
                subscribed = 1,
            )
        )
        
        every { clientService.listClients(filter, pageRequest) } returns clientListing
        
        // Act
        val result = adminClientFacade.listClients(filter, pageRequest).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.data.rows.size)
        assertEquals(1L, result.data.rows[0].id)
        assertEquals("<EMAIL>", result.data.rows[0].email)
        assertEquals("IN", result.data.rows[0].countryCode)
        assertEquals(AccountStatus.ACTIVE, result.data.rows[0].status)
        assertEquals(1, result.stats?.active)

        verify { clientService.listClients(filter, pageRequest) }
    }

    @Test
    fun `test listClientsReferredBy returns client listing`() {
        // Arrange
        val referredById = 2L
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val client = Client(
            id = 1,
            email = "<EMAIL>",
            fullName = "a",
            status = AccountStatus.ACTIVE,
            company = "",
            subscription = true,
            userType = UserType.CORPORATE,
            createdDate = 12345,
            referredBy = "",
            referral = 4,
            userId = 4,
            countryCode = "IN",
            expertiseIds = "",
            isLinkedin = true,
            country = "IN",
            expertise = "",
            subscriptionType = "",
            expertType = "",
            corporateType = "",
            secondaryUserCount = 3,
            bandName = "",
            lastLoginTime = 1234,
            lastTermsViewDate = 1234
        )
        
        val clientListing = ClientListing(
            data = PagedResult(
                rows = listOf(client),
                totalElements = 1,
                currentPage = 0,
                totalPages = 1
            ),
            stats = ClientStats(
                active = 1,
                users = 4,
                corporate = 5,
                expert = 1,
                subscribed = 1,
            )
        )
        
        every { clientService.listClientsReferredBy(referredById, pageRequest) } returns clientListing
        
        // Act
        val result = adminClientFacade.listClientsReferredBy(referredById, pageRequest).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.data.rows.size)
        assertEquals(1L, result.data.rows[0].id)
        assertEquals("<EMAIL>", result.data.rows[0].email)
        assertEquals("IN", result.data.rows[0].countryCode)
        assertEquals(AccountStatus.ACTIVE, result.data.rows[0].status)
        assertEquals(1, result.stats?.active)

        verify { clientService.listClientsReferredBy(referredById, pageRequest) }
    }

    @Test
    fun `test resendVerificationCode returns success`() {
        // Arrange
        val userId = 1L
        
        every { clientService.resendVerificationCode(userId) } returns Unit
        
        // Act
        val result = adminClientFacade.resendVerificationCode(userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        // Since the service method returns Unit, we just verify it was called
        verify { clientService.resendVerificationCode(userId) }
    }

    @Test
    fun `test updateUserStatus returns success`() {
        // Arrange
        val userId = 1L
        val accountStatus = AccountStatus.ACTIVE
        
        every { clientService.updateUserStatus(userId, accountStatus) } returns Unit
        
        // Act
        val result = adminClientFacade.updateUserStatus(userId, accountStatus).get(5, TimeUnit.SECONDS)
        
        // Assert
        // Since the service method returns Unit, we just verify it was called
        verify { clientService.updateUserStatus(userId, accountStatus) }
    }

    @Test
    fun `test exportListClients returns client listing`() {
        // Arrange
        val filter = ClientSearchFilter.Builder.build(
            search = "test",
            status = AccountStatus.ACTIVE.toString(),
            from = 1234567890L,
            to = 1234567899L,
            userType = "",
            countryCodes = "",
            expertiseIds = "",
            corporateId = 4,
            partnerId = 2,
            expertCompanyId = 6
        )
        
        val client1 = Client(
            id = 1,
            email = "<EMAIL>",
            fullName = "a",
            status = AccountStatus.ACTIVE,
            company = "",
            subscription = true,
            userType = UserType.CORPORATE,
            createdDate = 12345,
            referredBy = "",
            referral = 4,
            userId = 4,
            countryCode = "IN",
            expertiseIds = "",
            isLinkedin = true,
            country = "IN",
            expertise = "",
            subscriptionType = "",
            expertType = "",
            corporateType = "",
            secondaryUserCount = 3,
            bandName = "",
            lastLoginTime = 1234,
            lastTermsViewDate = 1234
        )
        
        val client2 = Client(
            id = 2,
            email = "<EMAIL>",
            fullName = "a",
            status = AccountStatus.ACTIVE,
            company = "",
            subscription = true,
            userType = UserType.CORPORATE,
            createdDate = 12345,
            referredBy = "",
            referral = 4,
            userId = 4,
            countryCode = "IN",
            expertiseIds = "",
            isLinkedin = true,
            country = "IN",
            expertise = "",
            subscriptionType = "",
            expertType = "",
            corporateType = "",
            secondaryUserCount = 3,
            bandName = "",
            lastLoginTime = 1234,
            lastTermsViewDate = 1234
        )
        
        val clientListing = ClientListing(
            data = PagedResult(
                rows = listOf(client1, client2),
                totalElements = 2,
                currentPage = 0,
                totalPages = 1
            ),
            stats = ClientStats(
                active = 1,
                users = 4,
                corporate = 5,
                expert = 1,
                subscribed = 1,
            )
        )
        
        every { clientService.exportClientList(filter) } returns clientListing
        
        // Act
        val result = adminClientFacade.exportListClients(filter).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.data.rows.size)
        assertEquals(1L, result.data.rows[0].id)
        assertEquals(2L, result.data.rows[1].id)
        assertEquals(1, result.stats?.active)

        verify { clientService.exportClientList(filter) }
    }
}