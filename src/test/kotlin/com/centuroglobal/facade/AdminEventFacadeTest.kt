package com.centuroglobal.facade

import com.centuroglobal.data.payload.event.CreateEventRequest
import com.centuroglobal.data.payload.event.CreateSpeakersResponse
import com.centuroglobal.data.payload.event.EditEventInviteesResponse
import com.centuroglobal.data.payload.event.EventSpeakerImageUpdateResponse
import com.centuroglobal.service.EventService
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.event.CreateSessionRequest
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.circle.CircleBannerUploadResponse
import com.centuroglobal.shared.data.pojo.event.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.Pageable
import org.springframework.mock.web.MockMultipartFile
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.TimeUnit

class AdminEventFacadeTest {

    private lateinit var eventService: EventService
    private lateinit var adminEventFacade: AdminEventFacade
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        eventService = mockk()
        adminEventFacade = AdminEventFacade(eventService)
        authenticatedUser = mockk()
        
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns "ADMIN"
    }

    @Test
    fun `test createEvent returns event details`() {
        // Arrange
        val request = CreateEventRequest(
            name = "Test Event",
            about = "This is a test event",
            timeZone = "UTC",
            status = EventStatus.PUBLISHED,
            location = "Online",
            id = 33,
            locationDetails = "",
            startDate = Date(),
            endDate = Date(),
            startTime = Date(),
            endTime = Date(),
        )
        
        val eventDetails = EventDetails(
            id = 5,
            name = "abc",
            about = "abc",
            bannerImageFullUrl = "",
            startDate = 1234,
            endDate = 1234,
            startTime = Date(),
            endTime = Date(),
            duration = "3",
            timeZone = "TZ" ,
            status = EventStatus.PUBLISHED,
            location = "",
            inviteesExternal = "",
            locationDetails = "",
            sessions = 4,
            speakers = 4,
            invitees = 3,
            attending = 2,
            rsvpFlag = true,
            joinFlag = false,
            attendingFlag = false,
            attendedFlag = false,
            isInvitee = true
        )
        
        every { eventService.createEvent(request, authenticatedUser) } returns eventDetails
        
        // Act
        val result = adminEventFacade.createEvent(request, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(5L, result.id)
        assertEquals("abc", result.name)

        verify { eventService.createEvent(request, authenticatedUser) }
    }

    @Test
    fun `test eventStatusUpdate returns success`() {
        // Arrange
        val eventId = 1L
        val status = EventStatus.PUBLISHED
        
        every { eventService.changeStatus(eventId, status, authenticatedUser) } returns Unit
        
        // Act
        val result = adminEventFacade.eventStatusUpdate(eventId, status, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(Unit, result)
        
        verify { eventService.changeStatus(eventId, status, authenticatedUser) }
    }

    @Test
    fun `test uploadCoverPicture returns upload response`() {
        // Arrange
        val eventId = 1L
        val photo = MockMultipartFile(
            "bannerImage",
            "banner.jpg",
            "image/jpeg",
            "test image content".toByteArray()
        )
        
        val uploadResponse = CircleBannerUploadResponse(
            bannerPhotoUrl = "https://example.com/banner.jpg",
            bannerPhotoKey = "key"
        )
        
        every { 
            eventService.uploadCoverPicture(any(), any(), any())
        } returns uploadResponse
        
        // Act
        val result = adminEventFacade.uploadCoverPicture(eventId, photo, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("key", result.bannerPhotoKey)
        assertEquals("https://example.com/banner.jpg", result.bannerPhotoUrl)
        
    }

    @Test
    fun `test addUpdateSpeakers returns speakers response`() {
        // Arrange
        val eventId = 1L
        val speakers = listOf(
            CreateEventSpeaker(id = 2L, type = EventSpeakerType.INTERNAL, profile = mockk(relaxed = true),
                internalMemberId = 5,
                internalMemberRole = Role.ROLE_USER,
                isHost = false)
        )
        
        val eventSpeakers = listOf(
            EventSpeaker(
                id = 4,
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_ADMIN,
                internalMemberId = 2,
                isHost = true,
                profile = null,
                expertProfile = null
            )
        )
        
        val pagedResult = PagedResult(
            rows = eventSpeakers,
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        val speakersResponse = CreateSpeakersResponse(
            speakers = eventSpeakers,
            id = 4
        )
        
        every { eventService.addUpdateSpeaker(eventId, speakers, authenticatedUser) } returns Unit
        every { eventService.speakerListing(eventId, Pageable.unpaged(), authenticatedUser) } returns pagedResult
        
        // Act
        val result = adminEventFacade.addUpdateSpeakers(eventId, speakers, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.speakers.size)
        
        verify { 
            eventService.addUpdateSpeaker(eventId, speakers, authenticatedUser)
            eventService.speakerListing(eventId, Pageable.unpaged(), authenticatedUser)
        }
    }

    @Test
    fun `test addUpdateSessions returns sessions`() {
        // Arrange
        val eventId = 1L
        val sessions = listOf(
            CreateSessionRequest(
                id = null, name = "Session 1", speakers = mutableListOf(1L, 2L),
                about = "",
                date = Date(),
                startTime = Date(),
                endTime = Date(),
                duration = "5",
                timeZone = ""
            )
        )
        
        val eventSessions = listOf(
            EventSession(
                id = 1L,
                name = "Session 1",
                about = "Session description",
                date = 1634567890000,
                timeZone = "UTC",
                startTime = Date(),
                endTime = Date(),
                duration = "1 hour",
                speakers = listOf()
            )
        )
        
        val pagedResult = PagedResult(
            rows = eventSessions,
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { eventService.addUpdateSession(eventId, sessions, authenticatedUser) } returns Unit
        every { eventService.getSessions(eventId, Pageable.unpaged(), authenticatedUser) } returns pagedResult
        
        // Act
        val result = adminEventFacade.addUpdateSessions(eventId, sessions, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.rows.size)
        assertEquals("Session 1", result.rows[0].name)
        assertEquals("Session description", result.rows[0].about)
        assertEquals(1634567890000, result.rows[0].date)
        assertEquals("UTC", result.rows[0].timeZone)
        
        verify { 
            eventService.addUpdateSession(eventId, sessions, authenticatedUser)
            eventService.getSessions(eventId, Pageable.unpaged(), authenticatedUser)
        }
    }

    @Test
    fun `test addUpdateInvitees returns invitees`() {
        // Arrange
        val eventId = 1L
        val external = "<EMAIL>, <EMAIL>"
        val internal = listOf(2L, 3L)
        
        val invitees = listOf(
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_EXPERT,
                internalMemberId = 2,
                isHost = true,
                profile = ClientView(
                    2,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.EXPERT,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            ),
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_EXPERT,
                internalMemberId = 3,
                isHost = true,
                profile = ClientView(
                    3,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.EXPERT,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            ),
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_EXPERT,
                internalMemberId = 4,
                isHost = true,
                profile = ClientView(
                    4,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.EXPERT,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            ),
            EventInvitee(
                type = EventSpeakerType.EXTERNAL,
                internalMemberRole = Role.ROLE_EXPERT,
                internalMemberId = 5,
                isHost = true,
                profile = ClientView(
                    5,
                    "<EMAIL>",
                    "fName",
                    AccountStatus.PENDING_VERIFICATION,
                    "cName",
                    UserType.EXPERT,
                    true,
                    LocalDateTime.now(),
                    null,
                    null,
                    1,
                    1,
                    null,
                    null,
                    null,
                    false,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
                )
            )
        )
        
        every { 
            eventService.createInvitees(eventId, external, internal, authenticatedUser) 
        } returns invitees
        
        // Act
        val result = adminEventFacade.addUpdateInvitees(eventId, external, internal, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(4, result.size)
        
        verify { eventService.createInvitees(eventId, external, internal, authenticatedUser) }
    }

    @Test
    fun `test uploadProfilePictureForExternalSpeakers returns image update responses`() {
        // Arrange
        val eventId = 1L
        val speakerIds = listOf("1", "2")
        val photos = listOf(
            MockMultipartFile(
                "photo1",
                "speaker1.jpg",
                "image/jpeg",
                "test image content 1".toByteArray()
            ),
            MockMultipartFile(
                "photo2",
                "speaker2.jpg",
                "image/jpeg",
                "test image content 2".toByteArray()
            )
        )
        
        val imageUpdateResponses = listOf(
            EventSpeakerImageUpdateResponse(
                internalId = "1",
                profilePictureFullUrl = "a.com"
            ),
            EventSpeakerImageUpdateResponse(
                internalId = "2",
                profilePictureFullUrl = "b.com"
            )
        )
        
        every { 
            eventService.uploadProfilePictureForExternalSpeakers(eventId, speakerIds, authenticatedUser, photos) 
        } returns imageUpdateResponses
        
        // Act
        val result = adminEventFacade.uploadProfilePictureForExternalSpeakers(
            eventId, speakerIds, photos, authenticatedUser
        ).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)

        verify { eventService.uploadProfilePictureForExternalSpeakers(eventId, speakerIds, authenticatedUser, photos) }
    }

    @Test
    fun `test deleteEvent returns success message`() {
        // Arrange
        val eventId = 1L
        val successMessage = "Event deleted successfully"
        
        every { eventService.deleteEvent(eventId, authenticatedUser) } returns successMessage
        
        // Act
        val result = adminEventFacade.deleteEvent(eventId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successMessage, result)
        
        verify { eventService.deleteEvent(eventId, authenticatedUser) }
    }

    @Test
    fun `test getInviteesToEdit returns invitees response`() {
        // Arrange
        val eventId = 1L
        
        val inviteesResponse = EditEventInviteesResponse(
            external = "<EMAIL>, <EMAIL>",
            internal = listOf(),
        )
        
        every { eventService.getInviteesToEdit(eventId, authenticatedUser) } returns inviteesResponse
        
        // Act
        val result = adminEventFacade.getInviteesToEdit(eventId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("<EMAIL>, <EMAIL>", result.external)

        verify { eventService.getInviteesToEdit(eventId, authenticatedUser) }
    }

    @Test
    fun `test deleteBannerPicture returns success message`() {
        // Arrange
        val eventId = 1L
        val successMessage = "Banner picture deleted successfully"
        
        every { eventService.deleteBannerPicture(eventId, authenticatedUser) } returns successMessage
        
        // Act
        val result = adminEventFacade.deleteBannerPicture(eventId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successMessage, result)
        
        verify { eventService.deleteBannerPicture(eventId, authenticatedUser) }
    }
}