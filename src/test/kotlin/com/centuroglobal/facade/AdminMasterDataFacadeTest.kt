package com.centuroglobal.facade

import com.centuroglobal.service.AdminMasterDataService
import com.centuroglobal.shared.data.enums.stripe.TemplateType
import io.mockk.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayOutputStream


class AdminMasterDataFacadeTest {

    private lateinit var adminMasterDataService: AdminMasterDataService
    private lateinit var adminMasterDataFacade: AdminMasterDataFacade

    @BeforeEach
    fun setup() {
        adminMasterDataService = mockk()
        adminMasterDataFacade = AdminMasterDataFacade(adminMasterDataService)
    }

    private fun createExcelBytes(): ByteArray {
        val workbook = XSSFWorkbook()
        workbook.createSheet("Sheet1")
        val byteArrayOutputStream = ByteArrayOutputStream()
        workbook.write(byteArrayOutputStream)
        workbook.close()
        return byteArrayOutputStream.toByteArray()
    }

    @Test
    fun `test uploadTemplate for CORPORATE_TAX template type`() {
        // Arrange
        val templateType = TemplateType.CORPORATE_TAX.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test1.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setTaxData(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setTaxData(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for EMPLOYEE_TAX template type`() {
        // Arrange
        val templateType = TemplateType.EMPLOYEE_TAX.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test2.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setTaxData(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setTaxData(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for EMPLOYER_TAX template type`() {
        // Arrange
        val templateType = TemplateType.EMPLOYER_TAX.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test3.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setTaxData(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setTaxData(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for GLOBAL_RANKING template type`() {
        // Arrange
        val templateType = TemplateType.GLOBAL_RANKING.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test4.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setGlobalRankingData(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setGlobalRankingData(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for COUNTRY_HIGHLIGHTS template type`() {
        // Arrange
        val templateType = TemplateType.COUNTRY_HIGHLIGHTS.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test5.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setCountryHighlightsData(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setCountryHighlightsData(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for IMMIGRATION_REQUIREMENT template type`() {
        // Arrange
        val templateType = TemplateType.IMMIGRATION_REQUIREMENT.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test6.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setImmigrationRequirement(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.ACCEPTED, result)
        verify { adminMasterDataService.setImmigrationRequirement(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for ENTITY_TYPE template type`() {
        // Arrange
        val templateType = TemplateType.ENTITY_TYPE.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test7.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setEntityType(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setEntityType(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for CASE_STATUS_MILESTONE template type`() {
        // Arrange
        val templateType = TemplateType.CASE_STATUS_MILESTONE.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test8.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setMilestoneStatusData(any(), templateType, userId, displayName, multipartFile)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setMilestoneStatusData(any(), templateType, userId, displayName, multipartFile) }
    }

    @Test
    fun `test uploadTemplate for SUBSCRIPTION_PLAN template type`() {
        // Arrange
        val templateType = TemplateType.SUBSCRIPTION_PLAN.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with Excel content
        val fileBytes = createExcelBytes()
        val multipartFile = MockMultipartFile(
            "file",
            "test9.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileBytes
        )

        // Mock the service method
        every {
            adminMasterDataService.setSubscriptionData(any(), userId, displayName)
        } just Runs

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.OK, result)
        verify { adminMasterDataService.setSubscriptionData(any(), userId, displayName) }
    }

    @Test
    fun `test uploadTemplate handles IOException`() {
        // Arrange
        val templateType = TemplateType.CORPORATE_TAX.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile with invalid content
        val multipartFile = MockMultipartFile(
            "file",
            "test10.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "invalid content".toByteArray()
        )

        // Act
        val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)

        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result)
    }

    @Test
    fun `test uploadTemplate handles general Exception`() {
        // Arrange
        val templateType = TemplateType.CORPORATE_TAX.name
        val userId = 1L
        val displayName = "Test User"

        // Create a mock MultipartFile that will cause an exception
        val multipartFile: MultipartFile = mockk()
        every { multipartFile.bytes } throws RuntimeException("Test exception")

        try {// Act
            val result = adminMasterDataFacade.uploadTemplate(templateType, multipartFile, userId, displayName)
        }catch (ex: Exception){
            assertEquals("Test exception", (ex as RuntimeException).message)
        }
    }
}