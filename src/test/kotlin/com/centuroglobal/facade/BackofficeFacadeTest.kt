package com.centuroglobal.facade

import com.centuroglobal.service.BackofficeUserService
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.account.CreateAdminUserRequest
import com.centuroglobal.shared.data.payload.account.UpdateAdminUserRequest
import com.centuroglobal.shared.data.pojo.AdminAuthorities
import com.centuroglobal.shared.data.pojo.AdminUserSearchFilter
import com.centuroglobal.shared.data.pojo.BackofficeUser
import com.centuroglobal.shared.data.pojo.BackofficeUserList
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class BackofficeFacadeTest {

    private lateinit var backofficeUserService: BackofficeUserService
    private lateinit var backofficeFacade: BackofficeFacade

    @BeforeEach
    fun setup() {
        backofficeUserService = mockk()
        backofficeFacade = BackofficeFacade(backofficeUserService)
    }

    @Test
    fun `test retrieveBackofficeUsers returns list of users`() {
        // Arrange
        val searchFilter = AdminUserSearchFilter(
            role = Role.ROLE_ADMIN,
            status = AccountStatus.ACTIVE,
            responsibility = "USERS"
        )
        
        val backofficeUserList = listOf(
            BackofficeUserList(
                id = 1L,
                firstName = "John",
                lastName = "Doe",
                email = "<EMAIL>",
                role = Role.ROLE_ADMIN,
                status = AccountStatus.ACTIVE,
                createdDate = 1234,
                read = listOf(),
                readNWrite = listOf(),
            ),
            BackofficeUserList(
                id = 2L,
                firstName = "Jane",
                lastName = "Smith",
                email = "<EMAIL>",
                role = Role.ROLE_SUPER_ADMIN,
                status = AccountStatus.ACTIVE,
                createdDate = 12,
                read = listOf(),
                readNWrite = listOf()
            )
        )
        
        every { backofficeUserService.retrieveBackofficeUsers(searchFilter) } returns backofficeUserList
        
        // Act
        val result = backofficeFacade.retrieveBackofficeUsers(searchFilter).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(1L, result[0].id)
        assertEquals("John", result[0].firstName)
        assertEquals("Doe", result[0].lastName)
        assertEquals("<EMAIL>", result[0].email)
        assertEquals(Role.ROLE_ADMIN, result[0].role)
        
        verify { backofficeUserService.retrieveBackofficeUsers(searchFilter) }
    }

    @Test
    fun `test retrieveBackofficeUser returns user details`() {
        // Arrange
        val userId = 1L
        
        val backofficeUser = BackofficeUser(
            id = userId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            role = Role.ROLE_ADMIN,
            status = AccountStatus.ACTIVE,
            createdDate = 454,
            adminAuthorities = listOf(),
            profilePhotoUrl = "",
            aiMessageCount = 3
        )
        
        every { backofficeUserService.retrieveBackofficeUser(userId) } returns backofficeUser
        
        // Act
        val result = backofficeFacade.retrieveBackofficeUser(userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(userId, result.id)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("<EMAIL>", result.email)
        assertEquals(Role.ROLE_ADMIN, result.role)

        verify { backofficeUserService.retrieveBackofficeUser(userId) }
    }

    @Test
    fun `test updateBackofficeUser updates and returns user details`() {
        // Arrange
        val userId = 1L
        val requesterUserId = 2L
        
        val updateRequest = UpdateAdminUserRequest(
            "John",
            "Doe",
            AccountStatus.ACTIVE.name,
            Role.ROLE_SUPER_ADMIN.name,
            listOf(
                AdminAuthorities("CASE", true, "READ"),
                AdminAuthorities("CASE", true, "WRITE")
            ),
            aiMessageCount = 1234
        )
        
        val updatedUser = BackofficeUser(
            id = userId,
            firstName = "John",
            lastName = "Updated",
            email = "<EMAIL>",
            role = Role.ROLE_ADMIN,
            status = AccountStatus.ACTIVE,
            createdDate = 123,
            adminAuthorities = listOf(),
            profilePhotoUrl = "",
            aiMessageCount = 34
        )
        
        every { 
            backofficeUserService.updateBackofficeUser(userId, requesterUserId, updateRequest) 
        } returns updatedUser
        
        // Act
        val result = backofficeFacade.updateBackofficeUser(userId, requesterUserId, updateRequest)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(userId, result.id)
        assertEquals("John", result.firstName)
        assertEquals("Updated", result.lastName)
        assertEquals("<EMAIL>", result.email)
        assertEquals(Role.ROLE_ADMIN, result.role)

        verify { backofficeUserService.updateBackofficeUser(userId, requesterUserId, updateRequest) }
    }

    @Test
    fun `test createBackofficeUser creates and returns user details`() {
        // Arrange
        val requesterUserId = 2L
        
        val createRequest = CreateAdminUserRequest(
            email = "<EMAIL>",
            firstName = "New",
            lastName = "User",
            role = Role.ROLE_ADMIN.name,
            adminAuthorities = listOf(),
            aiMessageCount = 4
        )
        
        val createdUser = BackofficeUser(
            id = 3L,
            firstName = "New",
            lastName = "User",
            email = "<EMAIL>",
            role = Role.ROLE_ADMIN,
            status = AccountStatus.ACTIVE,
            createdDate = 32,
            adminAuthorities = listOf(),
            profilePhotoUrl = "",
            aiMessageCount = 33
        )
        
        every { 
            backofficeUserService.createBackofficeUser(requesterUserId, createRequest) 
        } returns createdUser
        
        // Act
        val result = backofficeFacade.createBackofficeUser(requesterUserId, createRequest)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3L, result.id)
        assertEquals("New", result.firstName)
        assertEquals("User", result.lastName)
        assertEquals("<EMAIL>", result.email)
        assertEquals(Role.ROLE_ADMIN, result.role)

        verify { backofficeUserService.createBackofficeUser(requesterUserId, createRequest) }
    }

    @Test
    fun `test resend returns success message`() {
        // Arrange
        val userId = 1L
        val successMessage = "Invitation resent successfully"
        
        every { backofficeUserService.resend(userId) } returns successMessage
        
        // Act
        val result = backofficeFacade.resend(userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successMessage, result)
        
        verify { backofficeUserService.resend(userId) }
    }
}