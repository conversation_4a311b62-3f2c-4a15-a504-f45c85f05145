package com.centuroglobal.facade

import com.centuroglobal.service.BlueprintPdfService
import com.centuroglobal.service.BlueprintService
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.pojo.Blueprint
import com.centuroglobal.shared.data.pojo.CountrySummary
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintStep
import com.centuroglobal.shared.exception.ApplicationException
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.OutputStream
import java.util.concurrent.TimeUnit

class BlueprintFacadeTest {

    private lateinit var blueprintService: BlueprintService
    private lateinit var blueprintPdfService: BlueprintPdfService
    private lateinit var blueprintFacade: BlueprintFacade

    @BeforeEach
    fun setup() {
        blueprintService = mockk()
        blueprintPdfService = mockk()
        blueprintFacade = BlueprintFacade(blueprintService, blueprintPdfService)
    }

    @Test
    fun `test listActive returns list of country summaries`() {
        // Arrange
        val countrySummaries = listOf(
            CountrySummary("US", "United States"),
            CountrySummary("UK", "United Kingdom"),
            CountrySummary("CA", "Canada")
        )
        
        every { blueprintService.listActive() } returns countrySummaries
        
        // Act
        val result = blueprintFacade.listActive().get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        assertEquals("US", result[0].code)
        assertEquals("United States", result[0].name)
        assertEquals("UK", result[1].code)
        assertEquals("United Kingdom", result[1].name)
        assertEquals("CA", result[2].code)
        assertEquals("Canada", result[2].name)
        
        verify { blueprintService.listActive() }
    }

    @Test
    fun `test retrieveBluePrint returns blueprint for country`() {
        // Arrange
        val countryCode = "US"
        val sessionId = "session123"
        
        val blueprint = Blueprint(
            countryCode = countryCode,
            countryName = "United States",
            status = mockk(),
            lastPublishedDate = 1234567890,
            steps = listOf(),
            sessionId = sessionId
        )
        
        every { blueprintService.retrieveBluePrint(countryCode, false, sessionId) } returns blueprint
        
        // Act
        val result = blueprintFacade.retrieveBluePrint(countryCode, sessionId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(countryCode, result.countryCode)
        assertEquals("United States", result.countryName)
        assertEquals(1234567890, result.lastPublishedDate)
        assertEquals(sessionId, result.sessionId)
        
        verify { blueprintService.retrieveBluePrint(countryCode, false, sessionId) }
    }

    @Test
    fun `test retrieveStep returns blueprint step for country and step name`() {
        // Arrange
        val countryCode = "US"
        val stepName = StepName.STEP_1
        
        val blueprintStep = BlueprintStep(
            stepName = stepName,
            content = ""
        )
        
        every { blueprintService.retrieveStep(countryCode, stepName) } returns blueprintStep
        
        // Act
        val result = blueprintFacade.retrieveStep(countryCode, stepName).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(stepName, result.stepName)

        verify { blueprintService.retrieveStep(countryCode, stepName) }
    }

    @Test
    fun `test retrieveTeaser returns blueprint teaser for country`() {
        // Arrange
        val countryCode = "US"
        
        val blueprint = Blueprint(
            countryCode = countryCode,
            countryName = "United States",
            status = mockk(),
            lastPublishedDate = 1234567890,
            steps = listOf(),
            sessionId = "teaser-session"
        )
        
        every { blueprintService.retrieveTeaser(countryCode) } returns blueprint
        
        // Act
        val result = blueprintFacade.retrieveTeaser(countryCode).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(countryCode, result.countryCode)
        assertEquals("United States", result.countryName)
        assertEquals(1234567890, result.lastPublishedDate)
        assertEquals("teaser-session", result.sessionId)
        
        verify { blueprintService.retrieveTeaser(countryCode) }
    }

    @Test
    fun `test downloadPdf returns streaming response when pdf exists`() {
        // Arrange
        val countryCode = "US"
        val stepName = StepName.STEP_1
        
        every { blueprintPdfService.validateBlueprintPdf(countryCode, stepName) } just Runs
        every { blueprintPdfService.downloadBlueprint(countryCode, stepName, any()) } answers {
            val outputStream = arg<OutputStream>(2)
            outputStream.write("PDF content".toByteArray())
        }
        
        // Act
        val result = blueprintFacade.downloadPdf(countryCode, stepName)
        
        // Assert
        assertEquals(HttpStatus.OK, result.statusCode)
    }

    @Test
    fun `test downloadPdf returns error response when pdf validation fails`() {
        // Arrange
        val countryCode = "US"
        val stepName = StepName.STEP_1
        val errorMessage = "PDF not found"
        
        val applicationException = ApplicationException(ErrorCode.BLUEPRINT_PDF_NOT_FOUND)
        
        every { 
            blueprintPdfService.validateBlueprintPdf(countryCode, stepName) 
        } throws applicationException
        
        // Act
        val result = blueprintFacade.downloadPdf(countryCode, stepName)
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, result.statusCode)
        
        // Verify the response body contains the error message
        val streamingResponseBody = result.body as StreamingResponseBody
        val outputStream = mockk<OutputStream>(relaxed = true)
        streamingResponseBody.writeTo(outputStream)
        
    }
}