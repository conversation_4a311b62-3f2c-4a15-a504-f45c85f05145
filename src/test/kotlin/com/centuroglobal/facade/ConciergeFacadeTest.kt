package com.centuroglobal.facade

import com.centuroglobal.service.ConciergeService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.payload.ConciergeRequest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit

class ConciergeFacadeTest {

    private lateinit var conciergeService: ConciergeService
    private lateinit var conciergeFacade: ConciergeFacade

    @BeforeEach
    fun setup() {
        conciergeService = mockk()
        conciergeFacade = ConciergeFacade(conciergeService)
    }

    @Test
    fun `test post creates notification and returns success message`() {
        // Arrange
        val userId = 1L
        val query = "How can I get help with my visa application?"
        val request = ConciergeRequest(query)
        val successResponse = AppConstant.SUCCESS_RESPONSE_STRING
        
        every { conciergeService.createNotification(request, userId) } returns successResponse
        
        // Act
        val result = conciergeFacade.post(request, userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successResponse, result)
        verify { conciergeService.createNotification(request, userId) }
    }

    @Test
    fun `test post with empty query returns success message`() {
        // Arrange
        val userId = 1L
        val query = ""
        val request = ConciergeRequest(query)
        val successResponse = AppConstant.SUCCESS_RESPONSE_STRING
        
        every { conciergeService.createNotification(request, userId) } returns successResponse
        
        // Act
        val result = conciergeFacade.post(request, userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successResponse, result)
        verify { conciergeService.createNotification(request, userId) }
    }

    @Test
    fun `test post with long query returns success message`() {
        // Arrange
        val userId = 1L
        val query = "a".repeat(1000)
        val request = ConciergeRequest(query)
        val successResponse = AppConstant.SUCCESS_RESPONSE_STRING
        
        every { conciergeService.createNotification(request, userId) } returns successResponse
        
        // Act
        val result = conciergeFacade.post(request, userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successResponse, result)
        verify { conciergeService.createNotification(request, userId) }
    }

    @Test
    fun `test post with special characters in query returns success message`() {
        // Arrange
        val userId = 1L
        val query = "How can I get help with my visa application? I need assistance ASAP! #urgent @support"
        val request = ConciergeRequest(query)
        val successResponse = AppConstant.SUCCESS_RESPONSE_STRING
        
        every { conciergeService.createNotification(request, userId) } returns successResponse
        
        // Act
        val result = conciergeFacade.post(request, userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successResponse, result)
        verify { conciergeService.createNotification(request, userId) }
    }

    @Test
    fun `test post with different user ID returns success message`() {
        // Arrange
        val userId = 999L
        val query = "How can I get help with my visa application?"
        val request = ConciergeRequest(query)
        val successResponse = AppConstant.SUCCESS_RESPONSE_STRING
        
        every { conciergeService.createNotification(request, userId) } returns successResponse
        
        // Act
        val result = conciergeFacade.post(request, userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successResponse, result)
        verify { conciergeService.createNotification(request, userId) }
    }

    @Test
    fun `test post with custom success message returns that message`() {
        // Arrange
        val userId = 1L
        val query = "How can I get help with my visa application?"
        val request = ConciergeRequest(query)
        val customSuccessResponse = "Your concierge request has been submitted successfully"
        
        every { conciergeService.createNotification(request, userId) } returns customSuccessResponse
        
        // Act
        val result = conciergeFacade.post(request, userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(customSuccessResponse, result)
        verify { conciergeService.createNotification(request, userId) }
    }
}