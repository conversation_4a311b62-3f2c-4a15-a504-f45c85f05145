package com.centuroglobal.facade

import com.centuroglobal.service.CorporateService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.AccountEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.enums.SubscriptionType
import com.centuroglobal.shared.data.payload.account.*
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.data.payload.corporate.CorporateUserInfoRequest
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class CorporateFacadeTest {

    private lateinit var corporateService: CorporateService
    private lateinit var corporateFacade: CorporateFacade
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        corporateService = mockk()
        corporateFacade = CorporateFacade(corporateService)
        authenticatedUser = mockk()
        
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns "ADMIN"
    }

    @Test
    fun `test retrieve returns corporate details`() {
        // Arrange
        val userId = 1L
        val corporate = Corporate(
            id = userId,
            countryCode = "US",
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            jobTitle = "Manager",
            status = AccountStatus.ACTIVE,
            corporateName = "Test Corp",
            onBoardingInfo = null,
            keepMeInformed = true,
            bandId = 1L,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            dialCode = "+1",
            contactNo = "**********",
            isPrimary = true,
            aiMessageCount = 0,
            notificationSettings = listOf(),
            corporateCountryCode = "US",
            partnerName = "Partner",
            profilePhotoUrl = "https://example.com/photo.jpg"
        )
        
        every { corporateService.retrieveCorporate(userId) } returns corporate
        
        // Act
        val result = corporateFacade.retrieve(userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(userId, result.id)
        assertEquals("US", result.countryCode)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("Manager", result.jobTitle)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals("Test Corp", result.corporateName)
        assertEquals(true, result.keepMeInformed)
        assertEquals(1L, result.bandId)
        assertEquals("+1", result.dialCode)
        assertEquals("**********", result.contactNo)
        assertEquals(true, result.isPrimary)
        assertEquals("US", result.corporateCountryCode)
        assertEquals("Partner", result.partnerName)
        assertEquals("https://example.com/photo.jpg", result.profilePhotoUrl)
        
        verify { corporateService.retrieveCorporate(userId) }
    }

    @Test
    fun `test update updates corporate profile`() {
        // Arrange
        val corporateUserId = 1L
        val requesterUserId = 2L
        val requesterUserType = "ADMIN"
        
        val profileRequest = UpdateCorporateProfileRequest(
            corporateInfo = UpdateCorporateInfoRequest(
                firstName = "John",
                lastName = "Doe",
                jobTitle = "Manager",
                corporateName = "ABC Corporation",
                countryCode = "US",
                keepMeInformed = true,
                dialCode = "+1",
                contactNo = "**********",
                corporateId = 1L,
                isPrimary = true,
                aiMessageCount = 10,
                notificationSettings = listOf(
                    NotificationSettingRequest(
                        key = NotificationType.CASE_GCHAT_EMAIL,
                        value = true
                    ),
                    NotificationSettingRequest(
                        key = NotificationType.CASE_UPDATE_EMAIL,
                        value = false
                    )
                ),
                profilePicS3Key = "",
                educationQualification = "",
                salary = "12345",
                relevantExperience = "4",
                bandId = 2,
                managerUserIds = listOf(),
                accounts = listOf(),
                email = "<EMAIL>"
            ),
            status = UpdateCorporateStatusRequest(
                status = "ACTIVE"
            ),
            userInfo = CorporateUserInfoRequest(
                bandId = 1L,
                accounts = listOf(),
                managerUserIds = listOf()
            )
        )
        
        val updatedCorporate = Corporate(
            id = corporateUserId,
            countryCode = "UK",
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            jobTitle = "Senior Manager",
            status = AccountStatus.ACTIVE,
            corporateName = "Test Corp",
            onBoardingInfo = null,
            keepMeInformed = false,
            bandId = 1L,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            dialCode = "+44",
            contactNo = "**********",
            isPrimary = true,
            aiMessageCount = 0,
            notificationSettings = listOf(),
            corporateCountryCode = "US",
            partnerName = "Partner",
            profilePhotoUrl = "https://example.com/photo.jpg"
        )
        
        every { 
            corporateService.updateCorporate(corporateUserId, requesterUserId, requesterUserType, profileRequest) 
        } returns updatedCorporate
        
        // Act
        val result = corporateFacade.update(
            corporateUserId, requesterUserId, requesterUserType, profileRequest
        ).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(corporateUserId, result.id)
        assertEquals("UK", result.countryCode)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("Senior Manager", result.jobTitle)
        assertEquals(false, result.keepMeInformed)
        assertEquals("+44", result.dialCode)
        assertEquals("**********", result.contactNo)
        
        verify { 
            corporateService.updateCorporate(corporateUserId, requesterUserId, requesterUserType, profileRequest) 
        }
    }

    @Test
    fun `test signUp creates corporate and returns success message`() {
        // Arrange
        val signUpRequest = SignUpRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            corporateName = "New Corp",
            countryCode = "US",
            referralCode = "REF123",
            keepMeInformed = true,
            recaptchaResponse = "recaptcha-token"
        )
        
        val corporateId = 1L
        
        every { corporateService.createCorporate(signUpRequest) } returns corporateId
        
        // Act
        val result = corporateFacade.signUp(signUpRequest).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        verify { corporateService.createCorporate(signUpRequest) }
    }

    @Test
    fun `test resend resends verification email`() {
        // Arrange
        val corporateUserId = 1L
        val successMessage = "Verification email resent successfully"
        
        every { corporateService.resend(corporateUserId) } returns successMessage
        
        // Act
        val result = corporateFacade.resend(corporateUserId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successMessage, result)
        
        verify { corporateService.resend(corporateUserId) }
    }

    @Test
    fun `test addAccount adds account and returns success message`() {
        // Arrange
        val corporateId = 1L
        val request = AddAccountRequest(
            id = null,
            name = "Test Account",
            description = "Test Description",
            companyName = "Test Company",
            status = AccountStatus.ACTIVE,
            corporateId = corporateId
        )
        
        val accountId = 1L
        
        every { 
            corporateService.addAccount(request, corporateId, authenticatedUser) 
        } returns accountId
        
        // Act
        val result = corporateFacade.addAccount(request, corporateId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        verify { corporateService.addAccount(request, corporateId, authenticatedUser) }
    }

    @Test
    fun `test updateAccount updates account`() {
        // Arrange
        val accountId = 1L
        val request = AddAccountRequest(
            id = accountId,
            name = "Updated Account",
            description = "Updated Description",
            companyName = "Updated Company",
            status = AccountStatus.ACTIVE,
            corporateId = 1L
        )
        
        val accountEntity = mockk<AccountEntity>()
        
        every { 
            corporateService.updateAccount(accountId, authenticatedUser, request) 
        } returns accountEntity
        
        // Act
        val result = corporateFacade.updateAccount(accountId, authenticatedUser, request).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(accountEntity, result)
        
        verify { corporateService.updateAccount(accountId, authenticatedUser, request) }
    }

    @Test
    fun `test retrieveSecondaryCorporateUsers returns list of corporate user details`() {
        // Arrange
        val corporateId = 1L
        val corporateUserDetails = listOf(
            CorporateUserDetail(
                id = 1L,
                email = "<EMAIL>",
                bandName = "Admin",
                status = "ACTIVE",
                fullName = "abcd",
                company = "abcd",
                band = "abcd",
                accountName = listOf(),
                createdDate = 12345,
                subscriptionType = SubscriptionType.FREE
            ),
            CorporateUserDetail(
                id = 2L,
                email = "<EMAIL>",
                bandName = "Admin",
                status = "ACTIVE",
                fullName = "abcd",
                company = "abcd",
                band = "abcd",
                accountName = listOf(),
                createdDate = 12345,
                subscriptionType = SubscriptionType.FREE
            )
        )
        
        every { 
            corporateService.retrieveSecondaryCorporateUsers(corporateId) 
        } returns corporateUserDetails
        
        // Act
        val result = corporateFacade.retrieveSecondaryCorporateUsers(corporateId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(1L, result[0].id)

        verify { corporateService.retrieveSecondaryCorporateUsers(corporateId) }
    }

    @Test
    fun `test listCorporates returns list of reference data`() {
        // Arrange
        val isPartnerCompany = true
        val referenceData = listOf(
            ReferenceData(1L, "Corporate 1"),
            ReferenceData(2L, "Corporate 2"),
            ReferenceData(3L, "Corporate 3")
        )
        
        every { 
            corporateService.retrieveCorporates(isPartnerCompany, null) 
        } returns referenceData
        
        // Act
        val result = corporateFacade.listCorporates(isPartnerCompany)?.get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result?.size)
        assertEquals(1L, result?.get(0)?.id)
        assertEquals("Corporate 1", result?.get(0)?.name)
        assertEquals(2L, result?.get(1)?.id)
        assertEquals("Corporate 2", result?.get(1)?.name)
        assertEquals(3L, result?.get(2)?.id)
        assertEquals("Corporate 3", result?.get(2)?.name)
        
        verify { corporateService.retrieveCorporates(isPartnerCompany, null) }
    }

    @Test
    fun `test listCorporateUsers returns list of corporate users`() {
        // Arrange
        val corporateId = 1L
        val corporateUsers = listOf(
            CorporateUsers(
                id = 1L,
                firstName = "John",
                lastName = "Doe",
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE
            ),
            CorporateUsers(
                id = 2L,
                firstName = "Jane",
                lastName = "Smith",
                email = "<EMAIL>",
                status = AccountStatus.ACTIVE
            )
        )
        
        every { 
            corporateService.retrieveCorporateUsers(corporateId) 
        } returns corporateUsers
        
        // Act
        val result = corporateFacade.listCorporateUsers(corporateId)?.get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result?.size)
        assertEquals(1L, result?.get(0)?.id)
        assertEquals("John", result?.get(0)?.firstName)
        assertEquals("Doe", result?.get(0)?.lastName)
        assertEquals("<EMAIL>", result?.get(0)?.email)
        assertEquals(AccountStatus.ACTIVE, result?.get(0)?.status)
        
        assertEquals(2L, result?.get(1)?.id)
        assertEquals("Jane", result?.get(1)?.firstName)
        assertEquals("Smith", result?.get(1)?.lastName)
        assertEquals("<EMAIL>", result?.get(1)?.email)
        assertEquals(AccountStatus.ACTIVE, result?.get(1)?.status)
        
        verify { corporateService.retrieveCorporateUsers(corporateId) }
    }

    @Test
    fun `test listCorporateUserAccounts returns list of reference data`() {
        // Arrange
        val userId = 1L
        val referenceData = listOf(
            ReferenceData(1L, "Account 1"),
            ReferenceData(2L, "Account 2")
        )
        
        every { 
            corporateService.retrieveCorporateUserAccounts(userId) 
        } returns referenceData
        
        // Act
        val result = corporateFacade.listCorporateUserAccounts(userId)?.get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result?.size)
        assertEquals(1L, result?.get(0)?.id)
        assertEquals("Account 1", result?.get(0)?.name)
        assertEquals(2L, result?.get(1)?.id)
        assertEquals("Account 2", result?.get(1)?.name)
        
        verify { corporateService.retrieveCorporateUserAccounts(userId) }
    }

    @Test
    fun `test listCorporateAccounts returns list of reference data`() {
        // Arrange
        val corporateId = 1L
        val referenceData = listOf(
            ReferenceData(1L, "Account 1"),
            ReferenceData(2L, "Account 2")
        )
        
        every { 
            corporateService.retrieveCorporateAccounts(corporateId) 
        } returns referenceData
        
        // Act
        val result = corporateFacade.listCorporateAccounts(corporateId)
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(1L, result[0].id)
        assertEquals("Account 1", result[0].name)
        assertEquals(2L, result[1].id)
        assertEquals("Account 2", result[1].name)
        
        verify { corporateService.retrieveCorporateAccounts(corporateId) }
    }

    @Test
    fun `test retrieveByCorporateId returns corporate response`() {
        // Arrange
        val corporateId = 1L
        val corporateResponse = CorporateResponse(
            id = corporateId,
            countryCode = "US",
            primaryColor = "#FF0000",
            secondaryColor = "#00FF00",
            companyLogoId = "logo123",
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            jobTitle = "Manager",
            corporateName = "Test Corp",
            referralCode = "REF123",
            keepMeInformed = true,
            recaptchaResponse = "",
            aiMessageCount = 0,
            assignedTeam = listOf(),
            onboardingDocs = listOf(),
            associatedPartner = 1L,
            subscriptions = listOf(),
            features = listOf(),
            isTeamEmail = false
        )
        
        every { 
            corporateService.retrieveByCorporateId(corporateId) 
        } returns corporateResponse
        
        // Act
        val result = corporateFacade.retrieveByCorporateId(corporateId)
        
        // Assert
        assertEquals(corporateId, result.id)
        assertEquals("US", result.countryCode)
        assertEquals("#FF0000", result.primaryColor)
        assertEquals("#00FF00", result.secondaryColor)
        assertEquals("logo123", result.companyLogoId)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("Manager", result.jobTitle)
        assertEquals("Test Corp", result.corporateName)
        assertEquals("REF123", result.referralCode)
        assertEquals(true, result.keepMeInformed)
        assertEquals(0, result.aiMessageCount)
        assertEquals(1L, result.associatedPartner)
        assertEquals(false, result.isTeamEmail)
        
        verify { corporateService.retrieveByCorporateId(corporateId) }
    }
}