package com.centuroglobal.facade

import com.centuroglobal.service.CorporateUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.account.CorporateUserRequest
import com.centuroglobal.shared.data.pojo.CorporateUserResponse
import com.centuroglobal.shared.data.pojo.CorporateUserSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.UserCardDetails
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageRequest
import java.util.concurrent.TimeUnit

class CorporateUserFacadeTest {

    private lateinit var corporateUserService: CorporateUserService
    private lateinit var corporateUserFacade: CorporateUserFacade
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        corporateUserService = mockk()
        corporateUserFacade = CorporateUserFacade(corporateUserService)
        authenticatedUser = mockk()
        
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
    }

    @Test
    fun `test list returns paged result of user card details`() {
        // Arrange
        val searchFilter = CorporateUserSearchFilter(
            search = "john",
            accountId = 1L,
            status = AccountStatus.ACTIVE,
            bandId = 1L,
            countryCode = "US",
        )
        
        val pageRequest = PageRequest.of(0, 10)
        
        val userCardDetails = UserCardDetails(
            id = 1L,
            email = "<EMAIL>",
            status = AccountStatus.ACTIVE.toString(),
            jobTitle = "Manager",
            lastSeen = **********,
            bandColor = "#FF0000",
            bandLabel = "Admin",
            fullName = "John Doe",
            country = "US",
            casesCreated = 5,
            reportees = 2,
            profilePhotoUrl = "https://example.com/photo.jpg"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(userCardDetails),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser) 
        } returns pagedResult
        
        // Act
        val result = corporateUserFacade.list(searchFilter, pageRequest, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.rows.size)
        assertEquals(1, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(1, result.totalPages)
        
        val user = result.rows[0]
        assertEquals(1L, user.id)
        assertEquals("<EMAIL>", user.email)
        assertEquals(AccountStatus.ACTIVE.toString(), user.status)
        assertEquals("Manager", user.jobTitle)
        assertEquals(**********, user.lastSeen)
        assertEquals("#FF0000", user.bandColor)
        assertEquals("Admin", user.bandLabel)
        assertEquals("John Doe", user.fullName)
        assertEquals("US", user.country)
        assertEquals(5, user.casesCreated)
        assertEquals(2, user.reportees)
        assertEquals("https://example.com/photo.jpg", user.profilePhotoUrl)
        
        verify { corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser) }
    }

    @Test
    fun `test update updates corporate user and returns success message`() {
        // Arrange
        val userId = 1L
        val corporateUserRequest = CorporateUserRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Senior Manager",
            bandId = 1L,
            corporateId = 1L,
            countryCode = "US",
            referralCode = "",
            keepMeInformed = true,
            accounts = listOf(1L, 2L),
            managerUserIds = listOf(3L),
            dialCode = "+1",
            contactNo = "**********",
            isDraft = false
        )
        
        every { 
            corporateUserService.updateCorporateUser(userId, corporateUserRequest, authenticatedUser) 
        } returns Unit
        
        // Act
        val result = corporateUserFacade.update(userId, corporateUserRequest, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        verify { corporateUserService.updateCorporateUser(userId, corporateUserRequest, authenticatedUser) }
    }

    @Test
    fun `test fetchUser returns corporate user response`() {
        // Arrange
        val userId = 1L
        val corporateId = 1L
        
        val corporateUserResponse = CorporateUserResponse(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            bandId = 1L,
            corporateId = corporateId,
            countryCode = "US",
            accounts = listOf(1L, 2L),
            managerUserIds = listOf(3L),
            dialCode = "+1",
            contactNo = "**********",
            keepMeInformed = true,
            profilePhotoUrl = "https://example.com/photo.jpg",
            corporateName = "Test Corp",
            isPrimary = true,
            notificationSettings = listOf(),
            corporateCountryCode = "US",
            educationQualification = "MBA",
            salary = "100000",
            relevantExperience = "5 years"
        )
        
        every { 
            corporateUserService.fetchUser(userId, corporateId) 
        } returns corporateUserResponse
        
        // Act
        val result = corporateUserFacade.fetchUser(userId, corporateId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("<EMAIL>", result.email)
        assertEquals("Manager", result.jobTitle)
        assertEquals(1L, result.bandId)
        assertEquals(corporateId, result.corporateId)
        assertEquals("US", result.countryCode)
        assertEquals(2, result.accounts.size)
        result.managerUserIds?.let { assertEquals(1, it.size) }
        assertEquals("+1", result.dialCode)
        assertEquals("**********", result.contactNo)
        assertEquals(true, result.keepMeInformed)
        assertEquals("https://example.com/photo.jpg", result.profilePhotoUrl)
        assertEquals("Test Corp", result.corporateName)
        assertEquals(true, result.isPrimary)
        assertEquals("US", result.corporateCountryCode)
        assertEquals("MBA", result.educationQualification)
        assertEquals("100000", result.salary)
        assertEquals("5 years", result.relevantExperience)
        
        verify { corporateUserService.fetchUser(userId, corporateId) }
    }

    @Test
    fun `test list with empty search filter returns paged result`() {
        // Arrange
        val searchFilter = CorporateUserSearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        
        val pagedResult = PagedResult<UserCardDetails>(
            rows = listOf(),
            totalElements = 0,
            currentPage = 0,
            totalPages = 0
        )
        
        every { 
            corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser) 
        } returns pagedResult
        
        // Act
        val result = corporateUserFacade.list(searchFilter, pageRequest, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(0, result.rows.size)
        assertEquals(0, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(0, result.totalPages)
        
        verify { corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser) }
    }

    @Test
    fun `test list with multiple users returns paged result`() {
        // Arrange
        val searchFilter = CorporateUserSearchFilter(
        )
        
        val pageRequest = PageRequest.of(0, 10)
        
        val userCardDetails1 = UserCardDetails(
            id = 1L,
            email = "<EMAIL>",
            status = AccountStatus.ACTIVE.toString(),
            jobTitle = "Manager",
            lastSeen = **********,
            bandColor = "#FF0000",
            bandLabel = "Admin",
            fullName = "John Doe",
            country = "US",
            casesCreated = 5,
            reportees = 2,
            profilePhotoUrl = "https://example.com/photo1.jpg"
        )
        
        val userCardDetails2 = UserCardDetails(
            id = 2L,
            email = "<EMAIL>",
            status = AccountStatus.ACTIVE.toString(),
            jobTitle = "Director",
            lastSeen = **********,
            bandColor = "#00FF00",
            bandLabel = "Manager",
            fullName = "Jane Smith",
            country = "UK",
            casesCreated = 10,
            reportees = 5,
            profilePhotoUrl = "https://example.com/photo2.jpg"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(userCardDetails1, userCardDetails2),
            totalElements = 2,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser) 
        } returns pagedResult
        
        // Act
        val result = corporateUserFacade.list(searchFilter, pageRequest, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.rows.size)
        assertEquals(2, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(1, result.totalPages)
        
        val user1 = result.rows[0]
        assertEquals(1L, user1.id)
        assertEquals("<EMAIL>", user1.email)
        assertEquals("John Doe", user1.fullName)
        assertEquals("US", user1.country)
        
        val user2 = result.rows[1]
        assertEquals(2L, user2.id)
        assertEquals("<EMAIL>", user2.email)
        assertEquals("Jane Smith", user2.fullName)
        assertEquals("UK", user2.country)
        
        verify { corporateUserService.listUsers(searchFilter, pageRequest, authenticatedUser) }
    }

    @Test
    fun `test update with minimal data updates corporate user`() {
        // Arrange
        val userId = 1L
        val corporateUserRequest = CorporateUserRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            bandId = 1L,
            corporateId = 1L,
            countryCode = "US",
            referralCode = "",
            keepMeInformed = true,
            accounts = listOf(),
            managerUserIds = listOf(),
            dialCode = "",
            contactNo = "",
            isDraft = false
        )
        
        every { 
            corporateUserService.updateCorporateUser(userId, corporateUserRequest, authenticatedUser) 
        } returns Unit
        
        // Act
        val result = corporateUserFacade.update(userId, corporateUserRequest, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        verify { corporateUserService.updateCorporateUser(userId, corporateUserRequest, authenticatedUser) }
    }

    @Test
    fun `test fetchUser with different corporate id returns corporate user response`() {
        // Arrange
        val userId = 1L
        val corporateId = 2L
        
        val corporateUserResponse = CorporateUserResponse(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Manager",
            bandId = 1L,
            corporateId = corporateId,
            countryCode = "US",
            accounts = listOf(),
            managerUserIds = listOf(),
            dialCode = "",
            contactNo = "",
            keepMeInformed = true,
            profilePhotoUrl = "",
            corporateName = "Test Corp",
            isPrimary = false,
            notificationSettings = listOf(),
            corporateCountryCode = "",
            educationQualification = "",
            salary = "",
            relevantExperience = ""
        )
        
        every { 
            corporateUserService.fetchUser(userId, corporateId) 
        } returns corporateUserResponse
        
        // Act
        val result = corporateUserFacade.fetchUser(userId, corporateId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("<EMAIL>", result.email)
        assertEquals("Manager", result.jobTitle)
        assertEquals(1L, result.bandId)
        assertEquals(corporateId, result.corporateId)
        assertEquals("US", result.countryCode)
        assertEquals(0, result.accounts.size)
        result.managerUserIds?.let { assertEquals(0, it.size) }
        assertEquals("", result.dialCode)
        assertEquals("", result.contactNo)
        assertEquals(true, result.keepMeInformed)
        assertEquals("", result.profilePhotoUrl)
        assertEquals("Test Corp", result.corporateName)
        assertEquals(false, result.isPrimary)
        
        verify { corporateUserService.fetchUser(userId, corporateId) }
    }
}