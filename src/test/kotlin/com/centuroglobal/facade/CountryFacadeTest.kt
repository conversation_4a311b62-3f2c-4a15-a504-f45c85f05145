package com.centuroglobal.facade

import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.service.CountryService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class CountryFacadeTest {

    private lateinit var countryService: CountryService
    private lateinit var countryFacade: CountryFacade

    @BeforeEach
    fun setup() {
        countryService = mockk()
        countryFacade = CountryFacade(countryService)
    }

    @Test
    fun `test listCountries returns list of countries`() {
        // Arrange
        val prefix = "U"
        val countries = listOf(
            Country(
                code = "US",
                name = "United States",
                dialCode = "+1",
            ),
            Country(
                code = "UK",
                name = "United Kingdom",
                dialCode = "+44",
            ),
            Country(
                code = "UG",
                name = "Uganda",
                dialCode = "+256",
            )
        )
        
        every { countryService.listCountries(prefix) } returns countries
        
        // Act
        val result = countryFacade.listCountries(prefix).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        
        // Verify first country
        assertEquals("US", result[0].code)
        assertEquals("United States", result[0].name)
        assertEquals("+1", result[0].dialCode)

        // Verify second country
        assertEquals("UK", result[1].code)
        assertEquals("United Kingdom", result[1].name)
        assertEquals("+44", result[1].dialCode)

        // Verify third country
        assertEquals("UG", result[2].code)
        assertEquals("Uganda", result[2].name)
        assertEquals("+256", result[2].dialCode)

        verify { countryService.listCountries(prefix) }
    }

    @Test
    fun `test listCountries with null prefix returns all countries`() {
        // Arrange
        val prefix = null
        val countries = listOf(
            Country(
                code = "US",
                name = "United States",
                dialCode = "+1",
            ),
            Country(
                code = "CA",
                name = "Canada",
                dialCode = "+1",
            ),
            Country(
                code = "MX",
                name = "Mexico",
                dialCode = "+52",
            )
        )
        
        every { countryService.listCountries(prefix) } returns countries
        
        // Act
        val result = countryFacade.listCountries(prefix).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        
        // Verify countries are returned
        assertEquals("US", result[0].code)
        assertEquals("CA", result[1].code)
        assertEquals("MX", result[2].code)
        
        verify { countryService.listCountries(prefix) }
    }

    @Test
    fun `test listCountries with empty prefix returns all countries`() {
        // Arrange
        val prefix = ""
        val countries = listOf(
            Country(
                code = "US",
                name = "United States",
                dialCode = "+1",
            ),
            Country(
                code = "CA",
                name = "Canada",
                dialCode = "+1",
            ),
            Country(
                code = "MX",
                name = "Mexico",
                dialCode = "+52",
            )
        )
        
        every { countryService.listCountries(prefix) } returns countries
        
        // Act
        val result = countryFacade.listCountries(prefix).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        
        // Verify countries are returned
        assertEquals("US", result[0].code)
        assertEquals("CA", result[1].code)
        assertEquals("MX", result[2].code)
        
        verify { countryService.listCountries(prefix) }
    }

    @Test
    fun `test listCountries with no matching countries returns empty list`() {
        // Arrange
        val prefix = "XYZ"
        val countries = emptyList<Country>()
        
        every { countryService.listCountries(prefix) } returns countries
        
        // Act
        val result = countryFacade.listCountries(prefix).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(0, result.size)
        
        verify { countryService.listCountries(prefix) }
    }

    @Test
    fun `test retrieveByCountryCode returns country`() {
        // Arrange
        val countryCode = "US"
        val country = Country(
            code = "US",
            name = "United States",
            dialCode = "+1",
        )
        
        every { countryService.retrieveByCountryCode(countryCode) } returns country
        
        // Act
        val result = countryFacade.retrieveByCountryCode(countryCode).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("US", result.code)
        assertEquals("United States", result.name)
        assertEquals("+1", result.dialCode)

        verify { countryService.retrieveByCountryCode(countryCode) }
    }

    @Test
    fun `test retrieveByCountryCode with lowercase code returns country`() {
        // Arrange
        val countryCode = "us"
        val country = Country(
            code = "US",
            name = "United States",
            dialCode = "+1",
        )
        
        every { countryService.retrieveByCountryCode(countryCode) } returns country
        
        // Act
        val result = countryFacade.retrieveByCountryCode(countryCode).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("US", result.code)
        assertEquals("United States", result.name)
        
        verify { countryService.retrieveByCountryCode(countryCode) }
    }

    @Test
    fun `test retrieveByCountryCode with different country code returns corresponding country`() {
        // Arrange
        val countryCode = "CA"
        val country = Country(
            code = "CA",
            name = "Canada",
            dialCode = "+1",
        )
        
        every { countryService.retrieveByCountryCode(countryCode) } returns country
        
        // Act
        val result = countryFacade.retrieveByCountryCode(countryCode).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("CA", result.code)
        assertEquals("Canada", result.name)
        assertEquals("+1", result.dialCode)

        verify { countryService.retrieveByCountryCode(countryCode) }
    }

    @Test
    fun `test retrieveByCountryCode with non-existent country code returns empty country`() {
        // Arrange
        val countryCode = "XX"
        val country = Country(
            code = "",
            name = "",
            dialCode = "",
        )
        
        every { countryService.retrieveByCountryCode(countryCode) } returns country
        
        // Act
        val result = countryFacade.retrieveByCountryCode(countryCode).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals("", result.code)
        assertEquals("", result.name)
        assertEquals("", result.dialCode)

        verify { countryService.retrieveByCountryCode(countryCode) }
    }
}