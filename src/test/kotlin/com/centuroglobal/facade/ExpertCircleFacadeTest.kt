package com.centuroglobal.facade

import com.centuroglobal.service.CircleService
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.circle.CircleListing
import com.centuroglobal.shared.data.pojo.circle.CircleMessageTrailRequest
import com.centuroglobal.shared.data.pojo.circle.CircleResponseTrail
import com.centuroglobal.shared.data.pojo.circle.CircleSearchFilter
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

class ExpertCircleFacadeTest {

    private lateinit var circleService: CircleService
    private lateinit var expertCircleFacade: ExpertCircleFacade
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        circleService = mockk()
        expertCircleFacade = ExpertCircleFacade(circleService)
        authenticatedUser = mockk()
        
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.userType } returns "EXPERT"
    }

    @Test
    fun `test circleDetails returns circle details`() {
        // Arrange
        val circleId = 1L
        
        val circle = Circle(
            id = 1,
            name = "Test Circle",
            about = "This is a test circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/banner.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = 1672531200000, // Jan 1, 2023
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 5,
            invitee = 2,
            request = 1,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf()
        )
        
        every { 
            circleService.circleDetails(circleId, authenticatedUser) 
        } returns circle
        
        // Act
        val result = expertCircleFacade.circleDetails(circleId, authenticatedUser).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.id)
        assertEquals("Test Circle", result.name)
        assertEquals("This is a test circle", result.about)
        assertEquals(CircleStatus.ACTIVE, result.status)
        assertEquals(CircleType.PUBLIC, result.circleAccessType)
        assertEquals(listOf("US", "UK"), result.countryCodes)
        assertEquals(listOf(1, 2), result.expertiseIds)
        assertEquals(5, result.member)
        assertEquals(2, result.invitee)
        assertEquals(1, result.request)
        
        verify { circleService.circleDetails(circleId, authenticatedUser) }
    }

    @Test
    fun `test circleMemberAction performs action and returns result`() {
        // Arrange
        val circleId = 1L
        val circleMemberAction = CircleMemberAction.LEAVE
        val actionResult = "Action successful"
        
        every { 
            circleService.circleMemberAction( any(), any(), any(), any())
        } returns Unit
        
        // Act
        val result = expertCircleFacade.circleMemberAction(circleId, circleMemberAction, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(Unit, result)
        
    }

    @Test
    fun `test getMessageTrail returns message trails`() {
        // Arrange
        val circleId = 1L
        val lastMessageDateTime = 1672531200000L // Jan 1, 2023
        
        val messageTrail1 = CircleResponseTrail(
            id = 1,
            message = "Hello everyone!",
            dateTime = 1234,
            rootUserId = 12,
            asset = "",
            isAsset = true,
            respondedBy = UserType.EXPERT,
            seen = true,
            shortAdminName = "",
            profilePictureFullUrl = ""
        )
        
        val messageTrail2 = CircleResponseTrail(
            id = 12,
            message = "Hello everyone!",
            dateTime = 1234,
            rootUserId = 12,
            asset = "",
            isAsset = true,
            respondedBy = UserType.EXPERT,
            seen = true,
            shortAdminName = "",
            profilePictureFullUrl = ""
        )
        
        val messageTrails = listOf(listOf(messageTrail1, messageTrail2))
        
        every { 
            circleService.getCircleResponseTrail(
                any(),
                any(),
                any(),
                any()
            ) 
        } returns messageTrails
        
        // Act
        val result = expertCircleFacade.getMessageTrail(circleId, lastMessageDateTime, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.size)
        assertEquals(2, result[0].size)
        
        val firstMessage = result[0][0]
        assertEquals(1, firstMessage.id)
        assertEquals("Hello everyone!", firstMessage.message)

        val secondMessage = result[0][1]
        assertEquals(12, secondMessage.id)

    }

    @Test
    fun `test saveMessageTrail saves message and returns updated trails`() {
        // Arrange
        val circleId = 1L
        val messageRequest = CircleMessageTrailRequest(
            circleId = circleId,
            message = "Hello, I'm new here!",
            id = 2,
            expertId = 4,
            respondedBy = UserType.EXPERT,
            lastMessageDateTime = 345
        )

        val messageTrail = CircleResponseTrail(
            id = 1,
            message = "Hello everyone!",
            dateTime = 1234,
            rootUserId = 12,
            asset = "",
            isAsset = true,
            respondedBy = UserType.EXPERT,
            seen = true,
            shortAdminName = "",
            profilePictureFullUrl = ""
        )
        
        val messageTrails = listOf(listOf(messageTrail))
        
        every { 
            circleService.saveMessageTrail(
                any(),
                any(),
                any(),
                any()
            ) 
        } returns messageTrails
        
        // Act
        val result = expertCircleFacade.saveMessageTrail(circleId, messageRequest, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.size)
        assertEquals(1, result[0].size)
        
        val savedMessage = result[0][0]
        assertEquals(1, savedMessage.id)
        assertEquals("Hello everyone!", savedMessage.message)

    }

    @Test
    fun `test listCircle returns circle listing`() {
        // Arrange
        val filter = CircleSearchFilter.Builder.build(
            status = CircleStatus.ACTIVE.toString(),
            countryCode = "US",
            expertiseId = "1",
            circleType = "",
            from = 123,
            to = 123,
            search = ""
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val circle = Circle(
            id = 1,
            name = "Test Circle",
            about = "This is a test circle",
            status = CircleStatus.ACTIVE,
            bannerImageFullUrl = "https://example.com/banner.jpg",
            circleAccessType = CircleType.PUBLIC,
            createdDate = 1672531200000L,
            countryCodes = listOf("US", "UK"),
            expertiseIds = listOf(1, 2),
            member = 5,
            invitee = 2,
            request = 1,
            members = listOf(),
            requests = listOf(),
            messages = listOf(),
            memberAction = null,
            membersThumbnail = listOf()
        )
        
        val circleListing = CircleListing(
            data = PagedResult(
                rows = listOf(circle),
                totalElements = 1,
                currentPage = 0,
                totalPages = 1
            ),
            stats = null
        )
        
        every { 
            circleService.listCircleExpert(authenticatedUser, filter, pageRequest) 
        } returns circleListing
        
        // Act
        val result = expertCircleFacade.listCircle(filter, pageRequest, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.data.rows.size)
        assertEquals(1, result.data.totalElements)
        assertEquals(0, result.data.currentPage)
        assertEquals(1, result.data.totalPages)
        
        val listedCircle = result.data.rows[0]
        assertEquals(1, listedCircle.id)
        assertEquals("Test Circle", listedCircle.name)
        assertEquals("This is a test circle", listedCircle.about)
        assertEquals(CircleStatus.ACTIVE, listedCircle.status)
        assertEquals(CircleType.PUBLIC, listedCircle.circleAccessType)
        assertEquals(listOf("US", "UK"), listedCircle.countryCodes)
        assertEquals(listOf(1, 2), listedCircle.expertiseIds)
        
        verify { circleService.listCircleExpert(authenticatedUser, filter, pageRequest) }
    }

    @Test
    fun `test circleMemberSearch returns paged result`() {
        // Arrange
        val circleId = 1L
        val filter = ExpertSearchFilter.Builder.build(
            search = "John",
            countryCode = "US",
            expertiseId = "1"
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val expertProfile = ExpertProfileSummary(
            id = 3L,
            bio = "Bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(expertProfile),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            circleService.circleMemberSearch(circleId, filter, pageRequest, authenticatedUser) 
        } returns pagedResult
        
        // Act
        val result = expertCircleFacade.circleMemberSearch(circleId, filter, pageRequest, authenticatedUser)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.rows.size)
        assertEquals(1, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(1, result.totalPages)
        
        val member = result.rows[0]
        assertEquals("Manager", member.jobTitle)

    }
}