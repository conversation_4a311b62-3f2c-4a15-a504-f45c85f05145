package com.centuroglobal.facade

import com.centuroglobal.service.ExpertUserService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.account.*
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

class ExpertFacadeTest {

    private lateinit var expertUserService: ExpertUserService
    private lateinit var expertFacade: ExpertFacade
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        expertUserService = mockk()
        expertFacade = ExpertFacade(expertUserService)
        authenticatedUser = mockk()
        
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
    }

    @Test
    fun `test retrieveProfile returns expert profile`() {
        // Arrange
        val userId = 1L
        
        val expertProfile = ExpertProfile(
            id = userId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Senior Consultant",
            profilePictureFullUrl = "https://example.com/profile/john.jpg",
            countryCode = "US",
            contactEmail = "<EMAIL>",
            contactNumber = "+**********",
            contactWebsite = "https://johndoe.com",
            status = AccountStatus.ACTIVE,
            infoVideoUrl = "https://example.com/john-intro.mp4",
            displayName = "",
            countryRegionId = 33,
            expertiseId = listOf(),
            bio = "",
            expertType = "",
            viewContract = true,
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            associations = listOf(),
        )
        
        every { expertUserService.retrieveProfile(userId) } returns expertProfile
        
        // Act
        val result = expertFacade.retrieveProfile(userId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(userId, result.id)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("<EMAIL>", result.email)
        assertEquals("Senior Consultant", result.jobTitle)
        assertEquals("https://example.com/profile/john.jpg", result.profilePictureFullUrl)
        assertEquals("US", result.countryCode)
        assertEquals("<EMAIL>", result.contactEmail)
        assertEquals("+**********", result.contactNumber)
        assertEquals("https://johndoe.com", result.contactWebsite)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals("https://example.com/john-intro.mp4", result.infoVideoUrl)

        verify { expertUserService.retrieveProfile(userId) }
    }

    @Test
    fun `test retrieveSecondaryExpert returns list of clients`() {
        // Arrange
        val expertCompanyId = 1L
        
        val clients = mutableListOf(
            Client(
                id = 1,
                email = "<EMAIL>",
                fullName = "a",
                status = AccountStatus.ACTIVE,
                company = "",
                subscription = true,
                userType = UserType.CORPORATE,
                createdDate = 12345,
                referredBy = "",
                referral = 4,
                userId = 4,
                countryCode = "IN",
                expertiseIds = "",
                isLinkedin = true,
                country = "IN",
                expertise = "",
                subscriptionType = "",
                expertType = "",
                corporateType = "",
                secondaryUserCount = 3,
                bandName = "",
                lastLoginTime = 1234,
                lastTermsViewDate = 1234
            ),
            Client(
                id = 2,
                email = "<EMAIL>",
                fullName = "a",
                status = AccountStatus.ACTIVE,
                company = "",
                subscription = true,
                userType = UserType.CORPORATE,
                createdDate = 12345,
                referredBy = "",
                referral = 4,
                userId = 4,
                countryCode = "IN",
                expertiseIds = "",
                isLinkedin = true,
                country = "IN",
                expertise = "",
                subscriptionType = "",
                expertType = "",
                corporateType = "",
                secondaryUserCount = 3,
                bandName = "",
                lastLoginTime = 1234,
                lastTermsViewDate = 1234
            )
        )
        
        every { expertUserService.retrieveSecondaryExpert(expertCompanyId, null) } returns clients
        
        // Act
        val result = expertFacade.retrieveSecondaryExpert(expertCompanyId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)
        
        val client1 = result[0]
        assertEquals(1L, client1.id)
        assertEquals("<EMAIL>", client1.email)

    }

    @Test
    fun `test retrievePrimaryExpert returns list of expert user summaries`() {
        // Arrange
        val partnerId = 1L
        
        val expertUsers = mutableListOf(
            ExpertUserSummary(
                id = 1L,
                email = "<EMAIL>",
                displayName = "",
            ),
            ExpertUserSummary(
                id = 2L,
                email = "<EMAIL>",
                displayName = "",
            )
        )
        
        every { expertUserService.retrievePrimaryExpert(partnerId) } returns expertUsers
        
        // Act
        val result = expertFacade.retrievePrimaryExpert(partnerId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)
        
        val expert1 = result[0]
        assertEquals(1L, expert1.id)
        assertEquals("<EMAIL>", expert1.email)

        val expert2 = result[1]
        assertEquals(2L, expert2.id)

        verify { expertUserService.retrievePrimaryExpert(partnerId) }
    }

    @Test
    fun `test retrieveProfileSummary returns expert profile summary`() {
        // Arrange
        val userId = 1L
        val hasAdminAccess = true
        
        val profileSummary = ExpertProfileSummary(
            id = 3L,
            bio = "Bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        
        every { expertUserService.retrieveProfileSummaryForAdmin(userId, hasAdminAccess) } returns profileSummary
        
        // Act
        val result = expertFacade.retrieveProfileSummary(userId, hasAdminAccess).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.id)

    }

    @Test
    fun `test retrieveActiveProfileSummary returns paged result of expert profile summaries`() {
        // Arrange
        val expertSearchFilter = ExpertSearchFilter.Builder.build(
            search = "John",
            countryCode = "US",
            expertiseId = "1"
        )
        
        val pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdDate"))
        
        val profileSummary = ExpertProfileSummary(
            id = 3L,
            bio = "Bio",
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            countryCode = "US",
            countryName = "US",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            profilePictureFullUrl = "url.com"
        )
        
        val pagedResult = PagedResult(
            rows = listOf(profileSummary),
            totalElements = 1,
            currentPage = 0,
            totalPages = 1
        )
        
        every { 
            expertUserService.retrieveActiveExpertsSummary(expertSearchFilter, pageRequest) 
        } returns pagedResult
        
        // Act
        val result = expertFacade.retrieveActiveProfileSummary(expertSearchFilter, pageRequest)
            .get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(1, result.rows.size)
        assertEquals(1, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(1, result.totalPages)
        
        val expert = result.rows[0]
        assertEquals(3L, expert.id)
    }

    @Test
    fun `test createPrimaryExpert creates expert and returns success message`() {
        // Arrange
        val requesterUserId = 1L
        val request = CreatePrimaryExpertUserRequest(
            "<EMAIL>",
            "test",
            "PJ",
            "Developer",
            "IN",
            "",
            CompanyRequest(
                "tet", "34", "Arora", "mxs", "qmw",
                "nl", "5", "small", 5L, 6L, "INR", "9000",
                "NA", "NA", "NA", "kl", 8L,
                6L, "abc", 0L
            ),
            aiMessageCount = 0
        )
        
        every { 
            expertUserService.createPrimaryExpertUser(requesterUserId, request) 
        } returns 4
        
        // Act
        val result = expertFacade.createPrimaryExpert(requesterUserId, request).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        verify { expertUserService.createPrimaryExpertUser(requesterUserId, request) }
    }

    @Test
    fun `test createSecondaryExpert creates expert and returns success message`() {
        // Arrange
        val requesterUserId = 1L
        val request = CreateSecondaryExpertUserRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            jobTitle = "Immigration Specialist",
            expertCompanyId = 1L,
            countryCode = "",
            expertType = "",
            partnerId = 3,
            profileImage = ""
        )
        
        every { 
            expertUserService.createSecondaryExpertUser(requesterUserId, request) 
        } returns Unit
        
        // Act
        val result = expertFacade.createSecondaryExpert(requesterUserId, request).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        verify { expertUserService.createSecondaryExpertUser(requesterUserId, request) }
    }

    @Test
    fun `test updateProfile updates expert profile and returns updated profile`() {
        // Arrange
        val userId = 1L
        val requestedById = 2L
        val request = UpdateExpertProfileRequest(
            personalProfile = null,
            companyProfile = UpdateExpertCompanyProfileRequest(
                name = "Global Immigration Consultants",
                summary = "Leading immigration consultancy firm",
                size = CompanySize.Size3,
                territory = "North America, Europe",
                aiMessageCount = 50
            ),
            contactInfo = UpdateExpertContactInfoRequest(
                contactNumber = "",
                contactEmail = "",
                contactWebsite = ""
            )
        )
        
        val updatedProfile = ExpertProfile(
            id = userId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            jobTitle = "Senior Consultant",
            profilePictureFullUrl = "https://example.com/profile/john.jpg",
            countryCode = "US",
            contactEmail = "<EMAIL>",
            contactNumber = "+**********",
            contactWebsite = "https://johndoe.com",
            status = AccountStatus.ACTIVE,
            infoVideoUrl = "https://example.com/john-intro.mp4",
            displayName = "",
            countryRegionId = 33,
            expertiseId = listOf(),
            bio = "",
            expertType = "",
            viewContract = true,
            companyProfile = ExpertCompanyProfile(
                name = "name",
                companyNumber = "*********",
                companyAddress = "companyAddress",
                aboutBusiness = "aboutBusiness",
                effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
                feesCurrency = "feesCurrency",
                feesAmount = "feesAmount",
                specialTerms = "specialTerms",
                membershipStatus = "membershipStatus",
                services = "services",
                territory = "territory",
                size = CompanySize.Size0,
                logoFullUrl = "logofullurl",
                sizeName = "M",
                summary = "summary"
            ),
            associations = listOf(),
        )
        
        every { 
            expertUserService.updateProfile(userId, requestedById, request) 
        } returns updatedProfile
        
        // Act
        val result = expertFacade.updateProfile(userId, request, requestedById).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(userId, result.id)
        assertEquals("John", result.firstName)
    }
}
