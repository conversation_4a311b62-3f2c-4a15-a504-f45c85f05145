package com.centuroglobal.facade

import com.centuroglobal.service.ExpertiseService
import com.centuroglobal.shared.data.pojo.Expertise
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class ExpertiseFacadeTest {

    private lateinit var expertiseService: ExpertiseService
    private lateinit var expertiseFacade: ExpertiseFacade

    @BeforeEach
    fun setup() {
        expertiseService = mockk()
        expertiseFacade = ExpertiseFacade(expertiseService)
    }

    @Test
    fun `test listExpertise with null prefix and groupBy true returns all expertise`() {
        // Arrange
        val expertiseList = listOf(
            Expertise(
                id = 1,
                name = "Immigration Law",
            ),
            Expertise(
                id = 2,
                name = "Visa Applications",
            ),
            Expertise(
                id = 3,
                name = "Tax Planning",
            )
        )
        
        every { expertiseService.listExpertise() } returns expertiseList
        
        // Act
        val result = expertiseFacade.listExpertise(null, true).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        assertEquals(1, result[0].id)
        assertEquals("Immigration Law", result[0].name)

        assertEquals(2, result[1].id)
        assertEquals("Visa Applications", result[1].name)
        
        assertEquals(3, result[2].id)
        assertEquals("Tax Planning", result[2].name)

        verify { expertiseService.listExpertise() }
    }

    @Test
    fun `test listExpertise with empty prefix and groupBy true returns all expertise`() {
        // Arrange
        val expertiseList = listOf(
            Expertise(
                id = 1,
                name = "Immigration Law",
            ),
            Expertise(
                id = 2,
                name = "Visa Applications",
            ),
            Expertise(
                id = 3,
                name = "Tax Planning",
            )
        )
        
        every { expertiseService.listExpertise() } returns expertiseList
        
        // Act
        val result = expertiseFacade.listExpertise("", true).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        assertEquals(1, result[0].id)
        assertEquals("Immigration Law", result[0].name)

        assertEquals(2, result[1].id)
        assertEquals("Visa Applications", result[1].name)
        
        assertEquals(3, result[2].id)
        assertEquals("Tax Planning", result[2].name)

        verify { expertiseService.listExpertise() }
    }

    @Test
    fun `test listExpertise with prefix and groupBy true searches by prefix`() {
        // Arrange
        val prefix = "Imm"
        val searchResults = listOf(
            Expertise(
                id = 1,
                name = "Immigration Law",
            ),
            Expertise(
                id = 4,
                name = "Immigration Consulting",
            )
        )
        
        every { expertiseService.searchByGroupNameOrNamePrefix(prefix) } returns searchResults
        
        // Act
        val result = expertiseFacade.listExpertise(prefix, true).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(1, result[0].id)
        assertEquals("Immigration Law", result[0].name)

        assertEquals(4, result[1].id)
        assertEquals("Immigration Consulting", result[1].name)

        verify { expertiseService.searchByGroupNameOrNamePrefix(prefix) }
    }

    @Test
    fun `test listExpertise with prefix and groupBy false searches by prefix`() {
        // Arrange
        val prefix = "Vis"
        val searchResults = listOf(
            Expertise(
                id = 2,
                name = "Visa Applications",
            ),
            Expertise(
                id = 5,
                name = "Visa Processing",
            )
        )
        
        every { expertiseService.searchByGroupNameOrNamePrefix(prefix) } returns searchResults
        
        // Act
        val result = expertiseFacade.listExpertise(prefix, false).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(2, result.size)
        assertEquals(2, result[0].id)
        assertEquals("Visa Applications", result[0].name)

        assertEquals(5, result[1].id)
        assertEquals("Visa Processing", result[1].name)

        verify { expertiseService.searchByGroupNameOrNamePrefix(prefix) }
    }

    @Test
    fun `test listExpertise with null prefix and groupBy false searches by prefix`() {
        // Arrange
        val searchResults = listOf(
            Expertise(
                id = 1,
                name = "Immigration Law",
            ),
            Expertise(
                id = 2,
                name = "Visa Applications",
            ),
            Expertise(
                id = 3,
                name = "Tax Planning",
            )
        )
        
        every { expertiseService.searchByGroupNameOrNamePrefix(null) } returns searchResults
        
        // Act
        val result = expertiseFacade.listExpertise(null, false).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(3, result.size)
        assertEquals(1, result[0].id)
        assertEquals("Immigration Law", result[0].name)

        assertEquals(2, result[1].id)
        assertEquals("Visa Applications", result[1].name)
        
        assertEquals(3, result[2].id)
        assertEquals("Tax Planning", result[2].name)

        verify { expertiseService.searchByGroupNameOrNamePrefix(null) }
    }
}