package com.centuroglobal.facade

import com.centuroglobal.shared.data.enums.CompanySize
import com.centuroglobal.shared.data.pojo.EnumValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class FactoryDataFacadeTest {

    private lateinit var factoryDataFacade: FactoryDataFacade

    @BeforeEach
    fun setup() {
        factoryDataFacade = FactoryDataFacade()
    }

    @Test
    fun `test retrieveCompanySizeList returns all company sizes`() {
        // Act
        val result = factoryDataFacade.retrieveCompanySizeList().get(5, TimeUnit.SECONDS)
        
        // Assert
        // Verify the result contains all CompanySize enum values
        assertEquals(CompanySize.values().size, result.size)
        
        // Verify each enum value is correctly mapped to EnumValue
        CompanySize.values().forEachIndexed { index, companySize ->
            assertEquals(companySize.name, result[index].id)
            assertEquals(companySize.displayName, result[index].displayName)
        }
        
        // Verify specific values to ensure correct mapping
        val size1 = result.find { it.id == CompanySize.Size1.name }
        assertEquals(CompanySize.Size1.displayName, size1?.displayName)
        
        val size2 = result.find { it.id == CompanySize.Size2.name }
        assertEquals(CompanySize.Size2.displayName, size2?.displayName)
        
        val size3 = result.find { it.id == CompanySize.Size3.name }
        assertEquals(CompanySize.Size3.displayName, size3?.displayName)
        
        val size4 = result.find { it.id == CompanySize.Size4.name }
        assertEquals(CompanySize.Size4.displayName, size4?.displayName)
        
        val size5 = result.find { it.id == CompanySize.Size5.name }
        assertEquals(CompanySize.Size5.displayName, size5?.displayName)
    }

    @Test
    fun `test retrieveCompanySizeList returns correct EnumValue objects`() {
        // Act
        val result = factoryDataFacade.retrieveCompanySizeList().get(5, TimeUnit.SECONDS)
        
        // Assert
        // Create expected list of EnumValue objects
        val expected = CompanySize.values().map { 
            EnumValue(id = it.name, displayName = it.displayName) 
        }
        
        // Verify the result matches the expected list
        assertEquals(expected.size, result.size)
        
        expected.forEachIndexed { index, expectedValue ->
            assertEquals(expectedValue.id, result[index].id)
            assertEquals(expectedValue.displayName, result[index].displayName)
        }
    }

    @Test
    fun `test retrieveCompanySizeList returns non-empty list`() {
        // Act
        val result = factoryDataFacade.retrieveCompanySizeList().get(5, TimeUnit.SECONDS)
        
        // Assert
        assert(result.isNotEmpty()) { "Company size list should not be empty" }
    }

    @Test
    fun `test retrieveCompanySizeList returns list with correct structure`() {
        // Act
        val result = factoryDataFacade.retrieveCompanySizeList().get(5, TimeUnit.SECONDS)
        
        // Assert
        // Verify each item in the result has the correct structure
        result.forEach { enumValue ->
            // Verify id is not empty
            assert(enumValue.id.isNotEmpty()) { "EnumValue id should not be empty" }
            
            // Verify displayName is not empty
            assert(enumValue.displayName.isNotEmpty()) { "EnumValue displayName should not be empty" }
            
            // Verify id matches one of the CompanySize enum names
            assert(CompanySize.values().any { it.name == enumValue.id }) { 
                "EnumValue id should match a CompanySize enum name" 
            }
            
            // Verify displayName matches the corresponding CompanySize displayName
            val matchingEnum = CompanySize.values().find { it.name == enumValue.id }
            assertEquals(matchingEnum?.displayName, enumValue.displayName)
        }
    }
}