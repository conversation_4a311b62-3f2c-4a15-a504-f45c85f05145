package com.centuroglobal.facade

import com.centuroglobal.data.payload.auth.ChangePasswordRequest
import com.centuroglobal.data.payload.auth.RecoverPasswordRequest
import com.centuroglobal.data.payload.auth.ResetPasswordRequest
import com.centuroglobal.service.PasswordService
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.pojo.TokenResult
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class PasswordFacadeTest {

    private lateinit var passwordService: PasswordService
    private lateinit var passwordFacade: PasswordFacade

    @BeforeEach
    fun setup() {
        passwordService = mockk()
        passwordFacade = PasswordFacade(passwordService)
    }

    @Test
    fun `test changePassword returns success message`() {
        // Arrange
        val userId = 123L
        val request = ChangePasswordRequest(
            oldPassword = "OldPassword123!",
            newPassword = "NewPassword123!"
        )
        val successMessage = AppConstant.SUCCESS_RESPONSE_STRING
        
        every { passwordService.changePassword(userId, request) } returns successMessage
        
        // Act
        val result = passwordFacade.changePassword(userId, request).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successMessage, result)
        verify { passwordService.changePassword(userId, request) }
    }

    @Test
    fun `test changePassword handles service exception`() {
        // Arrange
        val userId = 123L
        val request = ChangePasswordRequest(
            oldPassword = "OldPassword123!",
            newPassword = "NewPassword123!"
        )
        val errorMessage = "Old password is incorrect"
        
        every { passwordService.changePassword(userId, request) } throws RuntimeException(errorMessage)
        
        // Act & Assert
        try {
            passwordFacade.changePassword(userId, request).get(5, TimeUnit.SECONDS)
            assert(false) { "Expected exception was not thrown" }
        } catch (e: Exception) {
        }
        
        verify { passwordService.changePassword(userId, request) }
    }

    @Test
    fun `test recoverPassword returns success message`() {
        // Arrange
        val request = RecoverPasswordRequest(email = "<EMAIL>")
        val successMessage = "Password recovery email sent"
        
        every { passwordService.recoverPassword(request) } returns successMessage
        
        // Act
        val result = passwordFacade.recoverPassword(request).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(successMessage, result)
        verify { passwordService.recoverPassword(request) }
    }

    @Test
    fun `test recoverPassword handles service exception`() {
        // Arrange
        val request = RecoverPasswordRequest(email = "<EMAIL>")
        val errorMessage = "Email not found"
        
        every { passwordService.recoverPassword(request) } throws RuntimeException(errorMessage)
        
        // Act & Assert
        try {
            passwordFacade.recoverPassword(request).get(5, TimeUnit.SECONDS)
            assert(false) { "Expected exception was not thrown" }
        } catch (e: Exception) {
        }
        
        verify { passwordService.recoverPassword(request) }
    }

    @Test
    fun `test resetPassword returns token result`() {
        // Arrange
        val request = ResetPasswordRequest(
            code = "reset-code-123",
            password = "NewPassword123!"
        )
        val tokenResult = TokenResult(
            tokenType = "type",
            refreshToken = "",
            accessToken = "",
            expiresIn = 3,
            scope = "",
            onboard = false,
            lastLoginDate = 1234,
            validationToken = "",
            isTempPassword = false,
            isLinkedin = false,
            adminAuthorities = listOf(),
            userAccess = listOf(),
            userVisibilities = listOf(),
            companyName = "",
            bandName = "",
            profilePhotoUrl = "",
            aiMessageCount = 123,
            userRoles = listOf(),
            loginToken = "",
            isFirstTimeLogin = false,
            showOnboardingDashboard = false,
            onboardingSwitchAvailable = false,
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            companyLogo = "",
            isPartnerUser = true
        )
        
        every { passwordService.resetPassword(request) } returns tokenResult
        
        // Act
        val result = passwordFacade.resetPassword(request).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(tokenResult.accessToken, result.accessToken)
        assertEquals(tokenResult.refreshToken, result.refreshToken)
        assertEquals(tokenResult.expiresIn, result.expiresIn)
        verify { passwordService.resetPassword(request) }
    }

    @Test
    fun `test resetPassword handles service exception`() {
        // Arrange
        val request = ResetPasswordRequest(
            code = "invalid-code",
            password = "NewPassword123!"
        )
        val errorMessage = "Invalid reset code"
        
        every { passwordService.resetPassword(request) } throws RuntimeException(errorMessage)
        
        // Act & Assert
        try {
            passwordFacade.resetPassword(request).get(5, TimeUnit.SECONDS)
            assert(false) { "Expected exception was not thrown" }
        } catch (e: Exception) {
        }
        
        verify { passwordService.resetPassword(request) }
    }

    @Test
    fun `test loginAccountUuidApproval returns success status`() {
        // Arrange
        val uuid = "account-uuid-123"
        
        every { passwordService.loginAccountUuidApproval(uuid) } returns true
        
        // Act
        val result = passwordFacade.loginAccountUuidApproval(uuid).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertTrue(result)
        verify { passwordService.loginAccountUuidApproval(uuid) }
    }

    @Test
    fun `test loginAccountUuidApproval handles service exception`() {
        // Arrange
        val uuid = "invalid-uuid"
        val errorMessage = "Invalid UUID"
        
        every { passwordService.loginAccountUuidApproval(uuid) } throws RuntimeException(errorMessage)
        
        // Act & Assert
        try {
            passwordFacade.loginAccountUuidApproval(uuid).get(5, TimeUnit.SECONDS)
            assert(false) { "Expected exception was not thrown" }
        } catch (e: Exception) {
        }
        
        verify { passwordService.loginAccountUuidApproval(uuid) }
    }
}