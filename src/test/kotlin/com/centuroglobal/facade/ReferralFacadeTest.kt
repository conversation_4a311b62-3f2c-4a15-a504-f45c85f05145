package com.centuroglobal.facade

import com.centuroglobal.service.ReferralCodeService
import com.centuroglobal.shared.data.pojo.Referral
import com.centuroglobal.shared.data.pojo.ReferralResponse
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.TimeUnit

class ReferralFacadeTest {

    private lateinit var referralCodeService: ReferralCodeService
    private lateinit var referralFacade: ReferralFacade

    @BeforeEach
    fun setup() {
        referralCodeService = mockk()
        referralFacade = ReferralFacade(referralCodeService)
    }

    @Test
    fun `test getReferralCode returns referral response with code`() {
        // Arrange
        val userId = 123L
        val loginUserId = 456L
        val referralCode = "ABC123"
        val referralLink = "https://example.com/refer/ABC123"
        val referrerName = "John <PERSON>"
        
        val referral = Referral(
            referralCode = referralCode,
            referralLink = referralLink,
            referrerName = referrerName
        )
        
        every { 
            referralCodeService.getReferralCode(userId, loginUserId) 
        } returns referral
        
        // Act
        val result = referralFacade.getReferralCode(userId, loginUserId).get(5, TimeUnit.SECONDS)
        
        // Assert
        assertEquals(referralCode, result.data.referralCode)
        assertEquals(referralLink, result.data.referralLink)
        assertEquals(referrerName, result.data.referrerName)
        verify { referralCodeService.getReferralCode(userId, loginUserId) }
    }

    @Test
    fun `test getReferralCode handles service exception`() {
        // Arrange
        val userId = 123L
        val loginUserId = 456L
        val errorMessage = "User not found"
        
        every { 
            referralCodeService.getReferralCode(userId, loginUserId) 
        } throws RuntimeException(errorMessage)


        
        // Act & Assert
        try {
            referralFacade.getReferralCode(userId, loginUserId).get(5, TimeUnit.SECONDS)
            assert(false) { "Expected exception was not thrown" }
        } catch (e: Exception) {

        }
        
        verify { referralCodeService.getReferralCode(userId, loginUserId) }
    }
}