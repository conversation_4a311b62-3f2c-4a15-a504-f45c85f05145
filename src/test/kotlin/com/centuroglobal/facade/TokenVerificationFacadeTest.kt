package com.centuroglobal.facade

import com.centuroglobal.service.TokenVerificationService
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.VerifiedUser
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.Mockito.verify
import java.util.concurrent.CompletableFuture
import org.junit.jupiter.api.Assertions.assertEquals

class TokenVerificationFacadeTest {

    private lateinit var tokenVerificationService: TokenVerificationService
    private lateinit var tokenVerificationFacade: TokenVerificationFacade

    @BeforeEach
    fun setUp() {
        tokenVerificationService = Mockito.mock(TokenVerificationService::class.java)
        tokenVerificationFacade = TokenVerificationFacade(tokenVerificationService)
    }

    @Test
    fun `validateCode should return CompletableFuture with VerifiedUser`() {
        // Given
        val code = "test-code"
        val expectedUser = VerifiedUser(
            email = "<EMAIL>",
            firstName = "",
            lastName = "",
            userType = UserType.CORPORATE,
            jobTitle = "",
            metadata = mapOf(),
            termsAndCondition = "",
            contractDetails = null
        )
        `when`(tokenVerificationService.validateCode(code)).thenReturn(expectedUser)

        // When
        val result = tokenVerificationFacade.validateCode(code)

        // Then
        val actualUser = result.get()
        verify(tokenVerificationService).validateCode(code)
        assertEquals(expectedUser, actualUser)
    }

    @Test
    fun `validateCode should handle null code`() {
        // Given
        val code: String? = null
        val expectedUser = VerifiedUser(
            email = "<EMAIL>",
            firstName = "",
            lastName = "",
            userType = UserType.CORPORATE,
            jobTitle = "",
            metadata = mapOf(),
            termsAndCondition = "",
            contractDetails = null
        )
        `when`(tokenVerificationService.validateCode(code)).thenReturn(expectedUser)

        // When
        val result = tokenVerificationFacade.validateCode(code)

        // Then
        val actualUser = result.get()
        verify(tokenVerificationService).validateCode(code)
        assertEquals(expectedUser, actualUser)
    }
}