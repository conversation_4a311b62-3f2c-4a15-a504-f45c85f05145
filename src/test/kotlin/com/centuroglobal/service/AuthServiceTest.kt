package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.JwtClaim
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.JwtHelper
import com.centuroglobal.shared.service.aws.AwsS3Service
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.repository.findByIdOrNull
import java.time.LocalDateTime
import java.util.*

class AuthServiceTest {

    private lateinit var authService: AuthService
    private val corporateUserRepository: CorporateUserRepository = mockk()
    private val bandsDetailsRepository: BandsDetailsRepository = mockk()
    private val partnerUserRepository: LoginAccountRepository = mockk()
    private val userRoleRepository: UserRoleRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val loginHistoryRepository: LoginHistoryRepository = mockk()
    private val awsS3Service: AwsS3Service = mockk()
    private val jwtHelper: JwtHelper = mockk()
    private val partnerRepository: PartnerRepository = mockk()

    @BeforeEach
    fun setup() {
        authService = AuthService(
            corporateUserRepository,
            bandsDetailsRepository,
            partnerUserRepository,
            userRoleRepository,
            loginAccountRepository,
            loginHistoryRepository,
            awsS3Service,
            jwtHelper,
            partnerRepository
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `getUserAccess should return access details for ROLE_CORPORATE`() {
        val userId = 1L
        val role = Role.ROLE_CORPORATE.name
        val corporateUser = mockk<CorporateUserEntity>()
        val band = mockk<BandsEntity>()
        val bandDetail1 = mockk<BandDetailsEntity>()
        val bandDetail2 = mockk<BandDetailsEntity>()
        val access1 = mockk<MicroAccessMasterEntity>()
        val access2 = mockk<MicroAccessMasterEntity>()
        
        every { corporateUser.corporate.name } returns "Test Corp"
        every { corporateUser.band } returns band
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUser)
        every { band.id } returns 1L
        every { band.name } returns "Premium Band"
        
        every { access1.featureKey } returns "FEATURE_1"
        every { access1.accessLevel } returns "READ"
        every { access2.featureKey } returns "FEATURE_1"
        every { access2.accessLevel } returns "WRITE"
        
        every { bandDetail1.access } returns access1
        every { bandDetail2.access } returns access2
        
        every { bandsDetailsRepository.findByBandId(any()) } returns listOf(bandDetail1, bandDetail2)

        val result = authService.getUserAccess(userId, role)

        assertNotNull(result)
        assertEquals("Test Corp", result!!.third)
        assertEquals("Premium Band", result.second)
        assertEquals(1, result.first.size)
        assertEquals("FEATURE_1", result.first[0].feature)
        assertEquals(2, result.first[0].accesses.size)
        assertTrue(result.first[0].accesses.contains("READ"))
        assertTrue(result.first[0].accesses.contains("WRITE"))
    }

    @Test
    fun `getUserAccess should return access details for ROLE_PARTNER`() {
        val userId = 1L
        val role = Role.ROLE_PARTNER.name
        val partnerUser = mockk<LoginAccountEntity>()
        val band = mockk<BandsEntity>()
        val bandDetail = mockk<BandDetailsEntity>()
        val access = mockk<MicroAccessMasterEntity>()
        
        every { partnerUser.partner?.name } returns "Test Partner"
        every { partnerUser.partnerBand } returns band
        every { partnerUserRepository.findById(userId) } returns Optional.of(partnerUser)
        every { band.id } returns 1L
        every { band.name } returns "Partner Band"
        
        every { access.featureKey } returns "FEATURE_1"
        every { access.accessLevel } returns "FULL"
        
        every { bandDetail.access } returns access
        
        every { bandsDetailsRepository.findByBandId(any()) } returns listOf(bandDetail)

        val result = authService.getUserAccess(userId, role)

        assertNotNull(result)
        assertEquals("Test Partner", result!!.third)
        assertEquals("Partner Band", result.second)
        assertEquals(1, result.first.size)
        assertEquals("FEATURE_1", result.first[0].feature)
        assertEquals(1, result.first[0].accesses.size)
        assertEquals("FULL", result.first[0].accesses[0])
    }

    @Test
    fun `getUserAccess should return null for unsupported role`() {
        val userId = 1L
        val role = "UNSUPPORTED_ROLE"

        val result = authService.getUserAccess(userId, role)

        assertNull(result)
    }

    @Test
    fun `getUserAccess should throw ApplicationException when user not found`() {
        val userId = 1L
        val role = Role.ROLE_CORPORATE.name
        
        every { corporateUserRepository.findById(userId) } returns Optional.empty()

        assertThrows<ApplicationException> {
            authService.getUserAccess(userId, role)
        }.also { exception ->
            assertEquals(ErrorCode.NOT_FOUND, exception.error)
        }
    }

    @Test
    fun `getUserVisibilities should return visibility details for ROLE_CORPORATE`() {
        val userId = 1L
        val role = Role.ROLE_CORPORATE.name
        val corporateUser = mockk<CorporateUserEntity>()
        val band = mockk<BandsEntity>()
        val bandDetail1 = mockk<BandDetailsEntity>()
        val bandDetail2 = mockk<BandDetailsEntity>()
        val visibility1 = mockk<VisibilityMasterEntity>()
        val visibility2 = mockk<VisibilityMasterEntity>()
        
        every { corporateUser.corporate.name } returns "Test Corp"
        every { corporateUser.band } returns band
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUser)
        every { band.id } returns 1L
        
        every { visibility1.featureKey } returns "FEATURE_1"
        every { visibility1.visibility } returns "OWN"
        every { visibility2.featureKey } returns "FEATURE_1"
        every { visibility2.visibility } returns "TEAM"
        
        every { bandDetail1.visibility } returns visibility1
        every { bandDetail2.visibility } returns visibility2
        
        every { bandsDetailsRepository.findByBandId(any()) } returns listOf(bandDetail1, bandDetail2)

        val result = authService.getUserVisibilities(userId, role)

        assertNotNull(result)
        assertEquals(1, result!!.size)
        assertEquals("FEATURE_1", result[0].feature)
        assertEquals(2, result[0].accesses.size)
        assertTrue(result[0].accesses.contains("OWN"))
        assertTrue(result[0].accesses.contains("TEAM"))
    }

    @Test
    fun `getUserVisibilities should return null for unsupported role`() {
        val userId = 1L
        val role = "UNSUPPORTED_ROLE"

        val result = authService.getUserVisibilities(userId, role)

        assertNull(result)
    }

    @Test
    fun `getUserRoles should return list of user roles`() {
        val userId = 1L
        val userRole1 = mockk<UserRoleEntity>()
        val userRole2 = mockk<UserRoleEntity>()
        val role1 = mockk<Role>()
        val role2 = mockk<Role>()
        
        every { role1.name } returns "ROLE_1"
        every { role2.name } returns "ROLE_2"
        every { userRole1.role } returns role1
        every { userRole2.role } returns role2
        every { userRoleRepository.findAllByUserId(userId) } returns listOf(userRole1, userRole2)

        val result = authService.getUserRoles(userId)

        assertEquals(2, result.size)
        assertTrue(result.contains("ROLE_1"))
        assertTrue(result.contains("ROLE_2"))
    }

    @Test
    fun `generateTokenResult should return null when userId or role is null`() {
        val claims = mutableMapOf<String, Any?>()
        val refreshToken = "refresh-token"
        val lastLoginDate = LocalDateTime.now()
        
        val result1 = authService.generateTokenResult(claims, refreshToken, lastLoginDate, null, "ROLE", null)
        val result2 = authService.generateTokenResult(claims, refreshToken, lastLoginDate, 1L, null, null)

        assertNull(result1)
        assertNull(result2)
    }

    @Test
    fun `generateTokenResult should generate token for corporate user`() {
        val userId = 1L
        val role = Role.ROLE_CORPORATE.name
        val claims = mutableMapOf<String, Any?>()
        val refreshToken = "refresh-token"
        val lastLoginDate = LocalDateTime.now()
        val loginToken = "login-token"
        
        val user = mockk<CorporateUserEntity>(relaxed = true)
        val corporate = mockk<CorporateEntity>(relaxed = true)
        val partner = mockk<PartnerEntity>(relaxed = true)

        val userAccess = Triple(
            listOf(UserAccess("FEATURE_1", mutableListOf("READ", "WRITE"))),
            "Premium Band",
            "Test Corp"
        )
        val visibilities = listOf(UserAccess("FEATURE_1", mutableListOf("OWN", "TEAM")))
        val userRoles = listOf("ROLE_1", "ROLE_2")
        val tokenResult = mockk<TokenResult>(relaxed = true)

        every { user.id } returns userId
        every { user.corporate } returns corporate
        every { user.profilePhotoUrl } returns "profile-photo.jpg"
        every { user.questionsQuota } returns 10
        every { user.showOnboardingDashboard } returns true
        every { user.onboard } returns true
        every { user.partner } returns null

        every { corporate.name } returns "Test Corp"
        every { corporate.partner } returns partner

        every { partner.id } returns 2L

        every { loginAccountRepository.findById(userId) } returns Optional.of(user)
        every { loginHistoryRepository.countByUserId(userId) } returns 5
        
        val spyAuthService = spyk(authService)
        every { spyAuthService.getUserAccess(userId, role) } returns userAccess
        every { spyAuthService.getUserVisibilities(userId, role) } returns visibilities
        every { spyAuthService.getUserRoles(userId) } returns userRoles
        
        every { awsS3Service.getS3Url("profile-photo.jpg") } returns "https://s3.example.com/profile-photo.jpg"

        val partnerEntity = mockk<PartnerEntity>(relaxed = true)
        every { partnerEntity.companyLogo } returns "partner-logo.jpg"
        every { partnerRepository.findByIdOrNull(2L) } returns partnerEntity

        every { awsS3Service.getS3Url("partner-logo.jpg") } returns "https://s3.example.com/partner-logo.jpg"
        
        every { jwtHelper.generateTokenResult(refreshToken, any(), false, lastLoginDate) } returns tokenResult

        val result = spyAuthService.generateTokenResult(claims, refreshToken, lastLoginDate, userId, role, loginToken, user)

        assertNotNull(result)
        verify {
            tokenResult.userAccess = userAccess.first
            tokenResult.userVisibilities = visibilities
            tokenResult.bandName = userAccess.second
            tokenResult.companyName = userAccess.third
            tokenResult.profilePhotoUrl = "https://s3.example.com/profile-photo.jpg"
            tokenResult.aiMessageCount = 10
            tokenResult.userRoles = userRoles
            tokenResult.loginToken = loginToken
            tokenResult.showOnboardingDashboard = true
            tokenResult.onboardingSwitchAvailable = true
            tokenResult.onboard = true
            tokenResult.companyLogo = "https://s3.example.com/partner-logo.jpg"
            tokenResult.isPartnerUser = true
        }
        
        assertTrue(claims.containsKey(JwtClaim.USER_ACCESS.key))
        assertTrue(claims.containsKey(JwtClaim.USER_VISIBILITY.key))
        assertTrue(claims.containsKey(JwtClaim.PARTNER_ID.key))
    }

    @Test
    fun `generateTokenResult should generate token for expert user`() {
        val userId = 1L
        val role = Role.ROLE_EXPERT.name
        val claims = mutableMapOf<String, Any?>()
        val refreshToken = "refresh-token"
        val lastLoginDate = LocalDateTime.now()
        val loginToken = "login-token"
        
        val expertUser = mockk<ExpertUserEntity>()
        val companyProfile = mockk<ExpertCompanyProfileEntity>()
        val userAccess = Triple(
            listOf(UserAccess("FEATURE_1", mutableListOf("READ"))),
            "Expert Band",
            "Expert Company"
        )
        val visibilities = listOf(UserAccess("FEATURE_1", mutableListOf("OWN")))
        val userRoles = listOf("ROLE_EXPERT")
        val tokenResult = mockk<TokenResult>(relaxed = true)
        
        every { expertUser.id } returns userId
        every { expertUser.companyProfile } returns companyProfile
        every { companyProfile.invitedBy } returns 3L
        every { expertUser.profilePhotoUrl } returns null
        every { expertUser.questionsQuota } returns 5
        every { expertUser.showOnboardingDashboard } returns false
        every { expertUser.onboard } returns false
        every { expertUser.partner } returns null
        
        every { loginAccountRepository.findById(userId) } returns Optional.of(expertUser)
        every { loginHistoryRepository.countByUserId(userId) } returns 2

        val spyAuthService = spyk(authService)
        every { spyAuthService.getUserAccess(userId, role) } returns userAccess
        every { spyAuthService.getUserVisibilities(userId, role) } returns visibilities
        every { spyAuthService.getUserRoles(userId) } returns userRoles
        
        every { partnerRepository.findByIdOrNull(3L) } returns null
        
        every { jwtHelper.generateTokenResult(refreshToken, any(), false, lastLoginDate) } returns tokenResult

        val result = spyAuthService.generateTokenResult(claims, refreshToken, lastLoginDate, userId, role, loginToken)

        assertNotNull(result)
        verify {
            tokenResult.userAccess = userAccess.first
            tokenResult.userVisibilities = visibilities
            tokenResult.bandName = userAccess.second
            tokenResult.companyName = userAccess.third
            tokenResult.profilePhotoUrl = null
            tokenResult.aiMessageCount = 5
            tokenResult.userRoles = userRoles
            tokenResult.loginToken = loginToken
            tokenResult.showOnboardingDashboard = false
            tokenResult.onboardingSwitchAvailable = false
            tokenResult.onboard = false
            tokenResult.companyLogo = null
            tokenResult.isPartnerUser = true
        }
        
        assertTrue(claims.containsKey(JwtClaim.USER_ACCESS.key))
        assertTrue(claims.containsKey(JwtClaim.USER_VISIBILITY.key))
        assertTrue(claims.containsKey(JwtClaim.PARTNER_ID.key))
    }

    @Test
    fun `generateTokenResult should handle partner user with cases managed`() {
        val userId = 1L
        val role = Role.ROLE_PARTNER.name
        val claims = mutableMapOf<String, Any?>()
        val refreshToken = "refresh-token"
        val lastLoginDate = LocalDateTime.now()
        val loginToken = "login-token"

        val partner = mockk<PartnerEntity>(relaxed = true)

        every { partnerRepository.findById(any()) } returns Optional.of(partner)
        
        val partnerUser = mockk<LoginAccountEntity>(relaxed = true)
        val userAccess = Triple(
            listOf(UserAccess("FEATURE_1", mutableListOf("FULL"))),
            "Partner Band",
            "Partner Company"
        )
        val visibilities = listOf(UserAccess("FEATURE_1", mutableListOf("FULL")))
        val userRoles = listOf("ROLE_PARTNER")
        val tokenResult = mockk<TokenResult>(relaxed = true)
        
        every { partnerUser.id } returns userId
        every { partnerUser.partner } returns partner
        every { partner.id } returns 4L
        every { partner.casesManaged } returns PartnerCaseType.PARTNER
        every { partner.queriesManaged } returns PartnerCaseType.PARTNER
        every { partner.companyLogo } returns "partner-logo.jpg"
        
        every { partnerUser.profilePhotoUrl } returns "profile-photo.jpg"
        every { partnerUser.questionsQuota } returns 20
        every { partnerUser.showOnboardingDashboard } returns true
        every { partnerUser.onboard } returns true
        
        every { loginAccountRepository.findById(userId) } returns Optional.of(partnerUser)
        every { loginHistoryRepository.countByUserId(userId) } returns 8

        val spyAuthService = spyk(authService)
        every { spyAuthService.getUserAccess(userId, role) } returns userAccess
        every { spyAuthService.getUserVisibilities(userId, role) } returns visibilities
        every { spyAuthService.getUserRoles(userId) } returns userRoles
        
        every { awsS3Service.getS3Url("profile-photo.jpg") } returns "https://s3.example.com/profile-photo.jpg"
        every { awsS3Service.getS3Url("partner-logo.jpg") } returns "https://s3.example.com/partner-logo.jpg"
        
        every { jwtHelper.generateTokenResult(refreshToken, any(), false, lastLoginDate) } returns tokenResult

        val result = spyAuthService.generateTokenResult(claims, refreshToken, lastLoginDate, userId, role, loginToken)

        assertNotNull(result)
        verify {
            tokenResult.userAccess = userAccess.first
            tokenResult.userVisibilities = visibilities
            tokenResult.bandName = userAccess.second
            tokenResult.companyName = userAccess.third
            tokenResult.profilePhotoUrl = "https://s3.example.com/profile-photo.jpg"
            tokenResult.aiMessageCount = 20
            tokenResult.userRoles = userRoles
            tokenResult.loginToken = loginToken
            tokenResult.showOnboardingDashboard = true
            tokenResult.onboardingSwitchAvailable = true
            tokenResult.onboard = true
            tokenResult.casesManagedBy = PartnerCaseType.PARTNER
            tokenResult.queryManagedBy = PartnerCaseType.PARTNER
            tokenResult.companyLogo = "https://s3.example.com/partner-logo.jpg"
            tokenResult.isPartnerUser = false
        }
    }

    @Test
    fun `generateTokenResult should throw ApplicationException when user not found`() {
        val userId = 1L
        val role = Role.ROLE_CORPORATE.name
        val claims = mutableMapOf<String, Any?>()
        val refreshToken = "refresh-token"
        val lastLoginDate = LocalDateTime.now()
        val loginToken = "login-token"
        
        every { loginAccountRepository.findById(userId) } returns Optional.empty()

        val spyAuthService = spyk(authService)
        every { spyAuthService.getUserAccess(userId, role) } returns Triple(emptyList(), null, null)
        every { spyAuthService.getUserVisibilities(userId, role) } returns emptyList()
        every { spyAuthService.getUserRoles(userId) } returns emptyList()

        assertThrows<ApplicationException> {
            spyAuthService.generateTokenResult(claims, refreshToken, lastLoginDate, userId, role, loginToken)
        }.also { exception ->
            assertEquals(ErrorCode.NOT_FOUND, exception.error)
        }
    }
}