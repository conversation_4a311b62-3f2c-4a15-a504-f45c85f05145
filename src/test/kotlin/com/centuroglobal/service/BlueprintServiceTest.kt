package com.centuroglobal.service

import com.centuroglobal.data.payload.blueprint.UpdateBlueprintRequest
import com.centuroglobal.shared.data.entity.BlueprintEntity
import com.centuroglobal.shared.data.entity.BlueprintStepEntity
import com.centuroglobal.shared.data.entity.FeatureSessionEntity
import com.centuroglobal.shared.data.entity.view.BlueprintCountryView
import com.centuroglobal.shared.data.enums.BlueprintActionStatus
import com.centuroglobal.shared.data.enums.BlueprintStatus
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.StepName
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.pojo.blueprint.BlueprintCountrySearchFilter
import com.centuroglobal.shared.repository.BlueprintRepository
import com.centuroglobal.shared.repository.BlueprintStepRepository
import com.centuroglobal.shared.repository.FeatureSessionRepository
import com.centuroglobal.shared.repository.view.BlueprintCountryViewRepository
import com.centuroglobal.shared.service.CountryService
import com.centuroglobal.util.UserProfileUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import mu.KotlinLogging
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import java.time.LocalDateTime
import java.util.*

private val log = KotlinLogging.logger {}
private const val DEFAULT_BLUEPRINT_TEMPLATE = "ZZ"

class BlueprintServiceTest {

    private val countryCode = "US"
    private val freeBlueprints:List<String> = listOf(countryCode)

    private val blueprintRepository:BlueprintRepository=mockk()
    private val blueprintExpertService:BlueprintExpertService=mockk()
    private val blueprintAuditService:BlueprintAuditService=mockk()
    private val blueprintPdfService:BlueprintPdfService=mockk()
    private val countryService:CountryService=mockk()
    private val blueprintStepRepository:BlueprintStepRepository=mockk()
    private val blueprintCountryViewRepository:BlueprintCountryViewRepository=mockk()
    private val expertProfileSummary:ExpertProfileSummary=mockk()
    private val featureSessionRepository:FeatureSessionRepository=mockk()
    private val userProfileUtil:UserProfileUtil=mockk()

    private val bluePrintService=BlueprintService(
        freeBlueprints,
        blueprintRepository,
        blueprintExpertService,
        blueprintAuditService,
        blueprintPdfService,
        countryService,
        blueprintStepRepository,
        blueprintCountryViewRepository,
        featureSessionRepository = featureSessionRepository,
        userProfileUtil = userProfileUtil
    )

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    private val countryRegion=CountryRegion(
        1,
        "US",
        "007"
    )

    private val country=Country(
        "US",
        "US",
        "007",
        listOf(countryRegion)
    )

    private val stepName=StepName.STEP_1

    private val bluePrintStepEntity=BlueprintStepEntity(
        1,
        null,
        stepName,
        "test",
        "test",
        1L,
        true,
        1,
        1)

    private val blueprintEntity=BlueprintEntity(
        "US",
        BlueprintStatus.ACTIVE,
        mutableListOf(bluePrintStepEntity),
        1L,
        LocalDateTime.now(),
        1L
    )

    private val ganttChartSetting=GanttChartSetting(
        true,
        1,
        1
    )

    private val stepRequest=StepRequest(
        "STEP_2",
        "test",
        ganttChartSetting
    )

    val blueprintCountryView=BlueprintCountryView(
        "US",
        BlueprintStatus.ACTIVE,
        "1",
        LocalDateTime.now(),
        LocalDateTime.now(),
        false,
        "US"
    )


    @Test
    fun listActiveTest() {

        val blueprintEntity=BlueprintEntity(
            "US",
            BlueprintStatus.ACTIVE,
            mutableListOf(bluePrintStepEntity),
            1L,
            LocalDateTime.now(),
            1L
        )

        every {blueprintRepository.findAllByStatus(BlueprintStatus.ACTIVE)} returns listOf(blueprintEntity)
        every {countryService.retrieveByCountryCode("US")} returns country

        val result=bluePrintService.listActive()
        assertNotNull(result)
        assertEquals(result[0].code,country.code)
        log.debug(result.toString())
    }

    @Test
    fun retrieveBluePrintForAdminTest() {

        val countryCode="US"

        every {countryService.retrieveByCountryCode(countryCode)} returns country
        every {
            blueprintRepository.findAllByCountryCodeIn(
                listOf(
                    countryCode,
                    DEFAULT_BLUEPRINT_TEMPLATE
                )
            )
        } returns listOf(blueprintEntity)
        every {blueprintPdfService.listBlueprintStatus(countryCode)} returns mapOf(Pair(stepName,true))
        every {blueprintExpertService.retrieveStepExpertList(countryCode)} returns mutableMapOf(
            Pair(
                stepName,
                listOf(expertProfileSummary)
            ))

        val result=bluePrintService.retrieveBluePrintForAdmin(countryCode)
        assertNotNull(result)
        assertEquals(result.status,BlueprintStatus.ACTIVE)
        assertEquals(result.countryCode,blueprintEntity.countryCode)
    }

    @Test
    fun retrieveBluePrintTest() {
        val countryCode="US"

        every {countryService.retrieveByCountryCode(countryCode)} returns country
        every {
            blueprintRepository.findByCountryCodeAndStatusIsNot(
                countryCode,
                BlueprintStatus.INACTIVE
            )
        } returns blueprintEntity
        every {blueprintPdfService.listBlueprintStatus(countryCode)} returns mapOf(Pair(stepName,true))
        every {blueprintExpertService.retrieveStepExpertList(countryCode)} returns mutableMapOf(
            Pair(
                stepName,
                listOf(expertProfileSummary)
            )
        )

        every { featureSessionRepository.save(any()) } returns FeatureSessionEntity(
            id = 7,
            referenceId = "4",
            startTime = LocalDateTime.now(),
            endTime = LocalDateTime.now(),
            sessionId = "",
            referenceType = ReferenceType.CASE.toString(),
            type = ""
        )

        val result=bluePrintService.retrieveBluePrint(countryCode,false, UUID.randomUUID().toString())
        assertNotNull(result)
        assertEquals(result.status,BlueprintStatus.ACTIVE)
        assertEquals(result.countryCode,blueprintEntity.countryCode)
    }

    @Test
    fun retrieveTeaserTest() {

        val countryCode="US"
        every {countryService.retrieveByCountryCode(countryCode)} returns country
        every {
            blueprintRepository.findByCountryCodeAndStatusIsNot(
                countryCode,
                BlueprintStatus.INACTIVE
            )
        } returns blueprintEntity

        every { blueprintPdfService.listBlueprintStatus(countryCode) } returns mapOf(Pair(stepName,true))
        every { blueprintExpertService.retrieveStepExpertList(countryCode) } returns mutableMapOf(Pair(stepName,
            listOf(expertProfileSummary)
        ))

        every { featureSessionRepository.save(any()) } returns FeatureSessionEntity(
            id = 7,
            referenceId = "4",
            startTime = LocalDateTime.now(),
            endTime = LocalDateTime.now(),
            sessionId = "",
            referenceType = ReferenceType.CASE.toString(),
            type = ""
        )


        val result=bluePrintService.retrieveTeaser(countryCode)
        assertNotNull(result)
        assertEquals(result.status,BlueprintStatus.ACTIVE)
        assertEquals(result.countryCode,blueprintEntity.countryCode)
    }

    @Test
    fun retrieveTeaserTest_without_country_code() {

        val countryCode="US"
        val freeBlueprints:List<String> = listOf()

        every {countryService.retrieveByCountryCode(countryCode)} returns country
        every {
            blueprintRepository.findByCountryCodeAndStatusIsNot(
                countryCode,
                BlueprintStatus.INACTIVE
            )
        } returns blueprintEntity

        every { blueprintPdfService.listBlueprintStatus(countryCode) } returns mapOf(Pair(stepName,true))
        every { blueprintExpertService.retrieveStepExpertList(countryCode) } returns mutableMapOf(Pair(stepName,
            listOf(expertProfileSummary)
        ))


        every { featureSessionRepository.save(any()) } returns FeatureSessionEntity(
            id = 7,
            referenceId = "4",
            startTime = LocalDateTime.now(),
            endTime = LocalDateTime.now(),
            sessionId = "",
            referenceType = ReferenceType.CASE.toString(),
            type = ""
        )


        val result=bluePrintService.retrieveTeaser(countryCode)
        assertNotNull(result)
        assertEquals(result.status,BlueprintStatus.ACTIVE)
        assertEquals(result.countryCode,blueprintEntity.countryCode)
    }

//    @Test
//    fun updateBlueprintTest_SAVE() {
//
//        val countryCode="US"
//        val userId=1L
//
//        val updateBlueprintRequest=UpdateBlueprintRequest(
//            "SAVE",
//            listOf(stepRequest)
//        )
//
//        every { countryService.retrieveByCountryCode(countryCode)} returns country
//        every { blueprintRepository.findByIdOrNull(countryCode)} returns blueprintEntity
//        every { blueprintRepository.save(blueprintEntity)} returns blueprintEntity
//        every { blueprintAuditService.log(countryCode,userId,BlueprintActionStatus.SAVE)} returns Unit
//
//        val result=bluePrintService.updateBlueprint(countryCode,userId,updateBlueprintRequest)
//        assertNotNull(result)
//    }

    @Test
    fun updateBlueprintTest_ACTIVE() {

        val countryCode="US"
        val userId=1L

        val updateBlueprintRequest=UpdateBlueprintRequest(
            "ACTIVE",
            listOf(stepRequest)
        )

        every { countryService.retrieveByCountryCode(countryCode)} returns country
        every { blueprintRepository.findByIdOrNull(countryCode)} returns blueprintEntity
        every { blueprintRepository.save(blueprintEntity)} returns blueprintEntity
        every { blueprintAuditService.log(countryCode,userId,BlueprintActionStatus.ACTIVE)} returns Unit

        val result=bluePrintService.updateBlueprint(countryCode,userId,updateBlueprintRequest)
        assertNotNull(result)
    }

    @Test
    fun updateBlueprintTest_INACTIVE() {

        val countryCode="US"
        val userId=1L

        val updateBlueprintRequest=UpdateBlueprintRequest(
            "INACTIVE",
            listOf(stepRequest)
        )

        every { countryService.retrieveByCountryCode(countryCode)} returns country
        every { blueprintRepository.findByIdOrNull(countryCode)} returns blueprintEntity
        every { blueprintRepository.save(blueprintEntity)} returns blueprintEntity
        every { blueprintAuditService.log(countryCode,userId,BlueprintActionStatus.INACTIVE)} returns Unit

        val result=bluePrintService.updateBlueprint(countryCode,userId,updateBlueprintRequest)
        assertNotNull(result)
    }

    @Test
    fun updateBlueprintTest_PUBLISH() {

        val countryCode="US"
        val userId=1L

        val updateBlueprintRequest=UpdateBlueprintRequest(
            "PUBLISH",
            listOf(stepRequest)
        )
        every { countryService.retrieveByCountryCode(countryCode)} returns country
        every { blueprintRepository.findByIdOrNull(countryCode)} returns blueprintEntity
        every { blueprintRepository.save(blueprintEntity)} returns blueprintEntity
        every { blueprintAuditService.log(countryCode,userId,BlueprintActionStatus.PUBLISH)} returns Unit

        val result=bluePrintService.updateBlueprint(countryCode,userId,updateBlueprintRequest)
        assertNotNull(result)
    }

    @Test
    fun updateBlueprintTest_DISCARD() {

        val countryCode="US"
        val userId=1L

        val updateBlueprintRequest=UpdateBlueprintRequest(
            "DISCARD",
            listOf(stepRequest)
        )
        every { countryService.retrieveByCountryCode(countryCode)} returns country
        every { blueprintRepository.findByIdOrNull(countryCode)} returns blueprintEntity
        every { blueprintRepository.save(blueprintEntity)} returns blueprintEntity
        every { blueprintAuditService.log(countryCode,userId,BlueprintActionStatus.DISCARD) } returns Unit

        val result=bluePrintService.updateBlueprint(countryCode,userId,updateBlueprintRequest)
        assertNotNull(result)
    }

    @Test
    fun retrieveCountryListTest(){

        val countryCode = "US"
        val countryCodes = mutableSetOf("US")
        val pageRequest = PageRequest.of(1, 10)
        val blueprintCountrySearchFilter = BlueprintCountrySearchFilter(
            BlueprintStatus.ACTIVE,
            LocalDateTime.now(),
            LocalDateTime.now(),
            "US")

        every { countryService.listCountries(null) } returns listOf(country)
        every { blueprintCountryViewRepository.findStatsByCriteriaByCountryCodesIn(countryCodes,blueprintCountrySearchFilter) } returns listOf(listOf("1","1","1"))
        every { blueprintCountryViewRepository.searchByCriteriaByCountryCodesIn(countryCodes,blueprintCountrySearchFilter,pageRequest) } returns PageImpl(listOf(blueprintCountryView))
        every { countryService.retrieveByCountryCode(countryCode) } returns country
        every { blueprintCountryViewRepository.searchByCriteria(blueprintCountrySearchFilter,pageRequest) } returns PageImpl(
            listOf(blueprintCountryView))

        val result = bluePrintService.retrieveCountryList(blueprintCountrySearchFilter,pageRequest)
        assertNotNull(result)
    }

    @Test
    fun retrieveCountryListTest_Empty_Search(){

        val countryCode = "US"
        val countryCodes = mutableSetOf("US")
        val pageRequest = PageRequest.of(1, 10)
        val blueprintCountrySearchFilter = BlueprintCountrySearchFilter(
            BlueprintStatus.ACTIVE,
            LocalDateTime.now(),
            LocalDateTime.now(),
            null)

        every { countryService.listCountries(null) } returns listOf(country)
//        every { blueprintCountryViewRepository.findStatsByCriteriaByCountryCodesIn(countryCodes,blueprintCountrySearchFilter) } returns listOf(listOf("1","1","1"))
        every { blueprintCountryViewRepository.searchByCriteriaByCountryCodesIn(countryCodes,blueprintCountrySearchFilter,pageRequest) } returns PageImpl(listOf(blueprintCountryView))
        every { countryService.retrieveByCountryCode(countryCode) } returns country
        every { blueprintCountryViewRepository.searchByCriteria(blueprintCountrySearchFilter,pageRequest) } returns PageImpl(
            listOf(blueprintCountryView))
        every { blueprintCountryViewRepository.findStatsByCriteria(blueprintCountrySearchFilter) } returns listOf(listOf("1","1","1"))

        val result = bluePrintService.retrieveCountryList(blueprintCountrySearchFilter,pageRequest)
        assertNotNull(result)
    }


    @Test
fun updateBlueprintStatusTest()
{
    val countryCode = "US"
    val userId = 1L
    val status = "ACTIVE"

    every { blueprintRepository.findByIdOrNull(countryCode) } returns blueprintEntity
    every {  blueprintRepository.save(any()) } returns blueprintEntity
    val result = bluePrintService.updateBlueprintStatus(countryCode,userId,status)
    assertNotNull(result)
}

 @Test
 fun getCategoriesTest(){

     val result = bluePrintService.getCategories()
     assertNotNull(result)
 }

    @Test
    fun retrieveStepTest(){
        val countryCode = "US"
        val userId = 1L
        val status = "ACTIVE"

        every { blueprintStepRepository.findByBlueprintCountryCodeAndStepName(countryCode,stepName) } returns bluePrintStepEntity

        val result = bluePrintService.retrieveStep(countryCode,stepName)
        assertNotNull(result)
        assertEquals(result.stepName,stepName)
    }
}