package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.ClientReferralEntity
import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.enums.LeadStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.pojo.Country
import com.centuroglobal.shared.data.pojo.ExpertProfileSummary
import com.centuroglobal.shared.data.pojo.ExpertSearchFilter
import com.centuroglobal.shared.data.pojo.PagedResult
import com.centuroglobal.shared.data.pojo.referral.ClientReferralCreateRequest
import com.centuroglobal.shared.data.pojo.referral.ClientReferralSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.ClientReferralRepository
import com.centuroglobal.shared.repository.ExpertUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.CountryService
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import java.time.LocalDateTime
import java.util.*

class ClientReferralServiceTest {
    private val clientReferralRepository: ClientReferralRepository = mockk()
    private val countryService: CountryService = mockk()
    private val expertUserRepository: ExpertUserRepository = mockk()
    private val expertUserService: ExpertUserService = mockk()

    private val authenticatedUser: AuthenticatedUser =mockk()
    private val corporateUserEntity: CorporateUserEntity =mockk()
    private val corporateEntity: CorporateEntity =mockk()

    private val clientReferralService = ClientReferralService(
        clientReferralRepository,
        expertUserRepository,
        expertUserService,
        countryService
    )

    @BeforeEach
    fun setup() {
        every {authenticatedUser.userId} returns LOGGED_IN_USER_ID
        every {authenticatedUser.companyId} returns LOGGED_IN_USER_CORPORATE_ID
        every {authenticatedUser.email} returns LOGGED_IN_USER_EMAIL
        every {authenticatedUser.role} returns Role.ROLE_CORPORATE.name

        every {corporateUserEntity.id} returns LOGGED_IN_USER_ID
        every {corporateUserEntity.corporate} returns corporateEntity
        every {corporateEntity.name} returns "corporateName"
        every {corporateEntity.id} returns LOGGED_IN_USER_CORPORATE_ID
    }

    @Test
    fun createReferral() {

        val clientReferralCreateRequest = ClientReferralCreateRequest(
             "title",
             "clientName",
             "clientCountryCode",
             "<EMAIL>",
             "clientCompanyName",
             "*********",
             "description",
             "$",
             "dealValue",
             mutableListOf(1, 2, 3, 4, 5)
        )

        clientReferralCreateRequest.experts.forEach {
            val expertEntity = mockk<ExpertUserEntity>()
            every { expertEntity.id } returns it
            every { expertUserRepository.findByIdOrNull(it) } returns expertEntity
        }

        val experts = clientReferralCreateRequest.experts.map { mockk<ExpertUserEntity>() }.toMutableList()

        experts.forEachIndexed { i, it ->
            every { it.id } returns i+1L
        }


        val clientReferralEntity = ClientReferralEntity(
            2L,
            "title",
            "clientName",
            "clientCountryCode",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            experts,
            experts,
            1L,
            1L
        )

        val rows = mockk<PagedResult<ExpertProfileSummary>>()

        val profileSummary = mockk<ExpertProfileSummary>()

        every { profileSummary.displayName } returns "John Doe"

        every { clientReferralRepository.save(any())} returns clientReferralEntity
        every { clientReferralRepository.findByIdOrNull(clientReferralEntity.id!!) } returns clientReferralEntity
        every { authenticatedUser.userType } returns UserType.EXPERT.name
        every { expertUserService.retrieveProfileSummary(any()) } returns profileSummary
        every { expertUserService.retrieveActiveExpertsSummaryFromIdIn(any(), any(), any()) } returns rows
        every { rows.rows } returns emptyList()

        val response = clientReferralService.createReferral(clientReferralCreateRequest, authenticatedUser)

        assertNotNull(response)
        assertEquals(clientReferralEntity.id, response.id)
        assertEquals(clientReferralEntity.title, response.title)
        assertEquals("John Doe", response.createdBy)
    }

    @Test
    fun updateReferral() {

        val clientReferralCreateRequest = ClientReferralCreateRequest(
            "updated-title",
            "clientName",
            "clientCountryCode",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            mutableListOf(1, 2, 3, 4, 5)
        )

        val experts = clientReferralCreateRequest.experts.map { mockk<ExpertUserEntity>() }.toMutableList()

        experts.forEachIndexed { i, it ->
            every { it.id } returns i+1L
            every { expertUserRepository.findById(i+1L) } returns Optional.of(it)
        }

        val clientReferralEntity = ClientReferralEntity(
            2L,
            "updated-title",
            "clientName",
            "clientCountryCode",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            experts,
            experts,
            1L,
            1L
        )

        val rows = mockk<PagedResult<ExpertProfileSummary>>()

        val profileSummary = mockk<ExpertProfileSummary>()

        every { profileSummary.displayName } returns "John Doe"

        every { clientReferralRepository.save(any())} returns clientReferralEntity
        every { clientReferralRepository.findByIdOrNull(clientReferralEntity.id!!) } returns clientReferralEntity
        every { authenticatedUser.userType } returns UserType.EXPERT.name
        every { authenticatedUser.userId } returns clientReferralEntity.createdBy
        every { expertUserService.retrieveProfileSummary(any()) } returns profileSummary
        every { expertUserService.retrieveActiveExpertsSummaryFromIdIn(any(), any(), any()) } returns rows
        every { rows.rows } returns emptyList()

        val response = clientReferralService.updateReferral(clientReferralEntity.id!!, clientReferralCreateRequest, authenticatedUser)

        assertNotNull(response)
        assertEquals(clientReferralEntity.id, response.id)
        assertEquals(clientReferralEntity.title, response.title)
        assertEquals("John Doe", response.createdBy)
    }

    @Test
    fun delete() {

        val clientReferralEntity = ClientReferralEntity(
            2L,
            "updated-title",
            "clientName",
            "clientCountryCode",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            mutableListOf(),
            mutableListOf(),
            1L,
            1L
        )

        every { clientReferralRepository.findByIdOrNull(clientReferralEntity.id!!) } returns clientReferralEntity
        every { clientReferralRepository.save(any()) } returns clientReferralEntity

        val response = clientReferralService.delete(clientReferralEntity.id!!, authenticatedUser.userId)

        assertNotNull(response)
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, response)

        //save failure scenario
        every { clientReferralRepository.save(any()) } throws RuntimeException()

        assertThrows<ApplicationException> {
            clientReferralService.delete(1, authenticatedUser.userId)
        }
    }

    @Test
    fun search() {

        val clientReferralEntity = ClientReferralEntity(
            2L,
            "updated-title",
            "clientName",
            "US",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            mutableListOf(),
            mutableListOf(),
            1L,
            1L
        )

        val filter = ClientReferralSearchFilter("search-text", "US", 1,
            LocalDateTime.now().minusDays(15), LocalDateTime.now())
        val pageRequest = PageRequest.of(0, 20)

        val page = PageImpl(listOf(clientReferralEntity))

        val expertUser = mockk<ExpertUserEntity>()

        val countryEntity = Country("US", "United States", "+1")

        every { clientReferralRepository.searchByCriteria(arrayOf(
            LeadStatus.ACTIVE,
            LeadStatus.RESOLVED,
            LeadStatus.UNRESOLVED
        ), filter, pageRequest) } returns page

        every { expertUser.displayName } returns "John Doe"
        every { countryService.retrieveByCountryCode("US") } returns countryEntity
        every { expertUserRepository.findById(clientReferralEntity.createdBy) } returns Optional.of(expertUser)

        val response = clientReferralService.search(filter, pageRequest, authenticatedUser)
        assertNotNull(response)
        assertNotNull(response?.rows)

        response?.rows?.forEach {
            assertEquals("0 Experts", it.referredTo)
            assertEquals(clientReferralEntity.id, it.id)
            assertEquals(countryEntity.name, it.clientLocation)
            assertEquals(clientReferralEntity.clientCompanyName, it.companyName)
            assertEquals(expertUser.displayName, it.from)
        }
    }

    @Test
    fun referralMemberSearch() {

        val clientReferralEntity = ClientReferralEntity(
            2L,
            "updated-title",
            "clientName",
            "US",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            mutableListOf(),
            mutableListOf(),
            1L,
            1L
        )

        val filter = ExpertSearchFilter("search-text", "US", 1, listOf(), listOf())
        val pageRequest = PageRequest.of(0, 20)

        val rows = mockk<PagedResult<ExpertProfileSummary>>()

        every { clientReferralRepository.findByIdOrNull(clientReferralEntity.id!!) } returns clientReferralEntity
        every { expertUserService.retrieveActiveExpertsSummaryFromIdIn(any(), any(), any()) } returns rows

        clientReferralService.referralMemberSearch(clientReferralEntity.id!!, filter, pageRequest, authenticatedUser)
    }

    @Test
    fun listingForExpert() {

        val clientReferralEntity = ClientReferralEntity(
            2L,
            "updated-title",
            "clientName",
            "US",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            mutableListOf(),
            mutableListOf(),
            1L,
            1L
        )

        val filter = ClientReferralSearchFilter("search-text", "US", 1,
            LocalDateTime.now().minusDays(15), LocalDateTime.now())
        val pageRequest = PageRequest.of(0, 20)

        val page = PageImpl(listOf(clientReferralEntity))

        val expertUser = mockk<ExpertUserEntity>()

        val countryEntity = Country("US", "United States", "+1")

        val profileSummary = mockk<ExpertProfileSummary>()
        val rows = mockk<PagedResult<ExpertProfileSummary>>()

        every { profileSummary.displayName } returns "John Doe"

        every { clientReferralRepository.searchByCriteriaForExpert(
            10,
            arrayOf(
            LeadStatus.ACTIVE,
            LeadStatus.RESOLVED,
            LeadStatus.UNRESOLVED
        ), filter, pageRequest) } returns page

        every { expertUser.displayName } returns "John Doe"
        every { countryService.retrieveByCountryCode("US") } returns countryEntity
        every { expertUserRepository.findById(clientReferralEntity.createdBy) } returns Optional.of(expertUser)
        every { clientReferralRepository.findByIdOrNull(clientReferralEntity.id!!) } returns clientReferralEntity
        every { authenticatedUser.userType } returns UserType.EXPERT.name
        every { expertUserService.retrieveProfileSummary(any()) } returns profileSummary
        every { expertUserService.retrieveActiveExpertsSummaryFromIdIn(any(), any(), any()) } returns rows
        every { rows.rows } returns emptyList()

        val response = clientReferralService.listingForExpert(filter, pageRequest, authenticatedUser)
        assertNotNull(response)
        assertNotNull(response.rows)

        response.rows.forEach {
            assertEquals(clientReferralEntity.id, it.id)
            assertEquals(countryEntity.code, it.clientCountryCode)
            assertEquals(clientReferralEntity.clientCompanyName, it.clientCompanyName)
        }
    }

    @Test
    fun getReferralDetails() {

        val clientReferralEntity = ClientReferralEntity(
            2L,
            "updated-title",
            "clientName",
            "US",
            "<EMAIL>",
            "clientCompanyName",
            "*********",
            "description",
            "$",
            "dealValue",
            LeadStatus.ACTIVE,
            mutableListOf(),
            mutableListOf(),
            1L,
            1L
        )

        val filter = ClientReferralSearchFilter("search-text", "US", 1,
            LocalDateTime.now().minusDays(15), LocalDateTime.now())
        val pageRequest = PageRequest.of(0, 20)

        val page = PageImpl(listOf(clientReferralEntity))

        val expertUser = mockk<ExpertUserEntity>()

        val countryEntity = Country("US", "United States", "+1")

        val profileSummary = mockk<ExpertProfileSummary>()
        val rows = mockk<PagedResult<ExpertProfileSummary>>()

        every { profileSummary.displayName } returns "John Doe"

        every { clientReferralRepository.searchByCriteriaForExpert(
            10,
            arrayOf(
                LeadStatus.ACTIVE,
                LeadStatus.RESOLVED,
                LeadStatus.UNRESOLVED
            ), filter, pageRequest) } returns page

        every { expertUser.displayName } returns "John Doe"
        every { expertUser.id } returns 10L
        every { countryService.retrieveByCountryCode("US") } returns countryEntity
        every { expertUserRepository.findById(clientReferralEntity.createdBy) } returns Optional.of(expertUser)
        every { clientReferralRepository.findByIdOrNull(clientReferralEntity.id!!) } returns clientReferralEntity
        every { authenticatedUser.userType } returns UserType.EXPERT.name
        every { expertUserService.retrieveProfileSummary(any()) } returns profileSummary
        every { expertUserService.retrieveActiveExpertsSummaryFromIdIn(any(), any(), any()) } returns rows
        every { rows.rows } returns emptyList()
        every { expertUserRepository.getReferenceById(10) } returns expertUser
        every { clientReferralRepository.save(any()) } returns clientReferralEntity


        val response = clientReferralService.getReferralDetails(clientReferralEntity.id, authenticatedUser, true)
        assertNotNull(response)
        assertEquals(clientReferralEntity.id, response.id)
        assertEquals(countryEntity.code, response.clientCountryCode)
        assertEquals(clientReferralEntity.clientCompanyName, response.clientCompanyName)
    }


}