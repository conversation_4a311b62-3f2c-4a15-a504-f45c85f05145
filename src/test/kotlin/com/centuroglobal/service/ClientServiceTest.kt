package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.UserRoleEntity
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.client.ClientSearchFilter
import com.centuroglobal.shared.data.pojo.client.ClientStats
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.service.CountryService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import java.util.*

class ClientServiceTest {

    private lateinit var clientService: ClientService
    private val clientViewRepository: ClientViewRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val tokenVerificationService: TokenVerificationService = mockk()
    private val countryService: CountryService = mockk()
    private val expertiseService: ExpertiseService = mockk()

    private val clientView = ClientView( 1,
        "<EMAIL>",
        "fName",
        AccountStatus.PENDING_VERIFICATION,
        "cName",
        UserType.CORPORATE,
        true,
        LocalDateTime.now(),
        null,
        null,
        1,
        1,
        null,
        null,
        null,
        false,
        null,
        null,
        null,
        null,
        null,
        null,
        null
    )

    @BeforeEach
    fun setup() {

        clientService = ClientService(
            clientViewRepository = clientViewRepository,
            loginAccountRepository = loginAccountRepository,
            tokenVerificationService = tokenVerificationService,
            countryService = countryService,
            expertiseService = expertiseService
        )
    }

    @Test
    fun `listClients should return client listing with stats`() {
        val filter = ClientSearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        val pagedClients = PageImpl(listOf(clientView))
        val stats = ClientStats(users = 1, active = 1, corporate = 1, expert = 0, subscribed = 1)

        every { clientViewRepository.searchByCriteria(filter, pageRequest) } returns pagedClients
        every { clientViewRepository.findStatsByCriteria(filter) } returns listOf()
        every { countryService.retrieveByCountryCode("US") } returns mockk {
            every { name } returns "United States"
        }

        val result = clientService.listClients(filter, pageRequest)

        assertNotNull(result)
        assertEquals(1, result.data.totalElements)
        assertEquals(1, result.data.rows.size)
    }

    @Test
    fun `exportClientList should return all clients with expertise details`() {
        val filter = ClientSearchFilter()
        val pagedClients = PageImpl(listOf(clientView))

        every { clientViewRepository.searchByCriteria(filter, any()) } returns pagedClients
        every { countryService.retrieveByCountryCode("US") } returns mockk {
            every { name } returns "United States"
        }
        every { expertiseService.retrieveExpertiseByIds(listOf(1, 2, 3)) } returns listOf(
            mockk { every { name } returns "Expertise 1" },
            mockk { every { name } returns "Expertise 2" },
            mockk { every { name } returns "Expertise 3" }
        )

        val result = clientService.exportClientList(filter)

        assertNotNull(result)
        assertEquals(1, result.data.totalElements)
        assertEquals(1, result.data.rows.size)

        verify {
            clientViewRepository.searchByCriteria(
                filter, 
                match { 
                    it.pageSize == Integer.MAX_VALUE && 
                    it.pageNumber == 0 && 
                    it.sort.getOrderFor("createdDate")?.direction == Sort.Direction.DESC &&
                    it.sort.getOrderFor("fullName")?.direction == Sort.Direction.ASC
                }
            ) 
        }
    }

    @Test
    fun `exportClientList should handle null expertise IDs`() {
        val filter = ClientSearchFilter()
        val clientWithNoExpertise = clientView.copy(expertiseIds = null)
        val pagedClients = PageImpl(listOf(clientWithNoExpertise))

        every { clientViewRepository.searchByCriteria(filter, any()) } returns pagedClients
        every { countryService.retrieveByCountryCode("US") } returns mockk {
            every { name } returns "United States"
        }

        val result = clientService.exportClientList(filter)

        assertNotNull(result)
        assertEquals(1, result.data.rows.size)
        assertNull(result.data.rows[0].expertise)
    }

    @Test
    fun `retrieveStats should calculate correct statistics`() {
        val filter = ClientSearchFilter()

        every { clientViewRepository.findStatsByCriteria(filter) } returns listOf(
            listOf(1, AccountStatus.ACTIVE.name, UserType.CORPORATE.name, true),
            listOf(1, AccountStatus.ACTIVE.name, UserType.EXPERT.name, true),
            listOf(1, AccountStatus.PENDING_VERIFICATION.name, UserType.CORPORATE.name, false),
            listOf(1, AccountStatus.PENDING_VERIFICATION.name, UserType.EXPERT.name, false)
        )

        val result = clientService.retrieveStats(filter)

        assertEquals(4, result.users)
        assertEquals(2, result.active)
        assertEquals(2, result.corporate)
        assertEquals(2, result.expert)
        assertEquals(2, result.subscribed) // Only active experts and corporates with subscription=true
    }

    @Test
    fun `listClientsReferredBy should return clients referred by specific user`() {
        val referredById = 2L
        val pageRequest = PageRequest.of(0, 10)
        val referredClientView = clientView.copy(referredById = referredById)
        val pagedClients = PageImpl(listOf(referredClientView))

        every { clientViewRepository.findByReferredById(referredById, pageRequest) } returns pagedClients
        every { clientViewRepository.findStatsByReferredById(referredById) } returns listOf()
        every { countryService.retrieveByCountryCode("US") } returns mockk {
            every { name } returns "United States"
        }

        val result = clientService.listClientsReferredBy(referredById, pageRequest)

        assertNotNull(result)
        assertEquals(1, result.data.totalElements)
        assertEquals(1, result.data.rows.size)
    }

    @Test
    fun `retrieveStatsReferredBy should calculate correct statistics for referred clients`() {
        val referredById = 2L

        every { clientViewRepository.findStatsByReferredById(referredById) } returns listOf(
            listOf(1, AccountStatus.ACTIVE.name, UserType.CORPORATE.name, true),
            listOf(1, AccountStatus.ACTIVE.name, UserType.EXPERT.name, true),
            listOf(1, AccountStatus.SUSPENDED.name, UserType.EXPERT.name, true))

        val result = clientService.retrieveStatsReferredBy(referredById)

        assertEquals(3, result.users)
        assertEquals(2, result.active)
        assertEquals(1, result.corporate)
        assertEquals(2, result.expert)
        assertEquals(3, result.subscribed)
    }

    @Test
    fun `resendVerificationCode should resend verification for BACKOFFICE user`() {

        val userId = 1L
        val backofficeUser = mockk<LoginAccountEntity>()
        every { backofficeUser.userRoles } returns mutableListOf(UserRoleEntity(
            id = 1,
            user = mockk(),
            role = Role.ROLE_ADMIN
        ))
        every { backofficeUser.getUserType() } returns UserType.BACKOFFICE

        every { loginAccountRepository.findById(userId) } returns Optional.of(backofficeUser)
        every { tokenVerificationService.resend(userId, ValidationType.INVITE_BACKOFFICE) } returns "Success"

        clientService.resendVerificationCode(userId)

        verify(exactly = 1) { tokenVerificationService.resend(userId, ValidationType.INVITE_BACKOFFICE) }
    }

    @Test
    fun `resendVerificationCode should resend verification for regular EXPERT user`() {
        val userId = 1L
        val expertUser = mockk<ExpertUserEntity> {
            every { companyProfile } returns mockk {
                every { companyType } returns ExpertCompanyType.EXPERT
            }
        }

        every { expertUser.userRoles } returns mutableListOf(UserRoleEntity(
            id = 1,
            user = mockk(),
            role = Role.ROLE_ADMIN
        ))
        every { expertUser.getUserType() } returns UserType.EXPERT

        every { loginAccountRepository.findById(userId) } returns Optional.of(expertUser)
        every { tokenVerificationService.resend(userId, ValidationType.INVITE_EXPERT) } returns "Success"

        clientService.resendVerificationCode(userId)

        verify(exactly = 1) { tokenVerificationService.resend(userId, ValidationType.INVITE_EXPERT) }
    }

    @Test
    fun `resendVerificationCode should resend verification for SUPPLIER EXPERT user`() {
        val userId = 1L
        val expertUser = mockk<ExpertUserEntity> {
            every { companyProfile } returns mockk {
                every { companyType } returns ExpertCompanyType.SUPPLIER
            }
        }

        every { expertUser.userRoles } returns mutableListOf(UserRoleEntity(
            id = 1,
            user = mockk(),
            role = Role.ROLE_ADMIN
        ))
        every { expertUser.getUserType() } returns UserType.EXPERT

        every { loginAccountRepository.findById(userId) } returns Optional.of(expertUser)
        every { tokenVerificationService.resend(userId, ValidationType.INVITE_PARTNER_EXPERT) } returns "Success"

        clientService.resendVerificationCode(userId)

        verify(exactly = 1) { tokenVerificationService.resend(userId, ValidationType.INVITE_PARTNER_EXPERT) }
    }

    @Test
    fun `resendVerificationCode should resend verification for regular CORPORATE user`() {
        val userId = 1L
        val corporateUser = mockk<CorporateUserEntity> {
            every { corporate } returns mockk {
                every { partner } returns null
            }
        }

        every { corporateUser.userRoles } returns mutableListOf(UserRoleEntity(
            id = 1,
            user = mockk(),
            role = Role.ROLE_ADMIN
        ))
        every { corporateUser.getUserType() } returns UserType.CORPORATE

        every { loginAccountRepository.findById(userId) } returns Optional.of(corporateUser)
        every { tokenVerificationService.resend(userId, ValidationType.CORPORATE_SIGNUP) } returns "Success"

        clientService.resendVerificationCode(userId)

        verify(exactly = 1) { tokenVerificationService.resend(userId, ValidationType.CORPORATE_SIGNUP) }
    }

    @Test
    fun `resendVerificationCode should resend verification for PARTNER CORPORATE user`() {
        val userId = 1L
        val corporateUser = mockk<CorporateUserEntity> {
            every { corporate } returns mockk {
                every { partner } returns mockk()
            }
        }

        every { corporateUser.userRoles } returns mutableListOf(UserRoleEntity(
            id = 1,
            user = mockk(),
            role = Role.ROLE_ADMIN
        ))
        every { corporateUser.getUserType() } returns UserType.CORPORATE

        every { loginAccountRepository.findById(userId) } returns Optional.of(corporateUser)
        every { tokenVerificationService.resend(userId, ValidationType.INVITE_PARTNER_CORPORATE) } returns "Success"

        clientService.resendVerificationCode(userId)

        verify(exactly = 1) { tokenVerificationService.resend(userId, ValidationType.INVITE_PARTNER_CORPORATE) }
    }

    @Test
    fun `resendVerificationCode should resend verification for PARTNER user`() {
        val userId = 1L
        val partnerUser = mockk<LoginAccountEntity>()

        every { loginAccountRepository.findById(userId) } returns Optional.of(partnerUser)
        every { tokenVerificationService.resend(userId, ValidationType.PARTNER_SIGNUP) } returns "Success"

        every { partnerUser.userRoles } returns mutableListOf(UserRoleEntity(
            id = 1,
            user = mockk(),
            role = Role.ROLE_ADMIN
        ))
        every { partnerUser.getUserType() } returns UserType.PARTNER

        clientService.resendVerificationCode(userId)

        verify(exactly = 1) { tokenVerificationService.resend(userId, ValidationType.PARTNER_SIGNUP) }
    }

    @Test
    fun `resendVerificationCode should throw exception when user not found`() {
        val userId = 1L

        every { loginAccountRepository.findById(userId) } returns Optional.empty()

        assertThrows<ApplicationException> {
            clientService.resendVerificationCode(userId)
        }
    }

    @Test
    fun `updateUserStatus should update user status successfully`() {
        val userId = 1L
        val newStatus = AccountStatus.ACTIVE
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)

        every { loginAccountRepository.findById(userId) } returns Optional.of(loginAccount)
        every { loginAccountRepository.save(loginAccount) } returns loginAccount

        clientService.updateUserStatus(userId, newStatus)

        verify { loginAccount.status = newStatus }
        verify { loginAccountRepository.save(loginAccount) }
    }

    @Test
    fun `updateUserStatus should throw exception when user not found`() {
        val userId = 1L
        val newStatus = AccountStatus.ACTIVE

        every { loginAccountRepository.findById(userId) } returns Optional.empty()

        assertThrows<ApplicationException> {
            clientService.updateUserStatus(userId, newStatus)
        }
    }
}