package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.entity.ConciergeNotificationEntity
import com.centuroglobal.shared.data.enums.ConciergeNotificationState
import com.centuroglobal.shared.data.payload.ConciergeRequest
import com.centuroglobal.shared.repository.ConciergeNotificationRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ConciergeServiceTest {

    private lateinit var conciergeService: ConciergeService
    private lateinit var conciergeNotificationRepository: ConciergeNotificationRepository

    @BeforeEach
    fun setup() {
        conciergeNotificationRepository = mockk()
        conciergeService = ConciergeService(conciergeNotificationRepository)
    }

    @Test
    fun `createNotification should save notification and return success`() {
        val userId = 1L
        val query = "How can I get help with my visa application?"
        val request = ConciergeRequest(query)
        
        val entitySlot = slot<ConciergeNotificationEntity>()
        
        every { conciergeNotificationRepository.save(capture(entitySlot)) } returns 
            ConciergeNotificationEntity(1L, userId, query, ConciergeNotificationState.CREATED)

        val result = conciergeService.createNotification(request, userId)

        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        val capturedEntity = entitySlot.captured
        assertEquals(userId, capturedEntity.userId)
        assertEquals(ConciergeNotificationState.CREATED, capturedEntity.state)
        
        verify(exactly = 1) { conciergeNotificationRepository.save(any()) }
    }
}