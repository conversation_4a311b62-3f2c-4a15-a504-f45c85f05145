package com.centuroglobal.service

import com.centuroglobal.shared.data.AppConstant.STATIC_PUBLIC_BUCKET
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.ContentLibraryEntity
import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.pojo.ContentLibraryMetadata
import com.centuroglobal.shared.data.pojo.ContentLibraryRequest
import com.centuroglobal.shared.data.pojo.ContentLibrarySearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.ContentLibraryRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.InputStream

class ContentLibraryServiceTest {

    private lateinit var contentLibraryService: ContentLibraryService
    private lateinit var contentLibraryRepository: ContentLibraryRepository
    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var awsS3Service: AwsS3Service
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        contentLibraryRepository = mockk()
        loginAccountRepository = mockk()
        awsS3Service = mockk()
        authenticatedUser = mockk()

        contentLibraryService = ContentLibraryService(
            contentLibraryRepository,
            loginAccountRepository,
            awsS3Service
        )

        every { authenticatedUser.userId } returns 1L
    }

    @Test
    fun `create should save content library item when identifier is not IMAGES`() {
        // Arrange
        val userId = 1L
        val request = ContentLibraryRequest(
            title = "Test Content",
            identifier = "TEST_CONTENT",
            countryCode = "US",
            data = "Test data content",
            isActive = true,
            metadata = listOf(
                ContentLibraryMetadata("key1", "value1", "Label 1"),
                ContentLibraryMetadata("key2", "value2", "Label 2")
            )
        )

        val savedEntity = ContentLibraryEntity(
            id = 1L,
            title = request.title,
            identifier = request.identifier,
            countryCode = request.countryCode,
            data = request.data,
            isActive = request.isActive
        )

        every { contentLibraryRepository.findByIdentifierAndCountryCode(request.identifier, request.countryCode) } returns null
        every { contentLibraryRepository.save(any()) } returns savedEntity

        // Act
        val result = contentLibraryService.create(request, userId)

        // Assert
        assertEquals(1L, result)
        verify {
            contentLibraryRepository.save(match {
                it.title == request.title &&
                it.identifier == request.identifier &&
                it.countryCode == request.countryCode &&
                it.data == request.data &&
                it.isActive == request.isActive &&
                it.metadata.size == 2
            })
        }
    }

    @Test
    fun `create should update existing content library item when it exists`() {
        // Arrange
        val userId = 1L
        val existingId = 5L
        val request = ContentLibraryRequest(
            title = "Test Content",
            identifier = "TEST_CONTENT",
            countryCode = "US",
            data = "Updated data content",
            isActive = true,
            metadata = listOf(
                ContentLibraryMetadata("key1", "value1", "Label 1")
            )
        )

        val existingEntity = ContentLibraryEntity(
            id = existingId,
            title = "Old Title",
            identifier = request.identifier,
            countryCode = request.countryCode,
            data = "Old data",
            isActive = false
        )

        val updatedEntity = ContentLibraryEntity(
            id = existingId,
            title = request.title,
            identifier = request.identifier,
            countryCode = request.countryCode,
            data = request.data,
            isActive = request.isActive
        )

        every { contentLibraryRepository.findByIdentifierAndCountryCode(request.identifier, request.countryCode) } returns existingEntity
        every { contentLibraryRepository.save(any()) } returns updatedEntity

        // Act
        val result = contentLibraryService.create(request, userId)

        // Assert
        assertEquals(existingId, result)
        verify {
            contentLibraryRepository.save(match {
                it.id == existingId &&
                it.title == request.title &&
                it.identifier == request.identifier &&
                it.countryCode == request.countryCode &&
                it.data == request.data &&
                it.isActive == request.isActive
            })
        }
    }

    /*@Test
    fun `create should handle IMAGES identifier by uploading and unzipping files`() {
        // Arrange
        val userId = 1L
        val request = ContentLibraryRequest(
            title = "Images",
            identifier = "IMAGES",
            countryCode = "US",
            data = "temp-file-path",
            isActive = true,
            metadata = emptyList()
        )

        val zipInputStream = mockk<ZipInputStream>()
        val zipEntry1 = mockk<ZipEntry>()
        val zipEntry2 = mockk<ZipEntry>()
        val inputStream = mockk<InputStream>()

        every { awsS3Service.uploadFromTmp(request.data, "playbook/US/US.zip", STATIC_PUBLIC_BUCKET) } returns true
        every { awsS3Service.downLoadFile(STATIC_PUBLIC_BUCKET, "playbook/US/US.zip") } returns inputStream
        every { zipInputStream.nextEntry } returns zipEntry1 andThen zipEntry2 andThen null
        every { zipEntry1.isDirectory } returns false
        every { zipEntry1.name } returns "image1.png"
        every { zipEntry2.isDirectory } returns false
        every { zipEntry2.name } returns "image2.svg"
        
        // Mock for ByteArrayInputStream creation inside unzipFilesToS3
        val byteArray1 = "test1".toByteArray()
        val byteArray2 = "test2".toByteArray()
        
        every { contentLibraryService["getFileBytes"](zipInputStream) } returns byteArray1 andThen byteArray2
        
        every { 
            awsS3Service.uploadFile(
                any<ByteArrayInputStream>(), 
                "playbook/US/image1.png", 
                STATIC_PUBLIC_BUCKET, 
                any(), 
                false, 
                null
            ) 
        } just runs
        
        every { 
            awsS3Service.uploadFile(
                any<ByteArrayInputStream>(), 
                "playbook/US/image2.svg", 
                STATIC_PUBLIC_BUCKET, 
                any(), 
                false, 
                "image/svg+xml"
            ) 
        } just runs

        // Act
        val result = contentLibraryService.create(request, userId)

        // Assert
        assertEquals(0L, result)
        verify {
            awsS3Service.uploadFromTmp(request.data, "playbook/US/US.zip", STATIC_PUBLIC_BUCKET)
            awsS3Service.downLoadFile(STATIC_PUBLIC_BUCKET, "playbook/US/US.zip")
        }
    }*/

    @Test
    fun `list should return paged content library items`() {
        // Arrange
        val filter = ContentLibrarySearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        
        val entity1 = ContentLibraryEntity(
            id = 1L,
            title = "Content 1",
            identifier = "CONTENT_1",
            countryCode = "US",
            data = "Data 1",
            isActive = true
        )
        
        val entity2 = ContentLibraryEntity(
            id = 2L,
            title = "Content 2",
            identifier = "CONTENT_2",
            countryCode = "UK",
            data = "Data 2",
            isActive = false
        )
        
        val page = PageImpl(listOf(entity1, entity2), pageRequest, 2)
        
        every { contentLibraryRepository.searchByCriteria(filter, pageRequest) } returns page
        every { loginAccountRepository.findById(1L).get() } returns ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            expertType = "USER"
        )
        every { loginAccountRepository.findById(2L).get() } returns ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            expertType = "USER"
        )

        // Act
        val result = contentLibraryService.list(filter, pageRequest, authenticatedUser)

        // Assert
        assertEquals(2, result.totalElements)
        assertEquals(2, result.rows.size)
        
        val item1 = result.rows[0]
        assertEquals(1L, item1.id)
        assertEquals("Content 1", item1.title)
        assertEquals("CONTENT_1", item1.identifier)
        assertEquals("US", item1.countryCode)
        assertEquals(true, item1.isActive)

        val item2 = result.rows[1]
        assertEquals(2L, item2.id)
        assertEquals("Content 2", item2.title)
        assertEquals("CONTENT_2", item2.identifier)
        assertEquals("UK", item2.countryCode)
        assertEquals(false, item2.isActive)
    }

    @Test
    fun `delete should delete content library item by id`() {
        // Arrange
        val id = 1L
        
        every { contentLibraryRepository.deleteById(id) } just runs

        // Act
        val result = contentLibraryService.delete(id, authenticatedUser)

        // Assert
        assertTrue(result)
        verify { contentLibraryRepository.deleteById(id) }
    }

    @Test
    fun `getByCountryAndIdentifier should return content library item`() {
        // Arrange
        val country = "US"
        val identifier = "CONTENT_1"
        
        val entity = ContentLibraryEntity(
            id = 1L,
            title = "Content 1",
            identifier = identifier,
            countryCode = country,
            data = "Data 1",
            isActive = true
        )
        entity.updatedBy = 1
        
        every { contentLibraryRepository.findByIdentifierAndCountryCode(identifier, country) } returns entity

        var user = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            expertType = "USER"
        )

        every { loginAccountRepository.findById(1L).get() } returns user

        // Act
        val result = contentLibraryService.getByCountryAndIdentifier(country, identifier)

        // Assert
        assertNotNull(result)
        assertEquals(1L, result.id)
        assertEquals("Content 1", result.title)
        assertEquals(identifier, result.identifier)
        assertEquals(country, result.countryCode)
        assertEquals("Data 1", result.data)
    }

    @Test
    fun `getByCountryAndIdentifier should throw ApplicationException when item not found`() {
        // Arrange
        val country = "US"
        val identifier = "NONEXISTENT"
        
        every { contentLibraryRepository.findByIdentifierAndCountryCode(identifier, country) } returns null

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            contentLibraryService.getByCountryAndIdentifier(country, identifier)
        }
        
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `getUpdatedByName should return user's full name`() {
        // Arrange
        val userId = 1L

        var user = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "*********",
            contactWebsite = "mail.com",
            displayName = "Trial",
            infoVideoUrl = "url.com",
            jobTitle = "Manager",
            expertType = "USER"
        )
        
        every { loginAccountRepository.findById(userId).get() } returns user

        user.firstName = "John"
        user.lastName = "Doe"

        // Act
        val result = contentLibraryService.getUpdatedByName(userId)

        // Assert
        assertEquals("John Doe", result)
    }

    @Test
    fun `identifiers should return list of identifiers excluding TEMPLATE`() {
        // Arrange
        val entities = listOf(
            ContentLibraryEntity(
                identifier = "CONTENT_1",
                id = 3,
                title = "",
                countryCode = "IN",
                data = "",
                isActive = true,
                metadata = mutableListOf()
            ),
            ContentLibraryEntity(identifier = "CONTENT_2",
                id = 3,
                title = "",
                countryCode = "IN",
                data = "",
                isActive = true,
                metadata = mutableListOf()),
            ContentLibraryEntity(identifier = "CONTENT_3",
                id = 3,
                title = "",
                countryCode = "IN",
                data = "",
                isActive = true,
                metadata = mutableListOf())
        )
        
        every { contentLibraryRepository.findByIdentifierNot("TEMPLATE") } returns entities

        // Act
        val result = contentLibraryService.identifiers()

        // Assert
        assertEquals(3, result.size)
        assertTrue(result.contains("CONTENT_1"))
        assertTrue(result.contains("CONTENT_2"))
        assertTrue(result.contains("CONTENT_3"))
    }

    @Test
    fun `updateStatus should update content library item status`() {
        // Arrange
        val country = "US"
        val identifier = "CONTENT_1"
        val newStatus = true
        
        val entity = ContentLibraryEntity(
            id = 1L,
            title = "Content 1",
            identifier = identifier,
            countryCode = country,
            data = "Data 1",
            isActive = false
        )
        
        every { contentLibraryRepository.findByIdentifierAndCountryCode(identifier, country) } returns entity
        every { contentLibraryRepository.save(any()) } returns entity

        // Act
        val result = contentLibraryService.updateStatus(country, identifier, newStatus)

        // Assert
        assertTrue(result!!)
        verify {
            contentLibraryRepository.save(match {
                it.isActive == newStatus
            })
        }
    }

    @Test
    fun `updateStatus should throw ApplicationException when item not found`() {
        // Arrange
        val country = "US"
        val identifier = "NONEXISTENT"
        val newStatus = true
        
        every { contentLibraryRepository.findByIdentifierAndCountryCode(identifier, country) } returns null

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            contentLibraryService.updateStatus(country, identifier, newStatus)
        }
        
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `getActiveCountries should return list of active country codes`() {
        // Arrange
        val entities = listOf(
            ContentLibraryEntity(identifier = "CONTENT_1",
                id = 3,
                title = "",
                countryCode = "US",
                data = "",
                isActive = true,
                metadata = mutableListOf()),
            ContentLibraryEntity(identifier = "CONTENT_1",
                id = 3,
                title = "",
                countryCode = "UK",
                data = "",
                isActive = true,
                metadata = mutableListOf()),
            ContentLibraryEntity(identifier = "CONTENT_1",
                id = 3,
                title = "",
                countryCode = "CA",
                data = "",
                isActive = true,
                metadata = mutableListOf())
        )
        
        every { contentLibraryRepository.findAllByIsActive(true) } returns entities

        // Act
        val result = contentLibraryService.getActiveCountries()

        // Assert
        assertEquals(3, result.size)
        assertTrue(result.contains("US"))
        assertTrue(result.contains("UK"))
        assertTrue(result.contains("CA"))
    }

    @Test
    fun `download should return response entity with streaming response body`() {
        // Arrange
        val countryCode = "US"
        val inputStream = mockk<InputStream>()
        val responseEntity = mockk<ResponseEntity<StreamingResponseBody>>()
        every { responseEntity.statusCode } returns HttpStatus.OK

        every { awsS3Service.downLoadFile(STATIC_PUBLIC_BUCKET, "playbook/$countryCode/$countryCode.zip") } returns inputStream

        // Act
        val result = contentLibraryService.download(countryCode)

        // Assert
        assertEquals(responseEntity.statusCode, result.statusCode)
    }
}