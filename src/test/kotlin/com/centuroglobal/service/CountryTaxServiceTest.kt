package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.CountryTaxEntity
import com.centuroglobal.shared.repository.CountryTaxRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CountryTaxServiceTest {

    private lateinit var countryTaxService: CountryTaxService
    private lateinit var countryTaxRepository: CountryTaxRepository

    @BeforeEach
    fun setup() {
        countryTaxRepository = mockk()
        countryTaxService = CountryTaxService(countryTaxRepository)
    }

    @Test
    fun `getCountryTax should return all country tax entities`() {
        // Arrange
        val countryTaxEntities = listOf(
            CountryTaxEntity(
                id = 1,
                country = "United States",
                countryCode = "US",
                corporateTaxRate = 21,
                employerTaxRate = 745,
                employeeTaxRate = 75,
                infoText = "Federal tax rates"
            ),
            CountryTaxEntity(
                id = 2,
                country = "United Kingdom",
                countryCode = "UK",
                corporateTaxRate = 19,
                employerTaxRate = 13,
                employeeTaxRate = 12,
                infoText = "UK tax rates"
            ),
            CountryTaxEntity(
                id = 3,
                country = "Germany",
                countryCode = "DE",
                corporateTaxRate = 15,
                employerTaxRate = 19,
                employeeTaxRate = 19,
                infoText = "German tax rates"
            )
        )

        every { countryTaxRepository.findAll() } returns countryTaxEntities

        // Act
        val result = countryTaxService.getCountryTax()

        // Assert
        assertNotNull(result)
        assertEquals(3, result.size)
        
        // Verify first entity
        assertEquals(1, result[0].id)
        assertEquals("United States", result[0].country)
        assertEquals("US", result[0].countryCode)
        assertEquals(21, result[0].corporateTaxRate)
        assertEquals(745, result[0].employerTaxRate)
        assertEquals(75, result[0].employeeTaxRate)
        assertEquals("Federal tax rates", result[0].infoText)
        
        // Verify second entity
        assertEquals(2, result[1].id)
        assertEquals("United Kingdom", result[1].country)
        assertEquals("UK", result[1].countryCode)
        
        // Verify third entity
        assertEquals(3, result[2].id)
        assertEquals("Germany", result[2].country)
        assertEquals("DE", result[2].countryCode)
        
        // Verify repository was called
        verify(exactly = 1) { countryTaxRepository.findAll() }
    }

    @Test
    fun `getCountryTax should return empty list when no tax entities exist`() {
        // Arrange
        every { countryTaxRepository.findAll() } returns emptyList()

        // Act
        val result = countryTaxService.getCountryTax()

        // Assert
        assertNotNull(result)
        assertEquals(0, result.size)
        
        // Verify repository was called
        verify(exactly = 1) { countryTaxRepository.findAll() }
    }
}