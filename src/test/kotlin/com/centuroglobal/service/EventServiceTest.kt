package com.centuroglobal.service

import com.centuroglobal.data.payload.event.CreateEventRequest
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.EventEntity
import com.centuroglobal.shared.data.entity.EventInviteeEntity
import com.centuroglobal.shared.data.entity.EventSessionEntity
import com.centuroglobal.shared.data.entity.EventSpeakerEntity
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.payload.event.CreateSessionRequest
import com.centuroglobal.shared.data.pojo.Corporate
import com.centuroglobal.shared.data.pojo.aws.AwsS3FileMetadata
import com.centuroglobal.shared.data.pojo.event.CreateEventSpeaker
import com.centuroglobal.shared.data.pojo.event.EventSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.EventInviteeRepository
import com.centuroglobal.shared.repository.EventRepository
import com.centuroglobal.shared.repository.EventSessionRepository
import com.centuroglobal.shared.repository.EventSpeakerRepository
import com.centuroglobal.shared.repository.view.ClientViewRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime
import java.util.*

class EventServiceTest {

    private lateinit var eventService: EventService
    private lateinit var eventRepository: EventRepository
    private lateinit var eventSpeakerRepository: EventSpeakerRepository
    private lateinit var eventSessionRepository: EventSessionRepository
    private lateinit var eventInviteeRepository: EventInviteeRepository
    private lateinit var expertUserService: ExpertUserService
    private lateinit var corporateService: CorporateService
    private lateinit var awsS3Service: AwsS3Service
    private lateinit var mailSendingService: MailSendingService
    private lateinit var authenticatedUser: AuthenticatedUser
    private lateinit var clientViewRepository: ClientViewRepository

    @BeforeEach
    fun setup() {
        eventRepository = mockk()
        eventSpeakerRepository = mockk()
        eventSessionRepository = mockk()
        eventInviteeRepository = mockk()
        clientViewRepository = mockk()
        expertUserService = mockk()
        corporateService = mockk()
        awsS3Service = mockk()
        mailSendingService = mockk()
        authenticatedUser = mockk(relaxed = true)

        eventService = EventService(
            webUrl = "http://example.com",
            eventBannerFolder = "event-banners",
            eventRepository = eventRepository,
            eventSpeakerRepository = eventSpeakerRepository,
            eventSessionRepository = eventSessionRepository,
            eventInviteeRepository = eventInviteeRepository,
            expertUserService = expertUserService,
            corporateService = corporateService,
            awsS3Service = awsS3Service,
            clientViewRepository = clientViewRepository,
            mailSendingService = mailSendingService
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `createEvent should save event and return details`() {
        val createEventRequest = mockk<CreateEventRequest>(relaxed = true)
        val eventEntity = mockk<EventEntity>(relaxed = true)

        every { eventRepository.save(any<EventEntity>()) } returns eventEntity
        every { authenticatedUser.userId } returns 5


        val result = eventService.createEvent(createEventRequest, authenticatedUser)

        assertNotNull(result)
        verify { eventRepository.save(any<EventEntity>()) }
    }

    @Test
    fun `uploadCoverPicture should upload and update event banner`() {
        val eventId = 1L
        val requestedById = 1L
        val photo = mockk<MultipartFile>(relaxed = true)
        val eventEntity = mockk<EventEntity>(relaxed = true) {
            every { bannerPhotoKey } returns null
        }

        every { eventRepository.findById(eventId) } returns Optional.of(eventEntity)
        every { awsS3Service.uploadFile(any(), any(), false, any<String>()) } returns AwsS3FileMetadata(
            contentType = "image/png",
            fileName = "abc",
            key = "new-banner-key",
            url = "a.com",
            publicAccess = false
        )
        every { awsS3Service.getS3Url(any()) } returns "http://s3.example.com/new-banner-key"
        every { eventRepository.save(any<EventEntity>()) } returns eventEntity

        val result = eventService.uploadCoverPicture(eventId, requestedById, photo)

        assertEquals("new-banner-key", result.bannerPhotoKey)
        assertEquals("http://s3.example.com/new-banner-key", result.bannerPhotoUrl)
        verify { eventRepository.save(eventEntity) }
    }

    @Test
    fun `uploadCoverPicture should throw ApplicationException when event not found`() {
        val eventId = 1L
        val requestedById = 1L
        val photo = mockk<MultipartFile>(relaxed = true)

        every { eventRepository.findByIdOrNull(eventId) } returns null

        val exception = assertThrows<ApplicationException> {
            eventService.uploadCoverPicture(eventId, requestedById, photo)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `deleteEvent should mark event as deleted`() {
        val eventId = 1L
        val eventEntity = mockk<EventEntity>(relaxed = true)

        every { eventRepository.findById(eventId) } returns Optional.of(eventEntity)
        every { eventRepository.save(any<EventEntity>()) } returns eventEntity

        every { authenticatedUser.userId } returns 4

        val result = eventService.deleteEvent(eventId, authenticatedUser)

        assertEquals("Success", result)
        verify { eventRepository.save(eventEntity) }
    }

    @Test
    fun `deleteEvent should throw ApplicationException when event not found`() {
        val eventId = 1L

        every { eventRepository.findByIdOrNull(eventId) } returns null

        val exception = assertThrows<ApplicationException> {
            eventService.deleteEvent(eventId, authenticatedUser)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `uploadProfilePictureForExternalSpeakers should upload and update speaker images`() {
        val eventId = 1L
        val internalIds = listOf("speaker1", "speaker2")
        val photos = listOf(mockk<MultipartFile>(relaxed = true), mockk<MultipartFile>(relaxed = true))
        val speakerEntities = internalIds.map {
            mockk<EventSpeakerEntity>(relaxed = true) {
                every { profilePictureKey } returns null
            }
        }

        every { authenticatedUser.userId } returns 5

        every { eventSpeakerRepository.findFirstByEventIdAndInternalId(eventId, any()) } returnsMany speakerEntities
        every { awsS3Service.uploadFile(any(), any(), any(), any<String>()) } returns mockk {
            every { key } returns "new-profile-key"
        }
        every { awsS3Service.getS3Url(any()) } returns "http://s3.example.com/new-profile-key"
        every { eventSpeakerRepository.save(any<EventSpeakerEntity>()) } returnsMany speakerEntities

        val result = eventService.uploadProfilePictureForExternalSpeakers(eventId, internalIds, authenticatedUser, photos)

        assertEquals(2, result.size)
        verify(exactly = 2) { eventSpeakerRepository.save(any<EventSpeakerEntity>()) }
    }

    @Test
    fun `addUpdateSpeaker should add or update speakers for an event`() {
        val eventId = 1L
        val createEventSpeakers = listOf(
            CreateEventSpeaker(
                id = null, type = EventSpeakerType.EXTERNAL, profile = mockk(relaxed = true),
                internalMemberId = 4,
                internalMemberRole = Role.ROLE_USER,
                isHost = false
            ),
            CreateEventSpeaker(id = 2L, type = EventSpeakerType.INTERNAL, profile = mockk(relaxed = true),
                internalMemberId = 5,
                internalMemberRole = Role.ROLE_USER,
                isHost = false)
        )
        val eventEntity = mockk<EventEntity>(relaxed = true) {
            every { speakers } returns mutableListOf()
        }
        val speakerEntity = mockk<EventSpeakerEntity>(relaxed = true)

        every { eventRepository.findByIdOrNull(eventId) } returns eventEntity
        every { eventSpeakerRepository.findByIdOrNull(2L) } returns speakerEntity
        every { eventSpeakerRepository.saveAll(any<List<EventSpeakerEntity>>()) } returns listOf(speakerEntity)

        every { authenticatedUser.userId } returns 6
        every { eventRepository.save(any()) } returns mockk()

        eventService.addUpdateSpeaker(eventId, createEventSpeakers, authenticatedUser)

        verify { eventSpeakerRepository.saveAll(any<List<EventSpeakerEntity>>()) }
        verify { eventRepository.save(eventEntity) }
    }

    @Test
    fun `addUpdateSession should add or update sessions for an event`() {
        val eventId = 1L
        val createSessionRequest = listOf(
            CreateSessionRequest(
                id = null, name = "Session 1", speakers = mutableListOf(1L, 2L),
                about = "",
                date = Date(),
                startTime = Date(),
                endTime = Date(),
                duration = "5",
                timeZone = ""
            ),
            CreateSessionRequest(id = 2L, name = "Session 2", speakers = mutableListOf(1L, 2L),
                about = "",
                date = Date(),
                startTime = Date(),
                endTime = Date(),
                duration = "5",
                timeZone = "")
        )
        val eventEntity = mockk<EventEntity>(relaxed = true) {
            every { sessions } returns mutableListOf()
        }
        val sessionEntity = mockk<EventSessionEntity>(relaxed = true)

        every { eventRepository.findByIdOrNull(eventId) } returns eventEntity
        every { eventSessionRepository.findByIdOrNull(2L) } returns sessionEntity
        every { eventSessionRepository.saveAll(any<List<EventSessionEntity>>()) } returns listOf(sessionEntity)
        every { eventSpeakerRepository.findAllById(any<List<Long>>()) } returns listOf(mockk(), mockk())
        every { authenticatedUser.userId } returns 4
        every { eventSessionRepository.deleteAll(any()) } returns Unit
        every { eventRepository.save(any()) } returns mockk()

        eventService.addUpdateSession(eventId, createSessionRequest, authenticatedUser)

        verify { eventSessionRepository.saveAll(any<List<EventSessionEntity>>()) }
        verify { eventRepository.save(eventEntity) }
    }

    @Test
    fun `getSessions should return paged result of event sessions`() {
        val eventId = 1L
        val pageable: PageRequest = PageRequest.of(0, 10)
        val sessionEntities = listOf(mockk<EventSessionEntity>(relaxed = true))
        val page = PageImpl(sessionEntities, pageable, sessionEntities.size.toLong())

        every { eventSessionRepository.findAllByEvent_Id(eventId, pageable) } returns page
        every { expertUserService.retrieveProfileSummary(any()) } returns mockk(relaxed = true)
        every { awsS3Service.getS3Url(any()) } returns "http://s3.example.com/profile.jpg"

        val result = eventService.getSessions(eventId, pageable, authenticatedUser)

        assertEquals(1, result.totalElements)
        verify { eventSessionRepository.findAllByEvent_Id(eventId, pageable) }
    }

    @Test
    fun `createInvitees should create invitees and return list`() {
        val eventId = 1L
        val external = "<EMAIL>"
        val internal = listOf(1L, 2L)
        val eventEntity = mockk<EventEntity>(relaxed = true) {
            every { inviteesExternal } returns null
            every { invitees } returns mutableListOf(
                EventInviteeEntity(
                    id = 4,
                    client = ClientView(
                        1,
                        "<EMAIL>",
                        "fName",
                        AccountStatus.PENDING_VERIFICATION,
                        "cName",
                        UserType.EXPERT,
                        true,
                        LocalDateTime.now(),
                        null,
                        null,
                        1,
                        1,
                        null,
                        null,
                        null,
                        false,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                    ),
                    event = mockk(),
                    status = EventInviteeStatus.INVITEE,
                    lastUpdatedBy = 5
                ))
        }
        val inviteeEntities = internal.map { mockk<EventInviteeEntity>(relaxed = true) }

        every { eventRepository.findByIdOrNull(eventId) } returns eventEntity
        every { eventInviteeRepository.deleteAll(any()) } returns Unit
        every { eventRepository.save(any<EventEntity>()) } returns eventEntity
        every { eventInviteeRepository.saveAll(any<List<EventInviteeEntity>>()) } returns inviteeEntities
        every { clientViewRepository.findAllById(any()) } returns mutableListOf(ClientView(
            1,
            "<EMAIL>",
            "fName",
            AccountStatus.PENDING_VERIFICATION,
            "cName",
            UserType.EXPERT,
            true,
            LocalDateTime.now(),
            null,
            null,
            1,
            1,
            null,
            null,
            null,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        ))
        every { authenticatedUser.userId } returns 4
        every { awsS3Service.getS3Url(any()) } returns ""
        every { awsS3Service.getS3PublicUrl(any()) } returns ""
        every { mailSendingService.sendEmail(any()) } returns true

        val result = eventService.createInvitees(eventId, external, internal, authenticatedUser)

        assertEquals(1, result.size)
        verify { eventRepository.save(eventEntity) }
    }

    @Test
    fun `changeStatus should update event status`() {
        val eventId = 1L
        val eventStatus = EventStatus.PUBLISHED
        val eventEntity = mockk<EventEntity>(relaxed = true)
        every { eventEntity.status } returns EventStatus.PUBLISHED

        every { eventRepository.findByIdOrNull(eventId) } returns eventEntity
        every { eventRepository.save(any<EventEntity>()) } returns eventEntity

        every { authenticatedUser.userId } returns 4

        eventService.changeStatus(eventId, eventStatus, authenticatedUser)

        assertEquals(eventStatus, eventEntity.status)
        verify { eventRepository.save(eventEntity) }
    }

    @Test
    fun `changeStatus should throw ApplicationException when event not found`() {
        val eventId = 1L
        val eventStatus = EventStatus.PUBLISHED

        every { eventRepository.findByIdOrNull(eventId) } returns null

        val exception = assertThrows<ApplicationException> {
            eventService.changeStatus(eventId, eventStatus, authenticatedUser)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `speakerListing should return paged result of event speakers`() {
        val eventId = 1L
        val pageable: Pageable = PageRequest.of(0, 10)
        val speakerEntities = listOf(mockk<EventSpeakerEntity>(relaxed = true))
        val page = PageImpl(speakerEntities, pageable, speakerEntities.size.toLong())

        every { eventRepository.findByIdOrNull(eventId) } returns mockk(relaxed = true)
        every { expertUserService.retrieveProfileSummary(any()) } returns mockk(relaxed = true)
        every { awsS3Service.getS3Url(any()) } returns "http://s3.example.com/profile.jpg"

        every { eventSpeakerRepository.findAllByEvent_Id(any(), any()) } returns page

        val result = eventService.speakerListing(eventId, pageable, authenticatedUser)

        assertEquals(1, result.totalElements)
        verify { eventSpeakerRepository.findAllByEvent_Id(any(), any()) }
    }

    @Test
    fun `retrieve should return event details`() {
        val eventId = 1L
        val eventEntity = mockk<EventEntity>(relaxed = true)

        every { eventRepository.findByIdOrNull(eventId) } returns eventEntity
        every { awsS3Service.getS3Url(any()) } returns "http://s3.example.com/banner.jpg"

        val result = eventService.retrieve(eventId, authenticatedUser)

        assertNotNull(result)
        verify { eventRepository.findByIdOrNull(eventId) }
    }

    @Test
    fun `retrieve should throw ApplicationException when event not found`() {
        val eventId = 1L

        every { eventRepository.findByIdOrNull(eventId) } returns null

        val exception = assertThrows<ApplicationException> {
            eventService.retrieve(eventId, authenticatedUser)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `getEventDetails should return event details`() {
        val eventEntity = mockk<EventEntity>(relaxed = true) {
            every { bannerPhotoKey } returns "banner-key"
        }
        every { awsS3Service.getS3Url("banner-key") } returns "http://s3.example.com/banner.jpg"

        val result = eventService.getEventDetails(eventEntity, authenticatedUser)

        assertNotNull(result)
        assertEquals("http://s3.example.com/banner.jpg", result.bannerImageFullUrl)
    }

    @Test
    fun `listing should return paged result of event details`() {
        val filter = mockk<EventSearchFilter>(relaxed = true)
        val pageRequest: PageRequest = PageRequest.of(0, 10)
        val eventEntities = listOf(mockk<EventEntity>(relaxed = true))
        val page = PageImpl(eventEntities, pageRequest, eventEntities.size.toLong())

        every { eventRepository.searchByCriteria(any(), any(), filter, pageRequest) } returns page
        every { eventRepository.searchByCriteriaWithoutStatus(any(), any(), filter, pageRequest) } returns page
        every { awsS3Service.getS3Url(any()) } returns "http://s3.example.com/banner.jpg"
        every { authenticatedUser.userType } returns UserType.EXPERT.toString()
        every { eventRepository.searchByCriteriaWithoutStatus(any(), any(), any(), any()) } returns PageImpl(eventEntities)

        val result = eventService.listing(filter, pageRequest, authenticatedUser)

        assertEquals(0, result.totalElements)
        //verify { eventRepository.searchByCriteria(any(), any(), any(), any()) }
    }

    @Test
    fun `sessionListing should return list of event sessions`() {
        val eventId = 1L
        val pageIndex = 0
        val sessionEntities = listOf(mockk<EventSessionEntity>(relaxed = true))

        every { eventSessionRepository.findAllByEvent_Id(any(), any()) } returns PageImpl(sessionEntities)

        every { eventSessionRepository.findAllByDateGroup(any()) } returns sessionEntities
        every { eventSessionRepository.findAllByEvent_IdAndDate(any(), any()) } returns sessionEntities

        val result = eventService.sessionListing(eventId, pageIndex)

        assertEquals(1, result.size)
        verify { eventSessionRepository.findAllByEvent_IdAndDate(any(), any()) }
    }

    @Test
    fun `inviteesList should return paged result of event invitees`() {
        val eventId = 1L
        val pageable: Pageable = PageRequest.of(0, 10)
        val inviteeEntities = listOf(mockk<EventInviteeEntity>(relaxed = true))
        val page = PageImpl(inviteeEntities, pageable, inviteeEntities.size.toLong())

        every { eventRepository.findById(any()) } returns Optional.of(EventEntity(
            id = 3,
            name = "",
            about = "",
            bannerPhotoKey = "",
            startDate = Date(),
            startTime = Date(),
            endDate = Date(),
            endTime = Date(),
            timeZone = "",
            status = EventStatus.PUBLISHED,
            location = "",
            locationDetails = "",
            inviteesExternal = "",
            speakers = mutableListOf(),
            sessions = mutableListOf(),
            invitees = mutableListOf(),
            lastUpdatedBy = 1234
        ))

        every { eventRepository.getInvitees(any(), any()) } returns PageImpl(inviteeEntities)
        every { corporateService.retrieveCorporate(any()) } returns Corporate(
            id = 4,
            email = "<EMAIL>",
            firstName = "fname",
            lastName = "lname",
            jobTitle = "",
            status = AccountStatus.ACTIVE,
            corporateName = "",
            countryCode = "IN",
            onBoardingInfo = null,
            keepMeInformed = false,
            bandId = 4,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            dialCode = "",
            contactNo = "",
            isPrimary = true,
            aiMessageCount = 4,
            notificationSettings = listOf(),
            corporateCountryCode = "IN",
            partnerName = "",
            profilePhotoUrl = ""
        )

        //every { eventInviteeRepository.findA(eventId, pageable) } returns page

        val result = eventService.inviteesList(eventId, pageable, authenticatedUser)

        assertEquals(1, result.totalElements)
        //verify { eventInviteeRepository.findAllByEvent_Id(eventId, pageable) }
    }

    @Test
    fun `attendeesList should return paged result of event attendees`() {
        val eventId = 1L
        val pageable: Pageable = PageRequest.of(0, 10)
        val attendeeEntities = listOf(mockk<EventInviteeEntity>(relaxed = true))
        val page = PageImpl(attendeeEntities, pageable, attendeeEntities.size.toLong())

        every { eventRepository.findById(any()) } returns Optional.of(EventEntity(
            id = 3,
            name = "",
            about = "",
            bannerPhotoKey = "",
            startDate = Date(),
            startTime = Date(),
            endDate = Date(),
            endTime = Date(),
            timeZone = "",
            status = EventStatus.PUBLISHED,
            location = "",
            locationDetails = "",
            inviteesExternal = "",
            speakers = mutableListOf(),
            sessions = mutableListOf(),
            invitees = mutableListOf(),
            lastUpdatedBy = 1234
        ))

        every { eventRepository.getInvitees(any(), any(), any()) } returns PageImpl(attendeeEntities)
        every { corporateService.retrieveCorporate(any()) } returns Corporate(
            id = 4,
            email = "<EMAIL>",
            firstName = "fname",
            lastName = "lname",
            jobTitle = "",
            status = AccountStatus.ACTIVE,
            corporateName = "",
            countryCode = "IN",
            onBoardingInfo = null,
            keepMeInformed = false,
            bandId = 4,
            accounts = listOf(),
            reportingManagerIds = listOf(),
            dialCode = "",
            contactNo = "",
            isPrimary = true,
            aiMessageCount = 4,
            notificationSettings = listOf(),
            corporateCountryCode = "IN",
            partnerName = "",
            profilePhotoUrl = ""
        )

        //every { eventInviteeRepository.findAllByEvent_IdAndStatus(eventId, EventInviteeStatus.ATTENDEE, pageable) } returns page

        val result = eventService.attendeesList(eventId, pageable, authenticatedUser)

        assertEquals(1, result.totalElements)
        //verify { eventInviteeRepository.findAllByEvent_IdAndStatus(eventId, EventInviteeStatus.ATTENDEE, pageable) }
    }

    @Test
    fun `rsvp should update invitee status and return success`() {
        val eventId = 1L
        val inviteeEntity = mockk<EventInviteeEntity>(relaxed = true)

        //every { eventInviteeRepository.findByEvent_IdAndClient_UserId(eventId, authenticatedUser.userId) } returns inviteeEntity
        every { eventInviteeRepository.save(any<EventInviteeEntity>()) } returns inviteeEntity

        every { eventRepository.findById(any()) } returns Optional.of(EventEntity(
            id = 3,
            name = "",
            about = "",
            bannerPhotoKey = "",
            startDate = Date(),
            startTime = Date(),
            endDate = Date(),
            endTime = Date(),
            timeZone = "",
            status = EventStatus.PUBLISHED,
            location = "",
            locationDetails = "",
            inviteesExternal = "",
            speakers = mutableListOf(),
            sessions = mutableListOf(),
            invitees = mutableListOf(),
            lastUpdatedBy = 1234
        ))

        authenticatedUser.userId = 3

        every { eventInviteeRepository.findFirstByEventAndClient_UserId(any(), any()) } returns inviteeEntity

        val result = eventService.rsvp(eventId, authenticatedUser)

        assertEquals("Success", result)
        verify { eventInviteeRepository.save(inviteeEntity) }
    }

    @Test
    fun `rsvp should throw ApplicationException when invitee not found`() {
        val eventId = 1L

        //every { eventInviteeRepository.findByEvent_IdAndClient_UserId(eventId, authenticatedUser.userId) } returns null

        every { eventRepository.findByIdOrNull(any()) } returns null

        every { eventInviteeRepository.save(any()) } returns mockk()

        every { eventInviteeRepository.findFirstByEventAndClient_UserId(any(), any()) } returns EventInviteeEntity(
            id = 5,
            client = mockk(),
            event = mockk(),
            status = EventInviteeStatus.INVITEE,
            lastUpdatedBy = 34334
        )

        val exception = assertThrows<ApplicationException> {
            eventService.rsvp(eventId, authenticatedUser)
        }

        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }
}
