package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.ChatType
import com.centuroglobal.shared.data.enums.NotificationType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.pojo.chat.GroupChatMessageRequest
import com.centuroglobal.shared.data.pojo.chat.GroupChatMessageResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.GroupChatUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.*

class GroupChatServiceTest {


    private var awsS3Service: AwsS3Service = mockk()
    private var groupChatRepository: GroupChatRepository = mockk()
    private var groupChatMessageRepository: GroupChatMessageRepository = mockk()
    private var groupChatParticipantRepository: GroupChatParticipantRepository = mockk()
    private var loginAccountRepository: LoginAccountRepository = mockk()
    private var caseRepository: CaseRepository = mockk()
    private var queryRepository: QueryRepository = mockk()
    private val rfpRepository: RfpRepository = mockk()

    private val authenticatedUser:AuthenticatedUser=mockk()

    private var groupChatService = GroupChatService(
        awsS3Service,
        groupChatRepository,
        groupChatMessageRepository,
        groupChatParticipantRepository,
        loginAccountRepository,
        caseRepository,
        queryRepository,
        groupChatUtil = GroupChatUtil(
            caseRepository = caseRepository,
            queryRepository = queryRepository,
            rfpRepository = rfpRepository
        )
    )



    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `createMessage should create a new message and return the response`() {
        // Arrange
        val messageRequest = GroupChatMessageRequest(
            message = "Test message",
            chatType = ChatType.CASE,
            referenceId = 12345L
        )
        val authenticatedUser = createRegularUser()

        authenticatedUser.userId = 1L

        val loginAccountEntity = createLoginAccountEntity(authenticatedUser)

        val groupChatEntity = GroupChatEntity(
            id = 1L,
            chatType = ChatType.CASE,
            referenceId = messageRequest.referenceId,
            participants = listOf(
                GroupChatParticipantEntity(
                    user = loginAccountEntity,
                    id=1,
                    isActive = true,
                    lastSeen = null,
                    groupChat = mockk()
                )
            )
        )

        val participantEntity = GroupChatParticipantEntity(
            id = 1,
            groupChat = groupChatEntity,
            user = loginAccountEntity,
            isActive = true,
            lastSeen = null
        )

        every {
            groupChatRepository.findByChatTypeAndReferenceId(messageRequest.chatType, messageRequest.referenceId)
        } returns groupChatEntity
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, authenticatedUser.userId) } returns Optional.of(
            GroupChatParticipantEntity(user = loginAccountEntity, groupChat = groupChatEntity)
        )
        every { groupChatParticipantRepository.save(any()) } returns participantEntity

        every { caseRepository.touch(any(), any()) } returns Unit


        val savedMessage = GroupChatMessageEntity(
            id = 1L,
            groupChat = groupChatEntity,
            message = messageRequest.message,
            userId = authenticatedUser.userId,
            assetUrl = "",
            isSeen = false
        )

        every { groupChatMessageRepository.save(any()) } returns savedMessage

        val expectedResponse = GroupChatMessageResponse(
            id = savedMessage.id,
            timestamp = TimeUtil.toEpochMillis(savedMessage.createdDate),
            message = savedMessage.message,
            userId = savedMessage.userId,
            isSeen = savedMessage.isSeen,
            assetUrl = savedMessage.assetUrl,
            isHidden = savedMessage.isHidden
        )

        // Act
        val result = groupChatService.createMessage(messageRequest, authenticatedUser)

        // Assert
        assertEquals(expectedResponse, result)

        verify {
            groupChatRepository.findByChatTypeAndReferenceId(messageRequest.chatType, messageRequest.referenceId)
            groupChatMessageRepository.save(any())
        }
    }

    @Test
    fun `createMessage should throw an exception when group chat is not found`() {
        // Arrange
        val messageRequest = GroupChatMessageRequest(
            message = "Test message",
            chatType = ChatType.CASE,
            referenceId = 12345L
        )
        val authenticatedUser = mockk<AuthenticatedUser>()

        every {
            groupChatRepository.findByChatTypeAndReferenceId(messageRequest.chatType, messageRequest.referenceId)
        } returns null

        // Act & Assert
        assertThrows<ApplicationException> {
            groupChatService.createMessage(messageRequest, authenticatedUser)
        }

        verify {
            groupChatRepository.findByChatTypeAndReferenceId(messageRequest.chatType, messageRequest.referenceId)
        }
        verify(exactly = 0) {
            groupChatMessageRepository.save(any())
        }
    }
    @Test
    fun `getMessages should return all messages for admin user`() {
        val chatType = ChatType.CASE
        val referenceId = 12345L
        val lastMessageDateTime = 1620864000000L // Example last message date/time in milliseconds
        val authenticatedUser = createAdminUser() // Create an admin user

        val groupChatEntity = GroupChatEntity(
            id = 1,
            chatType = chatType,
            referenceId = referenceId
        )

        val loginAccount = createLoginAccountEntity(authenticatedUser)
        val participantEntity = GroupChatParticipantEntity(
            id = 1,
            groupChat = groupChatEntity,
            user = loginAccount,
            isActive = true,
            lastSeen = null
        )

        val messages = createMockMessages(groupChatEntity, authenticatedUser.userId, 5)

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChatEntity
        every { groupChatMessageRepository.findByGroupChatAndCreatedDateGreaterThanOrderByCreatedDate(groupChatEntity, any()) } returns messages
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, authenticatedUser.userId) } returns Optional.of(participantEntity)

        val response = groupChatService.getMessages(chatType, referenceId, lastMessageDateTime, authenticatedUser)

        assertEquals(messages.size, response?.size)

        verify(exactly = 1) { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) }
        verify(exactly = 1) { groupChatMessageRepository.findByGroupChatAndCreatedDateGreaterThanOrderByCreatedDate(groupChatEntity, any()) }
    }
    @Test
    fun `getMessages should return non-hidden messages for non-admin user`() {
        val chatType = ChatType.CASE
        val referenceId = 12345L
        val lastMessageDateTime = 1620864000000L // Example last message date/time in milliseconds
        val authenticatedUser = createRegularUser() // Create a regular user

        val groupChatEntity = GroupChatEntity(
            id = 1,
            chatType = chatType,
            referenceId = referenceId
        )

        val loginAccount = createLoginAccountEntity(authenticatedUser)
        val participantEntity = GroupChatParticipantEntity(
            id = 1,
            groupChat = groupChatEntity,
            user = loginAccount,
            isActive = true,
            lastSeen = null
        )

        val messages = createMockMessages(groupChatEntity, authenticatedUser.userId, 5)

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChatEntity
        every { groupChatMessageRepository.findByGroupChatAndIsHiddenAndCreatedDateGreaterThanOrderByCreatedDate(groupChatEntity, false, any()) } returns messages
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, authenticatedUser.userId) } returns Optional.of(participantEntity)


        val response = groupChatService.getMessages(chatType, referenceId, lastMessageDateTime, authenticatedUser)

        assertEquals(messages.size, response?.size)

        verify(exactly = 1) { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) }
        verify(exactly = 1) { groupChatMessageRepository.findByGroupChatAndIsHiddenAndCreatedDateGreaterThanOrderByCreatedDate(groupChatEntity, false, any()) }
    }
    @Test
    fun `getMessages should throw Exception when group chat not found`() {
        val chatType = ChatType.CASE
        val referenceId = 12345L
        val lastMessageDateTime = 1620864000000L // Example last message date/time in milliseconds
        val authenticatedUser = createRegularUser() // Create a regular user

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns null

        assertThrows<ApplicationException>{
            groupChatService.getMessages(chatType, referenceId, lastMessageDateTime, authenticatedUser)
        }
    }

    @Test
    fun `getParticipants should return list of participant`() {
        // Arrange
        val chatType = ChatType.CASE
        val referenceId = 12345L

        val groupChat = GroupChatEntity(1L, chatType, referenceId)

        val user1 = createLoginAccountEntity(createRegularUser())
        val participant1 = GroupChatParticipantEntity(1L, groupChat, user1, true, null)

        val user2 = createExpertUserEntity(createRegularUser())
        val participant2 = GroupChatParticipantEntity(2L, groupChat, user2, true, null)

        val user3 = createBackOfficeUserEntity(createAdminUser())
        val participant3 = GroupChatParticipantEntity(3L, groupChat, user3, true, null)

        every { authenticatedUser.userId } returns 1L

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChat
        every { groupChatParticipantRepository.findAllByGroupChat(groupChat) } returns listOf(participant1, participant2, participant3)

        // Act
        val result = groupChatService.getParticipants(
            chatType, referenceId,
            user = authenticatedUser
        )

        // Assert
        //assertEquals(3, result?.size)
        assertEquals(user1.id, result?.participants?.get(0)?.id)
        assertEquals(user1.email, result?.participants?.get(0)?.email)
        assertEquals(user1.firstName, result?.participants?.get(0)?.firstName)
        assertEquals(user1.lastName, result?.participants?.get(0)?.lastName)
        assertEquals(user1.role.name, result?.participants?.get(0)?.role)
        assertEquals(true, result?.participants?.get(0)?.isActive)

        assertEquals(user2.id, result?.participants?.get(1)?.id)
        assertEquals(user2.email, result?.participants?.get(1)?.email)
        assertEquals(user2.firstName, result?.participants?.get(1)?.firstName)
        assertEquals(user2.lastName, result?.participants?.get(1)?.lastName)
        assertEquals(user2.role.name, result?.participants?.get(1)?.role)
        assertEquals(true, result?.participants?.get(1)?.isActive)

        assertEquals(user3.id, result?.participants?.get(2)?.id)
        assertEquals(user3.email, result?.participants?.get(2)?.email)
        assertEquals(user3.firstName, result?.participants?.get(2)?.firstName)
        assertEquals(user3.lastName, result?.participants?.get(2)?.lastName)
        assertEquals(user3.role.name, result?.participants?.get(2)?.role)
        assertEquals(true, result?.participants?.get(2)?.isActive)
    }

    @Test
    fun `getParticipants should throw exception when group chat is not found`() {
        // Arrange
        val chatType = ChatType.CASE
        val referenceId = 12345L

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns  null
        assertThrows<ApplicationException>{
            groupChatService.getParticipants(
                chatType, referenceId,
                user = authenticatedUser
            )
        }
    }

    @Test
    fun `deleteMessage should throw ApplicationException when message is not found`() {
        val messageId = 1L
        every { groupChatMessageRepository.findById(messageId) } returns Optional.empty()

        assertThrows<ApplicationException> {
            groupChatService.deleteMessage(messageId, true, createAdminUser())
        }
    }

    @Test
    fun `deleteMessage should not update message when message deletion is not allowed`() {
        val caseId = 121L
        val messageId = 1L
        val user1 = createAdminUser()
        val groupChatEntity = GroupChatEntity(
            referenceId = caseId,
            chatType = ChatType.CASE
        )
        val messageEntity = GroupChatMessageEntity(
            id = messageId,
            groupChat = groupChatEntity,
            message = "test message",
            userId = user1.userId,
            assetUrl = "",
            isHidden = false,
            isSeen = true
        )

        val caseEntity: CaseEntity = mockk()

        every { groupChatMessageRepository.findById(messageId) } returns Optional.of(messageEntity)
        every { caseRepository.findById(caseId) } returns Optional.of(caseEntity)
        every { caseEntity.status } returns "CASE_COMPLETE"
        every { caseEntity.archive } returns false

        groupChatService.deleteMessage(messageId, true, user1)
        verify(exactly = 0) { groupChatMessageRepository.save(any()) }
    }

    @Test
    fun `deleteMessage should not update message when user is not participant of group chat`() {
        val caseId = 121L
        val messageId = 1L
        val user1 = createAdminUser()
        val groupChatEntity = GroupChatEntity(
            referenceId = caseId,
            chatType = ChatType.CASE
        )
        val messageEntity = GroupChatMessageEntity(
            id = messageId,
            groupChat = groupChatEntity,
            message = "test message",
            userId = user1.userId,
            assetUrl = "",
            isHidden = false,
            isSeen = true
        )

        val caseEntity: CaseEntity = mockk()

        every { groupChatMessageRepository.findById(messageId) } returns Optional.of(messageEntity)
        every { caseRepository.findById(caseId) } returns Optional.of(caseEntity)
        every { caseEntity.status } returns "IN_PROGRESS"
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, user1.userId) } returns Optional.empty()
        every { caseEntity.archive } returns false

        groupChatService.deleteMessage(messageId, true, user1)
        verify(exactly = 0) { groupChatMessageRepository.save(any()) }
    }

    @Test
    fun deleteMessage() {
        val caseId = 121L
        val messageId = 1L
        val user1 = createAdminUser()
        val groupChatEntity = GroupChatEntity(
            referenceId = caseId,
            chatType = ChatType.CASE
        )
        val messageEntity = GroupChatMessageEntity(
            id = messageId,
            groupChat = groupChatEntity,
            message = "test message",
            userId = user1.userId,
            assetUrl = "",
            isHidden = false,
            isSeen = true
        )
        val caseEntity: CaseEntity = mockk()
        val groupChatParticipantEntity: GroupChatParticipantEntity = mockk()

        every { groupChatMessageRepository.findById(messageId) } returns Optional.of(messageEntity)
        every { groupChatMessageRepository.save(messageEntity) } returns messageEntity
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChatEntity, user1.userId) } returns Optional.of(groupChatParticipantEntity)
        every { caseRepository.findById(caseId) } returns Optional.of(caseEntity)
        every { caseEntity.status } returns "IN_PROGRESS"
        every { caseEntity.archive } returns false

        groupChatService.deleteMessage(messageId, true, user1)

        verify { groupChatMessageRepository.save(messageEntity) }
    }

    @Test
    fun `createGroupChat should save group chat entity and add participants`() {
        val chatType = ChatType.CASE
        val referenceId = 1L
        val user1 = createLoginAccountEntity(createRegularUser())
        val user2 = createLoginAccountEntity(createRegularUser())

        val groupChatEntity = GroupChatEntity(chatType = chatType, referenceId = referenceId)
        val participantEntities = listOf(
            GroupChatParticipantEntity(groupChat = groupChatEntity, user = user1),
            GroupChatParticipantEntity(groupChat = groupChatEntity, user = user2)
        )
        every { groupChatRepository.save(groupChatEntity) } returns groupChatEntity
        participantEntities.forEach {
            every { groupChatParticipantRepository.save(it) } returns it
        }
        every { loginAccountRepository.findById(user1.id!!) } returns Optional.of(user1)
        every { loginAccountRepository.findById(user2.id!!) } returns Optional.of(user2)

        val participants = listOf(user1.id!!, user2.id!!)

        val result = groupChatService.createGroupChat(
            chatType, referenceId, participants,
            virtualParticipants = listOf()
        )

        assertEquals(groupChatEntity, result)
        verify { groupChatRepository.save(groupChatEntity) }
        participantEntities.forEach {
            verify { groupChatParticipantRepository.save(it) }
        }
        verify(exactly = participants.size) { loginAccountRepository.findById(any()) }
    }

    @Test
    fun `createGroupChatWithOwner should save group chat entity and add participants`() {
        val chatType = ChatType.CASE
        val referenceId = 1L
        val user1 = createLoginAccountEntity(createRegularUser())
        val user2 = createLoginAccountEntity(createRegularUser())

        val groupChatEntity = GroupChatEntity(chatType = chatType, referenceId = referenceId)
        val participantEntities = listOf(
            GroupChatParticipantEntity(groupChat = groupChatEntity, user = user1),
            GroupChatParticipantEntity(groupChat = groupChatEntity, user = user2)
        )
        every { groupChatRepository.save(groupChatEntity) } returns groupChatEntity
        participantEntities.forEach {
            every { groupChatParticipantRepository.save(it) } returns it
        }
        every { loginAccountRepository.findById(user1.id!!) } returns Optional.of(user1)
        every { loginAccountRepository.findById(user2.id!!) } returns Optional.of(user2)

        val participants = listOf(user1.id!!, user2.id!!)

        val result = groupChatService.createGroupChatWithOwner(chatType, referenceId, participants, listOf(), user1.id)

        assertEquals(groupChatEntity, result)
        verify { groupChatRepository.save(groupChatEntity) }
        participantEntities.forEach {
            verify { groupChatParticipantRepository.save(it) }
        }
        verify(exactly = participants.size) { loginAccountRepository.findById(any()) }
    }

    @Test
    fun `getMessagesCount should return messages count for a given chat type and reference ID`() {
        val chatType = ChatType.CASE
        val referenceId = 1L
        val groupChatEntity = GroupChatEntity(chatType = chatType, referenceId = referenceId)
        val messageCount = 5L

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChatEntity
        every { groupChatMessageRepository.countByGroupChat(groupChatEntity) } returns messageCount

        val result = groupChatService.getMessagesCount(chatType, referenceId)

        assertEquals(messageCount, result)
        verify { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) }
        verify { groupChatMessageRepository.countByGroupChat(groupChatEntity) }
    }

    @Test
    fun `getMessagesCount should throw exception when group chat not found`() {
        val chatType = ChatType.CASE
        val referenceId = 5L
        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns null
        assertThrows<ApplicationException>{
            groupChatService.getMessagesCount(chatType, referenceId)
        }
    }

    @Test
    fun `updateChatParticipants should update chat participants correctly`() {
        val referenceId = 1L
        val chatType = ChatType.CASE

        val groupChat = GroupChatEntity(chatType = chatType, referenceId = referenceId)
        val participants = listOf(
            GroupChatParticipantEntity(groupChat = groupChat, user = createLoginAccountEntity(createRegularUser())),
            GroupChatParticipantEntity(groupChat = groupChat, user = createLoginAccountEntity(createRegularUser())),
            GroupChatParticipantEntity(groupChat = groupChat, user = createLoginAccountEntity(createRegularUser()))
        )

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChat
        // simulate participant1 already exists scenario
        every { groupChatParticipantRepository.findAllByGroupChat(groupChat) } returns listOf(participants[0])
        participants.forEach {
            every { loginAccountRepository.findById(it.user.id!!) } returns Optional.of(it.user)
        }
        every { groupChatParticipantRepository.saveAll(listOf(participants[0])) } returns listOf(participants[0])
        participants.forEach {
            every { groupChatParticipantRepository.save(it) } returns it
        }

        val updatedParticipants = participants.map { it.user.id!! }.toSet()
        groupChatService.updateChatParticipants(referenceId, chatType, updatedParticipants)

        // Assert
        verify { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) }
        verify { groupChatParticipantRepository.saveAll(listOf(participants[0])) }
        participants.stream().skip(1).forEach {
            verify { groupChatParticipantRepository.save(it) }
        }
    }

    @Test
    fun `updateLastSeen should call updateLastSeen with correct parameters`() {
        // Arrange
        val chatType = ChatType.CASE
        val referenceId = 123L
        val userId = 456L
        val groupChat = GroupChatEntity(id = 1, chatType = chatType, referenceId = referenceId)

        val participant = GroupChatParticipantEntity(
            groupChat=groupChat,
            user = createLoginAccountEntity(createRegularUser())
        )

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChat
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChat, userId) } returns Optional.of(participant)
        every { groupChatParticipantRepository.save(any()) } returns participant
        // Act
        groupChatService.updateLastSeen(chatType, referenceId, userId)

        // Assert
        verify { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) }
        verify { groupChatParticipantRepository.findByGroupChatAndUserId(groupChat, userId)  }
        verify { groupChatParticipantRepository.save(any())  }
    }

    @Test
    fun `updateLastSeen should not call updateLastSeen when groupChat is not found`() {
        // Arrange
        val chatType = ChatType.CASE
        val referenceId = 123L
        val userId = 456L

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns null

        // Act
        groupChatService.updateLastSeen(chatType, referenceId, userId)

        // Assert
        verify { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) }
    }

    @Test
    fun `getUnreadMessagesCount should return count when participantEntity is present`() {
        // Arrange
        val chatType = ChatType.CASE
        val referenceId = 123L
        val userId = 456L
        val groupChat = GroupChatEntity(id = 1, chatType = chatType, referenceId = referenceId)
        val participantEntity = GroupChatParticipantEntity(
            id = 1,
            groupChat = groupChat,
            user = createLoginAccountEntity(createRegularUser()),
            lastSeen = TimeUtil.fromInstantMillis(*************) // example last seen time
        )
        val count = 5L

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChat
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChat, userId) } returns Optional.of(participantEntity)
        every {
            groupChatMessageRepository.countByGroupChatAndCreatedDateGreaterThan(
                groupChat,
                participantEntity.lastSeen ?: TimeUtil.fromInstantMillis(0)
            )
        } returns count

        // Act
        val result = groupChatService.getUnreadMessagesCount(chatType, referenceId, userId)

        // Assert
        assertEquals(count, result)
    }

    @Test
    fun `getUnreadMessagesCount should return 0 when participantEntity is not present`() {
        // Arrange
        val chatType = ChatType.CASE
        val referenceId = 123L
        val userId = 456L
        val groupChat = GroupChatEntity(id = 1, chatType = chatType, referenceId = referenceId)

        every { groupChatRepository.findByChatTypeAndReferenceId(chatType, referenceId) } returns groupChat
        every { groupChatParticipantRepository.findByGroupChatAndUserId(groupChat, userId) } returns Optional.empty()

        // Act
        val result = groupChatService.getUnreadMessagesCount(chatType, referenceId, userId)

        // Assert
        assertEquals(0L, result)
    }

    @Test
    fun `sendChatNotifications should send notifications when messages and participants exist`() {
        val caseId1 = 123L
        val caseId2 = 124L
        val groupChat1 = GroupChatEntity(id = 1, chatType = ChatType.CASE, referenceId = caseId1)
        val groupChat2 = GroupChatEntity(id = 2, chatType = ChatType.CASE, referenceId = caseId2)
        val messages = mutableListOf<GroupChatMessageEntity>()
        messages.addAll(createMockMessages(groupChat1, 22L, 4))
        messages.addAll(createMockMessages(groupChat2, 22L, 2))

        val user1 = createLoginAccountEntity(createRegularUser())
        val user2 = createLoginAccountEntity(createRegularUser())

        val participant1 = GroupChatParticipantEntity(id = 1, groupChat = groupChat1, user = user1)
        val participant2 = GroupChatParticipantEntity(id = 2, groupChat = groupChat1, user = user2)
        val participant3 = GroupChatParticipantEntity(id = 3, groupChat = groupChat2, user = user2) // user2 belongs to 2 cases/chats

        val case1: CaseEntity = mockk()
        val case2: CaseEntity = mockk()

        every { groupChatMessageRepository.findByCreatedDateGreaterThanAndGroupChatChatType(any(), ChatType.CASE) } returns messages
        every { groupChatParticipantRepository.findAllByGroupChatAndIsActive(groupChat1, true) } returns listOf(participant1, participant2)
        every { groupChatParticipantRepository.findAllByGroupChatAndIsActive(groupChat2, true) } returns listOf(participant3)
        every { caseRepository.findById(caseId1) } returns Optional.of(case1)
        every { caseRepository.findById(caseId2) } returns Optional.of(case2)
        every { case1.id } returns caseId1
        every { case2.id } returns caseId2
        every { case1.category?.subCategoryName } returns "Individual Tax Assessment"
        every { case2.category?.subCategoryName } returns "Company Tax Assessment"

        (user1 as CorporateUserEntity).notificationPreferences = NotificationType.values().map {
            NotificationPreferencesEntity(
                notificationKey = it,
                value = true,
                corporateUser = user1
            )
        }.toMutableList()

        (user2 as CorporateUserEntity).notificationPreferences = NotificationType.values().map {
            NotificationPreferencesEntity(
                notificationKey = it,
                value = true,
                corporateUser = user2
            )
        }.toMutableList()



        /*every { loginAccountRepository.findById(user1.id!!) } returns Optional.of(user1)
        every { loginAccountRepository.findById(user2.id!!) } returns Optional.of(user2)
        every { awsS3Service.getS3PublicUrl("static/email_template") } returns "https://s3.email-templates"
        every { mailSendingService.sendEmail(any()) } returns true*/

        // Act
        val userCaseMap = groupChatService.getUserChatMap(ChatType.CASE, 12, NotificationType.CASE_GCHAT_EMAIL)

        // Assert

        assertNotNull(userCaseMap)
        assertNotNull(userCaseMap!![user1.id])
        assertEquals(1, userCaseMap[user1.id]?.size)
        assertEquals(case1.id, userCaseMap[user1.id]?.map { it["id"] }!![0])

        //user2 should have 2 cases
        assertNotNull(userCaseMap[user2.id])
        assertEquals(2, userCaseMap[user2.id]?.size)
        assertEquals(case1.id, userCaseMap[user2.id]?.map { it["id"] }!![0])
        assertEquals(case2.id, userCaseMap[user2.id]?.map { it["id"] }!![1])
    }


    private fun createLoginAccountEntity(authenticatedUser: AuthenticatedUser): LoginAccountEntity {

        val loginAccount = CorporateUserEntity()
        loginAccount.id = authenticatedUser.userId
        loginAccount.role = authenticatedUser.role.let { Role.valueOf(authenticatedUser.role) }

        return loginAccount
    }

    private fun createExpertUserEntity(authenticatedUser: AuthenticatedUser): LoginAccountEntity {

        val expertUserEntity = ExpertUserEntity(
            jobTitle = "CEO",
            displayName = authenticatedUser.displayName,
            contactNumber = "+1564656${(0..9).random()}",
            contactEmail = authenticatedUser.email,
            contactWebsite = "www.test${(0..9).random()}.com",
            expertType = "expert"
        )
        expertUserEntity.id = authenticatedUser.userId
        expertUserEntity.role = authenticatedUser.role.let { Role.valueOf(authenticatedUser.role) }

        return expertUserEntity
    }

    private fun createBackOfficeUserEntity(authenticatedUser: AuthenticatedUser): LoginAccountEntity {

        val backofficeUserEntity = BackofficeUserEntity(

        )
        backofficeUserEntity.id = authenticatedUser.userId
        backofficeUserEntity.role = authenticatedUser.role.let { Role.valueOf(authenticatedUser.role) }

        return backofficeUserEntity
    }



    private fun createAdminUser(): AuthenticatedUser {
        // Create and return an admin user
        return AuthenticatedUser(
            firstName = "John",
            lastName = "Admin",
            userId = 1234L,
            displayName = "John Admin",
            email = "<EMAIL>",
            role = Role.ROLE_ADMIN.name,
            userType = "individual",
            status = "active",
            onboard = true,
            subscriptionFlag = "standard",
            countryCode = "US",
            adminAuthorities = ""
        )
    }

    private fun createRegularUser(): AuthenticatedUser {
        // Create and return a regular user
       return AuthenticatedUser(
            firstName = "John",
            lastName = "Doe",
            userId = (1..9999).random().toLong(),
            displayName = "John Doe",
            email = "john.doe${(0..10).random()}@example.com",
            role = Role.ROLE_CORPORATE.name,
            userType = "individual",
            status = "active",
            onboard = true,
            subscriptionFlag = "standard",
            countryCode = "US",
            adminAuthorities = ""
        )
    }



    private fun createMockMessages(groupChatEntity: GroupChatEntity, userId: Long, n: Int): List<GroupChatMessageEntity> {
        // Create and return a list of mock messages
        val messages = mutableListOf<GroupChatMessageEntity>()
        for (i in 1..n) {
            messages.add(
                GroupChatMessageEntity(
                id = i.toLong(),
                groupChat = groupChatEntity,
                message = "test message $i",
                userId = userId,
                assetUrl = "",
                isSeen = false
                )
            )
        }
        return messages
    }
}
