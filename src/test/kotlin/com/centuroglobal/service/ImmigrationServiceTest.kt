package com.centuroglobal.service

import com.centuroglobal.shared.data.entity.ImmigrationCategoryEntity
import com.centuroglobal.shared.data.entity.ImmigrationRequirementEntity
import com.centuroglobal.shared.data.entity.playbook.PlaybookSessionEntity
import com.centuroglobal.shared.data.enums.Common
import com.centuroglobal.shared.data.enums.ImmigrationCategory
import com.centuroglobal.shared.repository.ImmigrationCategoryRepository
import com.centuroglobal.shared.repository.ImmigrationRequirementRepository
import com.centuroglobal.shared.repository.playbook.PlaybookSessionRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ImmigrationServiceTest {

    private lateinit var immigrationService: ImmigrationService
    private lateinit var immigrationCategoryRepository: ImmigrationCategoryRepository
    private lateinit var immigrationRequirementRepository: ImmigrationRequirementRepository
    private lateinit var playbookSessionRepository: PlaybookSessionRepository

    @BeforeEach
    fun setup() {
        immigrationCategoryRepository = mockk()
        immigrationRequirementRepository = mockk()
        playbookSessionRepository = mockk()

        immigrationService = ImmigrationService(
            immigrationCategoryRepository,
            immigrationRequirementRepository,
            playbookSessionRepository
        )
    }

    @Test
    fun `getVisaDetails should return visa requirements with business visa required`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "UK"
        
        val requirementEntity = ImmigrationRequirementEntity(
            id = 1L,
            sourceCountry = sourceCountry,
            destCountry = destinationCountry,
            businessVisa = Common.REQUIRED.name,
            workVisa = "",
            eVisa = "",
            validDays = 90
        )
        
        every { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        } returns requirementEntity

        // Act
        val result = immigrationService.getVisaDetails(sourceCountry, destinationCountry)

        // Assert
        assertNotNull(result)
        assertTrue(result.businessVisa)
        assertFalse(result.workVisa)
        assertFalse(result.eVisa)
        assertEquals(0, result.validDays)
        
        verify(exactly = 1) { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        }
    }

    @Test
    fun `getVisaDetails should return visa requirements with work visa required`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "DE"
        
        val requirementEntity = ImmigrationRequirementEntity(
            id = 1L,
            sourceCountry = sourceCountry,
            destCountry = destinationCountry,
            businessVisa = "",
            workVisa = Common.REQUIRED.name,
            eVisa = "",
            validDays = 90
        )
        
        every { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        } returns requirementEntity

        // Act
        val result = immigrationService.getVisaDetails(sourceCountry, destinationCountry)

        // Assert
        assertNotNull(result)
        assertFalse(result.businessVisa)
        assertTrue(result.workVisa)
        assertFalse(result.eVisa)
        assertEquals(0, result.validDays)
    }

    @Test
    fun `getVisaDetails with visa type should return details for business visa`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "UK"
        val visaType = "BUSINESS"
        
        val requirementEntity = ImmigrationRequirementEntity(
            id = 1L,
            sourceCountry = sourceCountry,
            destCountry = destinationCountry,
            businessVisa = Common.REQUIRED.name,
            workVisa = "",
            eVisa = "",
            validDays = 90
        )
        
        val categoryEntity = ImmigrationCategoryEntity(
            id = 1L,
            visaCode = visaType,
            category = ImmigrationCategory.BUSINESS_VISA.name,
            visaDisplayName = ""
        )
        
        val sessionSlot = slot<PlaybookSessionEntity>()
        
        every { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        } returns requirementEntity
        
        every { immigrationCategoryRepository.findByVisaCode(visaType) } returns categoryEntity
        
        every { playbookSessionRepository.save(capture(sessionSlot)) } answers { 
            sessionSlot.captured.also { it.id = 1L }
        }

        // Act
        val result = immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType)

        // Assert
        assertNotNull(result)
        assertTrue(result.isVisaRequired)
        assertEquals(0, result.noOfDays)
        assertNotNull(result.sessionId)
        
        // Verify session was created correctly
        val capturedSession = sessionSlot.captured
        assertEquals("VISA", capturedSession.type)
        assertEquals(0, capturedSession.questionCount)
        assertNotNull(capturedSession.sessionId)
        assertNotNull(capturedSession.startTime)
    }

    @Test
    fun `getVisaDetails with visa type should return details for work visa`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "DE"
        val visaType = "WORK"
        
        val requirementEntity = ImmigrationRequirementEntity(
            id = 1L,
            sourceCountry = sourceCountry,
            destCountry = destinationCountry,
            businessVisa = "",
            workVisa = Common.REQUIRED.name,
            eVisa ="" ,
            validDays = 90
        )
        
        val categoryEntity = ImmigrationCategoryEntity(
            id = 1L,
            visaCode = visaType,
            category = ImmigrationCategory.WORK_VISA.name,
            visaDisplayName = "Work Visa"
        )
        
        every { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        } returns requirementEntity
        
        every { immigrationCategoryRepository.findByVisaCode(visaType) } returns categoryEntity
        
        every { playbookSessionRepository.save(any()) } answers { firstArg() }

        // Act
        val result = immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType)

        // Assert
        assertNotNull(result)
        assertTrue(result.isVisaRequired)
        assertEquals(0, result.noOfDays)
        assertNotNull(result.sessionId)
    }

    @Test
    fun `getVisaDetails with visa type should return details for e-visa`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "AU"
        val visaType = "EVISA"
        
        val requirementEntity = ImmigrationRequirementEntity(
            id = 1L,
            sourceCountry = sourceCountry,
            destCountry = destinationCountry,
            businessVisa = "",
            workVisa = "",
            eVisa = Common.REQUIRED.name,
            validDays = 90
        )
        
        val categoryEntity = ImmigrationCategoryEntity(
            id = 1L,
            visaCode = visaType,
            category = ImmigrationCategory.E_VISA.name,
            visaDisplayName = "Electronic Visa"
        )
        
        every { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        } returns requirementEntity
        
        every { immigrationCategoryRepository.findByVisaCode(visaType) } returns categoryEntity
        
        every { playbookSessionRepository.save(any()) } answers { firstArg() }

        // Act
        val result = immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType)

        // Assert
        assertNotNull(result)
        assertTrue(result.isVisaRequired)
        assertEquals(0, result.noOfDays)
        assertNotNull(result.sessionId)
    }

    @Test
    fun `getVisaDetails with visa type should return not required for unknown visa type`() {
        // Arrange
        val sourceCountry = "US"
        val destinationCountry = "CA"
        val visaType = "UNKNOWN"
        
        val requirementEntity = ImmigrationRequirementEntity(
            id = 1L,
            sourceCountry = sourceCountry,
            destCountry = destinationCountry,
            businessVisa = "",
            workVisa = "",
            eVisa = "",
            validDays = 90
        )
        
        val categoryEntity = ImmigrationCategoryEntity(
            id = 1L,
            visaCode = visaType,
            category = "UNKNOWN_TYPE",
            visaDisplayName = "Unknown Visa Type"
        )
        
        every { 
            immigrationRequirementRepository.findBySourceCountryAndDestCountry(sourceCountry, destinationCountry) 
        } returns requirementEntity
        
        every { immigrationCategoryRepository.findByVisaCode(visaType) } returns categoryEntity
        
        every { playbookSessionRepository.save(any()) } answers { firstArg() }

        // Act
        val result = immigrationService.getVisaDetails(sourceCountry, destinationCountry, visaType)

        // Assert
        assertNotNull(result)
        assertFalse(result.isVisaRequired)
        assertEquals(0, result.noOfDays)
        assertNotNull(result.sessionId)
    }

    @Test
    fun `getVisaCategory should return all visa categories when visa type is null`() {
        // Arrange
        val categories = listOf(
            ImmigrationCategoryEntity(
                id = 1L,
                visaCode = "BUSINESS",
                category = ImmigrationCategory.BUSINESS_VISA.name,
                visaDisplayName = "Business Visa"
            ),
            ImmigrationCategoryEntity(
                id = 2L,
                visaCode = "WORK",
                category = ImmigrationCategory.WORK_VISA.name,
                visaDisplayName = "Work Visa"
            ),
            ImmigrationCategoryEntity(
                id = 3L,
                visaCode = "EVISA",
                category = ImmigrationCategory.E_VISA.name,
                visaDisplayName = "Electronic Visa"
            )
        )
        
        every { immigrationCategoryRepository.findAll() } returns categories

        // Act
        val result = immigrationService.getVisaCategory(null)

        // Assert
        assertNotNull(result)
        assertEquals(3, result.size)
        assertEquals("BUSINESS", result[0].visaCode)
        assertEquals("WORK", result[1].visaCode)
        assertEquals("EVISA", result[2].visaCode)
        
        verify(exactly = 1) { immigrationCategoryRepository.findAll() }
    }

    @Test
    fun `getVisaCategory should return filtered visa categories when visa type is provided`() {
        // Arrange
        val visaType = "BUSINESS"
        
        val categories = listOf(
            ImmigrationCategoryEntity(
                id = 1L,
                visaCode = visaType,
                category = ImmigrationCategory.BUSINESS_VISA.name,
                visaDisplayName = "Business Visa"
            )
        )
        
        every { immigrationCategoryRepository.findAllByVisaCode(visaType) } returns categories

        // Act
        val result = immigrationService.getVisaCategory(visaType)

        // Assert
        assertNotNull(result)
        assertEquals(1, result.size)
        assertEquals(visaType, result[0].visaCode)
        assertEquals(ImmigrationCategory.BUSINESS_VISA.name, result[0].category)
        
        verify(exactly = 1) { immigrationCategoryRepository.findAllByVisaCode(visaType) }
    }
}