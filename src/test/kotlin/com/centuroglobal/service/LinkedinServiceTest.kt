package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.payload.account.signup.SignUpRequest
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.web.client.RestTemplate
import java.util.*
/*
@ExtendWith(MockitoExtension::class)
class LinkedinServiceTest {

    private lateinit var linkedinService: LinkedinService
    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var corporateService: CorporateService
    private lateinit var corporateUserRepository: CorporateUserRepository
    private lateinit var restTemplate: RestTemplate
    private lateinit var objectMapper: ObjectMapper

    private val clientId = "test-client-id"
    private val clientSecret = "test-client-secret"

    @BeforeEach
    fun setup() {
        loginAccountRepository = mockk()
        corporateService = mockk()
        corporateUserRepository = mockk()
        restTemplate = mockk()
        objectMapper = ObjectMapper()

        mockkConstructor(RestTemplate::class)
        
        linkedinService = LinkedinService(
            clientId,
            clientSecret,
            loginAccountRepository,
            corporateService,
            corporateUserRepository
        )
    }

    @Test
    fun `auth should return existing login account when user exists`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"
        val email = "<EMAIL>"
        val accessToken = "test-access-token"

        val loginAccount = mockk<LoginAccountEntity> {
            every { getUserType() } returns UserType.CORPORATE
            every { isLinkedin } returns false
            every { isLinkedin = true } just runs
        }

        val accessTokenNode = objectMapper.createObjectNode().put("access_token", accessToken)

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } returns accessTokenNode

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(createEmailJsonNode(email))

        every { loginAccountRepository.findByEmail(email) } returns loginAccount
        every { loginAccountRepository.save(loginAccount) } returns loginAccount

        // Act
        val result = linkedinService.auth(code, redirectURL, keepMeInformed)

        // Assert
        assertNotNull(result)
        assertSame(loginAccount, result)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            loginAccountRepository.findByEmail(email)
            loginAccount.isLinkedin = true
            loginAccountRepository.save(loginAccount)
        }
    }

    @Test
    fun `auth should throw ApplicationException when user exists but is not CORPORATE type`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"
        val email = "<EMAIL>"
        val accessToken = "test-access-token"

        val loginAccount = mockk<LoginAccountEntity> {
            every { getUserType() } returns UserType.EXPERT
        }

        val accessTokenNode = objectMapper.createObjectNode().put("access_token", accessToken)

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } returns accessTokenNode

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(createEmailJsonNode(email))

        every { loginAccountRepository.findByEmail(email) } returns loginAccount

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            linkedinService.auth(code, redirectURL, keepMeInformed)
        }

        assertEquals(ErrorCode.LINKEDIN_NOT_SUPPORTED, exception.error)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            loginAccountRepository.findByEmail(email)
        }
        verify(exactly = 0) {
            loginAccountRepository.save(any())
        }
    }

    @Test
    fun `auth should create new account when user does not exist`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"
        val email = "<EMAIL>"
        val accessToken = "test-access-token"
        val firstName = "John"
        val lastName = "Doe"
        val corporateId = 123L

        val newLoginAccount = mockk<LoginAccountEntity> {
            every { isLinkedin } returns false
            every { isLinkedin = true } just runs
        }

        val accessTokenNode = objectMapper.createObjectNode().put("access_token", accessToken)
        val profileNode = createProfileJsonNode(firstName, lastName)

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } returns accessTokenNode

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(createEmailJsonNode(email))

        every { loginAccountRepository.findByEmail(email) } returns null

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/me"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(profileNode)

        every { 
            corporateService.createCorporate(
                match<SignUpRequest> { 
                    it.email == email && 
                    it.firstName == firstName && 
                    it.lastName == lastName && 
                    it.keepMeInformed == true
                },
                false
            ) 
        } returns corporateId

        every { corporateUserRepository.findFirstByCorporateId(corporateId) } returns newLoginAccount
        every { loginAccountRepository.save(newLoginAccount) } returns newLoginAccount

        // Act
        val result = linkedinService.auth(code, redirectURL, keepMeInformed)

        // Assert
        assertNotNull(result)
        assertSame(newLoginAccount, result)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            loginAccountRepository.findByEmail(email)
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/me"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            corporateService.createCorporate(any(), false)
            corporateUserRepository.findFirstByCorporateId(corporateId)
            newLoginAccount.isLinkedin = true
            loginAccountRepository.save(newLoginAccount)
        }
    }

    @Test
    fun `auth should throw ApplicationException when LinkedIn API returns no access token`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } returns null

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            linkedinService.auth(code, redirectURL, keepMeInformed)
        }

        assertEquals(ErrorCode.LOGIN_FAIL, exception.error)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
        }
        verify(exactly = 0) {
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            loginAccountRepository.findByEmail(any())
            loginAccountRepository.save(any())
        }
    }

    @Test
    fun `auth should throw ApplicationException when LinkedIn API returns no email`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"
        val accessToken = "test-access-token"

        val accessTokenNode = objectMapper.createObjectNode().put("access_token", accessToken)

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } returns accessTokenNode

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(createEmptyEmailJsonNode())

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            linkedinService.auth(code, redirectURL, keepMeInformed)
        }

        assertEquals(ErrorCode.LOGIN_FAIL, exception.error)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        }
        verify(exactly = 0) {
            loginAccountRepository.findByEmail(any())
            loginAccountRepository.save(any())
        }
    }

    @Test
    fun `auth should throw ApplicationException when new user creation fails`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"
        val email = "<EMAIL>"
        val accessToken = "test-access-token"
        val firstName = "John"
        val lastName = "Doe"
        val corporateId = 123L

        val accessTokenNode = objectMapper.createObjectNode().put("access_token", accessToken)
        val profileNode = createProfileJsonNode(firstName, lastName)

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } returns accessTokenNode

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(createEmailJsonNode(email))

        every { loginAccountRepository.findByEmail(email) } returns null

        every { 
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/me"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
        } returns ResponseEntity.ok(profileNode)

        every { 
            corporateService.createCorporate(any(), false)
        } returns corporateId

        every { corporateUserRepository.findFirstByCorporateId(corporateId) } returns null

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            linkedinService.auth(code, redirectURL, keepMeInformed)
        }

        assertEquals(ErrorCode.INTERNAL_SERVER_ERROR, exception.error)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            loginAccountRepository.findByEmail(email)
            any<RestTemplate>().exchange(
                eq("https://api.linkedin.com/v2/me"),
                eq(HttpMethod.GET),
                any(),
                eq(JsonNode::class.java)
            )
            corporateService.createCorporate(any(), false)
            corporateUserRepository.findFirstByCorporateId(corporateId)
        }
        verify(exactly = 0) {
            loginAccountRepository.save(any())
        }
    }

    @Test
    fun `auth should handle general exceptions and throw ApplicationException`() {
        // Arrange
        val code = "test-auth-code"
        val redirectURL = "https://example.com/callback"
        val keepMeInformed = "true"

        every { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            ) 
        } throws RuntimeException("API error")

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            linkedinService.auth(code, redirectURL, keepMeInformed)
        }

        assertEquals(ErrorCode.INTERNAL_SERVER_ERROR, exception.error)

        // Verify
        verify { 
            any<RestTemplate>().getForObject(
                contains("https://www.linkedin.com/oauth/v2/accessToken"),
                eq(JsonNode::class.java),
                any()
            )
        }
    }

    // Helper methods to create test JSON nodes
    private fun createEmailJsonNode(email: String): JsonNode {
        val emailAddressNode = objectMapper.createObjectNode()
            .put("emailAddress", email)
        
        val handleNode = objectMapper.createObjectNode()
            .set("handle~", emailAddressNode)
        
        val elementNode = objectMapper.createObjectNode()
            .set("handle~", emailAddressNode)
        
        val elementsArray = objectMapper.createArrayNode()
            .add(elementNode)
        
        return objectMapper.createObjectNode()
            .set("elements", elementsArray)
    }

    private fun createEmptyEmailJsonNode(): JsonNode {
        val elementsArray = objectMapper.createArrayNode()
        return objectMapper.createObjectNode()
            .set("elements", elementsArray)
    }

    private fun createProfileJsonNode(firstName: String, lastName: String): JsonNode {
        return objectMapper.createObjectNode()
            .put("localizedFirstName", firstName)
            .put("localizedLastName", lastName)
    }
}*/