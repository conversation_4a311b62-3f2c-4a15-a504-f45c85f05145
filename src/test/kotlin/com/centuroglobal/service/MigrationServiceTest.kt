package com.centuroglobal.service

import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.view.ClientView
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.task.TaskTemplateRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.*

class MigrationServiceTest {

    private lateinit var migrationService: MigrationService
    private lateinit var bandsRepository: BandsRepository
    private lateinit var corporateRepository: CorporateRepository
    private lateinit var corporateUserRepository: CorporateUserRepository
    private lateinit var accountEntityRepository: AccountEntityRepository
    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var expertCompanyProfileRepository: ExpertCompanyProfileRepository
    private lateinit var caseRepository: CaseRepository
    private lateinit var caseStatusMasterRepository: CaseStatusMasterRepository
    private lateinit var caseStatusHistoryRepository: CaseStatusHistoryRepository
    private lateinit var caseManagerRepository: CaseManagerRepository
    private lateinit var partnerRepository: PartnerRepository
    private lateinit var groupChatRepository: GroupChatRepository
    private lateinit var taskRepository: TaskRepository
    private lateinit var taskTemplateRepository: TaskTemplateRepository
    private lateinit var excelService: ExcelService
    private lateinit var subscriptionService: SubscriptionService
    private lateinit var groupChatService: GroupChatService
    private lateinit var corporateUserService: CorporateUserService
    private lateinit var taskWorkflowService: TaskWorkflowService

    @BeforeEach
    fun setup() {
        bandsRepository = mockk()
        corporateRepository = mockk()
        corporateUserRepository = mockk()
        accountEntityRepository = mockk()
        loginAccountRepository = mockk()
        expertCompanyProfileRepository = mockk()
        caseRepository = mockk()
        caseStatusMasterRepository = mockk()
        caseStatusHistoryRepository = mockk()
        caseManagerRepository = mockk()
        partnerRepository = mockk()
        groupChatRepository = mockk()
        taskRepository = mockk()
        taskTemplateRepository = mockk()
        excelService = mockk()
        subscriptionService = mockk()
        groupChatService = mockk()
        corporateUserService = mockk()
        taskWorkflowService = mockk()

        migrationService = MigrationService(
            bandsRepository,
            corporateRepository,
            corporateUserRepository,
            accountEntityRepository,
            loginAccountRepository,
            expertCompanyProfileRepository,
            caseRepository,
            caseStatusMasterRepository,
            caseStatusHistoryRepository,
            caseManagerRepository,
            partnerRepository,
            groupChatRepository,
            taskRepository,
            taskTemplateRepository,
            excelService,
            subscriptionService,
            groupChatService,
            corporateUserService,
            taskWorkflowService
        )
    }

    @Test
    fun `getCorporates should return filtered list of corporates`() {
        // Arrange
        val corporate1 = CorporateEntity(
            1,
            "test",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            1,
            null,
            listOf(),
            1
        )
        val corporate2 = CorporateEntity(
            1,
            "test",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            1,
            null,
            listOf(),
            1
        )
        val dummyCorporate = CorporateEntity(
            1,
            "test",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            1,
            null,
            listOf(),
            1
        )

        every { corporateRepository.findAll() } returns listOf(corporate1, corporate2, dummyCorporate)

        // Act
        val result = migrationService.getCorporates()

        // Assert
        assertEquals(3, result.size)
    }

    @Test
    fun `bandMigration should create bands for each corporate`() {
        // Arrange
        val corporate1 = CorporateEntity(
            1,
            "test",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            1,
            null,
            listOf(),
            1
        )
        val corporate2 = CorporateEntity(
            1,
            "test",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            1,
            null,
            listOf(),
            1
        )

        val defaultBand1 = BandsEntity(
            id = 1,
            name = "Super Admin",
            status = BandStatus.ACTIVE,
            corporate = CorporateEntity(
                -1,
                "test",
                "US",
                CorporateStatus.ACTIVE,
                true,
                null,
                1,
                null,
                listOf(),
                1
            )
        )
        val defaultBand2 = BandsEntity(
            id = 2,
            name = "Admin",
            status = BandStatus.ACTIVE,
            corporate = CorporateEntity(
                1,
                "test",
                "US",
                CorporateStatus.ACTIVE,
                true,
                null,
                1,
                null,
                listOf(),
                1
            )
        )

        val bandAccess1 = BandDetailsEntity(
            id = 1L,
            band = defaultBand1,
            access = MicroAccessMasterEntity(id = 1L, accessLevel = "ACCESS_1"),
            visibility = VisibilityMasterEntity()
        )
        val bandAccess2 = BandDetailsEntity(
            id = 1L,
            band = defaultBand1,
            access = MicroAccessMasterEntity(id = 1L, accessLevel = "ACCESS_2"),
            visibility = VisibilityMasterEntity()
        )

        defaultBand1.bandAccesses = mutableListOf(bandAccess1)
        defaultBand2.bandAccesses = mutableListOf(bandAccess2)

        val rootUser1 = CorporateUserEntity()
        val rootUser2 = CorporateUserEntity()

        val newBand1 = slot<BandsEntity>()
        val newBand2 = slot<BandsEntity>()
        val newBand3 = slot<BandsEntity>()
        val newBand4 = slot<BandsEntity>()

        every { corporateRepository.findAll() } returns listOf(corporate1, corporate2)
        every { bandsRepository.findByCorporateId(-1) } returns listOf(defaultBand1, defaultBand2)

        every { bandsRepository.save(capture(newBand1)) } returns defaultBand1.copy(id = 3)
        every { bandsRepository.save(capture(newBand2)) } returns defaultBand2.copy(id = 4)
        every { bandsRepository.save(capture(newBand3)) } returns defaultBand1.copy(id = 5)
        every { bandsRepository.save(capture(newBand4)) } returns defaultBand2.copy(id = 6)

        every { loginAccountRepository.findById(101L) } returns Optional.of(rootUser1)
        every { loginAccountRepository.findById(102L) } returns Optional.of(rootUser2)

        every { bandsRepository.findByNameAndCorporate("Super Admin", corporate1) } returns BandsEntity(
            id = 3, name = "Super Admin", corporate = corporate1,
            description = "",
            status = BandStatus.ACTIVE,
            color = "",
            corporateUsers = listOf(),
            bandAccesses = mutableListOf()
        )
        every { bandsRepository.findByNameAndCorporate("Super Admin (free)", corporate2) } returns BandsEntity(
            id = 5, name = "Super Admin (free)", corporate = corporate2,
            description = "",
            status = BandStatus.ACTIVE,
            color = "",
            corporateUsers = listOf(),
            bandAccesses = mutableListOf()
        )

        every { corporateUserRepository.save(any()) } returns rootUser1

        every { loginAccountRepository.findById(any()) } returns Optional.of(rootUser1)

        // Act
        val result = migrationService.bandMigration()

        // Assert
        assertTrue(result)

        // Verify bands were created for each corporate
        verify(exactly = 4) { bandsRepository.save(any()) }
        verify(exactly = 2) { corporateUserRepository.save(any()) }


    }

    @Test
    fun `corporateUserAccountMigration should create accounts for each corporate`() {
        // Arrange
        val corporate1 = CorporateEntity(
            1L,
            "Corp 1",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            101L,
            null,
            listOf(),
            1,
        )


        val corporate2 = CorporateEntity(
            2L,
            "Corp 2",
            "US",
            CorporateStatus.ACTIVE,
            true,
            null,
            102L,
            null,
            listOf(),
            1,
        )

        val rootUser1 = CorporateUserEntity()
        val rootUser2 = CorporateUserEntity()

        val accountSlot1 = slot<AccountEntity>()
        val accountSlot2 = slot<AccountEntity>()

        val userSlot1 = slot<CorporateUserEntity>()
        val userSlot2 = slot<CorporateUserEntity>()

        every { corporateRepository.findAll() } returns listOf(corporate1, corporate2)

        every { accountEntityRepository.saveAndFlush(capture(accountSlot1)) } answers {
            accountSlot1.captured.apply { id = 201L }
        }

        every { accountEntityRepository.saveAndFlush(capture(accountSlot2)) } answers {
            accountSlot2.captured.apply { id = 202L }
        }

        every { corporateUserRepository.findById(101L) } returns Optional.of(rootUser1)
        every { corporateUserRepository.findById(102L) } returns Optional.of(rootUser2)

        every { corporateUserRepository.saveAndFlush(capture(userSlot1)) } returns rootUser1
        every { corporateUserRepository.saveAndFlush(capture(userSlot2)) } returns rootUser2

        // Act
        val result = migrationService.corporateUserAccountMigration()

        // Assert
        assertTrue(result)

        // Verify accounts were created
        verify(exactly = 2) { accountEntityRepository.saveAndFlush(any()) }
        verify(exactly = 2) { corporateUserRepository.saveAndFlush(any()) }


    }

    @Test
    fun `expertCompanyAccountMigration should create accounts for expert companies`() {
        // Arrange
        val company1 = ExpertCompanyProfileEntity(
            id = 1,
            name = "Expert Co 1",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )
        val company2 = ExpertCompanyProfileEntity(
            id = 1,
            name = "Expert Co 2",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )
        val company3 = ExpertCompanyProfileEntity(
            id = 1,
            name = "Expert Co 3",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        val accountSlot1 = slot<AccountEntity>()
        val accountSlot2 = slot<AccountEntity>()

        val companySlot1 = slot<ExpertCompanyProfileEntity>()
        val companySlot2 = slot<ExpertCompanyProfileEntity>()

        every { expertCompanyProfileRepository.findAll() } returns listOf(company1, company2, company3)

        every { accountEntityRepository.saveAndFlush(capture(accountSlot1)) } answers {
            accountSlot1.captured.apply { id = 201L }
        }

        every { accountEntityRepository.saveAndFlush(capture(accountSlot2)) } answers {
            accountSlot2.captured.apply { id = 202L }
        }

        every { expertCompanyProfileRepository.save(capture(companySlot1)) } returns company1
        every { expertCompanyProfileRepository.save(capture(companySlot2)) } returns company2

        // Act
        val result = migrationService.expertCompanyAccountMigration()

        assertFalse(result)

    }

    @Test
    fun `expertCompanyAccountMigration should return false when no companies need migration`() {
        // Arrange
        val company1 = ExpertCompanyProfileEntity(
            id = 1,
            name = "Expert Co 1",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )
        val company2 = ExpertCompanyProfileEntity(
            id = 1,
            name = "Expert Co 2",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        every { expertCompanyProfileRepository.findAll() } returns listOf(company1, company2)

        // Act
        val result = migrationService.expertCompanyAccountMigration()

        // Assert
        assertFalse(result)

        // Verify no accounts were created
        verify(exactly = 0) { accountEntityRepository.saveAndFlush(any()) }
        verify(exactly = 0) { expertCompanyProfileRepository.save(any()) }
    }

    @Test
    fun `caseAccountMigration should update case accounts`() {
        // Arrange
        val corporateUser = CorporateUserEntity()
        val corporateAccount = AccountEntity(
            id = 201L, name = "Corp Account",
            status = AccountStatus.ACTIVE,
            description = "",
            companyName = "",
            corporate = CorporateEntity(
                2L,
                "Corp 2",
                "US",
                CorporateStatus.ACTIVE,
                true,
                null,
                102L,
                null,
                listOf(),
                1,
            ),
            corporateUsers = listOf(corporateUser),
            cases = mutableListOf()
        )
        corporateUser.accounts = setOf(corporateAccount)

        val expertUser = ExpertUserEntity(
            id = 1,
            jobTitle = "SDE",
            countryRegionId = 2,
            expertiseId = 3,
            expertises = listOf(),
            bio = "",
            displayName = "",
            infoVideoUrl = "",
            contactNumber = "123456",
            contactEmail = "<EMAIL>",
            contactWebsite = "a.com",
            companyProfile = ExpertCompanyProfileEntity(
                id = 5,
                name = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                users = mutableListOf(),
                lastUpdatedBy = 1,
                companyNumber = "123",
                companyAddress = "",
                aboutBusiness = "",
                effectiveDate = LocalDateTime.now(),
                effectiveEndDate = LocalDateTime.now(),
                contractAcceptedDate = LocalDateTime.now(),
                feesCurrency = "",
                feesAmount = "",
                specialTerms = "",
                membershipStatus = "",
                territory = "",
                renewContract = false,
                services = "",
                account = AccountEntity(
                    id = 6,
                    name = "",
                    status = AccountStatus.ACTIVE,
                    description = "",
                    companyName = "",
                    corporate = null,
                    corporateUsers = listOf(),
                    cases = mutableListOf()
                ),
                profileImage = "",
                expertContract = null,
                status = AccountStatus.ACTIVE,
                questionsQuota = 13,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 6
            ),
            expertType = "",
            viewContract = false,
            case = mutableListOf(),
            profileImage = ""
        )
        val expertCompany = ExpertCompanyProfileEntity(
            id = 1,
            name = "Expert Co 2",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        val case1 = mockk<CaseEntity>(relaxed = true) {
            every { id } returns 1L
            every { createdBy } returns ClientView(
                101L,
                "<EMAIL>",
                "fName",
                AccountStatus.PENDING_VERIFICATION,
                "cName",
                UserType.CORPORATE,
                true,
                LocalDateTime.now(),
                null,
                null,
                1,
                1,
                null,
                null,
                null,
                false,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )
        }

        val case2 = mockk<CaseEntity>(relaxed = true) {
            every { id } returns 2L
            every { createdBy } returns ClientView(
                102L,
                "<EMAIL>",
                "fName",
                AccountStatus.PENDING_VERIFICATION,
                "cName",
                UserType.EXPERT,
                true,
                LocalDateTime.now(),
                null,
                null,
                1,
                1,
                null,
                null,
                null,
                false,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )
        }

        val case3 = mockk<CaseEntity>(relaxed = true) {
            every { id } returns 3L
            every { createdBy } returns ClientView(
                103L,
                "<EMAIL>",
                "fName",
                AccountStatus.PENDING_VERIFICATION,
                "cName",
                UserType.BACKOFFICE,
                true,
                LocalDateTime.now(),
                null,
                null,
                1,
                1,
                null,
                null,
                null,
                false,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )
        }

        val caseSlot1 = slot<CaseEntity>()
        val caseSlot2 = slot<CaseEntity>()

        every { caseRepository.findByAccountIsNull() } returns listOf(case1, case2, case3)
        every { corporateUserRepository.findById(101L) } returns Optional.of(corporateUser)
        every { corporateUserRepository.findById(any()) } returns Optional.of(corporateUser)
        every { expertCompanyProfileRepository.findByUsers_Id(102L) } returns expertCompany
        every { expertCompanyProfileRepository.findByUsers_Id(any()) } returns expertCompany

        every { caseRepository.save(capture(caseSlot1)) } returns case1
        every { caseRepository.save(capture(caseSlot2)) } returns case2

        // Act
        val result = migrationService.caseAccountMigration()

        // Assert
        assertTrue(result)

        // Verify cases were updated
        verify(exactly = 2) { caseRepository.save(any()) }

    }

    @Test
    fun `caseActionForMigration should update case action for`() {
        // Arrange
        val case1 = mockk<CaseEntity>(relaxed = true) {
            every { category } returns CaseCategoryEntity(
                subCategoryId = "SUB_CAT_1",
                id = 1,
                subCategoryName = "name",
                parentCategoryId = "",
                parentCategoryName = ""
            )
            every { status } returns "STATUS_1"
        }

        val case2 = mockk<CaseEntity>(relaxed = true) {
            every { category } returns CaseCategoryEntity(
                subCategoryId = "SUB_CAT_2",
                id = 1,
                subCategoryName = "name",
                parentCategoryId = "",
                parentCategoryName = ""
            )
            every { status } returns "STATUS_2"
        }

        val statusMaster1 = CaseStatusMasterEntity(
            subCategory = "SUB_CAT_1",
            status = "STATUS_1",
            actionFor = "ACTION_1",
            id = 1,
            statusDisplayText = "",
            percentage = 22,
            dealStatusId = 1,
            showStatus = true
        )

        val statusMaster2 = CaseStatusMasterEntity(
            subCategory = "SUB_CAT_2",
            status = "STATUS_2",
            actionFor = "ACTION_2",
            id = 1,
            statusDisplayText = "",
            percentage = 22,
            dealStatusId = 1,
            showStatus = true
        )

        every { caseRepository.findByActionForIsNull() } returns listOf(case1, case2)
        every { caseStatusMasterRepository.findBySubCategoryAndStatus("SUB_CAT_1", "STATUS_1") } returns statusMaster1
        every { caseStatusMasterRepository.findBySubCategoryAndStatus("SUB_CAT_2", "STATUS_2") } returns statusMaster2
        every { caseRepository.saveAllAndFlush(any<List<CaseEntity>>()) } returns listOf(case1, case2)

        // Act
        val result = migrationService.caseActionForMigration()

        // Assert
        assertTrue(result)

        // Verify cases were updated
        verify { case1.actionFor = "ACTION_1" }
        verify { case2.actionFor = "ACTION_2" }
        verify(exactly = 1) { caseRepository.saveAllAndFlush(any<List<CaseEntity>>()) }
    }
}

    /*@Test
    fun `caseHistoryActionForMigration should update case history action for`() {
        // Arrange
        val case1 = mockk<CaseEntity>(relaxed = true) {
            every { category } returns CaseCategoryEntity(
                subCategoryId = "SUB_CAT_1",
                id = 1,
                subCategoryName = "",
                parentCategoryId = "",
                parentCategoryName = ""
            )
        }
        
        val case2 = mockk<CaseEntity>(relaxed = true) {
            every { category } returns CaseCategoryEntity(
                subCategoryId = "SUB_CAT_2",
                id = 1,
                subCategoryName = "",
                parentCategoryId = "",
                parentCategoryName = ""
            )
        }
        
        val history1 = CaseStatusHistoryEntity(
            id = 1L,
            case = case1,
            status = "STATUS_1"
        )
        
        val history2 = CaseStatusHistoryEntity(
            id = 2L,
            case = case2,
            status = "STATUS_2"
        )
        
        val history3 = CaseStatusHistoryEntity(
            id = 3L,
            case = case2,
            status = "OLD_STATUS",
            actionFor = "EXISTING_ACTION"
        )
        
        val statusMaster1 = CaseStatusMasterEntity(
            subCategory = "SUB_CAT_1",
            status = "STATUS_1",
            actionFor = "ACTION_1"
        )
        
        val statusMaster2 = CaseStatusMasterEntity(
            subCategory = "SUB_CAT_2",
            status = "STATUS_2",
            actionFor = "ACTION_2"
        )
        
        val statusMaster3 = CaseStatusMasterEntity(
            subCategory =*/