package com.centuroglobal.service

import com.centuroglobal.data.pojo.*
import com.centuroglobal.facade.AdminClientFacade
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.*
import com.centuroglobal.shared.data.properties.AwsS3Properties
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.PartnerEmailUtil
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.random.Random


class PartnerServiceTest {

    private val corporateUserRepository:CorporateUserRepository  = mockk()

    private val partnerRepository: PartnerRepository = mockk()
    //private val loginAccountRepository: loginAccountRepository = mockk()
    private val expertUserRepository: ExpertUserRepository = mockk()
    private val expertCompanyProfileRepository: ExpertCompanyProfileRepository = mockk()
    private val corporateService: CorporateService = mockk()
    private val bandsRepository: BandsRepository = mockk()
    private val microAccessMasterRepository: MicroAccessMasterRepository = mockk()
    private val s3Service: AwsS3Service = mockk()
    private val corporateRepository:CorporateRepository= mockk()
    private val tokenVerificationService: TokenVerificationService = mockk()
    private val loginAccountRepository : LoginAccountRepository = mockk()
    private val mailSendingService: MailSendingService= mockk()
    private  val userRoleRepository: UserRoleRepository= mockk()
    private val adminUserService: AdminUserService= mockk()
    private val expertUserService: ExpertUserService= mockk()
    private val validationTokenRepository: ValidationTokenRepository= mockk()
    private val clientService: ClientService= mockk()
    private val adminClientFacade: AdminClientFacade= mockk()
    private val caseDashboardService: CaseDashboardService = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()
    private val clientDocRepository: ClientDocRepository = mockk()
    private val visibilityMasterRepository: VisibilityMasterRepository = mockk()
    private val partnerEmailUtil: PartnerEmailUtil = mockk()
    private val s3Properties:AwsS3Properties = mockk()

    private val userProfileFolder = "test-bucket"

    private val onboardingDocsFolder = "test-bucket2"

    private val partnerService = PartnerService(
        userProfileFolder,
        onboardingDocsFolder,
        partnerRepository,
        corporateUserRepository,
        corporateRepository,
        expertUserRepository,
        expertCompanyProfileRepository,
        bandsRepository,
        tokenVerificationService,
        microAccessMasterRepository,
        s3Service,
        loginAccountRepository,
        mailSendingService,
        userRoleRepository,
        expertUserService,
        validationTokenRepository,
        adminClientFacade,
        userProfileUtil,
        clientDocRepository,
        visibilityMasterRepository,
        partnerEmailUtil,
        corporateService
    )

    private val corporateUserEntity:CorporateUserEntity= mockk()
    private val authenticatedUser:AuthenticatedUser= mockk()
    private val loginAccountEntity:LoginAccountEntity  = mockk()


    private val corporateEntity = CorporateEntity(
        1,
        "test",
        "US",
        CorporateStatus.ACTIVE,
        true,
        null,
        1,
        null,
        listOf(corporateUserEntity),
        1
    )

    @BeforeEach
    fun setup(){
        every { authenticatedUser.userId } returns LOGGED_IN_USER_ID
        every { authenticatedUser.companyId } returns LOGGED_IN_USER_CORPORATE_ID
        every { authenticatedUser.email } returns LOGGED_IN_USER_EMAIL

        every { corporateUserEntity.id } returns LOGGED_IN_USER_ID
        every { corporateUserEntity.corporate } returns corporateEntity
        every { corporateUserRepository.findById(LOGGED_IN_USER_ID) } returns Optional.of(corporateUserEntity)

        every { loginAccountEntity.id } returns LOGGED_IN_USER_ID

        every { microAccessMasterRepository.findAllByFeatureKey(any()) } returns listOf(mockk<MicroAccessMasterEntity>())
        val bandsEntity = mockk<BandsEntity>()
        every { bandsRepository.save(any()) } returns bandsEntity
        every { bandsEntity.id } returns 105L
        every { s3Service.uploadFromTmp(any(), any(), any()) } returns true
        every { s3Properties.defaultBucket } returns "test-bucket"
        every { s3Properties.publicBucketUri } returns "test-bucket"
        every {(s3Service.updateProfilePicture("string", "test-bucket/string", null))} returns(true.toString())

        every { (tokenVerificationService.createToken(any(), any()))} returns Unit
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    private fun generateCreatePartnerUserRequest(): PartnerUserDetails {
        return PartnerUserDetails(
            "John",
            "Doe",
            "CTO",
            "US",
            "<EMAIL>",
            "CENTURO GLOBAL",
            "+198754542",
            UUID.randomUUID().toString(),
            "string"
        )
    }

    private fun generateCreatePartnerRequest(createFrom: PartnerType, refId: Long? = null): PartnerCreateRequest {

        return PartnerCreateRequest(
            createFrom = createFrom,
            createReferenceId = refId,
            name = "John Partner",
            country = "US",
            startDate = TimeUtil.toEpochMillis(LocalDate.now().minusDays(10)),
            endDate = TimeUtil.toEpochMillis(LocalDate.now().plusDays(10)),
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.SELF,
            companyLogo = null,
            primaryColor = "RED",
            secondaryColor = "BLUE",
            rootUserDetails = if (createFrom == PartnerType.NEW) generateCreatePartnerUserRequest() else null,
            features = mutableListOf("CASE", "QUERY"),
            onboardingDocs = listOf(),
            aiMessageCount = 6,
            corporateFeatures = listOf()
        )
    }

    private fun generatePartnerEntity(request: PartnerCreateRequest): PartnerEntity {

        return PartnerEntity(
            id = Random(10).nextLong(),
            name = request.name!!,
            createdFrom = request.createFrom,
            contractFromDate = TimeUtil.fromInstant(request.startDate / 1000),
            contractToDate = TimeUtil.fromInstant(request.endDate / 1000),
            casesManaged = request.casesManagedBy,
            queriesManaged = request.queryManagedBy,
            status = CorporateStatus.PENDING_VERIFICATION,
            companyLogo = request.companyLogo,
            themePrimaryColor = request.primaryColor,
            themeSecondaryColor = request.secondaryColor,
            country = request.country,
            band = mockk<BandsEntity>(),
            referenceId = request.createReferenceId,
            rootUserId = 1,
            corporates = listOf(),
            partnerUsers = mutableListOf(),
            associatedCompanies = mutableListOf(),
            corporateAccess = "",
        )
    }

    private fun generateloginAccountEntity(request: PartnerUserDetails?, partnerEntity: PartnerEntity?=null): LoginAccountEntity {
        val loginAccountEntity = ExpertUserEntity(
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")

        loginAccountEntity.id = Random(10).nextLong()
        loginAccountEntity.firstName = request!!.firstName
        loginAccountEntity.lastName = request.lastName
        //loginAccountEntity.jobTitle = request.jobTitle
        loginAccountEntity.countryCode = request.country
        loginAccountEntity.email = request.email
        loginAccountEntity.contactNo = request.contactNumber
        loginAccountEntity.partner = partnerEntity
        loginAccountEntity.partnerBand = partnerEntity?.band
        loginAccountEntity.profilePhotoUrl = request.profilePicS3Key
        loginAccountEntity.role = Role.ROLE_PARTNER


        return loginAccountEntity
    }


    @Test
    fun `create new Partner`() {

        val request = generateCreatePartnerRequest(PartnerType.NEW, authenticatedUser.userId)

        val partnerEntity = generatePartnerEntity(request)
        val loginAccountEntity = generateloginAccountEntity(request.rootUserDetails, partnerEntity)

        every { partnerRepository.save(any()) } returns partnerEntity
        every { loginAccountRepository.save(any()) } returns loginAccountEntity

        val expertCompanyProfileEntity =  ExpertCompanyProfileEntity(
            id = 1,
            name = "Test Name",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = true,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 1,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 2
        )

        val expertUserEntity = ExpertUserEntity(
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        expertUserEntity.id = 1L
        expertUserEntity.companyProfile = expertCompanyProfileEntity

        partnerEntity.partnerUsers = mutableListOf(expertUserEntity)
        val validationToken = ValidationTokenEntity( id = 1L, code= "Test",
            expiryDate = LocalDateTime.now(), state = ValidationState.VERIFIED, type = ValidationType.INVITE_PARTNER_EXPERT,
            userId = 1L)

        every { expertUserService.createPrimaryExpertUser(any(), any())} returns 1L
        every { expertCompanyProfileRepository.findById(any())} returns Optional.of(expertCompanyProfileEntity)
        every { expertUserRepository.findAllByCompanyProfileAndExpertType(any(), any())} returns listOf(expertUserEntity)
        every { expertUserRepository.save(any())} returns expertUserEntity
        //every { loginAccountRepository.saveUser(any(), any(), any(), any())} returns Unit
        every{ loginAccountRepository.findById(any())} returns Optional.of(loginAccountEntity)

        every { userRoleRepository.save(any())} returns UserRoleEntity(role = Role.ROLE_EXPERT, id = 1, user = expertUserEntity)
        //every { loginAccountRepository.update(any(), any())} returns Unit
        every { s3Service.getS3PublicUrl(any())} returns "string"
        every { mailSendingService.sendEmail(any()) } returns true
        every { expertUserRepository.findById(any())} returns Optional.of(expertUserEntity)
        every { validationTokenRepository.findByUserId(any())} returns validationToken
        every { validationTokenRepository.save(any())} returns validationToken


        every { visibilityMasterRepository.findAllByFeatureKey(any())} returns listOf(mockk())
        every { validationTokenRepository.findByUserIdAndType(any(), any()) } returns validationToken
        every { clientDocRepository.saveAll(mutableListOf()) } returns mutableListOf()

        val result = partnerService.createPartner(request, authenticatedUser)
        assertNotNull(result)
        assertEquals(partnerEntity.id, result)
    }

    @Test
    fun `create a Partner from existing corporate`() {

        val corporateId = 5L
        val corporateRootUserId = 10L
        val request = generateCreatePartnerRequest(PartnerType.CORPORATE, corporateId)

        val corporateEntity = CorporateEntity(countryCode = "US", rootUserId = corporateRootUserId,
            name = "John Corporate", lastUpdatedBy = authenticatedUser.userId, status = CorporateStatus.ACTIVE,
            subscriptionActive = true)

        val corporateUserEntity2 = CorporateUserEntity()
        corporateUserEntity2.id = corporateRootUserId
        corporateUserEntity2.jobTitle = "CFO"

        corporateEntity.users = listOf(corporateUserEntity2)

        val partnerEntity = generatePartnerEntity(request)

        val loginAccountEntity = CorporateUserEntity()
        loginAccountEntity.firstName = "test"
        loginAccountEntity.lastName = "test"

        val validationToken = ValidationTokenEntity( id = 1L, code= "Test",
            expiryDate = LocalDateTime.now(), state = ValidationState.VERIFIED, type = ValidationType.INVITE_PARTNER_EXPERT,
            userId = 1L)

        //every { loginAccountEntity.id } returns corporateRootUserId
        every { corporateRepository.findById(corporateId) } returns Optional.of(corporateEntity)
        every { corporateUserRepository.findById(corporateRootUserId) } returns Optional.of(corporateUserEntity2)
        every { partnerRepository.save(any()) } returns partnerEntity
        //every { loginAccountRepository.saveUser(any(), any(), any(), any()) } returns Unit
        every { loginAccountRepository.findById(corporateRootUserId) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.save(any())} returns corporateUserEntity
        every { userRoleRepository.save(any())} returns UserRoleEntity(role = Role.ROLE_PARTNER, id = 1, user = loginAccountEntity)
        //every { loginAccountRepository.updateUserType(any(), any())} returns Unit
        every { s3Service.getS3PublicUrl(any())} returns "string"
        every { mailSendingService.sendEmail(any()) } returns true
        every { validationTokenRepository.findByUserId(any())} returns validationToken
        every { validationTokenRepository.save(any())} returns validationToken

        every { visibilityMasterRepository.findAllByFeatureKey(any())} returns listOf(mockk())
        every { validationTokenRepository.findByUserIdAndType(any(), any()) } returns validationToken
        every { clientDocRepository.saveAll(mutableListOf()) } returns mutableListOf()

        val result = partnerService.createPartner(request, authenticatedUser)
        assertNotNull(result)
        assertEquals(partnerEntity.id, result)
    }

    @Test
    fun `create a Partner from existing expert company`() {

        val expertCompanyId = 5L
        val primaryExpertId = 10L
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, expertCompanyId)

        val expertEntity = mockk<ExpertCompanyProfileEntity>()

        val expertUserEntity = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        expertUserEntity.role = Role.ROLE_EXPERT

        val partnerEntity = generatePartnerEntity(request)

        expertUserEntity.firstName = "test"
        expertUserEntity.lastName = "test"

        val validationToken = ValidationTokenEntity( id = 1L, code= "Test",
            expiryDate = LocalDateTime.now(), state = ValidationState.VERIFIED, type = ValidationType.INVITE_PARTNER_EXPERT,
            userId = 1L)


        every { expertCompanyProfileRepository.findById(expertCompanyId) } returns Optional.of(expertEntity)
        every { expertUserRepository.findAllByCompanyProfileAndExpertType(expertEntity, ExpertType.PRIMARY.name) } returns listOf(expertUserEntity)
        every { expertUserRepository.save(any())} returns expertUserEntity
        every { partnerRepository.save(any()) } returns partnerEntity
        every { loginAccountEntity.firstName} returns "test"
        every { loginAccountEntity.email} returns "<EMAIL>"
        //every { loginAccountRepository.saveUser(any(), any(), any(), any()) } returns Unit
        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccountEntity)
        every { userRoleRepository.save(any())} returns UserRoleEntity(role = Role.ROLE_PARTNER, id = 1, user = loginAccountEntity)
        //every { loginAccountRepository.updateUserType(any(), any())} returns Unit
        every { s3Service.getS3PublicUrl(any())} returns "string"
        every { mailSendingService.sendEmail(any()) } returns true
        every { validationTokenRepository.findByUserId((any()))} returns validationToken
        every {validationTokenRepository.save(any())} returns validationToken

        every { visibilityMasterRepository.findAllByFeatureKey(any())} returns listOf(mockk())
        every { validationTokenRepository.findByUserIdAndType(any(), any()) } returns validationToken
        every { clientDocRepository.saveAll(mutableListOf()) } returns mutableListOf()


        val result = partnerService.createPartner(request, authenticatedUser)
        assertNotNull(result)
        assertEquals(partnerEntity.id, result)
    }



    @Test
    fun `test retrieveCorporates - returns list of corporates`() {
        // Arrange
        val partnerId = 10L
        val corporates = listOf(
            CorporateEntity(id = 10L, name = "Corporate 1", countryCode = "IN", lastUpdatedBy = TimeUtil.toEpochMillis(LocalDateTime.now()),
                rootUserId = 1L, subscriptionActive = true, status = CorporateStatus.ACTIVE)
        )

        val answer = corporates.filter { it.id != -1L }.filter { it.users.isNotEmpty() }
            .map { mapOf("id" to it.id!!.toString(), "name" to it.name) }

        every { corporateRepository.findAllByPartnerId(partnerId) } returns listOf()

        // Act
        val result = partnerService.retrieveCorporates(partnerId)

        // Assert
        assertEquals(answer.size, result.size)
        assertEquals(answer, result)
        verify(exactly = 1) { corporateRepository.findAllByPartnerId(partnerId) }
    }

    @Test
    fun `test ExpertCompany - returns list of ExpertCompany`() {
        // Arrange
        val partnerId = 10L
        val expertCompany = listOf(
            ExpertCompanyProfileEntity(
                id = 1,
                name = "Test Name",
                users = mutableListOf(),
                lastUpdatedBy = 0,
                aboutBusiness = "",
                account = AccountEntity(
                    name = "Test Name",
                    corporate = null
                ),
                companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
                effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
                feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                renewContract = false,
                profileImage = "",
                expertContract = ExpertContractEntity(),
                status = AccountStatus.ACTIVE,
                questionsQuota = 2,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 5
            )
        )
        val expertCompanyProfile = ExpertCompanyProfile(

            name = "Test Name",
            aboutBusiness = "",
            companyAddress = "", companyNumber = "",
            effectiveDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            effectiveEndDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            associatedPartners = mutableListOf(),
            logoFullUrl = "",
            sizeName = "",
            partnerId = 4,
            contract = "",
            aiMessageCount = 5
        )

        val answer = listOf(expertCompanyProfile)

//        val answer = expertCompany.filter { it.id != -1L }.filter { it.users.isNotEmpty() }
//            .map { mapOf("id" to it.id!!.toString(), "name" to it.name) }
        //every { expertCompanyProfileRepository.findAllByAssociatedPartnersId(partnerId) } returns expertCompany
//        every { expertUserService.retrieveExpertCompany(any())} returns expertCompany.filter { it.id != -1L }.filter { it.users.isNotEmpty() }
//            .map { mapOf("id" to it.id!!.toString(), "name" to it.name) }

        every { expertUserService.retrieveExpertCompany(any())} returns expertCompanyProfile

        val refData = ReferenceData(
            id = 4,
            name = "abc"
        )

        every { expertUserService.retrieveExpertCompany(any(), any())} returns listOf(refData)

        // Act
        val result = partnerService.retrieveExpertCompany(partnerId)

        // Assert
        assertEquals(answer.size, result.size)
        //assertEquals(answer, result)
    }

    @Test
    fun `test retrieveBaseUsersList for corporate partner`() {
        // Arrange
        val partnerId = 1L
        val corporateId = 5L
        val corporateRootUserId = 10L
        val request = generateCreatePartnerRequest(PartnerType.CORPORATE, corporateId)

        val corporateEntity = CorporateEntity(countryCode = "US", rootUserId = corporateRootUserId,
            name = "John Corporate", lastUpdatedBy = authenticatedUser.userId, status = CorporateStatus.ACTIVE,
            subscriptionActive = true)

        val corporateUserEntity2 = CorporateUserEntity()
        corporateUserEntity2.id = corporateRootUserId
        corporateUserEntity2.jobTitle = "CFO"

        val corporateUsers = listOf(corporateUserEntity2)

        val answer = corporateUsers.filter { it.status == AccountStatus.ACTIVE }.map {
            mapOf(
                "id" to it.id.toString(),
                "name" to it.firstName + " " + it.lastName,
                "type" to it.getUserType().name
            )
        }

        val partnerEntity = generatePartnerEntity(request)

        every { (partnerRepository.findById(partnerId))} returns Optional.of(partnerEntity)
        every { corporateUserRepository.findAllByCorporateIdAndBandNameIn(corporateId, listOf("Super Admin (free)", "Super Admin"))} returns corporateUsers

        // Act
        val result = partnerService.retrieveBaseUsersList(partnerId)

        // Assert
        assertEquals(answer.size, result?.size)
        assertEquals(answer, result)
    }

    @Test
    fun `test retrieveBaseUsersList for expert partner`() {
        // Arrange
        val partnerId = 1L
        val expertId = 5L
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, expertId)

        val expertCompanyProfileEntityEntity = ExpertCompanyProfileEntity(
            id = expertId,
            name = "Test Name",
            users = mutableListOf(),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        val answer = expertCompanyProfileEntityEntity.users.map {
            mapOf(
                "id" to it.id.toString(),
                "name" to it.firstName + " " + it.lastName
            )
        }

        val partnerEntity = generatePartnerEntity(request)

        every { (partnerRepository.findById(partnerId))} returns Optional.of(partnerEntity)
        every { expertCompanyProfileRepository.findByUsers_Id(expertId)} returns expertCompanyProfileEntityEntity
        every { expertCompanyProfileRepository.findById(expertId)} returns Optional.of(expertCompanyProfileEntityEntity)

        // Act
        val result = partnerService.retrieveBaseUsersList(partnerId)

        // Assert
        assertEquals(answer.size, result?.size)
        assertEquals(answer, result)
    }

    @Test
    fun `test retrievePartnerUserDetails for existing user`() {
        // Arrange
        val userId = 1L
        val partnerUser = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        )

        every { s3Service.getProfilePicUrl(any()) } returns ""

        partnerUser.id = 1L

        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 1L)
        val partnerEntity = generatePartnerEntity(request)

        val loginAccountEntity: LoginAccountEntity = mockk()
        every { loginAccountEntity.partnerJobTitle } returns "string"
        every { loginAccountEntity.id } returns 1
        every { loginAccountEntity.email } returns  ""
        every { loginAccountEntity.contactNo } returns  "9123"
        every { loginAccountEntity.profilePhotoUrl } returns null
        every { loginAccountEntity.firstName } returns ""
        every { loginAccountEntity.lastName } returns ""
        every { loginAccountEntity.countryCode } returns ""
        every { loginAccountEntity.dialCode } returns null
        every { loginAccountEntity.partner } returns partnerEntity

        every { (partnerRepository.findById(1L))} returns Optional.of(partnerEntity)
        every { loginAccountRepository.findByPartnerAndId(partnerEntity, userId) } returns Optional.of(loginAccountEntity)
        every { loginAccountEntity.partnerJobTitle} returns "string"

        val answer2 = UserDetails(
            firstName = partnerUser.firstName,
            lastName = partnerUser.lastName,
            jobTitle = partnerUser.jobTitle,
            country = partnerUser.countryCode,
            email = partnerUser.email,
            contactNumber = partnerUser.contactNumber,
            dialCode = partnerUser.dialCode,
            profileImageUrl = partnerUser.profilePhotoUrl?.let { s3Service.getProfilePicUrl(it) },
            companyName = partnerEntity.name
        )

        // Act
        val result = partnerService.retrievePartnerUserDetails(1L,userId)

        // Assert
        assertEquals(answer2, result)
    }

    @Test
    fun `test updatePartnerUser for existing user`() {
        // Arrange
        val partnerId = 1L
        val userId = 1L
        val updateRequest = UpdatePartnerUserRequest(
            firstName = "John",
            lastName = "Doe",
            jobTitle = "Software Engineer",
            contactNumber = "",
            country = "IN",
            profilePicS3Key = "string",
            dialCode = "91"
        )
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 1L)
        val partnerEntity = generatePartnerEntity(request)
        val partnerUser = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        partnerUser.id = 1L

        every { (partnerRepository.findById(1L))} returns Optional.of(partnerEntity)
        every { partnerRepository.save(any()) } returns partnerEntity
        every { loginAccountRepository.save(any()) } returns partnerUser
        every { loginAccountRepository.findByPartnerAndId(partnerEntity, userId) } returns Optional.of(partnerUser)

        every { s3Service.getProfilePicUrl(any()) } returns ""

        // Act
        val result = partnerService.updatePartnerUser(partnerId, userId, updateRequest, authenticatedUser)

        // Assert
        assertEquals(updateRequest.firstName, partnerUser.firstName)
        assertEquals(updateRequest.lastName, partnerUser.lastName)
        //assertEquals(updateRequest.jobTitle, result?.jobTitle)
        assertEquals(updateRequest.country, partnerUser.countryCode)
        assertEquals(updateRequest.contactNumber, partnerUser.contactNo)
        verify(exactly = 1) { loginAccountRepository.save(partnerUser) }
    }

    @Test
    fun `test updatePartner - successful partner update`() {
        // Arrange
        val partnerId = 1L
        val userId = 1L
        val bandId=2L
        val updateRequest = UpdatePartnerRequest(
            name = "Updated Name",
            startDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            endDate = TimeUtil.toEpochMillis(LocalDateTime.now()),
            casesManagedBy = PartnerCaseType.CG,
            queryManagedBy = PartnerCaseType.CG,
            country = "IN",
            rootUserDetails = UpdatePartnerUserDetails(
                firstName = "Test",
                lastName = "Name",
                jobTitle = "test",
                country = "IN",
                contactNumber = "123",
                dialCode = "91",
                profilePicS3Key = "string"
            ),
            primaryColor = "",
            secondaryColor = "",
            companyLogo = "string",
            features = listOf(),
            onboardingDocs = listOf(),
            corporateFeatures = listOf()
        )

        val request = generateCreatePartnerRequest(PartnerType.NEW, 1L)
        val partnerEntity = generatePartnerEntity(request)
        val partnerUser = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        partnerUser.id = 1L

        val bandsEntity = BandsEntity(
            id = bandId,
            name = "band Name",
            status = BandStatus.ACTIVE
        )
        every { bandsRepository.findById(bandId) }  returns Optional.of(bandsEntity)
        every { bandsRepository.save(any()) } returns bandsEntity

        partnerEntity.band = bandsEntity
        partnerEntity.rootUserId = userId
        every { partnerRepository.findById(partnerId) } returns Optional.of(partnerEntity)
        every { loginAccountRepository.findById(userId) } returns Optional.of(partnerUser)
        every { partnerRepository.save(any()) } returns partnerEntity
        every { loginAccountRepository.save(any()) } returns partnerUser

        every { partnerEmailUtil.getPartnerLogoURI(any()) } returns ""

        val clientDocEntity = ClientDocEntity(
            referenceId = 1,
            referenceType = UserDocType.PARTNER,
            docType = ClientDocType.USER,
            fileName = "logo.png",
            id = 1,
            docName = "logo.png",
            docKey = "logo.png",
            fileSize = 1234,
            fileType = "",
            country = "IN",
            createdBy = 3,
            expiryDate = null,
            docSubType = DocSubType.VISA.toString(),
            metadata = "",
            issueCountry = "",
            clientDocFile = mutableListOf(),
        )
        every { clientDocRepository.findByReferenceIdAndReferenceTypeAndDocType(any(), any(), any()) } returns mutableListOf(clientDocEntity)
        every { clientDocRepository.deleteAll()} returns Unit
        every { clientDocRepository.deleteAll(any())} returns Unit
        every { clientDocRepository.saveAll(mutableListOf(clientDocEntity)) } returns mutableListOf(clientDocEntity)
        every { partnerRepository.saveAndFlush(any()) } returns partnerEntity
        every { partnerRepository.flush()} returns Unit
        every { loginAccountRepository.save(any()) } returns partnerUser


        // Act
        val updatedPartner = partnerService.updatePartner(partnerId, updateRequest, authenticatedUser)

        // Assert
        assertEquals(updateRequest.name, updatedPartner?.name)
        assertEquals(updateRequest.country, updatedPartner?.country)
        assertEquals(updateRequest.casesManagedBy, updatedPartner?.casesManaged)
        assertEquals(updateRequest.primaryColor, updatedPartner?.themePrimaryColor)

        verify(exactly = 1) { partnerRepository.findById(partnerId) }
    }


    @Test
    fun testCreatePartnerUser() {
        // Mock data
        val partnerId = 1L
        val request = generateCreatePartnerRequest(PartnerType.NEW, 1L)
        val partnerEntity = generatePartnerEntity(request)
        val partnerUserCreateRequest = PartnerUserCreateRequest(
            partnerId = partnerId,
            userDetails = PartnerUserDetails(
                firstName = "John",
                lastName = "Doe",
                jobTitle = "Manager",
                country = "US",
                email = "<EMAIL>",
                contactNumber = "**********",
                profilePicS3Key = "",
                dialCode = "+1",
                companyName = "string"
            )
        )
        val expertCompanyProfileEntityEntity = ExpertCompanyProfileEntity(
            id = 1L,
            name = "Test Name",
            users = mutableListOf(),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )
        val loginAccountEntity = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")

        // Mock repository methods
        every{ (partnerRepository.findById(partnerId))} returns (Optional.of(partnerEntity))
        every { partnerRepository.save(any()) } returns partnerEntity
        every { loginAccountRepository.save(any()) } returns loginAccountEntity
        every { expertCompanyProfileRepository.findById(1)} returns Optional.of(expertCompanyProfileEntityEntity)
        every { expertUserRepository.save(any())} returns loginAccountEntity
        every { userRoleRepository.save(any())} returns UserRoleEntity(role = Role.ROLE_EXPERT, user = loginAccountEntity)
        every { userRoleRepository.saveAll(any<List<UserRoleEntity>>()) } returns
                mutableListOf(UserRoleEntity(id= 1L,
                    role = Role.ROLE_SUPPLIER, user = loginAccountEntity))

        // Call the function
        val createdUserId = partnerService.createPartnerUser(partnerUserCreateRequest)

        // Verify the result
        assertEquals(loginAccountEntity.id, createdUserId)
    }

    @Test
    fun `test retrieveRootUserDetails for corporate partner`() {
        // Arrange
        val partnerId = 1L
        val corporateId = 5L
        val corporateRootUserId = 10L
        val request = generateCreatePartnerRequest(PartnerType.CORPORATE, corporateId)
        val partnerEntity = generatePartnerEntity(request)

        val corporateEntity = CorporateEntity(countryCode = "US", rootUserId = corporateRootUserId,
            name = "John Corporate", lastUpdatedBy = authenticatedUser.userId, status = CorporateStatus.ACTIVE,
            subscriptionActive = true)

        val corporateUserEntity2 = CorporateUserEntity()
        corporateUserEntity2.id = corporateRootUserId
        corporateUserEntity2.jobTitle = "CFO"

        val answer = mapOf(
            "firstName" to corporateUserEntity2.firstName,
            "lastName" to corporateUserEntity2.lastName,
            "jobTitle" to corporateUserEntity2.jobTitle,
            "email" to corporateUserEntity2.email,
            "country" to corporateUserEntity2.countryCode,
            "dialCode" to corporateUserEntity2.dialCode,
            "contactNumber" to corporateUserEntity2.contactNo,
            "profileImageUrl" to corporateUserEntity2.profilePhotoUrl?.let {s3Service.getProfilePicUrl(it) },
            "aiMessageCount" to corporateUserEntity2.questionsQuota
        )



        every { corporateRepository.findById((corporateId))} returns Optional.of(corporateEntity)
        every { partnerRepository.findById(partnerId)} returns Optional.of(partnerEntity)
        every { corporateUserRepository.findById(corporateRootUserId)} returns Optional.of(corporateUserEntity2)

        // Act
        val result = partnerService.retrieveRootUserDetails(PartnerType.CORPORATE.toString(),corporateId)

        // Assert
        assertEquals(answer.size, result.size)
        assertEquals(answer, result)
    }

    @Test
    fun `test retrieveRootUserDetails for expert partner`() {
        // Arrange
        val partnerId = 1L
        val expertId = 5L
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, expertId)
        val partnerEntity = generatePartnerEntity(request)

        val expertCompanyProfileEntityEntity = ExpertCompanyProfileEntity(
            id = expertId,
            name = "Test Name",
            users = mutableListOf(),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        val primaryExpert = mockk<ExpertUserEntity>()

        val primaryExpertList = listOf(primaryExpert)

        every {(primaryExpert.dialCode)} returns("91")

        every { primaryExpert.id } returns 5L
        every { primaryExpert.jobTitle } returns "CFO"
        every {primaryExpert.firstName } returns "Test"
        every {primaryExpert.lastName } returns "Name"
        every {primaryExpert.email } returns "<EMAIL>"
        every {primaryExpert.countryCode } returns "IN"
        every {primaryExpert.contactNo} returns "91"
        every {primaryExpert.profilePhotoUrl} returns "string"
        every { s3Service.getProfilePicUrl(any())} returns  "string"
        every { primaryExpert.questionsQuota } returns 5
        val answer = mapOf(
            "firstName" to primaryExpert.firstName,
            "lastName" to primaryExpert.lastName,
            "jobTitle" to primaryExpert.jobTitle,
            "email" to primaryExpert.email,
            "country" to primaryExpert.countryCode,
            "dialCode" to primaryExpert.dialCode,
            "contactNumber" to primaryExpert.contactNo,
            "profileImageUrl" to primaryExpert.profilePhotoUrl?.let {s3Service.getProfilePicUrl(it) },
            "aiMessageCount" to primaryExpert.questionsQuota
        )


        every { (partnerRepository.findById(partnerId))} returns Optional.of(partnerEntity)
        every { expertCompanyProfileRepository.findById(expertId)} returns Optional.of(expertCompanyProfileEntityEntity)
        every { expertUserRepository.findAllByCompanyProfileAndExpertType(expertCompanyProfileEntityEntity,
            ExpertType.PRIMARY.name)} returns primaryExpertList
        every { expertCompanyProfileRepository.findByUsers_Id(expertId)} returns expertCompanyProfileEntityEntity



        // Act
        val result = partnerService.retrieveRootUserDetails("EXPERT", expertId)

        // Assert
        assertEquals(answer.size, result?.size)
        assertEquals(answer, result)
    }

    @Test
    fun `test getPartnerId with valid user ID belonging to a partner`() {
        // Arrange
        val userId = 123L
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 5L)
        val partnerEntity = generatePartnerEntity(request)


        every {(partnerRepository.findByPartnerUsersId(userId))} returns (partnerEntity)

        // Act
        val result = partnerService.getPartnerId(userId)

        // Assert
        assertEquals(partnerEntity.id, result)
    }
    @Test
    fun `test listUserDetails`() {
        // Mock data
        val userId = 1L
        val user = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")

        user.firstName = "John"
        user.lastName ="Partner"
        user.jobTitle="job"
        user.email = "<EMAIL>"
        user.contactNo = "69310"
        user.dialCode ="91"
        user.profilePhotoUrl ="string"


        // Mock repository method
        every { user.id?.let { loginAccountRepository.findById(it) } } returns Optional.of(user)
        every { loginAccountRepository.findById(user.id!!) } returns Optional.of(user)
        every { s3Service.getProfilePicUrl(user.profilePhotoUrl) } returns ("string")
        every { expertUserRepository.findById(1L)} returns Optional.of(user)


        // Call the method
        val userDetails = user.id?.let { partnerService.listUserDetails(it) }

        // Verify the repository method was called

        verify { user.id?.let { loginAccountRepository.findById(it) } }

        // Verify the result
        assertEquals(userDetails!!.firstName, user.firstName)
        assertEquals(userDetails.email, user.email)
    }


    @Test
    fun `test listPartnerExpert`() {
        // Arrange
        val filter = PartnerExpertSearchFilter()
        val pageRequest = PageRequest.of(0, 10)

        val partnerId = 10L

        val companyProfile: ExpertCompanyProfileEntity= mockk()
        every { companyProfile.companyType } returns ExpertCompanyType.EXPERT
        every { companyProfile.associatedPartners } returns mutableListOf()

        val expertList = listOf(
            ExpertCompanyProfileEntity(
                id = 1,
                name = "Test Name",
                users = mutableListOf(
                    ExpertUserEntity(
                        id = 1L,
                        contactEmail = "<EMAIL>",
                        contactNumber = "9123",
                        contactWebsite = "1@test",
                        displayName = "test",
                        expertType = "ACTIVE",
                        jobTitle = "string",
                        companyProfile = companyProfile
                    )
                ),
                lastUpdatedBy = 0,
                aboutBusiness = "",
                account = AccountEntity(
                    name = "Test Name",
                    corporate = null
                ),
                companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
                effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
                feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                renewContract = false,
                profileImage = "",
                expertContract = ExpertContractEntity(),
                status = AccountStatus.ACTIVE,
                questionsQuota = 2,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 5
            )
        )

        every { (expertCompanyProfileRepository.searchByCriteria(partnerId, filter, pageRequest))
        } returns PageImpl(expertList, pageRequest, expertList.size.toLong())

        // Act
        val result = partnerService.listPartnerExpert(partnerId, filter, pageRequest, authenticatedUser)

        // Assert
        assertEquals(expertList.size.toLong(), result.totalElements)
        assertEquals(expertList[0].id, result.rows[0]!!.id)
        assertEquals(expertList[0].name, result.rows[0]!!.name)
        assertEquals(expertList[0].users[0].countryCode, result.rows[0]!!.country)
        assertEquals(expertList[0].users.size.toLong(), result.rows[0]!!.expertUser)

    }

    @Test
    fun `test listPartnerExpertWithStats`() {
        // Arrange
        val filter = PartnerExpertSearchFilter()
        val pageRequest = PageRequest.of(0, 10)

        val partnerId = 10L

        val companyProfile: ExpertCompanyProfileEntity= mockk()
        every { companyProfile.companyType } returns ExpertCompanyType.EXPERT
        every { companyProfile.associatedPartners } returns mutableListOf()

        val expertList = listOf(
            ExpertCompanyProfileEntity(
                id = 1,
                name = "Test Name",
                users = mutableListOf(
                    ExpertUserEntity(
                        id = 1L,
                        contactEmail = "<EMAIL>",
                        contactNumber = "9123",
                        contactWebsite = "1@test",
                        displayName = "test",
                        expertType = "ACTIVE",
                        jobTitle = "string",
                        companyProfile = companyProfile
                    )
                ),
                lastUpdatedBy = 0,
                aboutBusiness = "",
                account = AccountEntity(
                    name = "Test Name",
                    corporate = null
                ),
                companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
                effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
                feesCurrency = "", membershipStatus = "ACTIVE", services = "", specialTerms = "", territory = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                renewContract = false,
                profileImage = "",
                expertContract = ExpertContractEntity(),
                status = AccountStatus.ACTIVE,
                questionsQuota = 2,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 5
            )
        )


        every { (expertCompanyProfileRepository.searchByCriteria(partnerId, filter, pageRequest))
        } returns PageImpl(expertList, pageRequest, expertList.size.toLong())

        every{  expertCompanyProfileRepository.findStatsByCriteria(partnerId, filter)} returns listOf(listOf("active", 1L))

        // Act
        val result = partnerService.listExpertStats(partnerId, filter, pageRequest, authenticatedUser)

        // Assert
        assertEquals(expertList.size.toLong(), result!!.data.totalElements)
        assertNotNull(result.stats)
        assertEquals(expertList[0].id, result.data.rows[0]!!.id)
        assertEquals(expertList[0].name, result.data.rows[0]!!.name)
        assertEquals(expertList[0].users[0].countryCode, result.data.rows[0]!!.country)
        assertEquals(expertList[0].users.size.toLong(), result.data.rows[0]!!.expertUser)

    }

    @Test
    fun `test listPartnerCorporate`() {
        // Arrange
        val filter = PartnerCorporateSearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        val accountEntity = AccountEntity(
            cases = mutableListOf(),
            corporate = null,
            name = "String"
        )

        val partnerId = 10L
        val corporateList = listOf(
            CorporateEntity(id = 10L, name = "Corporate 1", countryCode = "IN", lastUpdatedBy = TimeUtil.toEpochMillis(LocalDateTime.now()),
                rootUserId = 1L, subscriptionActive = true, status = CorporateStatus.ACTIVE,
                accountList = listOf(accountEntity)
            )
        )

        every { (corporateRepository.searchByPartnerCriteria(partnerId, filter, pageRequest))
        } returns PageImpl(corporateList, pageRequest, corporateList.size.toLong())

        // Act
        val result = partnerService.listPartnerCorporate(partnerId, filter, pageRequest, authenticatedUser)

        // Assert
        assertEquals(corporateList.size.toLong(), result.totalElements)
        assertEquals(corporateList[0].id, result.rows[0]!!.id)
        assertEquals(corporateList[0].name, result.rows[0]!!.name)
        assertEquals(corporateList[0].users.size.toLong(), result.rows[0]!!.corporateUsers)

    }

    @Test
    fun `test listPartnerCorporateWithStats`() {
        // Arrange
        val filter = PartnerCorporateSearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        val accountEntity = AccountEntity(
            cases = mutableListOf(),
            corporate = null,
            name = "String"
        )

        val partnerId = 10L
        val corporateList = listOf(
            CorporateEntity(id = 10L, name = "Corporate 1", countryCode = "IN", lastUpdatedBy = TimeUtil.toEpochMillis(LocalDateTime.now()),
                rootUserId = 1L, subscriptionActive = true, status = CorporateStatus.ACTIVE,
                accountList = listOf(accountEntity)
            )
        )

        every { (corporateRepository.searchByPartnerCriteria(partnerId, filter, pageRequest))
        } returns PageImpl(corporateList, pageRequest, corporateList.size.toLong())

        every{  corporateRepository.findStatsByCriteria(partnerId, filter)} returns listOf(listOf("active", 1L))

        // Act
        val result = partnerService.listCorporateStats(partnerId, filter, pageRequest, authenticatedUser)

        // Assert
        assertEquals(corporateList.size.toLong(), result!!.data.totalElements)
        assertNotNull(result.stats)
        assertEquals(corporateList[0].id, result.data.rows[0]!!.id)
        assertEquals(corporateList[0].name, result.data.rows[0]!!.name)
        assertEquals(corporateList[0].users.size.toLong(), result.data.rows[0]!!.corporateUsers)

    }


    @Test
    fun `should update partner status`() {
        // Mock dependencies
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 5L)
        val partnerEntity = generatePartnerEntity(request)
        partnerEntity.status = CorporateStatus.ACTIVE
        val loginAccount = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        partnerEntity.partnerUsers = listOf(loginAccount).toMutableList()
        every { partnerEntity.id?.let { partnerRepository.findById(it) } } returns Optional.of(partnerEntity)
        every{ loginAccountRepository.save(any())} returns loginAccount
        every {partnerRepository.save(any())} returns partnerEntity

        // Call the method
        val result = partnerService.updateCompanyStatus(UpdateCompanyStatusRequest(partnerEntity.id,UserType.PARTNER,
            AccountStatus.ACTIVE), authenticatedUser)

        // Verify the result
        assertEquals(true, result)
        assertEquals(CorporateStatus.ACTIVE, partnerEntity.status)
        assertEquals(AccountStatus.ACTIVE, partnerEntity.partnerUsers[0].status)
    }

    @Test
    fun shouldUpdatePartnerCompanyStatusForValidPartnerAndExpertCompany() {
        // Mock the necessary dependencies
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 5L)
        val partnerEntity = generatePartnerEntity(request)
        val partnerId = 1L
        val expertCompanyId = 1L
        val expertCompanyProfileEntityMock = mockk<ExpertCompanyProfileEntity>()
//        every { expertCompanyProfileEntityMock.partner } returns partnerEntity
        every { expertCompanyProfileEntityMock.users } returns mutableListOf()
        every {authenticatedUser.partnerId} returns partnerId

        every { partnerRepository.findById(partnerId).get() } returns partnerEntity
        every { expertCompanyProfileRepository.findById(expertCompanyId) } returns Optional.of(expertCompanyProfileEntityMock)

        every { expertCompanyProfileEntityMock.associatedPartners } returns mutableListOf()


        // Call the method under test
        val result = partnerService.updatePartnerCompanyStatus(partnerId, UpdateCompanyStatusRequest(partnerId,UserType.EXPERT,
            AccountStatus.ACTIVE), authenticatedUser)

        // Assertion
        assertEquals(true, result)

    }

    @Test
    fun shouldRetrievePartnerAssociatedUsers() {
        // Mock the necessary dependencies
        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 5L)
        val partnerEntity = generatePartnerEntity(request)
        val expertCompany = mockk<ExpertCompanyProfileEntity>()
//        partnerEntity.expertCompanies = listOf(expertCompany)
        val corporateUsers = listOf(
            CorporateUserEntity(),
            CorporateUserEntity()
        )
        val expertUserEntity = ExpertUserEntity(id = 1,
            contactEmail = "<EMAIL>",
            viewContract = true,
            contactWebsite = "string",
            contactNumber = "string",
            displayName = "test",
            expertType = "tester",
            jobTitle = "test",
            )
        val expertUsers = listOf(
            expertUserEntity
        )


        every { partnerRepository.findById(partnerEntity.id!!) } returns Optional.of(partnerEntity)
        every { corporateUserRepository.findAllByCorporateIn(partnerEntity.corporates) } returns corporateUsers
//        every { expertUserRepository.findAllByCompanyProfileIn(partnerEntity.expertCompanies!!) } returns expertUsers

        val clientUser1: ClientUser = mockk()
        every { clientUser1.id } returns 1L
        every { clientUser1.userType } returns "EXPERT"
        every { clientUser1.name } returns "Client User 1"
        every { clientUser1.email } returns "<EMAIL>"

        val clientUser2: ClientUser = mockk()
        every { clientUser2.id } returns 2L
        every { clientUser2.userType } returns "EXPERT"
        every { clientUser2.name } returns "Client User 2"
        every { clientUser2.email } returns "<EMAIL>"

        val clientUser3: ClientUser = mockk()
        every { clientUser3.id } returns 3L
        every { clientUser3.userType } returns "EXPERT"
        every { clientUser3.name } returns "Client User 3"
        every { clientUser3.email } returns "<EMAIL>"

        every { partnerRepository.findPartnerAssociatedUsers(any()) } returns listOf(clientUser1, clientUser2, clientUser3)

        // Call the method under test
        val result = partnerService.retrievePartnerAssociatedUsers(partnerEntity.id!!)

        // Verify the results
        assertEquals(3, result.size)
        assertEquals(3, result[2].id)
        assertEquals("EXPERT", result[2].userType)

    }


    @Test
    fun `should update expert company status`() {
        // Mock dependencies
        val expertUser = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        )
        val expertCompany =  ExpertCompanyProfileEntity(
            id = 1,
            name = "Test Name",
            users = mutableListOf(expertUser),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        every { expertUserRepository.save(any())} returns expertUser
        every { expertCompany.id?.let { expertCompanyProfileRepository.findById(it) } } returns Optional.of(expertCompany)

        // Call the method
        val result = partnerService.updateCompanyStatus(UpdateCompanyStatusRequest(expertCompany.id, UserType.EXPERT, AccountStatus.ACTIVE),
            authenticatedUser)

        // Verify the result
        assertEquals(true, result)
        assertEquals(AccountStatus.ACTIVE, expertCompany.users[0].status)
    }

    @Test
    fun `should update corporate status`() {
        // Mock dependencies
        val corporate = CorporateEntity(id = 10L, name = "Corporate 1", countryCode = "IN", lastUpdatedBy = TimeUtil.toEpochMillis(LocalDateTime.now()),
            rootUserId = 1L, subscriptionActive = true, status = CorporateStatus.ACTIVE,
            accountList = listOf(AccountEntity(name = "Test Name",
                corporate = null))
        )
        val corporateUser = CorporateUserEntity()
        corporate.users= listOf(corporateUser)

        every {corporateUserRepository.save(any())} returns corporateUser
        every { corporateRepository.save(any())} returns corporate
        every { corporate.id?.let { corporateRepository.findById(it) } } returns Optional.of(corporate)

        // Call the method
        val result = partnerService.updateCompanyStatus(UpdateCompanyStatusRequest(corporate.id,UserType.CORPORATE,AccountStatus.ACTIVE),
            authenticatedUser)

        // Verify the result
        assertEquals(true, result)
        assertEquals(CorporateStatus.ACTIVE, corporate.status)
        assertEquals(AccountStatus.ACTIVE, corporate.users[0].status)
    }

    @Test
    fun `should return a list of partner users`() {
        // Mock dependencies
        val partnerId = 1L
        val filter = PartnerUserSearchFilter(search = "John",null, null,  status = AccountStatus.ACTIVE)
        val pageRequest = PageRequest.of(0, 20)
        val partnerUser1 = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        partnerUser1.firstName = "John"
        partnerUser1.lastName = "Test"
        val partnerUser2 = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        val partnerUserList = listOf(
            partnerUser1, partnerUser2
        )
        every { loginAccountRepository.searchByCriteria(partnerId, filter, pageRequest) } returns PageImpl(partnerUserList)

        // Call the method
        val result = partnerService.listPartnerUser(filter, partnerId, pageRequest)

        // Verify the result
        assertEquals(partnerUserList.size, result.rows.size)
        assertEquals(partnerUserList[0].id, result.rows[0].userId)
        assertEquals("${partnerUserList[0].firstName} ${partnerUserList[0].lastName}", result.rows[0].name)
        assertEquals(partnerUserList[0].email, result.rows[0].email)
    }

//    @Test
//    fun shouldGenerateTemporaryPassword() {
//
//
//        // Create a CompletableDeferred object to represent the asynchronous result
//        val deferredResult = CompletableFuture<TemporaryPassword>()
//
//        // Mock the temporaryPassword() method of the AdminUserService to return the deferred result
//        every { adminUserService.temporaryPassword(any(), any()) } returns deferredResult.get().temporaryPassword.toString()
//
//
//        // Create an instance of the PartnerService and pass the mock AdminUserService
//
//        // Call the temporaryPassword() method
//        val userId = 1L
//        val partnerId = 67890L
//        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 1L)
//        val partnerEntity = generatePartnerEntity(request)
//
//        val expertCompany =  ExpertCompanyProfileEntity(
//            id = 1,
//            name = "Test Name",
//            users = mutableListOf(
//                ExpertUserEntity(
//                    id = 1L,
//                    contactEmail = "<EMAIL>",
//                    contactNumber = "9123",
//                    contactWebsite = "1@test",
//                    displayName = "test",
//                    expertType = "ACTIVE",
//                    jobTitle = "string"
//                )
//            ),
//            lastUpdatedBy = 0,
//            aboutBusiness = "",
//            account = AccountEntity(
//                name = "Test Name",
//                corporate = null
//            ),
//            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
//            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
//            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
//
//            logoKey = "",
//            size = CompanySize.Size1,
//            summary = "",
//            renewContract = false,
//            profileImage = "",
//            expertContract = ExpertContractEntity(),
//            status = AccountStatus.ACTIVE,
//            questionsQuota = 2,
//            associatedPartners = mutableListOf(),
//            companyType = ExpertCompanyType.EXPERT,
//            invitedBy = 5
//        )
//
//        val loginAccountEntity = ExpertUserEntity(
//            id = 1L,
//            contactEmail = "<EMAIL>",
//            contactNumber = "9123",
//            contactWebsite = "1@test",
//            displayName = "test",
//            expertType = "ACTIVE",
//            jobTitle = "string"
//        )
//        loginAccountEntity.companyProfile = expertCompany
////        loginAccountEntity.companyProfile!!.partner = partnerEntity
//
////        loginAccountEntity.companyProfile!!.partner!!.id = partnerId
//
//        every { loginAccountRepository.findById(userId) } returns Optional.of(loginAccountEntity)
//
////        val result = partnerService.temporaryPassword(userId, partnerId, authenticatedUser)
//
//        // Verify that the temporaryPassword() method of the AdminUserService was called with the correct arguments
//        verify { adminUserService.temporaryPassword(userId, authenticatedUser) }
//
//
//        // Complete the deferred result with a value
//        val temporaryPassword = TemporaryPassword("abc123")
//        deferredResult.complete(temporaryPassword)
//
//        // Assert that the result of the temporaryPassword() method is equal to the completed deferred result
//       // assertEquals(temporaryPassword.temporaryPassword, result.get().temporaryPassword)
//    }

//    @Test
//    fun shouldGenerateAccessPassword() {
//        // Arrange
//        val userId = 1L
//        val partnerId = 67890L
//        val expectedPassword = "testPassword"
//
//        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 1L)
//        val partnerEntity = generatePartnerEntity(request)
//
//        val expertCompany =  ExpertCompanyProfileEntity(
//            id = 1,
//            name = "Test Name",
//            users = mutableListOf(
//                ExpertUserEntity(
//                    id = 1L,
//                    contactEmail = "<EMAIL>",
//                    contactNumber = "9123",
//                    contactWebsite = "1@test",
//                    displayName = "test",
//                    expertType = "ACTIVE",
//                    jobTitle = "string"
//                )
//            ),
//            lastUpdatedBy = 0,
//            aboutBusiness = "",
//            account = AccountEntity(
//                name = "Test Name",
//                corporate = null
//            ),
//            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
//            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
//            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",
//
//            logoKey = "",
//            size = CompanySize.Size1,
//            summary = "",
//            renewContract = false,
//            profileImage = "",
//            expertContract = ExpertContractEntity(),
//            status = AccountStatus.ACTIVE,
//            questionsQuota = 2,
//            associatedPartners = mutableListOf(),
//            companyType = ExpertCompanyType.EXPERT,
//            invitedBy = 5
//        )
//
//        val loginAccountEntity = ExpertUserEntity(
//            id = 1L,
//            contactEmail = "<EMAIL>",
//            contactNumber = "9123",
//            contactWebsite = "1@test",
//            displayName = "test",
//            expertType = "ACTIVE",
//            jobTitle = "string"
//        )
//        loginAccountEntity.companyProfile = expertCompany
//        //loginAccountEntity.companyProfile!!.partner = partnerEntity
//
//        //loginAccountEntity.companyProfile!!.partner!!.id = partnerId
//        //val loginAccountEntity: LoginAccountEntity = mockk()
//
//        every { loginAccountRepository.findById(userId) } returns Optional.of(loginAccountEntity)
//        //every { adminUserService.accessPassword(userId, authenticatedUser) } returns CompletableFuture.completedFuture(AccessPassword(expectedPassword))
//
//        // Act
//        //val result: CompletableFuture<AccessPassword> = partnerService.accessPassword(userId, partnerId, authenticatedUser)
//
//        // Assert
//        //assertEquals(expectedPassword, result.get().oneTimePassword)
//
//    }

    @Test
    fun `test retrievePartnerUsers when partnerId is valid`() {

        val request = generateCreatePartnerRequest(PartnerType.EXPERT, 1L)
        val partnerEntity = generatePartnerEntity(request)

        val expertCompany =  ExpertCompanyProfileEntity(
            id = 1,
            name = "Test Name",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        val loginAccountEntity = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        )
        loginAccountEntity.companyProfile = expertCompany
        //loginAccountEntity.companyProfile!!.partner = partnerEntity

        partnerEntity.partnerUsers = mutableListOf(loginAccountEntity)

        every { partnerRepository.findById(any())} returns Optional.of(partnerEntity)

        val clientUser1: ClientUser = mockk()
        every { clientUser1.id } returns 1L
        every { clientUser1.userType } returns "EXPERT"
        every { clientUser1.name } returns "Client User 1"
        every { clientUser1.email } returns "<EMAIL>"

        every { partnerRepository.findPartnerUsers(any()) } returns listOf(clientUser1)

        val result = partnerEntity.id?.let { partnerService.retrievePartnerUsers(it) }

        assertEquals(1, result!!.size)
    }

    @Test
    fun `test deletePartner`() {

        val request = generateCreatePartnerRequest(PartnerType.NEW)
        val partnerEntity = generatePartnerEntity(request)
        partnerEntity.id = 1L

        partnerEntity.corporates = emptyList()
        //partnerEntity.expertCompanies = emptyList()

        val loginAccountEntity = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        )

        partnerEntity.partnerUsers = mutableListOf(loginAccountEntity)

        every { partnerRepository.findById(1L) } returns Optional.of(partnerEntity)
        every { partnerRepository.delete(partnerEntity)} returns Unit
        every { loginAccountEntity.id?.let { loginAccountRepository.deleteById(it) } } returns Unit

        // Call the deletePartner function
        val result = partnerService.deletePartner(1L)

        //every { partnerRepository.findById(-555511166805379660)} returns Optional.of(partnerEntity)

        assertEquals(true, result)
    }

    @Test
    fun `test retrieveExpertUsers`() {
        val request = generateCreatePartnerRequest(PartnerType.NEW)
        val partnerEntity = generatePartnerEntity(request)

        val expertCompany =  ExpertCompanyProfileEntity(
            id = 1,
            name = "Test Name",
            users = mutableListOf(
                ExpertUserEntity(
                    id = 1L,
                    contactEmail = "<EMAIL>",
                    contactNumber = "9123",
                    contactWebsite = "1@test",
                    displayName = "test",
                    expertType = "ACTIVE",
                    jobTitle = "string"
                )
            ),
            lastUpdatedBy = 0,
            aboutBusiness = "",
            account = AccountEntity(
                name = "Test Name",
                corporate = null
            ),
            companyAddress = "", companyNumber = "", contractAcceptedDate = LocalDateTime.now(),
            effectiveDate = LocalDateTime.now(), effectiveEndDate = LocalDateTime.now(), feesAmount = "",
            feesCurrency = "", membershipStatus = "", services = "", specialTerms = "", territory = "",

            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            renewContract = false,
            profileImage = "",
            expertContract = ExpertContractEntity(),
            status = AccountStatus.ACTIVE,
            questionsQuota = 2,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 5
        )

        val loginAccountEntity = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        )
        loginAccountEntity.companyProfile = expertCompany

        //loginAccountEntity.companyProfile!!.partner = partnerEntity

        partnerEntity.partnerUsers = mutableListOf(loginAccountEntity)

        every { expertUserService.retrieveExpertUsersByCompanyIds(partnerEntity.id, listOf(expertCompany.id!!))} returns expertCompany.users.filter { it.status == AccountStatus.ACTIVE }.map {

            ExpertUserReferenceData(it.id, it.email, it.firstName, it.lastName)
        }

        // Define the expected result
        val expectedResult = expertCompany.users.filter { it.status == AccountStatus.ACTIVE }.map {

            ExpertUserReferenceData(it.id!!, it.email, it.firstName, it.lastName)
        }

        // Call the retrieveExpertUsers function
        val actualResult = partnerEntity.id?.let { expertCompany.id?.let { it1 ->
            partnerService.retrieveExpertUsers(it, it1)
        } }

        // Verify the result
        assertEquals(expectedResult, actualResult)
    }

    @Test
    fun `test getUserProfiles`() {
        // Test case 1: Empty userIds
        val userIds1 = emptyList<Long>()
        val result1 = partnerService.getUserProfiles(0L, userIds1)
        assertEquals(0, result1.size)

        // Test case 2: Non-empty userIds
        val userIds2 = listOf(1L, 2L, 3L)

        val user1 = UserProfile(id = 1L, email = "<EMAIL>", firstName = "String", lastName = "String",
            role = Role.ROLE_SUPPLIER, status = AccountStatus.ACTIVE)

        val user2 = UserProfile(id = 2L, email = "<EMAIL>", firstName = "String", lastName = "String",
            role = Role.ROLE_SUPPLIER, status = AccountStatus.ACTIVE)

        val user3 = UserProfile(id = 3L, email = "<EMAIL>", firstName = "String", lastName = "String",
            role = Role.ROLE_SUPPLIER, status = AccountStatus.ACTIVE)

//        every { caseDashboardService.retrieveProfile(1L)} returns user1
//        every { caseDashboardService.retrieveProfile(2L)} returns user2
//        every { caseDashboardService.retrieveProfile(3L)} returns user3

        every { userProfileUtil.retrieveProfile(1L) } returns user1
        every { userProfileUtil.retrieveProfile(2L) } returns user2
        every { userProfileUtil.retrieveProfile(3L) } returns user3

        val result2 = partnerService.getUserProfiles(1L,userIds2)
        assertEquals(3, result2.size)
        assertEquals(1L, result2[0].id)
        assertEquals(2L, result2[1].id)
        assertEquals(3L, result2[2].id)
    }


//    @Test
//    fun `should return a list of partner`() {
//        // Mock dependencies
//        val partnerId = 1L
//        val filter = PartnerSearchFilter(search = "John", null, null, null, status = CorporateStatus.ACTIVE)
//        val pageRequest = PageRequest.of(0, 20)
//        val bandsDetails = mockk<BandDetailsEntity>()
//        val microAccessMasterEntity = mockk<MicroAccessMasterEntity>()
//
//
//
//
//        val request1 = generateCreatePartnerRequest(PartnerType.NEW, 5L)
//        val partnerEntity1 = generatePartnerEntity(request1)
//        val request2 = generateCreatePartnerRequest(PartnerType.NEW, 5L)
//        val partnerEntity2 = generatePartnerEntity(request2)
//
//
//        every { partnerEntity1.band?.bandAccesses } returns mutableListOf(bandsDetails)
//        every { partnerEntity2.band?.bandAccesses } returns mutableListOf(bandsDetails)
//        every { partnerEntity1.band?.bandAccesses!![0].access} returns microAccessMasterEntity
//        every { partnerEntity2.band?.bandAccesses!![0].access} returns microAccessMasterEntity
//
//        val partnerUser1 = loginAccountEntity()
//        partnerUser1.id = partnerEntity1.rootUserId
//        partnerEntity1.partnerUsers = listOf(partnerUser1)
//        val partnerUser2 = loginAccountEntity()
//        partnerUser2.id = partnerEntity2.rootUserId
//        partnerEntity2.partnerUsers = listOf(partnerUser2)
//        val partnerList = listOf(
//            partnerEntity1, partnerEntity2
//        )
//
//
//        every { partnerRepository.searchByCriteria(filter, pageRequest) } returns PageImpl(partnerList)
//
//        // Call the method
//        val result = partnerService.listPartner(filter, pageRequest, authenticatedUser)
//
//        // Verify the result
//        assertEquals(partnerList.size, result.rows.size)
//        assertEquals(partnerList[0].id, result.rows[0]!!.id)
//        assertEquals(partnerList[0].name, result.rows[0]!!.name)
//        assertEquals(partnerList[0].country, result.rows[0]!!.country)
//    }
}
