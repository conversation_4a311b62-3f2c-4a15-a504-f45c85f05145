package com.centuroglobal.service

import com.centuroglobal.data.payload.auth.ChangePasswordRequest
import com.centuroglobal.data.payload.auth.RecoverPasswordRequest
import com.centuroglobal.data.payload.auth.ResetPasswordRequest
import com.centuroglobal.data.properties.PasswordPolicyProperties
import com.centuroglobal.shared.data.AppConstant
import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.ValidationTokenRepository
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.assertThrows
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.data.repository.findByIdOrNull
import org.springframework.security.crypto.password.PasswordEncoder
import java.time.LocalDateTime
import java.util.*

@ExtendWith(MockitoExtension::class)
class PasswordServiceTest {

    private lateinit var passwordService: PasswordService
    private lateinit var policyProperties: PasswordPolicyProperties
    private lateinit var passwordEncoder: PasswordEncoder
    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var validationTokenRepository: ValidationTokenRepository
    private lateinit var tokenVerificationService: TokenVerificationService

    @BeforeEach
    fun setup() {
        policyProperties = mockk {
            every { minLength } returns 8
            every { maxLength } returns 20
            every { requireLowercase } returns true
            every { requireUppercase } returns true
            every { requireDigit } returns true
            every { requireSpecialChar } returns true
            every { validationErrorMessage } returns "Password must meet complexity requirements"
        }
        passwordEncoder = mockk()
        loginAccountRepository = mockk()
        validationTokenRepository = mockk()
        tokenVerificationService = mockk()

        passwordService = PasswordService(
            policyProperties,
            passwordEncoder,
            loginAccountRepository,
            validationTokenRepository,
            tokenVerificationService
        )
    }

    @Test
    fun `changePassword should update password when old password is correct`() {
        // Arrange
        val userId = 1L
        val request = ChangePasswordRequest(
            oldPassword = "oldPassword123!",
            newPassword = "newPassword123!"
        )
        
        val loginAccount = ExpertUserEntity(
            id = 1,
            jobTitle = "SDE",
            countryRegionId = 2,
            expertiseId = 3,
            expertises = listOf(),
            bio = "",
            displayName = "",
            infoVideoUrl = "",
            contactNumber = "123456",
            contactEmail = "<EMAIL>",
            contactWebsite = "a.com",
            companyProfile = ExpertCompanyProfileEntity(
                id = 5,
                name = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                users = mutableListOf(),
                lastUpdatedBy = 1,
                companyNumber = "123",
                companyAddress = "",
                aboutBusiness = "",
                effectiveDate = LocalDateTime.now(),
                effectiveEndDate = LocalDateTime.now(),
                contractAcceptedDate = LocalDateTime.now(),
                feesCurrency = "",
                feesAmount = "",
                specialTerms = "",
                membershipStatus = "",
                territory = "",
                renewContract = false,
                services = "",
                account = AccountEntity(
                    id = 6,
                    name = "",
                    status = AccountStatus.ACTIVE,
                    description = "",
                    companyName = "",
                    corporate = null,
                    corporateUsers = listOf(),
                    cases = mutableListOf()
                ),
                profileImage = "",
                expertContract =  null,
                status = AccountStatus.ACTIVE,
                questionsQuota = 13,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 6
            ),
            expertType = "",
            viewContract = false,
            case = mutableListOf(),
            profileImage = ""
        )
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccount
        every { passwordEncoder.matches(any(), any()) } returns true
        every { passwordEncoder.encode(request.newPassword) } returns "encodedNewPassword"
        every { loginAccountRepository.save(any()) } returns loginAccount
        
        // Act
        val result = passwordService.changePassword(userId, request)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        

    }

    @Test
    fun `changePassword should throw exception when old password is incorrect`() {
        // Arrange
        val userId = 1L
        val request = ChangePasswordRequest(
            oldPassword = "wrongPassword123!",
            newPassword = "newPassword123!"
        )
        
        val loginAccount = mockk<LoginAccountEntity> {
            every { password } returns "encodedOldPassword"
        }
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccount
        every { passwordEncoder.matches(any(), any()) } returns false
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            passwordService.changePassword(userId, request)
        }
        
        assertEquals(ErrorCode.OLD_PASSWORD_IS_WRONG, exception.error)
        
        // Verify
        verify(exactly = 0) { 
            passwordEncoder.encode(any())
            loginAccountRepository.save(any())
        }
    }

    @Test
    fun `changePassword should throw exception when new password is same as old password`() {
        // Arrange
        val userId = 1L
        //val password = "samePassword123!"
        val request = ChangePasswordRequest(
            oldPassword = "samePassword123!",
            newPassword = "samePassword123!"
        )
        
        val loginAccount = mockk<LoginAccountEntity> {
            every { password } returns "encodedPassword"
        }
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccount
        every { passwordEncoder.matches(any(), any()) } returns true
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            passwordService.changePassword(userId, request)
        }
        
        assertEquals(ErrorCode.NEW_PASSWORD_SAME_AS_OLD_PASSWORD, exception.error)
        
        // Verify
        verify(exactly = 0) { 
            passwordEncoder.encode(any())
            loginAccountRepository.save(any())
        }
    }

    @Test
    fun `changePassword should throw exception when user not found`() {
        // Arrange
        val userId = 1L
        val request = ChangePasswordRequest(
            oldPassword = "oldPassword123!",
            newPassword = "newPassword123!"
        )
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns null
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            passwordService.changePassword(userId, request)
        }
        
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `recoverPassword should create token for active user`() {
        // Arrange
        val request = RecoverPasswordRequest(email = "<EMAIL>")
        val userId = 1L
        
        val loginAccount = mockk<LoginAccountEntity> {
            every { id } returns userId
        }
        
        every { 
            loginAccountRepository.findByEmailAndStatusIn(
                request.email, 
                listOf(AccountStatus.ACTIVE)
            ) 
        } returns loginAccount
        
        every { 
            tokenVerificationService.invalidateUnusedToken(userId, ValidationType.RECOVER_PASSWORD) 
        } just runs
        
        every { 
            tokenVerificationService.createToken(loginAccount, ValidationType.RECOVER_PASSWORD) 
        } just runs
        
        // Act
        val result = passwordService.recoverPassword(request)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        // Verify
        verify { 
            loginAccountRepository.findByEmailAndStatusIn(request.email, listOf(AccountStatus.ACTIVE))
            tokenVerificationService.invalidateUnusedToken(userId, ValidationType.RECOVER_PASSWORD)
            tokenVerificationService.createToken(loginAccount, ValidationType.RECOVER_PASSWORD)
        }
    }

    @Test
    fun `recoverPassword should return success even when user not found`() {
        // Arrange
        val request = RecoverPasswordRequest(email = "<EMAIL>")
        
        every { 
            loginAccountRepository.findByEmailAndStatusIn(
                request.email, 
                listOf(AccountStatus.ACTIVE)
            ) 
        } returns null
        
        // Act
        val result = passwordService.recoverPassword(request)
        
        // Assert
        assertEquals(AppConstant.SUCCESS_RESPONSE_STRING, result)
        
        // Verify
        verify(exactly = 0) { 
            tokenVerificationService.invalidateUnusedToken(any(), any())
            tokenVerificationService.createToken(any(), any())
        }
    }

    @Test
    fun `resetPassword should update password and return token`() {
        // Arrange
        val request = ResetPasswordRequest(
            code = "validCode",
            password = "newPassword123!"
        )
        
        val validationToken = ValidationTokenEntity(
            id = 1,
            userId = 1,
            type = ValidationType.RECOVER_PASSWORD,
            code = "code",
            state = ValidationState.EMAIL_SENT,
            expiryDate = LocalDateTime.now().plusDays(12)
        )

        val loginAccount = ExpertUserEntity(
            id = 1,
            jobTitle = "SDE",
            countryRegionId = 2,
            expertiseId = 3,
            expertises = listOf(),
            bio = "",
            displayName = "",
            infoVideoUrl = "",
            contactNumber = "123456",
            contactEmail = "<EMAIL>",
            contactWebsite = "a.com",
            companyProfile = ExpertCompanyProfileEntity(
                id = 5,
                name = "",
                logoKey = "",
                size = CompanySize.Size1,
                summary = "",
                users = mutableListOf(),
                lastUpdatedBy = 1,
                companyNumber = "123",
                companyAddress = "",
                aboutBusiness = "",
                effectiveDate = LocalDateTime.now(),
                effectiveEndDate = LocalDateTime.now(),
                contractAcceptedDate = LocalDateTime.now(),
                feesCurrency = "",
                feesAmount = "",
                specialTerms = "",
                membershipStatus = "",
                territory = "",
                renewContract = false,
                services = "",
                account = AccountEntity(
                    id = 6,
                    name = "",
                    status = AccountStatus.ACTIVE,
                    description = "",
                    companyName = "",
                    corporate = null,
                    corporateUsers = listOf(),
                    cases = mutableListOf()
                ),
                profileImage = "",
                expertContract =  null,
                status = AccountStatus.ACTIVE,
                questionsQuota = 13,
                associatedPartners = mutableListOf(),
                companyType = ExpertCompanyType.EXPERT,
                invitedBy = 6
            ),
            expertType = "",
            viewContract = false,
            case = mutableListOf(),
            profileImage = ""
        )
        
        val tokenResult = mockk<TokenResult>()
        
        every { tokenVerificationService.getValidTokenEntity(request.code) } returns validationToken
        every { loginAccountRepository.findByIdOrNull(1) } returns loginAccount
        every { validationTokenRepository.save(any()) } returns validationToken
        every { passwordEncoder.encode(request.password) } returns "encodedPassword"
        every { loginAccountRepository.save(any()) } returns loginAccount
        every { tokenVerificationService.generateLoginToken(loginAccount) } returns tokenResult
        
        // Act
        val result = passwordService.resetPassword(request)
        
        // Assert
        assertSame(tokenResult, result)

    }

    @Test
    fun `resetPassword should throw exception when user not found`() {
        // Arrange
        val request = ResetPasswordRequest(
            code = "validCode",
            password = "newPassword123!"
        )
        
        val validationToken = mockk<ValidationTokenEntity> {
            every { userId } returns 1
        }
        
        every { tokenVerificationService.getValidTokenEntity(request.code) } returns validationToken
        every { loginAccountRepository.findByIdOrNull(1) } returns null
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            passwordService.resetPassword(request)
        }
        
        assertEquals(ErrorCode.RESET_PASSWORD_FAIL, exception.error)
        
        // Verify
        verify(exactly = 0) { 
            validationTokenRepository.save(any())
            passwordEncoder.encode(any())
            loginAccountRepository.save(any())
            tokenVerificationService.generateLoginToken(any())
        }
    }

    @Test
    fun `generatePassword should return valid password`() {
        // Act
        val password = passwordService.generatePassword()
        
        // Assert
        assertNotNull(password)
        assertTrue(password.length >= policyProperties.minLength)
        assertTrue(password.length <= policyProperties.maxLength)
    }

    @Test
    fun `validateAndEncodePassword should encode valid password`() {
        // Arrange
        val validPassword = "ValidPassword123!"
        
        every { passwordEncoder.encode(validPassword) } returns "encodedPassword"
        
        // Act
        val result = passwordService.validateAndEncodePassword(validPassword)
        
        // Assert
        assertEquals("encodedPassword", result)
        
        // Verify
        verify { passwordEncoder.encode(validPassword) }
    }

    @Test
    fun `validateAndEncodePassword should throw exception for invalid password`() {
        // Arrange
        val invalidPassword = "weak"
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            passwordService.validateAndEncodePassword(invalidPassword)
        }
        
        // Verify
        verify(exactly = 0) { passwordEncoder.encode(any()) }
    }

    @Test
    fun `loginAccountUuidApproval should return true for valid unexpired token`() {
        // Arrange
        val uuid = "valid-uuid"
        val loginAccount = mockk<LoginAccountEntity> {
            every { loginTokenExpire } returns LocalDateTime.now().plusDays(1)
        }
        
        every { loginAccountRepository.findByLoginToken(uuid) } returns loginAccount
        
        // Act
        val result = passwordService.loginAccountUuidApproval(uuid)
        
        // Assert
        assertTrue(result)
    }

    @Test
    fun `loginAccountUuidApproval should return false for expired token`() {
        // Arrange
        val uuid = "expired-uuid"
        val loginAccount = mockk<LoginAccountEntity> {
            every { loginTokenExpire } returns LocalDateTime.now().minusDays(1)
        }
        
        every { loginAccountRepository.findByLoginToken(uuid) } returns loginAccount
        
        // Act
        val result = passwordService.loginAccountUuidApproval(uuid)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `loginAccountUuidApproval should return false when account not found`() {
        // Arrange
        val uuid = "invalid-uuid"
        
        every { loginAccountRepository.findByLoginToken(uuid) } returns null
        
        // Act
        val result = passwordService.loginAccountUuidApproval(uuid)
        
        // Assert
        assertFalse(result)
    }

    @Test
    fun `logout should clear login token and return true`() {
        // Arrange
        val userId = 1L
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)
        
        every { loginAccountRepository.findById(userId) } returns Optional.of(loginAccount)
        every { loginAccountRepository.save(any()) } returns loginAccount
        
        // Act
        val result = passwordService.logout(userId)
        
        // Assert
        assertTrue(result)
        
        // Verify
        verify { 
            loginAccountRepository.findById(userId)
            loginAccount.loginToken = null
            loginAccountRepository.save(loginAccount)
        }
    }
}