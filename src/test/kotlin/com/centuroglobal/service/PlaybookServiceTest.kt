package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.ContentLibraryEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.playbook.*
import com.centuroglobal.shared.data.entity.travel.TravelAssessmentEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.payload.openai.MessageData
import com.centuroglobal.shared.data.payload.openai.ThreadResponse
import com.centuroglobal.shared.data.pojo.AIMessageRequest
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.playbook.PlaybookLogsSearchFilter
import com.centuroglobal.shared.data.pojo.playbook.PlaybookRequest
import com.centuroglobal.shared.data.pojo.playbook.PlaybookSearchFilter
import com.centuroglobal.shared.data.pojo.playbook.PlaybookShareUserReferenceData
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AIChatRepository
import com.centuroglobal.shared.repository.ContentLibraryRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.repository.playbook.PlaybookChatRepository
import com.centuroglobal.shared.repository.playbook.PlaybookRepository
import com.centuroglobal.shared.repository.playbook.PlaybookSessionRepository
import com.centuroglobal.shared.repository.travel.TravelAssessmentRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.UserProfileUtil
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.time.LocalDateTime
import java.util.*

@ExtendWith(MockitoExtension::class)
class PlaybookServiceTest {

    private lateinit var playbookService: PlaybookService
    private lateinit var playbookRepository: PlaybookRepository
    private lateinit var playbookSessionRepository: PlaybookSessionRepository
    private lateinit var userProfileUtil: UserProfileUtil
    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var corporateUserRepository: CorporateUserRepository
    private lateinit var contentLibraryRepository: ContentLibraryRepository
    private lateinit var playbookChatRepository: PlaybookChatRepository
    private lateinit var travelAssessmentRepository: TravelAssessmentRepository
    private lateinit var aiChatRepository: AIChatRepository
    private lateinit var openAIService: OpenAIService
    private lateinit var authenticatedUser: AuthenticatedUser
    private lateinit var objectMapper: ObjectMapper

    @BeforeEach
    fun setup() {
        playbookRepository = mockk()
        playbookSessionRepository = mockk()
        userProfileUtil = mockk()
        loginAccountRepository = mockk()
        corporateUserRepository = mockk()
        contentLibraryRepository = mockk()
        playbookChatRepository = mockk()
        travelAssessmentRepository = mockk()
        aiChatRepository = mockk()
        openAIService = mockk()
        objectMapper = ObjectMapper()
        
        playbookService = PlaybookService(
            playbookRepository,
            playbookSessionRepository,
            userProfileUtil,
            loginAccountRepository,
            corporateUserRepository,
            contentLibraryRepository,
            playbookChatRepository,
            travelAssessmentRepository,
            aiChatRepository,
            openAIService
        )
        
        authenticatedUser = mockk {
            every { userId } returns 1L
            every { role } returns Role.ROLE_CORPORATE.name
            every { companyId } returns 1L
        }
    }

    @Test
    fun `create should successfully create a playbook`() {
        // Arrange
        val requestId = "test-request-id"
        val playbookRequest = PlaybookRequest(
            country = "US",
            about = "Test About",
            industry = "TECH",
            industryName = "Technology",
            details = listOf(
            )
        )
        
        val playbookEntity = mockk<PlaybookEntity> {
            every { id } returns 1L
            every { country } returns "US"
            every { about } returns "Test About"
            every { industry } returns "TECH"
            every { industryName } returns "Technology"
            every { details } returns mutableListOf(
                PlaybookDetailsEntity(
                    category = "Category1",
                    subCategory = "",
                    isSelected = true,
                    playbook = this
                ),
                PlaybookDetailsEntity(
                    category = "Category1",
                    subCategory = "SubCategory1",
                    isSelected = true,
                    playbook = this
                )
            )
            every { playbookContent } returns PlaybookContentEntity(playbook = this)
            every { createdBy } returns 1L
        }
        every { playbookEntity.createdDate } returns LocalDateTime.now()
        
        val sessionEntity = mockk<PlaybookSessionEntity> {
            every { sessionId } returns requestId ?: UUID.randomUUID().toString()
        }
        
        val templateData = """
            {
                "uiElements": [
                    {
                        "category": "Category1",
                        "pages": [
                            {
                                "subCategory": "SubCategory1"
                            }
                        ]
                    }
                ]
            }
        """.trimIndent()
        
        val templateEntity = ContentLibraryEntity(
            id = 1L,
            identifier = "TEMPLATE",
            countryCode = "US",
            data = templateData,
            isActive = true,
            title = "",
            metadata = mutableListOf()
        )
        
        every { playbookRepository.save(any()) } returns playbookEntity
        every { playbookSessionRepository.save(any()) } returns sessionEntity
        every { contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", "US") } returns templateEntity
        every { loginAccountRepository.getFirstNameLastNameById(1L) } returns "John Doe"
        
        // Act
        val result = playbookService.create(playbookRequest, requestId)
        
        // Assert
        assertNotNull(result)
        assertEquals(playbookEntity.id, result.id)
        assertEquals(playbookEntity.about, result.about)
        assertEquals(sessionEntity.sessionId, result.sessionId)
        assertEquals("John Doe", result.createdBy)
        
        // Verify
        verify { 
            playbookRepository.save(any())
            playbookSessionRepository.save(any())
            contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", "US")
            loginAccountRepository.getFirstNameLastNameById(1L)
        }
    }

    @Test
    fun `get should return playbook with session for regular users`() {
        // Arrange
        val playbookId = 1L
        val requestId = "test-request-id"
        
        val playbookEntity = mockk<PlaybookEntity> {
            every { id } returns playbookId
            every { country } returns "US"
            every { about } returns "Test About"
            every { industry } returns "TECH"
            every { industryName } returns "Technology"
            every { details } returns mutableListOf(
                PlaybookDetailsEntity(
                    category = "Category1",
                    subCategory = "",
                    isSelected = true,
                    playbook = this
                )
            )
            every { playbookContent } returns PlaybookContentEntity(
                playbook = this,
                data = """{"uiElements":[]}"""
            )
            every { createdBy } returns 1L
        }

        every { playbookEntity.createdDate } returns LocalDateTime.now()
        
        val sessionEntity = mockk<PlaybookSessionEntity> {
            every { sessionId } returns requestId ?: UUID.randomUUID().toString()
        }
        
        val templateData = """
            {
                "uiElements": [
                    {
                        "category": "Category1",
                        "pages": []
                    }
                ]
            }
        """.trimIndent()
        
        val templateEntity = ContentLibraryEntity(
            id = 1L,
            identifier = "TEMPLATE",
            countryCode = "US",
            data = templateData,
            isActive = true,
            title = "",
            metadata = mutableListOf()
        )
        
        every { playbookRepository.findById(playbookId) } returns Optional.of(playbookEntity)
        every { playbookRepository.save(any()) } returns playbookEntity
        every { playbookSessionRepository.save(any()) } returns sessionEntity
        every { contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", "US") } returns templateEntity
        every { loginAccountRepository.getFirstNameLastNameById(1L) } returns "John Doe"
        
        // Act
        val result = playbookService.get(playbookId, authenticatedUser, true, requestId)
        
        // Assert
        assertNotNull(result)
        assertEquals(playbookEntity.id, result.id)
        assertEquals(sessionEntity.sessionId, result.sessionId)
        assertEquals("John Doe", result.createdBy)
        
        // Verify
        verify { 
            playbookRepository.findById(playbookId)
            playbookSessionRepository.save(any())
            contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", "US")
            loginAccountRepository.getFirstNameLastNameById(1L)
        }
    }

    @Test
    fun `get should return playbook without session for admin users when createSession is false`() {
        // Arrange
        val playbookId = 1L
        val requestId = "test-request-id"
        
        val adminUser = mockk<AuthenticatedUser> {
            every { userId } returns 1L
            every { role } returns Role.ROLE_ADMIN.name
        }
        
        val playbookEntity = mockk<PlaybookEntity> {
            every { id } returns playbookId
            every { country } returns "US"
            every { about } returns "Test About"
            every { industry } returns "TECH"
            every { industryName } returns "Technology"
            every { details } returns mutableListOf(
                PlaybookDetailsEntity(
                    category = "Category1",
                    subCategory = "",
                    isSelected = true,
                    playbook = this
                )
            )
            every { playbookContent } returns PlaybookContentEntity(
                playbook = this,
                data = """{"uiElements":[]}"""
            )
            every { createdBy } returns 1L
        }

        every { playbookEntity.createdDate } returns LocalDateTime.now()
        
        val templateData = """
            {
                "uiElements": [
                    {
                        "category": "Category1",
                        "pages": []
                    }
                ]
            }
        """.trimIndent()
        
        val templateEntity = ContentLibraryEntity(
            id = 1L,
            identifier = "TEMPLATE",
            countryCode = "US",
            data = templateData,
            isActive = true,
            title = "",
            metadata = mutableListOf()
        )
        
        every { playbookRepository.findById(playbookId) } returns Optional.of(playbookEntity)
        every { contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", "US") } returns templateEntity
        every { loginAccountRepository.getFirstNameLastNameById(1L) } returns "John Doe"
        every { playbookRepository.save(any()) } returns playbookEntity

        // Act
        val result = playbookService.get(playbookId, adminUser, false, requestId)
        
        // Assert
        assertNotNull(result)
        assertEquals(playbookEntity.id, result.id)
        assertNull(result.sessionId)
        assertEquals("John Doe", result.createdBy)
        
        // Verify
        verify { 
            playbookRepository.findById(playbookId)
            contentLibraryRepository.findByIdentifierAndCountryCode("TEMPLATE", "US")
            loginAccountRepository.getFirstNameLastNameById(1L)
        }
        verify(exactly = 0) { 
            playbookSessionRepository.save(any())
        }
    }

    @Test
    fun `get should throw ApplicationException when playbook not found`() {
        // Arrange
        val playbookId = 999L
        
        every { playbookRepository.findById(playbookId) } returns Optional.empty()
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            playbookService.get(playbookId, authenticatedUser, true, null)
        }
        
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
        
        // Verify
        verify(exactly = 0) { 
            playbookSessionRepository.save(any())
            contentLibraryRepository.findByIdentifierAndCountryCode(any(), any())
            loginAccountRepository.getFirstNameLastNameById(any())
        }
    }

    @Test
    fun `list should return paged playbook list`() {
        // Arrange
        val filter = PlaybookSearchFilter(
            type = "",
            country = "IN",
            from = LocalDateTime.now().minusDays(2),
            to = LocalDateTime.now(),
            userId = 1
        )
        val pageRequest = PageRequest.of(0, 10)
        
        val playbookEntity = mockk<PlaybookEntity> {
            every { id } returns 1L
            every { country } returns "US"
            every { about } returns "Test About"
            every { industry } returns "TECH"
            every { industryName } returns "Technology"
            every { createdBy } returns 1L
            every { createdDate } returns LocalDateTime.now()
        }

        every { playbookEntity.sharedWith } returns mutableSetOf()

        val userProfile = UserProfile(
            id = 1L,
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_CORPORATE,
            countryCode = "US",
            profilePictureFullUrl = "",
            companyName = "ABC"
        )
        
        val page = PageImpl(listOf(playbookEntity))
        
        every { playbookRepository.searchByCriteria(filter, pageRequest) } returns page
        every { userProfileUtil.retrieveProfile(1L) } returns userProfile
        
        // Act
        val result = playbookService.list(filter, pageRequest)
        
        // Assert
        assertNotNull(result)
        assertEquals(1, result.totalElements)
        assertEquals(1, result.rows.size)
        assertEquals(playbookEntity.id, result.rows[0].id)
        assertEquals(playbookEntity.country, result.rows[0].country)

        // Verify
        verify { 
            playbookRepository.searchByCriteria(filter, pageRequest)
            userProfileUtil.retrieveProfile(1L)
        }
    }

    @Test
    fun `delete should return true when playbook is deleted`() {
        // Arrange
        val playbookId = 1L
        
        every { playbookRepository.deleteById(playbookId) } just runs
        
        // Act
        val result = playbookService.delete(playbookId)
        
        // Assert
        assertTrue(result)
        
        // Verify
        verify { playbookRepository.deleteById(playbookId) }
    }

    @Test
    fun `share should update shared users and return true`() {
        // Arrange
        val playbookId = 1L
        val userIds = listOf(2L, 3L)
        
        val playbookEntity = mockk<PlaybookEntity> {
            every { sharedWith } returns mutableSetOf()
        }
        
        val user1 = mockk<LoginAccountEntity>()
        val user2 = mockk<LoginAccountEntity>()
        
        every { playbookRepository.findById(playbookId) } returns Optional.of(playbookEntity)
        every { loginAccountRepository.getReferenceById(2L) } returns user1
        every { loginAccountRepository.getReferenceById(3L) } returns user2
        every { playbookRepository.save(playbookEntity) } returns playbookEntity
        
        // Act
        val result = playbookService.share(playbookId, userIds)
        
        // Assert
        assertTrue(result)
        

    }

    @Test
    fun `share should throw ApplicationException when playbook not found`() {
        // Arrange
        val playbookId = 999L
        val userIds = listOf(2L, 3L)
        
        every { playbookRepository.findById(playbookId) } returns Optional.empty()
        
        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            playbookService.share(playbookId, userIds)
        }
        
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
        
        // Verify
        verify(exactly = 0) { 
            loginAccountRepository.getReferenceById(any())
            playbookRepository.save(any())
        }
    }

    @Test
    fun `shareUsersList should return partner users for partner admin`() {
        // Arrange
        val partnerId1 = 5L
        val partnerUser = mockk<AuthenticatedUser> {
            every { role } returns Role.ROLE_PARTNER.name
            every { partnerId } returns partnerId1
        }
        
        val userList = listOf(
            PlaybookShareUserReferenceData(
                1L, "John", "Doe", "<EMAIL>",
                countryCode = "IN"
            ),
            PlaybookShareUserReferenceData(2L, "Jane", "Smith", "<EMAIL>",
                countryCode = "IN")
        )
        
        every { 
            loginAccountRepository.findPlaybookUsersByPartnerIdAndStatusIn(
                partnerId1,
                listOf(AccountStatus.ACTIVE)
            ) 
        } returns userList
        
        // Act
        val result = playbookService.shareUsersList(partnerUser)
        
        // Assert
        assertEquals(userList, result)
        assertEquals(2, result.size)
        
        // Verify
        verify { 
            loginAccountRepository.findPlaybookUsersByPartnerIdAndStatusIn(
                partnerId1,
                listOf(AccountStatus.ACTIVE)
            )
        }
    }

    @Test
    fun `shareUsersList should return corporate users with FULL access`() {
        // Arrange
        val companyId1 = 5L
        val corporateUser = mockk<AuthenticatedUser> {
            every { role } returns Role.ROLE_CORPORATE.name
            every { companyId } returns companyId1
            every { visibilities } returns listOf(mockk {
                every { feature } returns "USER"
                every { accesses } returns mutableListOf("FULL")
            })
        }

        every { authenticatedUser.companyId } returns companyId1

        val userList = listOf(
            PlaybookShareUserReferenceData(1L, "John", "Doe", "<EMAIL>",
                countryCode = "IN"),
            PlaybookShareUserReferenceData(2L, "Jane", "Smith", "<EMAIL>",
                countryCode = "IN")
        )

        every {
            corporateUserRepository.findPlaybookUsersByCorporateIdAndStatusIn(
                companyId1,
                listOf(AccountStatus.ACTIVE)
            )
        } returns userList

        // Act
        val result = playbookService.shareUsersList(corporateUser)

        // Assert
        assertEquals(userList, result)
        assertEquals(2, result.size)

        // Verify
        verify {
            corporateUserRepository.findPlaybookUsersByCorporateIdAndStatusIn(
                companyId1,
                listOf(AccountStatus.ACTIVE)
            )
        }
    }

    @Test
    fun `shareUsersList should return reportees for user with REPORTEES access`() {
        // Arrange
        val userId1 = 1L
        val corporateUser = mockk<AuthenticatedUser> {
            every { role } returns Role.ROLE_CORPORATE.name
            every { userId } returns userId1
            every { visibilities } returns listOf(mockk {
                every { feature } returns "USER"
                every { accesses } returns mutableListOf("REPORTEES")
            })
        }

        every { authenticatedUser.userId } returns userId1

        val userList = mutableListOf(
            PlaybookShareUserReferenceData(2L, "John", "Doe", "<EMAIL>",
                countryCode = "IN"),
            PlaybookShareUserReferenceData(3L, "Jane", "Smith", "<EMAIL>",
                countryCode = "IN")
        )

        every {
            corporateUserRepository.findPlaybookUsersByManagersManagerIdAndStatusIn(
                userId1,
                listOf(AccountStatus.ACTIVE)
            )
        } returns userList

        // Act
        val result = playbookService.shareUsersList(corporateUser)

        // Assert
        assertEquals(userList, result)
        assertEquals(2, result.size)

        // Verify
        verify {
            corporateUserRepository.findPlaybookUsersByManagersManagerIdAndStatusIn(
                userId1,
                listOf(AccountStatus.ACTIVE)
            )
        }
    }

    @Test
    fun `shareUsersList should throw ApplicationException when user has no access`() {
        // Arrange
        val corporateUser = mockk<AuthenticatedUser> {
            every { role } returns Role.ROLE_CORPORATE.name
            every { visibilities } returns listOf(mockk {
                every { feature } returns "USER"
                every { accesses } returns mutableListOf("VIEW")
            })
        }

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            playbookService.shareUsersList(corporateUser)
        }

        assertEquals(ErrorCode.FORBIDDEN, exception.error)
    }

    @Test
    fun `shareUsersList should throw ApplicationException when user has no USER feature`() {
        // Arrange
        val corporateUser = mockk<AuthenticatedUser> {
            every { role } returns Role.ROLE_CORPORATE.name
            every { visibilities } returns listOf(mockk {
                every { feature } returns "CASE"
                every { accesses } returns mutableListOf("FULL")
            })
        }

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            playbookService.shareUsersList(corporateUser)
        }

        assertEquals(ErrorCode.FORBIDDEN, exception.error)
    }

    @Test
    fun `closeSession should return true when session is closed successfully`() {
        // Arrange
        val sessionId = "test-session-id"

        val sessionEntity = PlaybookSessionEntity(
            playbook = mockk(),
            questionCount = 0,
            startTime = LocalDateTime.now(),
            sessionId = sessionId,
            type = "CREATE"
        )

        every {
            playbookSessionRepository.findBySessionId(sessionId)
        } returns sessionEntity

        every { playbookSessionRepository.save(any()) } returns sessionEntity
        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        // Act
        val result = playbookService.closeSession(sessionId, authenticatedUser)

        // Assert
        assertTrue(result)

    }

    @Test
    fun `closeSession should return false when session is already closed`() {
        // Arrange
        val sessionId = "test-session-id"

        val sessionEntity = mockk<PlaybookSessionEntity> {
            every { endTime } returns LocalDateTime.now()
        }

        every {
            playbookSessionRepository.findBySessionId(sessionId)
        } returns sessionEntity

        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        // Act
        val result = playbookService.closeSession(sessionId, authenticatedUser)

        // Assert
        assertFalse(result)

    }

    @Test
    fun `aiMessage should create message and return answer text`() {
        // Arrange
        val sessionId = "test-session-id"
        val request = AIMessageRequest(
            "What is a playbook?",
            threadId = sessionId
        )
        val threadId1 = "thread-123"

        val sessionEntity = PlaybookSessionEntity(
            playbook = mockk(),
            questionCount = 0,
            startTime = LocalDateTime.now(),
            sessionId = sessionId,
            type = "CREATE"
        )

        val thread = ThreadResponse(
            id = "12",
            createdAt = 1234
        )

        val messages = MessageData(
            id = "123",
            content = listOf()
        )
        val answerText = "A playbook is a document that outlines procedures and strategies."

        every {
            playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(
                sessionId,
                authenticatedUser.companyId!!
            )
        } returns sessionEntity

        every { openAIService.createThread(any()) } returns thread
        every { openAIService.getAnswerFromThread(thread.id, request.question) } returns messages
        every { openAIService.readMessageText(messages) } returns answerText

        every { playbookSessionRepository.save(any()) } returns sessionEntity
        every { playbookChatRepository.save(any()) } returns mockk()
        every { aiChatRepository.save(any()) } returns mockk()

        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        // Act
        val result = playbookService.aiMessage(sessionId, request, authenticatedUser)

        // Assert
        assertEquals(answerText, result)

    }

    @Test
    fun `aiMessage should handle visa-assessment special case`() {
        // Arrange
        val sessionId = "visa-assessment"
        val request = AIMessageRequest("What is a visa assessment?", threadId = sessionId)

        val thread = ThreadResponse(
            id = "12",
            createdAt = 1234
        )

        val messages = MessageData(
            id = "123",
            content = listOf()
        )
        val answerText = "A visa assessment evaluates eligibility for a visa."

        every { openAIService.createThread(null) } returns thread
        every { openAIService.getAnswerFromThread(thread.id, request.question) } returns messages
        every { openAIService.readMessageText(messages) } returns answerText

        every { aiChatRepository.save(any()) } returns mockk()

        // Act
        val result = playbookService.aiMessage(sessionId, request, authenticatedUser)

        // Assert
        assertEquals(answerText, result)

        // Verify
        verify {
            openAIService.createThread(null)
            openAIService.getAnswerFromThread(thread.id, request.question)
            openAIService.readMessageText(messages)
            aiChatRepository.save(any())
        }
        verify(exactly = 0) {
            playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any())
            playbookSessionRepository.save(any())
            playbookChatRepository.save(any())
        }
    }

    @Test
    fun `aiMessage should throw ApplicationException when session is closed`() {
        // Arrange
        val sessionId = "test-session-id"
        val request = AIMessageRequest("What is a playbook?", threadId = sessionId)

        val sessionEntity = mockk<PlaybookSessionEntity> {
            every { endTime } returns LocalDateTime.now()
        }

        every {
            playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(
                sessionId,
                authenticatedUser.companyId!!
            )
        } returns sessionEntity

        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            playbookService.aiMessage(sessionId, request, authenticatedUser)
        }

        assertEquals(ErrorCode.PLAYBOOK_SESSION_CLOSED, exception.error)

    }

    @Test
    fun `logs should return paged playbook logs`() {
        // Arrange
        val filter = PlaybookLogsSearchFilter()
        val pageRequest = PageRequest.of(0, 10)

        val sessionEntity = mockk<PlaybookSessionEntity> {
            every { id } returns 1L
            every { sessionId } returns "session-123"
            every { startTime } returns LocalDateTime.now()
            every { endTime } returns LocalDateTime.now().plusHours(1)
            every { type } returns "VIEW"
            every { questionCount } returns 5
            every { playbook } returns mockk {
                every { id } returns 1L
                every { country } returns "US"
                every { industryName }returns "Tech"
            }
        }

        every { sessionEntity.corporateUser } returns CorporateUserEntity()
        every { sessionEntity.createdDate } returns LocalDateTime.now()
        every { sessionEntity.playbookChats } returns mutableListOf()

        val page = PageImpl(listOf(sessionEntity))

        every { playbookSessionRepository.searchByCriteria(filter, pageRequest) } returns page

        // Act
        val result = playbookService.logs(filter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(1, result.totalElements)
        assertEquals(1, result.rows.size)
        assertEquals(sessionEntity.id, result.rows[0].id)
        assertEquals(sessionEntity.sessionId, result.rows[0].sessionId)

        // Verify
        verify { playbookSessionRepository.searchByCriteria(filter, pageRequest) }
    }

    @Test
    fun `getChatMessages should return chat messages for session`() {
        // Arrange
        val sessionId = "test-session-id"

        val playbookChatEntity1 = mockk<PlaybookChatEntity> {
            every { id } returns 1L
            every { question } returns "What is a playbook?"
            every { answer } returns "A playbook is a document that outlines procedures."
            every { createdBy } returns 1L
            every { createdDate } returns LocalDateTime.now()
        }

        val playbookChatEntity2 = mockk<PlaybookChatEntity> {
            every { id } returns 2L
            every { question } returns "How do I create a playbook?"
            every { answer } returns "You can create a playbook by following these steps..."
            every { createdBy } returns 1L
            every { createdDate } returns LocalDateTime.now().minusMinutes(5)
        }

        val sessionEntity = mockk<PlaybookSessionEntity> {
            every { playbookChats } returns mutableListOf(playbookChatEntity1, playbookChatEntity2)
        }

        val userProfile = UserProfile(
            id = 1L,
            email = "<EMAIL>",
            firstName = "John",
            lastName = "Doe",
            status = AccountStatus.ACTIVE,
            role = Role.ROLE_CORPORATE,
            countryCode = "US",
            profilePictureFullUrl = "",
            companyName = "ABC"
        )

        every {
            playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(
                sessionId,
                authenticatedUser.companyId!!
            )
        } returns sessionEntity

        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        every { userProfileUtil.retrieveProfile(1L) } returns userProfile

        // Act
        val result = playbookService.getChatMessages(sessionId, authenticatedUser)

        // Assert
        assertNotNull(result)
        assertEquals(2, result.size)
        assertEquals(playbookChatEntity1.id, result[0].id)
        assertEquals(playbookChatEntity1.question, result[0].question)
        assertEquals(playbookChatEntity1.answer, result[0].answer)

    }

    @Test
    fun `listCountries should return list of countries with active templates`() {
        // Arrange
        val countries = listOf("US", "UK", "CA")

        every { contentLibraryRepository.findByIdentifierAndActive("TEMPLATE") } returns countries

        // Act
        val result = playbookService.listCountries(null)

        // Assert
        assertEquals(countries, result)
        assertEquals(3, result.size)

        // Verify
        verify { contentLibraryRepository.findByIdentifierAndActive("TEMPLATE") }
    }

    @Test
    fun `assessmentAiMessage should create message with context for new thread`() {
        // Arrange
        val sessionId = "test-session-id"
        val request = AIMessageRequest("What is my assessment?", threadId = sessionId)

        val assessmentEntity = mockk<TravelAssessmentEntity> {
            every { data } returns "Assessment data for context"
        }

        val sessionEntity = PlaybookSessionEntity(
            playbook = mockk(),
            questionCount = 0,
            startTime = LocalDateTime.now(),
            sessionId = sessionId,
            type = "CREATE"
        )

        val thread = ThreadResponse(
            id = "12",
            createdAt = 1234
        )

        val messages = MessageData(
            id = "123",
            content = listOf()
        )
        val answerText = "Your assessment shows you are eligible for a visa."

        every {
            travelAssessmentRepository.findBySessionId(sessionId)
        } returns Optional.of(assessmentEntity)

        every {
            playbookSessionRepository.findBySessionId(sessionId)
        } returns sessionEntity

        every { openAIService.createThreadWithContext(any()) } returns thread
        every { openAIService.getAnswerFromThread(thread.id, request.question) } returns messages
        every { openAIService.readMessageText(messages) } returns answerText
        every { openAIService.createThread(any()) } returns thread

        every { playbookSessionRepository.save(any()) } returns sessionEntity
        every { playbookChatRepository.save(any()) } returns mockk()
        every { aiChatRepository.save(any()) } returns mockk()

        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        // Act
        val result = playbookService.assessmentAiMessage(sessionId, request, authenticatedUser)

        // Assert
        assertEquals(answerText, result)


    }

    @Test
    fun `assessmentAiMessage should use existing thread when available`() {
        // Arrange
        val sessionId = "test-session-id"
        val request = AIMessageRequest("What is my assessment?", threadId = sessionId)
        val threadId1 = "existing-thread-123"

        val assessmentEntity = mockk<TravelAssessmentEntity> {
            every { data } returns "Assessment data for context"
        }

        val sessionEntity = PlaybookSessionEntity(
            playbook = mockk(),
            questionCount = 0,
            startTime = LocalDateTime.now(),
            sessionId = sessionId,
            type = "CREATE"
        )

        val thread = ThreadResponse(
            id = "12",
            createdAt = 1234
        )

        val messages = MessageData(
            id = "123",
            content = listOf()
        )
        val answerText = "Your assessment shows you are eligible for a visa."

        every {
            travelAssessmentRepository.findBySessionId(sessionId)
        } returns Optional.of(assessmentEntity)

        every {
            playbookSessionRepository.findBySessionId(sessionId)
        } returns sessionEntity

        every { openAIService.createThread(any()) } returns thread
        every { openAIService.createThreadWithContext(any()) } returns thread
        every { openAIService.getAnswerFromThread(thread.id, request.question) } returns messages
        every { openAIService.readMessageText(messages) } returns answerText

        every { playbookSessionRepository.save(any()) } returns sessionEntity
        every { playbookChatRepository.save(any()) } returns mockk()
        every { aiChatRepository.save(any()) } returns mockk()
        every { playbookSessionRepository.findBySessionIdAndCorporateUserCorporateId(any(), any()) } returns sessionEntity

        // Act
        val result = playbookService.assessmentAiMessage(sessionId, request, authenticatedUser)

        // Assert
        assertEquals(answerText, result)

    }

    @Test
    fun `assessmentAiMessage should throw ApplicationException when assessment not found`() {
        // Arrange
        val sessionId = "test-session-id"
        val request = AIMessageRequest("What is my assessment?", sessionId)

        every {
            travelAssessmentRepository.findBySessionId(sessionId)
        } returns Optional.empty()

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            playbookService.assessmentAiMessage(sessionId, request, authenticatedUser)
        }

        assertEquals(ErrorCode.NOT_FOUND, exception.error)

        // Verify
        verify { travelAssessmentRepository.findBySessionId(sessionId) }
        verify(exactly = 0) {
            playbookSessionRepository.findBySessionId(any())
            openAIService.createThreadWithContext(any())
            openAIService.createThread(any())
            openAIService.getAnswerFromThread(any(), any())
            openAIService.readMessageText(any())
            playbookSessionRepository.save(any())
            playbookChatRepository.save(any())
            aiChatRepository.save(any())
        }
    }

}
