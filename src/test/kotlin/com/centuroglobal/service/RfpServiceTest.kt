package com.centuroglobal.service

import com.centuroglobal.data.payload.RfpRequest
import com.centuroglobal.data.payload.ServiceDetails
import com.centuroglobal.data.payload.UpdateRfpRequest
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.query.ProposalEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.data.pojo.RfpSearchFilter
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.ProposalRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.aws.AwsS3Service
import com.centuroglobal.shared.service.mail.MailSendingService
import com.centuroglobal.shared.util.PartnerEmailUtil
import com.centuroglobal.util.UserProfileUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.util.ReflectionTestUtils
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.util.*


class RfpServiceTest {

    private val corporateUserRepository:CorporateUserRepository  = mockk()
    private val rfpRepository:RfpRepository = mockk()
    private val groupChatService: GroupChatService = mockk()
    private val loginAccountRepository:LoginAccountRepository = mockk()
    private val rfpProposalRepository:ProposalRepository= mockk()

    private val corporateUserService: CorporateUserService = mockk()
    private val mailSendingService:MailSendingService = mockk()
    private val s3Service:AwsS3Service = mockk()
    private val rfpDocumentRepository: RfpDocumentRepository = mockk()
    private val corporateRepository:CorporateRepository= mockk()
    private val baseRepository = rfpRepository as BaseRepository<RfpEntity, Long>
    private val proposalService = ProposalService("test-bucket", listOf("jpg"),
        rfpProposalRepository, s3Service)
    private val partnerService: PartnerService= mockk()
    val partnerRepository: PartnerRepository= mockk()
    val rfpAssigneeRepository: RfpAssigneeRepository= mockk()
    private val partnerEmailUtil: PartnerEmailUtil= mockk()
    private val expertUserRepo: ExpertUserRepository= mockk()
    private val userProfileUtil:UserProfileUtil = mockk()

    private val rfpService = RfpService(
        "test-bucket",
        "test-url",
        listOf("<EMAIL>"),
        corporateUserRepository,
        rfpRepository,
        userProfileUtil,
        groupChatService,
        loginAccountRepository,
        corporateUserService,
        mailSendingService,
        s3Service,
        rfpDocumentRepository,
        proposalService,
        partnerRepository,
        rfpAssigneeRepository,
        partnerEmailUtil
    )

    private val corporateUserEntity:CorporateUserEntity= mockk()
    private val authenticatedUser:AuthenticatedUser= mockk()
    private val rfpDocumentEntity:RfpDocumentEntity = mockk()
    private val loginAccountEntity:LoginAccountEntity  = mockk()
    private val groupChatEntity: GroupChatEntity = mockk()
    private val serviceDetails: ServiceDetails = mockk()
    private val userProfile: UserProfile  = mockk()

    private val rfpServiceCountryEntity = RfpServiceCountryEntity(
        1,
        "US")

    private val rfpServiceEntity =RfpServiceEntity(
        1,"test",mutableListOf(rfpServiceCountryEntity))

    private val rfpEntity = RfpEntity(
        1,
        "heading",
        mutableListOf(rfpServiceEntity),
        "description",
            RfpStatus.OPEN,
        mutableListOf(rfpDocumentEntity),
        null,
        mutableListOf(loginAccountEntity),
        corporateUserEntity)

    val rfpSearchFilter = RfpSearchFilter(
        search = "rfp",
        accountId = null,
        user = null,
        responses = null,
        categories = null,
        corporateId = 1L,
        countryCode = null)

    val corporateUserProfile = CorporateUserProfile(
        id = 4,
        email = "<EMAIL>",
        firstName = "a",
        lastName = "a",
        status = AccountStatus.ACTIVE,
        role = Role.ROLE_USER,
        countryCode = "IN",
        profilePictureFullUrl = "",
        companyName = "a",
        userType = PartnerCaseType.CG,
        bandName = ""
    )

    val expertUserEntity = ExpertUserEntity(
        id = 1,
        jobTitle = "SDE",
        countryRegionId = 2,
        expertiseId = 3,
        expertises = listOf(),
        bio = "",
        displayName = "",
        infoVideoUrl = "",
        contactNumber = "123456",
        contactEmail = "<EMAIL>",
        contactWebsite = "a.com",
        companyProfile = ExpertCompanyProfileEntity(
            id = 5,
            name = "",
            logoKey = "",
            size = CompanySize.Size1,
            summary = "",
            users = mutableListOf(),
            lastUpdatedBy = 1,
            companyNumber = "123",
            companyAddress = "",
            aboutBusiness = "",
            effectiveDate = LocalDateTime.now(),
            effectiveEndDate = LocalDateTime.now(),
            contractAcceptedDate = LocalDateTime.now(),
            feesCurrency = "",
            feesAmount = "",
            specialTerms = "",
            membershipStatus = "",
            territory = "",
            renewContract = false,
            services = "",
            account = AccountEntity(
                id = 6,
                name = "",
                status = AccountStatus.ACTIVE,
                description = "",
                companyName = "",
                corporate = null,
                corporateUsers = listOf(),
                cases = mutableListOf()
            ),
            profileImage = "",
            expertContract =  null,
            status = AccountStatus.ACTIVE,
            questionsQuota = 13,
            associatedPartners = mutableListOf(),
            companyType = ExpertCompanyType.EXPERT,
            invitedBy = 6
        ),
        expertType = "",
        viewContract = false,
        case = mutableListOf(),
        profileImage = ""
    )

    private val rfpRequest = RfpRequest(1,"heading",listOf(serviceDetails),"description")
    private val updateRfpRequest = UpdateRfpRequest(1,"heading",listOf(serviceDetails),"description",listOf(1L))

    private val corporateEntity = CorporateEntity(
        1,
        "test",
        "US",
        CorporateStatus.ACTIVE,
        true,
        null,
        1,
        null,
        listOf(corporateUserEntity),
        1)

    @BeforeEach
    fun setup(){
        every { authenticatedUser.userId } returns LOGGED_IN_USER_ID
        every { authenticatedUser.companyId } returns LOGGED_IN_USER_CORPORATE_ID
        every { authenticatedUser.email } returns LOGGED_IN_USER_EMAIL
        every { authenticatedUser.userType } returns UserType.EXPERT.toString()
        every { authenticatedUser.role } returns  "ROLE_SUPER_ADMIN"

        every { expertUserRepo.findById(any()) } returns Optional.of(expertUserEntity)


        every { corporateUserEntity.id } returns LOGGED_IN_USER_ID
        every { corporateUserEntity.corporate } returns corporateEntity
        every { corporateUserRepository.findById(LOGGED_IN_USER_ID) } returns Optional.of(corporateUserEntity)

        every { loginAccountEntity.id } returns LOGGED_IN_USER_ID

        every { (rfpEntity.createdBy as LoginAccountEntity).firstName } returns "test"
        every { (rfpEntity.createdBy as LoginAccountEntity).lastName } returns "user"
        every { (rfpEntity.createdBy as LoginAccountEntity).email } returns LOGGED_IN_USER_EMAIL
        every { corporateUserService.getUserManagers(any()) } returns listOf()
        every { corporateUserRepository.findAllByCorporateIdAndBandNameIn(any(), listOf("Super Admin (free)", "Super Admin")) } returns listOf()
        every { groupChatService.createGroupChat(
            ChatType.RFP, any(), any(),
            virtualParticipants = listOf()
        ) } returns groupChatEntity
        every { s3Service.getS3PublicUrl(any()) } returns "this-is-s3-public-url"
        every { mailSendingService.sendEmail(any()) } returns true

        //every { caseDashboardService.retrieveProfile(any(), corporateName ) } returns UserProfile(LOGGED_IN_USER_ID, LOGGED_IN_USER_EMAIL, "test", "user", AccountStatus.ACTIVE, Role.ROLE_ADMIN, null, null)
        //every { caseDashboardService.retrieveProfile(any() ) } returns UserProfile(LOGGED_IN_USER_ID, LOGGED_IN_USER_EMAIL, "test", "user", AccountStatus.ACTIVE, Role.ROLE_ADMIN, null, null)

        every { groupChatService.getMessagesCount(ChatType.RFP, any()) } returns 13
        every { groupChatService.getUnreadMessagesCount(ChatType.RFP, any(), any()) } returns 4

        every { corporateRepository.findById(LOGGED_IN_USER_CORPORATE_ID) } returns Optional.of(corporateEntity)

        every { rfpProposalRepository.findAllByTypeAndReferenceId(ChatType.RFP, rfpEntity.id!!) } returns listOf()

        every { rfpRepository.findByIdForExpert(any(), any(), any()) } returns Optional.of(rfpEntity)

        every { rfpRepository.searchByCriteriaForAdmin(any(), any()) } returns PageImpl(listOf(rfpEntity))

        every { rfpAssigneeRepository.findAllByRfp(any()) } returns mutableListOf()

        every { userProfileUtil.retrieveCorporateProfile(any(), any()) } returns corporateUserProfile

        every { partnerRepository.findById(any()) } returns Optional.of(PartnerEntity(
            id = 6,
            name = "",
            createdFrom = PartnerType.NEW,
            contractFromDate = LocalDateTime.now(),
            contractToDate = LocalDateTime.now(),
            casesManaged = PartnerCaseType.CG,
            queriesManaged = PartnerCaseType.CG,
            companyLogo = "",
            themePrimaryColor = "",
            themeSecondaryColor = "",
            rootUserId = 6,
            status = CorporateStatus.ACTIVE,
            corporates = listOf(),
            partnerUsers = mutableListOf(),
            country = "IN",
            band = null,
            referenceId = 4,
            associatedCompanies = mutableListOf(),
            corporateAccess = ""
        ))

//        every {caseDashboardService.retrieveProfile(any())} returns UserProfile(
//            LOGGED_IN_USER_ID,
//            LOGGED_IN_USER_EMAIL,
//            "test",
//            "user",
//            AccountStatus.ACTIVE,
//            Role.ROLE_CORPORATE,
//            null,
//            null
//        )

        every { partnerService.getPartnerId(any()) } returns null

        ReflectionTestUtils.setField(rfpService, "corporateRepo", corporateRepository)
        ReflectionTestUtils.setField(rfpService, "corporateUserRepo", corporateUserRepository)
        ReflectionTestUtils.setField(rfpService, "expertUserRepo", expertUserRepo)

    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }


    @Test
    fun createRfpTest() {

        every { rfpRepository.findByIdAndStatus(1,RfpStatus.DRAFT) } returns Optional.of(rfpEntity)
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { authenticatedUser.partnerId } returns 5

        every { partnerEmailUtil.updateContext(any(), any()) } returns Unit


         every{ groupChatService.createGroupChat(
             ChatType.RFP, 1, listOf(10),
             virtualParticipants = listOf()
         ) } returns groupChatEntity

        val result = rfpService.createRfp(rfpRequest,authenticatedUser)
        assertEquals(rfpEntity.id,result)
    }

    @Test
    fun listRfpTestFullAccess() {
        val pageRequest = PageRequest.of(1, 10)

        every { rfpRepository.searchByCriteriaForFullAccess(rfpSearchFilter,pageRequest) } returns PageImpl(listOf(rfpEntity))
        every { authenticatedUser.visibilities } returns  listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { loginAccountEntity.id } returns 1
        every { groupChatService.getMessagesCount(ChatType.RFP,1) } returns 1
        every { groupChatService.getUnreadMessagesCount(ChatType.RFP,1,10) } returns 1
        //every { rfpDocumentEntity.isApproved } returns true
        //every { caseDashboardService.retrieveProfile(10,"test") } returns userProfile

        val result = rfpService.listRfp(rfpSearchFilter,pageRequest,authenticatedUser)
        assertNotNull(result)
        assertNotNull(result.rows)
        assertFalse(result.rows.isEmpty())
        assertNotNull(result.currentPage)
        assertNotNull(result.totalPages)
        assertEquals(result.rows[0].heading, rfpEntity.heading)
    }

    @Test
    fun listRfpTestReporteesAccess() {
        val pageRequest = PageRequest.of(1, 10)

        every { rfpRepository.searchByCriteria(rfpSearchFilter,listOf(10,10),pageRequest) } returns PageImpl(listOf(rfpEntity))
        every { authenticatedUser.visibilities } returns  listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { loginAccountEntity.id } returns 1
        every { groupChatService.getMessagesCount(ChatType.RFP,1) } returns 1
        every { groupChatService.getUnreadMessagesCount(ChatType.RFP,1,10) } returns 1
        //every { rfpDocumentEntity.isApproved } returns true
        every { corporateUserRepository.findAllByManagersManagerId(10) } returns mutableListOf(corporateUserEntity)
        //every { caseDashboardService.retrieveProfile(10,"test") } returns userProfile


        val result = rfpService.listRfp(rfpSearchFilter,pageRequest,authenticatedUser)
        assertNotNull(result)
        assertNotNull(result.rows)
        assertFalse(result.rows.isEmpty())
        assertNotNull(result.currentPage)
        assertNotNull(result.totalPages)
        assertEquals(result.rows[0].heading, rfpEntity.heading)
    }

    @Test
    fun listRfpTestOwnAccess() {
        val pageRequest = PageRequest.of(1, 10)

        every { rfpRepository.searchByCriteria(rfpSearchFilter,listOf(10),pageRequest) } returns PageImpl(listOf(rfpEntity))
        every { authenticatedUser.visibilities } returns  listOf(UserAccess("RFP", mutableListOf("OWN")))
        every { loginAccountEntity.id } returns 1
        every { groupChatService.getMessagesCount(ChatType.RFP,1) } returns 1
        every { groupChatService.getUnreadMessagesCount(ChatType.RFP,1,10) } returns 1
        //every { rfpDocumentEntity.isApproved } returns true
        every { corporateUserRepository.findAllByManagersManagerId(10) } returns mutableListOf(corporateUserEntity)
        // every { caseDashboardService.retrieveProfile(10,"test") } returns userProfile

        val result = rfpService.listRfp(rfpSearchFilter,pageRequest,authenticatedUser)
        assertNotNull(result)
        assertNotNull(result.rows)
        assertFalse(result.rows.isEmpty())
        assertNotNull(result.currentPage)
        assertNotNull(result.totalPages)
        assertEquals(result.rows[0].heading, rfpEntity.heading)
    }


    @Test
    fun getRfpById_With_FullAccess() {

        val rfpId = 1L

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpId, corporateEntity.id!!) } returns Optional.of(rfpEntity)

        every { groupChatService.updateLastSeen(ChatType.RFP, rfpEntity.id!!, LOGGED_IN_USER_ID) } returns Unit


        val result = rfpService.getRfpById(rfpId, authenticatedUser)

        assertNotNull(result)
        assertEquals(rfpEntity.id, result!!.id)
        assertEquals(rfpEntity.heading, result.heading)
    }



    /*@Test
    fun getRfpById_With_Reportees_throws_Exception() {

        val rfpId = 1L
        val userId = 2L

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        //Changing user id to acts as reportees
        every { authenticatedUser.userId } returns userId
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpId, corporateEntity.id!!) } returns Optional.of(rfpEntity)

        every { corporateUserRepository.findAllByManagersManagerId(userId) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(rfpId, mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.empty()
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUserEntity)

        every { groupChatService.updateLastSeen(any(), any(), any()) } returns Unit


        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.getRfpById(rfpId, authenticatedUser)
        }

    }*/



    /*@Test
    fun getRfpById_With_No_Corporate() {

        val rfpId = 1L

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns null

        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.getRfpById(rfpId, authenticatedUser)
        }
    }*/

    /*@Test
    fun getRfpById_privilegeEscalation() {

        val rfpId = 1L

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpId, corporateEntity.id!!) } returns Optional.empty()


        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.getRfpById(rfpId, authenticatedUser)
        }
    }*/
    /*@Test
    fun getRfpById_invalid_Access() {

        val rfpId = 1L

        every { authenticatedUser.userId } returns 2L
        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("INVALID")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpId, corporateEntity.id!!) } returns Optional.of(rfpEntity)


        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.getRfpById(rfpId, authenticatedUser)
        }
    }*/



    //download, view and update test cases

    @Test
    fun testDownloadDocument() {

        val rfpDocumentId = 1L
        val rfpDocumentFileName = "document.docx"
        val rfpDocumentContent = "Document content".toByteArray()
        val rfpDocument = RfpDocumentEntity(id = rfpDocumentId, fileName = rfpDocumentFileName, fileSize = rfpDocumentContent.size.toLong(), fileUploadDate = LocalDateTime.now(), fileType = "docx", rfp = rfpEntity)

        every { rfpDocumentRepository.findByIdAndRfp(rfpDocumentId, rfpEntity) } returns rfpDocument
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { s3Service.downLoadFile(any(), "rfp/${rfpEntity.id}/${rfpDocumentId}/${rfpDocumentFileName}") } returns ByteArrayInputStream(rfpDocumentContent)

        // Call the downloadDocument function
        val result: ResponseEntity<StreamingResponseBody> = rfpService.downloadDocument(rfpEntity.id!!, rfpDocumentId, authenticatedUser)

        // Verify the assertions
        assertEquals(HttpStatus.OK, result.statusCode)
        assertEquals(MediaType.APPLICATION_OCTET_STREAM, result.headers.contentType)
        assertTrue(result.headers[HttpHeaders.CONTENT_DISPOSITION]?.get(0)!!.contains(rfpDocumentFileName))
        assertNotNull(result.body)
    }

    @Test
    fun testDownloadDocument_document_not_found() {

        val rfpDocumentId = 1L
        val rfpDocumentFileName = "document.docx"
        val rfpDocumentContent = "Document content".toByteArray()

        every { rfpDocumentRepository.findByIdAndRfp(rfpDocumentId, rfpEntity) } returns null
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { s3Service.downLoadFile(any(), "rfp/${rfpEntity.id}/${rfpDocumentId}/${rfpDocumentFileName}") } returns ByteArrayInputStream(rfpDocumentContent)

        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.downloadDocument(rfpEntity.id!!, rfpDocumentId, authenticatedUser)
        }
    }

    @Test
    fun testViewDocument() {
        // Mock the input parameters
        val rfpId = 1L
        val document = RfpDocumentEntity(
            id = 101,
            fileName = "Document File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            rfp = rfpEntity
        )
        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.findByIdAndRfp(document.id!!, rfpEntity) } returns document
        every { s3Service.getS3Url(any(), any()) } returns "This is s3 public url"


        val result = rfpService.viewDocumentUrl(rfpId, document.id!!, authenticatedUser)

        assertNotNull(result)
        assertFalse(result!!.isEmpty())
    }
    @Test
    fun testViewDocument_throws_Exception() {
        // Mock the input parameters
        val rfpId = 1L
        val document = RfpDocumentEntity(
            id = 101,
            fileName = "Document File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            rfp = rfpEntity
        )
        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.findByIdAndRfp(document.id!!, rfpEntity) } returns null
        every { s3Service.getS3Url(any(), any()) } returns "This is s3 public url"


        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.viewDocumentUrl(rfpId, document.id!!, authenticatedUser)
        }
    }

    @Test
    fun testUpdateStatus() {

        every { rfpRepository.findById(rfpEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { groupChatService.updateLastSeen(ChatType.RFP, rfpEntity.id!!, LOGGED_IN_USER_ID) } returns Unit

        val newRfpEntity = rfpEntity.copy()
        newRfpEntity.status = RfpStatus.RESOLVED

        every { rfpRepository.save(any()) } returns newRfpEntity

        val result = rfpService.updateStatus(newRfpEntity.id!!, RfpStatus.RESOLVED, authenticatedUser)
        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun testUpdateStatusToOpen() {

        every { rfpRepository.findById(rfpEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { groupChatService.updateLastSeen(ChatType.RFP, rfpEntity.id!!, LOGGED_IN_USER_ID) } returns Unit

        val newRfpEntity = rfpEntity.copy()
        newRfpEntity.status = RfpStatus.OPEN

        every {rfpRepository.save(any()) } returns newRfpEntity

        val result = rfpService.updateStatus(newRfpEntity.id!!, RfpStatus.OPEN, authenticatedUser)
        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun getRfpById_With_ReporteesAccess() {

        val rfpId = 1L

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpId, corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { groupChatService.updateLastSeen(ChatType.RFP, rfpEntity.id!!, LOGGED_IN_USER_ID) } returns Unit
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.of(rfpEntity)

        val result = rfpService.getRfpById(rfpId, authenticatedUser)

        assertNotNull(result)
        assertEquals(rfpEntity.id, result!!.id)
        assertEquals(rfpEntity.heading, result.heading)
    }

    @Test
    fun getRfpById_With_OwnAccess() {

        val rfpId = 1L

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("OWN")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpId, corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { groupChatService.updateLastSeen(ChatType.RFP, rfpEntity.id!!, LOGGED_IN_USER_ID) } returns Unit
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)

        val result = rfpService.getRfpById(rfpId, authenticatedUser)

        assertNotNull(result)
        assertEquals(rfpEntity.id, result!!.id)
        assertEquals(rfpEntity.heading, result.heading)
    }

    @Test
    fun createDraftRfpTest(){

        val newRfpEntity = rfpEntity.copy()

        every { rfpRepository.save(any()) } returns newRfpEntity
        val result= rfpService.createDraftRfp(authenticatedUser)
        assertNotNull(result)
    }

    @Test
    fun editRfpTest_With_FULL_Access(){

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { groupChatService.updateChatParticipants(rfpEntity.id!!, ChatType.RFP,any()) } returns Unit
        every { rfpAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { rfpAssigneeRepository.saveAll(mutableListOf()) } returns mutableListOf()

        val result = rfpService.editRfp(updateRfpRequest,authenticatedUser)
        assertNotNull(result)
        assertEquals(rfpEntity.id,result)

    }

    @Test
    fun editRfpTest_With_REPORTEES_Access(){

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { groupChatService.updateChatParticipants(rfpEntity.id!!, ChatType.RFP,any()) } returns Unit
        every { rfpAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { rfpAssigneeRepository.saveAll(mutableListOf()) } returns mutableListOf()

        val result = rfpService.editRfp(updateRfpRequest,authenticatedUser)
        assertNotNull(result)
        assertEquals(rfpEntity.id,result)
    }

    @Test
    fun editRfpTest_With_Own_Access(){

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("OWN")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { groupChatService.updateChatParticipants(rfpEntity.id!!, ChatType.RFP,any()) } returns Unit
        every { rfpAssigneeRepository.deleteByUserIn(any()) } returns Unit
        every { rfpAssigneeRepository.saveAll(mutableListOf()) } returns mutableListOf()

        val result = rfpService.editRfp(updateRfpRequest,authenticatedUser)
        assertNotNull(result)
        assertEquals(rfpEntity.id,result)
    }

    @Test
    fun getDocumentsByRfpTest_With_OWN_Access(){
        val rfpId = 1L


        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("OWN")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentEntity.id } returns 1L
        every { rfpDocumentEntity.fileName } returns "test"
        every { rfpDocumentEntity.fileType } returns "pdf"
        every { rfpDocumentEntity.fileSize } returns 1L
        every { rfpDocumentEntity.fileUploadDate } returns LocalDateTime.now()
        val result = rfpService.getDocumentsByRfp(rfpId,authenticatedUser)

        assertNotNull(result)
        assertEquals(rfpDocumentEntity.id, result!![0].documentId )
        assertEquals(rfpDocumentEntity.fileName, result!![0].fileName )
    }

    @Test
    fun getDocumentsByRfpTest_With_FULL_Access(){
        val rfpId = 1L
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { rfpDocumentEntity.id } returns 1L
        every { rfpDocumentEntity.fileName } returns "test"
        every { rfpDocumentEntity.fileType } returns "pdf"
        every { rfpDocumentEntity.fileSize } returns 1L
        every { rfpDocumentEntity.fileUploadDate } returns LocalDateTime.now()
        val result = rfpService.getDocumentsByRfp(rfpId,authenticatedUser)

        assertNotNull(result)
        assertEquals(rfpDocumentEntity.id, result!![0].documentId )
        assertEquals(rfpDocumentEntity.fileName, result!![0].fileName )
    }

    @Test
    fun getDocumentsByRfpTest_With_REPORTEES_Access (){

        val rfpId = 1L

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.of(rfpEntity)

        every { rfpDocumentEntity.id } returns 1L
        every { rfpDocumentEntity.fileName } returns "test"
        every { rfpDocumentEntity.fileType } returns "pdf"
        every { rfpDocumentEntity.fileSize } returns 1L
        every { rfpDocumentEntity.fileUploadDate } returns LocalDateTime.now()
        val result = rfpService.getDocumentsByRfp(rfpId,authenticatedUser)

        assertNotNull(result)
        assertEquals(rfpDocumentEntity.id, result!![0].documentId )
        assertEquals(rfpDocumentEntity.fileName, result!![0].fileName )
    }

    @Test
    fun createDocument_With_FULL_Access(){
        val rfpId = 1L
        val file=MockMultipartFile(
            "test",null,"text/plain","Sample content".toByteArray(
                StandardCharsets.UTF_8
            ))

        every { rfpService.createDraftRfp(authenticatedUser) } returns 1
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.save(any()) } returns rfpDocumentEntity
        every { rfpDocumentEntity.id } returns 1
        every {  s3Service.uploadFile(any(),any(),"test-bucket") } returns Unit

        val result = rfpService.createDocument(rfpId,"test","text/plain",1L,file,authenticatedUser)
        assertNotNull(result)
        assertEquals(rfpDocumentEntity.id,result)
    }

    @Test
    fun createDocument_With_OWN_Access(){
        val rfpId = 1L
        val file=MockMultipartFile(
            "test",null,"text/plain","Sample content".toByteArray(
                StandardCharsets.UTF_8
            ))

        every { rfpService.createDraftRfp(authenticatedUser) } returns 1
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("OWN")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.save(any()) } returns rfpDocumentEntity
        every { rfpDocumentEntity.id } returns 1
        every {  s3Service.uploadFile(any(),any(),"test-bucket") } returns Unit

        val result = rfpService.createDocument(rfpId,"test","text/plain",1L,file,authenticatedUser)
        assertNotNull(result)
        assertEquals(rfpDocumentEntity.id,result)
    }

    @Test
    fun createDocument_With_REPORTEES_Access(){
        val rfpId = 1L
        val file=MockMultipartFile(
            "test",null,"text/plain","Sample content".toByteArray(
                StandardCharsets.UTF_8
            ))

        every { rfpService.createDraftRfp(authenticatedUser) } returns 1
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { serviceDetails.serviceName  } returns "test"
        every { serviceDetails.countries } returns listOf("US")
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.save(any()) } returns rfpDocumentEntity
        every { rfpDocumentEntity.id } returns 1
        every { s3Service.uploadFile(any(),any(),"test-bucket") } returns Unit

        val result = rfpService.createDocument(rfpId,"test","text/plain",1L,file,authenticatedUser)
        assertNotNull(result)
        assertEquals(rfpDocumentEntity.id,result)
    }
    @Test
    fun deleteDocument_With_FULL_Access(){
        val rfpId = 1L
        val documentId  = 1L

        val rfpDocument = RfpDocumentEntity(
            1,
            "test",
            "text/plain",
            1L,
            LocalDateTime.now(),
            rfpEntity)

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.save(any()) } returns rfpDocumentEntity
        every { rfpDocumentEntity.id } returns 1L
        every { s3Service.deleteFileByS3Key(any(), any()) } returns Unit
        every { rfpDocumentRepository.findByIdAndRfp(rfpDocument.id!!,rfpEntity) } returns rfpDocument
        every { expertUserRepo.findById(authenticatedUser.userId) } returns Optional.of(expertUserEntity)


        val result = rfpService.deleteDocument(rfpId,documentId,authenticatedUser)
        assertNotNull(result)
        assertEquals(result,true)
    }

    @Test
    fun deleteDocument_With_REPORTEES_Access(){
        val rfpId = 1L
        val documentId  = 1L

        val rfpDocument = RfpDocumentEntity(
            1,
            "test",
            "text/plain",
            1L,
            LocalDateTime.now(),
            rfpEntity)

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("REPORTEES")))
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity, corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.save(any()) } returns rfpDocumentEntity
        every { rfpDocumentEntity.id } returns 1L
        every { s3Service.deleteFileByS3Key(any(), any()) } returns Unit
        every { rfpDocumentRepository.findByIdAndRfp(rfpDocument.id!!,rfpEntity) } returns rfpDocument


        val result = rfpService.deleteDocument(rfpId,documentId,authenticatedUser)
        assertNotNull(result)
        assertEquals(result,true)
    }

    @Test
    fun deleteDocument_With_OWN_Access(){
        val rfpId = 1L
        val documentId  = 1L

        val rfpDocument = RfpDocumentEntity(
            1,
            "test",
            "text/plain",
            1L,
            LocalDateTime.now(),
            rfpEntity)

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { baseRepository.findByIdAndCreatedByCorporateId(1 , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("OWN")))
        every { rfpRepository.save(any()) } returns rfpEntity
        every { loginAccountRepository.findById(1) } returns Optional.of(loginAccountEntity)
        every { corporateUserRepository.findAllByManagersManagerId(LOGGED_IN_USER_ID) } returns mutableListOf(corporateUserEntity)
        every { baseRepository.findByIdAndCreatedByIn(1,mutableListOf(corporateUserEntity)) } returns Optional.of(rfpEntity)
        every { rfpDocumentRepository.save(any()) } returns rfpDocumentEntity
        every { rfpDocumentEntity.id } returns 1L
        every { s3Service.deleteFileByS3Key(any(), any()) } returns Unit
        every { rfpDocumentRepository.findByIdAndRfp(rfpDocument.id!!,rfpEntity) } returns rfpDocument


        val result = rfpService.deleteDocument(rfpId,documentId,authenticatedUser)
        assertNotNull(result)
        assertEquals(result,true)
    }

    @Test
    fun listRfpForAdminTest(){

        val pageRequest = PageRequest.of(1, 1)
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
        every { rfpRepository.searchByCriteriaForAdmin(rfpSearchFilter,pageRequest) } returns PageImpl(listOf(rfpEntity))
        every { rfpRepository.findByIdAndStatus(1,RfpStatus.OPEN) } returns Optional.of(rfpEntity)
        every { rfpRepository.findStatsByCriteria(rfpSearchFilter) } returns listOf(listOf(mapOf("status" to "open"), mapOf("count" to "1")))
        every { rfpDocumentRepository.findStatsByCriteria(rfpSearchFilter) } returns listOf(listOf(mapOf("status" to "open"), mapOf("count" to "1")))
        // every { caseDashboardService.retrieveProfile(10, "test") } returns userProfile
        val result = rfpService.listRfpForAdmin(rfpSearchFilter,pageRequest,authenticatedUser)
        assertNotNull(result)
    }

    @Test
    fun createProposal(){
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "jpg",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.RFP,
            referenceId = rfpId
        )

        val file = MockMultipartFile(proposal.fileName, proposal.fileName+"."+proposal.fileType, "text/plain", "Sample content".toByteArray(
            StandardCharsets.UTF_8))

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpProposalRepository.save(any()) } returns proposal
        every { s3Service.uploadFile(file, any(), "test-bucket") } returns Unit
        every { rfpRepository.touch(any(), any()) } returns Unit

        val result = rfpService.createProposal(rfpId,proposal.fileName,proposal.fileType!!,proposal.fileSize,file,authenticatedUser)
        assertEquals(proposal.id, result)
    }

    @Test
    fun testCreateProposal_s3_upload_throws_Exception() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.RFP,
            referenceId = rfpId
        )

        val file = MockMultipartFile(
            proposal.fileName, null, "text/plain", "Sample content".toByteArray(StandardCharsets.UTF_8))

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(
            rfpEntity
        )

        every { rfpProposalRepository.save(any()) } returns proposal
        every { s3Service.uploadFile(file, any(), "test-bucket") } throws (Exception())

        // Call the function to create the proposal
        org.junit.jupiter.api.assertThrows<Exception> {
            rfpService.createProposal(
                rfpId,
                proposal.fileName,
                proposal.fileType!!,
                proposal.fileSize,
                file,
                authenticatedUser
            )
        }
    }

    @Test
    fun testDeleteProposal() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.RFP,
            referenceId = rfpId
        )

        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!,proposal.referenceId,proposal.type) } returns Optional.of(proposal)
        every { rfpProposalRepository.save(any()) } returns proposal
        every { s3Service.deleteFileByS3Key(any(), any()) } returns Unit
        every { rfpProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!, rfpId, ChatType.QUERY) } returns Optional.of(proposal)
        every { rfpProposalRepository.deleteById(proposal.id!!) } returns Unit

        val result = rfpService.deleteProposal(rfpId, proposal.id!!, authenticatedUser)

        // Verify the result
        assertNotNull(result)
        assertEquals(true, result)
    }

    @Test
    fun testDeleteProposal_s3_delete_throws_Exception() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.RFP,
            referenceId = rfpId
        )

        every { rfpRepository.save(rfpEntity) } returns rfpEntity
        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!,proposal.referenceId,proposal.type) } returns Optional.of(proposal)

        every { rfpProposalRepository.save(any()) } returns proposal
        every { s3Service.deleteFileByS3Key(any(), any()) } throws(Exception())
        every { rfpProposalRepository.findAllByTypeAndReferenceId(ChatType.RFP, rfpId) } returns listOf(proposal)

        org.junit.jupiter.api.assertThrows<Exception> {
            rfpService.deleteProposal(rfpId,proposal.id!!,authenticatedUser)
        }
    }

    @Test
    fun testViewProposal() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.RFP,
            referenceId = rfpId
        )
        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!, rfpId, ChatType.RFP) } returns Optional.of(proposal)
        every { s3Service.getS3Url(any(), any()) } returns "This is s3 public url"


        val result = rfpService.viewProposalUrl(rfpId, proposal.id!!, authenticatedUser)

        assertNotNull(result)
        assertFalse(result!!.isEmpty())
    }

    @Test
    fun testViewProposal_throws_Exception() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = false,
            type = ChatType.RFP,
            referenceId = rfpId
        )
        every { rfpRepository.findById(rfpId) } returns Optional.of(rfpEntity)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { rfpProposalRepository.findByIdAndReferenceIdAndType(proposal.id!!, rfpId, ChatType.RFP) } returns Optional.empty()
        every { s3Service.getS3Url(any(), any()) } returns "This is s3 public url"


        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.viewProposalUrl(rfpId,proposal.id!!,authenticatedUser)
        }
    }

    @Test
    fun testApproveProposals() {
        // Mock the input parameters
        val  rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.RFP,
            referenceId = rfpId
        )
        val approvedRfp =  rfpEntity.copy()
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId( rfpEntity.id!!, corporateEntity.id!!) } returns Optional.of(approvedRfp)
        every {  rfpProposalRepository.findAllByTypeAndReferenceId(ChatType.RFP,  rfpId) } returns listOf(proposal)

        every {  rfpRepository.save(approvedRfp) } returns approvedRfp
        every {  rfpProposalRepository.saveAll(listOf(proposal)) } returns listOf(proposal)

        val result =  rfpService.approveRfpProposals( rfpId, authenticatedUser)

        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun testApproveProposals_when_no_proposals_present() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.RFP,
            referenceId = rfpId
        )
        val approvedRfp = rfpEntity.copy()
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(approvedRfp)
        every { rfpProposalRepository.findAllByTypeAndReferenceId(ChatType.RFP, rfpId) } returns listOf()

        every { rfpRepository.save(approvedRfp) } returns approvedRfp
        every { rfpProposalRepository.saveAll(listOf(proposal)) } returns listOf(proposal)

        val result = rfpService.approveRfpProposals(rfpId, authenticatedUser)

        assertNotNull(result)
        assertFalse(result!!)
    }

    @Test
    fun testDownloadProposal() {

        val rfpProposalId = 1L
        val rfpProposalFileName = "proposal.docx"
        val rfpProposalContent = "Proposal content".toByteArray()
        val rfpProposal = ProposalEntity(id = rfpProposalId, fileName = rfpProposalFileName, fileSize = rfpProposalContent.size.toLong(), fileUploadDate = LocalDateTime.now(), fileType = "docx", type = ChatType.RFP, referenceId = rfpEntity.id!!)

        every { rfpProposalRepository.findByIdAndReferenceIdAndType(rfpProposalId, rfpEntity.id!!, ChatType.RFP) } returns Optional.of(rfpProposal)
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { s3Service.downLoadFile(any(), "rfps/${rfpEntity.id}/${rfpProposalId}/${rfpProposalFileName}") } returns ByteArrayInputStream(rfpProposalContent)

        // Call the downloadProposal function
        val result: ResponseEntity<StreamingResponseBody> = rfpService.downloadProposal(rfpEntity.id!!, rfpProposalId, authenticatedUser)

        // Verify the assertions
        assertEquals(HttpStatus.OK, result.statusCode)
        assertEquals(MediaType.APPLICATION_OCTET_STREAM, result.headers.contentType)
        assertTrue(result.headers[HttpHeaders.CONTENT_DISPOSITION]?.get(0)!!.contains(rfpProposalFileName))
        assertNotNull(result.body)
    }

    @Test
    fun testDownloadProposal_proposal_not_found() {

        val rfpProposalId = 1L
        val rfpProposalFileName = "proposal.docx"
        val rfpProposalContent = "Proposal content".toByteArray()

        every { rfpProposalRepository.findByIdAndReferenceIdAndType(rfpProposalId,rfpEntity.id!!, ChatType.RFP) } returns Optional.empty()
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(rfpEntity)
        every { s3Service.downLoadFile(any(), "rfps/${rfpEntity.id}/${rfpProposalId}/${rfpProposalFileName}") } returns ByteArrayInputStream(rfpProposalContent)
        every { rfpProposalRepository.deleteById(rfpProposalId) } returns Unit

        org.junit.jupiter.api.assertThrows<ApplicationException> {
            rfpService.downloadProposal(rfpEntity.id!!,rfpProposalId,authenticatedUser)
        }
    }
    @Test
    fun testGetProposalsById() {
        // Mock the input parameters
        val rfpId = 1L
        val proposal = ProposalEntity(
            id = 101,
            fileName = "Proposal File",
            fileType = "pdf",
            fileSize = 1024L,
            fileUploadDate = LocalDateTime.now(),
            isApproved = true,
            type = ChatType.RFP,
            referenceId = rfpId
        )
        proposal.createdBy = 2

        val newRfpEntity = rfpEntity.copy()

        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("RFP", mutableListOf("FULL")))
        every { baseRepository.findByIdAndCreatedByCorporateId(rfpEntity.id!! , corporateEntity.id!!) } returns Optional.of(newRfpEntity)
        every { rfpProposalRepository.findAllByTypeAndReferenceId(ChatType.RFP, newRfpEntity.id!!) } returns listOf(proposal)
        every { loginAccountRepository.getFirstNameLastNameById(any()) } returns "abcd"
        every { proposalService.getAllProposals(any(), any()) } returns listOf(proposal)

        val result = rfpService.getProposalsByRfp(rfpId, authenticatedUser)

        assertNotNull(result)
        assertFalse(result!!.isEmpty())
        assertEquals(proposal.id, result[0].proposalId)
    }
}
