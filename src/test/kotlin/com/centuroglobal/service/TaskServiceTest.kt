package com.centuroglobal.service

import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.entity.task.TaskWorkflowEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.ReferenceType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.TaskAssigneeType
import com.centuroglobal.shared.data.enums.task.TaskCompanyType
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.enums.task.TaskVisibility
import com.centuroglobal.shared.data.pojo.CaseCategory
import com.centuroglobal.shared.data.pojo.UserProfile
import com.centuroglobal.shared.data.pojo.task.dto.*
import com.centuroglobal.shared.data.pojo.task.request.CreateTaskRequest
import com.centuroglobal.shared.data.pojo.task.response.TaskCountResponse
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.query.QueryCategoryRepository
import com.centuroglobal.shared.repository.query.QueryRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.shared.service.task.SharedTaskWorkflowService
import com.centuroglobal.shared.util.TimeUtil
import com.centuroglobal.util.UserProfileUtil
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.test.util.ReflectionTestUtils
import java.sql.Date
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

class TaskServiceTest {

    private val taskEmailService: TaskEmailService = mockk()
    private val taskRepository: TaskRepository = mockk()
    private val taskAssigneeRepository: TaskAssigneeRepository = mockk()
    private val taskReminderRepository: TaskReminderRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()
    private val caseRepository: CaseRepository = mockk()
    private val queryRepository: QueryRepository = mockk()
    private val queryCategoryRepository: QueryCategoryRepository = mockk()
    private val rfpRepository: RfpRepository = mockk()
    private val partnerRepository: PartnerRepository = mockk()
    private val corporateRepository: CorporateRepository = mockk()
    private val accountRepository: AccountEntityRepository = mockk()
    private val caseService: CaseService = mockk()
    private val taskWorkflowService: TaskWorkflowService = mockk()
    private val sharedTaskWorkflowService: SharedTaskWorkflowService = mockk()
    private val accountEntityRepository: AccountEntityRepository = mockk()

    private val loginAccountEntity: LoginAccountEntity = mockk()
    private val taskAssigneeEntity: TaskAssigneeEntity = mockk()
    private val taskReminderEntity: TaskReminderEntity = mockk()

    private val taskService = TaskService(
        taskEmailService,
        taskRepository,
        taskAssigneeRepository,
        taskReminderRepository,
        loginAccountRepository,
        userProfileUtil,
        caseRepository,
        queryRepository,
        queryCategoryRepository,
        rfpRepository,
        partnerRepository,
        corporateRepository,
        accountRepository,
        caseService,
        taskWorkflowService,
        sharedTaskWorkflowService
    )

    val task = TaskEntity(
        id = 1L,
        name = "Test Task",
        description = "Test Description",
        referenceId = 1L,
        referenceType = ReferenceType.WORKFLOW,
        visibility = TaskVisibility.PUBLIC,
        dueDate = LocalDateTime.now().plusDays(7),
        completedDate = null,
        priority = "",
        status = TaskStatus.IN_PROGRESS,
        inProgressDate = LocalDateTime.now(),
        companyId = 3,
        caseMilestone = "",
        caseStatus = "",
        progress = "",
        instruction = "",
        usefulLinks = "",
        plannedStartDate = LocalDateTime.now(),
        startDate = LocalDateTime.now(),
        expectedDueDate = LocalDateTime.now(),
        companyType = TaskCompanyType.EXPERT,
        partner = null,
        assignee = mutableListOf(TaskAssigneeEntity(
            id = 4,
            task = mockk(),
            assigneeId = loginAccountEntity,
            type = TaskAssigneeType.EXPERT,
            companyId = 3,
            corporatePartnerId = 7
        )),
        reminders = mutableListOf(TaskReminderEntity(
            id = 8,
            task = mockk(),
            dateTime = LocalDateTime.now()
        )),
        taskTemplate = null
    )

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.partnerId } returns 1L
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name

        every { loginAccountEntity.userRoles } returns mutableListOf()
        every { partnerRepository.getReferenceById(any()) } returns mockk()

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccountEntity)

        every { userProfileUtil.retrieveProfile(any()) } returns UserProfile(
            1,
            "<EMAIL>",
            "String",
            "String",
            AccountStatus.ACTIVE,
            Role.ROLE_EXPERT,
            "String",
            "String",
            "String")

        ReflectionTestUtils.setField(taskService, "accountRepository", accountEntityRepository)
    }

    @Test
    fun `createTask should create a task successfully`() {
        val createTaskRequest = CreateTaskRequest(
            name = "Test Task",
            description = "Test Description",
            referenceId = 1L,
            referenceType = ReferenceType.WORKFLOW,
            visibility = TaskVisibility.PUBLIC,
            dueDate = TimeUtil.toEpochMillis(LocalDate.now().plusDays(7)),
            completedDate = null,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            taskAssignee = listOf(TaskAssigneeDto(
                assigneeType = TaskAssigneeType.EXPERT,
                userId = 3,
                userProfile = null,
                companyId = 4
            )),
            taskReminder = listOf(TaskReminderDto(
                dateTime = TimeUtil.toEpochMillis(LocalDate.now().plusDays(5))
            )),
            instruction = "",
            usefulLinks = ""
        )

        every { taskRepository.save(any()) } returns task
        every { taskAssigneeRepository.save(any()) } returns task.assignee?.get(0)
        every { taskReminderRepository.save(any()) } returns task.reminders?.get(0)
        every { taskAssigneeEntity.assigneeId } returns loginAccountEntity

        val taskId = taskService.createTask(createTaskRequest, authenticatedUser)

        assertEquals(1L, taskId)
    }

    @Test
    fun `createTask should throw ApplicationException when referenceId is not found`() {
        val createTaskRequest = CreateTaskRequest(
            name = "Test Task",
            description = "Test Description",
            referenceId = 1L,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = Instant.now().toEpochMilli(),
            completedDate = null,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            taskAssignee = listOf(),
            taskReminder = listOf(),
            instruction = "",
            usefulLinks = ""
        )

        every { caseRepository.findByIdOrNull(999L) } returns null

        every { taskRepository.save(any()) } returns task

        every { caseRepository.findByIdAndPartnerId(any(), any()) } returns Optional.empty()

        assertThrows<ApplicationException> {
            taskService.createTask(createTaskRequest, authenticatedUser)
        }
    }

    @Test
    fun `updateTask should update a task successfully`() {
        val taskId = 1L
        val createTaskRequest = CreateTaskRequest(
            name = "Test Task",
            description = "Test Description",
            referenceId = 1L,
            referenceType = ReferenceType.WORKFLOW,
            visibility = TaskVisibility.PUBLIC,
            dueDate = TimeUtil.toEpochMillis(LocalDate.now().plusDays(7)),
            completedDate = null,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            taskAssignee = listOf(TaskAssigneeDto(
                assigneeType = TaskAssigneeType.EXPERT,
                userId = 3,
                userProfile = null,
                companyId = 4
            )),
            taskReminder = listOf(TaskReminderDto(
                dateTime = TimeUtil.toEpochMillis(LocalDate.now().plusDays(5))
            )),
            instruction = "",
            usefulLinks = ""
        )

        every { taskRepository.getTask(any(), any(), any(), any(), any(), any(), any()) } returns Optional.of(task)
        every { taskRepository.save(any()) } returns task
        every { taskRepository.flush() } returns Unit
        every { taskAssigneeRepository.deleteByTaskId(any()) } returns Unit
        every { taskReminderRepository.deleteByTask(any()) } returns Unit
        every { taskRepository.getReferenceById(any()) } returns task
        every { loginAccountRepository.getReferenceById(any()) } returns loginAccountEntity

        every { taskAssigneeRepository.save(any()) } returns task.assignee?.get(0)
        every { taskReminderRepository.save(any()) } returns task.reminders?.get(0)
        every { taskAssigneeEntity.assigneeId } returns loginAccountEntity
        every { loginAccountEntity.id } returns 2

        val updatedTaskId = taskService.updateTask(taskId, createTaskRequest, authenticatedUser)

        assertEquals(taskId, updatedTaskId)
    }

    @Test
    fun `updateTask should throw ApplicationException when task is not found`() {
        val taskId = 999L
        val createTaskRequest = CreateTaskRequest(
            name = "Test Task",
            description = "Test Description",
            referenceId = 1L,
            referenceType = ReferenceType.WORKFLOW,
            visibility = TaskVisibility.PUBLIC,
            dueDate = Instant.now().toEpochMilli(),
            completedDate = null,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            taskAssignee = listOf(),
            taskReminder = listOf(),
            instruction = "",
            usefulLinks = ""
        )

        every { taskRepository.getTask(any(), any(), any(), any(), any(), any(), any()) } returns Optional.empty()

        assertThrows<ApplicationException> {
            taskService.updateTask(taskId, createTaskRequest, authenticatedUser)
        }
    }

    @Test
    fun `retrieve should return task details successfully`() {
        val taskId = 1L
        val taskEntity = task.copy(id = taskId)

        every { taskRepository.getTask(any(), any(), any(), any(), any(), any(), any()) } returns Optional.of(taskEntity)
        every { userProfileUtil.retrieveProfile(any()) } returns mockk()

        every { loginAccountEntity.id } returns 4

        val taskDetails = taskService.retrieve(taskId, authenticatedUser)

        assertNotNull(taskDetails)
        assertEquals(taskId, taskDetails.id)
        assertEquals(taskEntity.name, taskDetails.name)
        assertEquals(taskEntity.description, taskDetails.description)
    }

    @Test
    fun `retrieve should throw ApplicationException when task is not found`() {
        val taskId = 999L

        every { taskRepository.getTask(any(), any(), any(), any(), any(), any(), any()) } returns Optional.empty()

        assertThrows<ApplicationException> {
            taskService.retrieve(taskId, authenticatedUser)
        }
    }

    @Test
    fun `updateStatus should update task status successfully`() {
        val taskId = 1L
        val taskStatus = TaskStatus.COMPLETED
        val taskEntity = task.copy(id = taskId, referenceType = ReferenceType.CASE)

        every { taskRepository.getTask(any(), any(), any(), any(), any(), any(), any()) } returns Optional.of(taskEntity)
        every { taskRepository.save(any()) } returns taskEntity
        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccountEntity)

        val result = taskService.updateStatus(taskId, taskStatus, authenticatedUser)

        assertTrue(result!!)
        assertEquals(taskStatus, taskEntity.status)
    }

    @Test
    fun `updateStatus should throw ApplicationException when task is not found`() {
        val taskId = 999L
        val taskStatus = TaskStatus.COMPLETED

        every { taskRepository.getTask(any(), any(), any(), any(), any(), any(), any()) } returns Optional.empty()

        assertThrows<ApplicationException> {
            taskService.updateStatus(taskId, taskStatus, authenticatedUser)
        }
    }

    @Test
    fun `listTasks should return paged result of task details`() {
        val taskSearchFilter = TaskSearchFilter(
            name = null,
            createdBy = null,
            type = null,
            referenceId = null,
            status = null,
            currentUser = null,
            visibility = null,
            taskView = null,
            priority = null,
            partnerId = null,
            month = null,
            year = null,
            dueDate = null,
            showBy = null,
            assignedTo = null,
            visibilityString = null,
            companyId = 4,
            companyType = TaskCompanyType.EXPERT,
            fromDate = null,
            toDate = null,
            currentUserPartnerId = null,
            accountUsers = null,
            expertAssignedCases = null
        )
        val pageRequest = mockk<PageRequest>()
        val taskEntityPage = mockk<Page<TaskEntity>>()
        val caseEntity = mockk<CaseEntity>()
        val caseCategory = CaseCategoryEntity(
            id = 1L,
            subCategoryId = "BUSINESS_INCORPORATION",
            subCategoryName = "Business Incorporation",
            parentCategoryId = "START_A_COMPANY",
            parentCategoryName = "Start A Company"
        )

        every { taskEntityPage.toList() } returns listOf(task)
        every { taskEntityPage.totalElements } returns 1
        every { taskEntityPage.totalPages } returns 1
        every { taskEntityPage.number } returns 0
        every { taskEntityPage.size } returns 1
        every { taskEntityPage.content } returns listOf(task)
        every { caseEntity.category } returns caseCategory
        every { caseRepository.findById(any()) } returns Optional.of(caseEntity)
        //every { taskRepository.findAll(any(), any()) } returns taskEntityPage
        every { taskRepository.searchByCriteriaForAdmin(any(), any()) } returns  PageImpl(listOf(task))

        every { loginAccountEntity.id } returns 4

        val pagedResult = taskService.listTasks(taskSearchFilter, pageRequest, authenticatedUser)

        assertNotNull(pagedResult)
        assertEquals(1, pagedResult.totalElements)
    }

    @Test
    fun `listTasksCount should return list of task count responses`() {
        val searchFilter = mockk<TaskSearchFilter>()

        val taskCount: ITaskCountDto= mockk()
        every { taskCount.getDt() } returns Date(545454)
        every { taskCount.getCount() } returns 3
        every { taskCount.getStatus() } returns TaskStatus.COMPLETED.toString()

        val taskCountDtoList = listOf(taskCount)

        val taskCountResponse = TaskCountResponse(
            date = LocalDate.of(1970,1,1,),
            completed = 3,
            inProgress = 0,
            notStarted = 0
        )
        val listTaskCountResponse = listOf(taskCountResponse)

        every { taskRepository.getStatusCount(any()) } returns taskCountDtoList

        val result = taskService.listTasksCount(searchFilter, authenticatedUser)

        assertNotNull(result)
        assertEquals(1, result?.size)
        assertEquals(listTaskCountResponse, result)
    }

    @Test
    fun `listTasksCount should return empty list when no tasks found`() {
        val searchFilter = mockk<TaskSearchFilter>()

        every { taskRepository.getStatusCount(any()) } returns emptyList()

        val result = taskService.listTasksCount(searchFilter, authenticatedUser)

        assertNotNull(result)
        assertTrue(result!!.isEmpty())
    }

    @Test
    fun `getTaskDetails should return list of task details`() {

        val searchFilter = mockk<TaskSearchFilter>()
        val createdBy: UserProfile = mockk()

        val task = TaskEntity(
            id = 1L,
            name = "Test Task",
            description = "Test Description",
            referenceId = 2L,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = LocalDateTime.now(),
            completedDate = null,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            inProgressDate = LocalDateTime.now(),
            companyId = 3,
            caseMilestone = "",
            caseStatus = "",
            progress = "",
            instruction = "",
            usefulLinks = "",
            plannedStartDate = LocalDateTime.now(),
            startDate = LocalDateTime.now(),
            expectedDueDate = LocalDateTime.now(),
            companyType = TaskCompanyType.EXPERT,
            partner = null,
            assignee = mutableListOf(),
            reminders = mutableListOf(),
            taskTemplate = null
        )
        task.createdBy = 2

        val taskDetailsList = listOf(TaskDetails(
            id = 1L, name = "Test Task",
            description = "Test Description",
            createdBy = createdBy,
            referenceType =ReferenceType.CASE,
            referenceId = 2,
            visibility = TaskVisibility.PUBLIC,
            dueDate = 1111,
            overDueDays = 2,
            completedDate = 1122,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            category = listOf(),
            assignee = mutableListOf(),
            createdAt = 1123,
            reminders = listOf(),
            usefulLinks = "",
            instruction = "",
            progress = "",
            caseMilestone = "",
            plannedStartDate = 12345,
            startDate = 23456,
            expectedDueDate = 34567,
            isActive = true
        ))


        every { taskRepository.searchByDueDateCriteria(any()) } returns listOf(task)
        every { caseRepository.findById(any()) } returns null

        val result = taskService.getTaskDetails(searchFilter, authenticatedUser)

        assertNotNull(result)
        assertEquals(taskDetailsList.size, result?.size)
        assertEquals(taskDetailsList.get(0).name, result?.get(0)?.name)
        assertEquals(taskDetailsList.get(0).description, result?.get(0)?.description)
        assertEquals(taskDetailsList.get(0).id, result?.get(0)?.id)
    }

    @Test
    fun `getCompanyIdAndType should return company id and type`() {
        val expectedCompanyId = 0L
        val expectedCompanyType = TaskCompanyType.CG

        val result = taskService.getCompanyIdAndType(authenticatedUser)

        assertNotNull(result)
        assertEquals(expectedCompanyId, result.first)
        assertEquals(expectedCompanyType, result.second)
    }

    @Test
    fun `delete should return true when task is deleted successfully`() {
        val taskId = 1L

        val task = TaskEntity(
            id = 1L,
            name = "Test Task",
            description = "Test Description",
            referenceId = 2L,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = LocalDateTime.now(),
            completedDate = null,
            priority = "",
            status = TaskStatus.NOT_STARTED,
            inProgressDate = LocalDateTime.now(),
            companyId = 3,
            caseMilestone = "",
            caseStatus = "",
            progress = "",
            instruction = "",
            usefulLinks = "",
            plannedStartDate = LocalDateTime.now(),
            startDate = LocalDateTime.now(),
            expectedDueDate = LocalDateTime.now(),
            companyType = TaskCompanyType.EXPERT,
            partner = null,
            assignee = mutableListOf(),
            reminders = mutableListOf(),
            taskTemplate = null
        )
        task.createdBy=1

        every { taskRepository.getTask(any(), any(), any(),any(),
            any(), any(), any()) }  returns Optional.of(task)

        every { taskRepository.deleteById(any()) } returns Unit

        val result = taskService.delete(taskId, authenticatedUser)

        assertNotNull(result)
        assertTrue(result!!)
    }

    @Test
    fun `delete should throw exception when task is not found`() {
        val taskId = 999L

        every { taskRepository.getTask(any(), any(), any(),any(),
            any(), any(), any()) }  returns Optional.empty()

        every { taskRepository.deleteById(any()) } returns Unit

        assertThrows<ApplicationException> {
            taskService.delete(taskId, authenticatedUser)
        }

    }

    @Test
    fun `listPendingTasks should return list of pending tasks`() {

        val userId: Long? = 1L
        val accountId: Long? = 2L
        val referenceId: Long? = null
        val pendingTasks = listOf(TaskDetails(
            id = 1L, name = "Test Task",
            description = "Test Description",
            createdBy = mockk(),
            referenceType =ReferenceType.CASE,
            referenceId = 2,
            visibility = TaskVisibility.PUBLIC,
            dueDate = 1111,
            overDueDays = 2,
            completedDate = 1122,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            category = listOf(),
            assignee = mutableListOf(),
            createdAt = 1123,
            reminders = listOf(),
            usefulLinks = "",
            instruction = "",
            progress = "",
            caseMilestone = "",
            plannedStartDate = 12345,
            startDate = 23456,
            expectedDueDate = 34567,
            isActive = true
        ))

        val task = TaskEntity(
            id = 1L,
            name = "Test Task",
            description = "Test Description",
            referenceId = 2L,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = LocalDateTime.now(),
            completedDate = null,
            priority = "",
            status = TaskStatus.NOT_STARTED,
            inProgressDate = LocalDateTime.now(),
            companyId = 3,
            caseMilestone = "",
            caseStatus = "",
            progress = "",
            instruction = "",
            usefulLinks = "",
            plannedStartDate = LocalDateTime.now(),
            startDate = LocalDateTime.now(),
            expectedDueDate = LocalDateTime.now(),
            companyType = TaskCompanyType.EXPERT,
            partner = null,
            assignee = mutableListOf(),
            reminders = mutableListOf(),
            taskTemplate = null
        )

        every { authenticatedUser.companyId } returns 3
        every { corporateRepository.getReferenceById(any()) } returns mockk()
        every { accountEntityRepository.findByIdAndCorporate(any(), any())} returns null

        every { taskRepository.searchByCriteriaForAdmin(any(), any()) } returns PageImpl(listOf(task))

        every { caseRepository.findById(any()) } returns null

        val result = taskService.listPendingTasks(authenticatedUser, userId, accountId, referenceId)

        assertNotNull(result)
        assertEquals(pendingTasks.size, result.size)
        assertEquals(pendingTasks.get(0).name, result.get(0).name)
        assertEquals(pendingTasks.get(0).description, result.get(0).description)
        assertEquals(pendingTasks.get(0).referenceId, result.get(0).referenceId)
    }

    @Test
    fun `getTaskDetails should return list of task details with isActive flag for workflow tasks`() {

        val searchFilter = mockk<TaskSearchFilter>()
        val createdBy: UserProfile = mockk()

        val taskTemplate = mockk<TaskTemplateEntity>()
        val taskWorkflowEntity = mockk<TaskWorkflowEntity>()

        val task = TaskEntity(
            id = 1L,
            name = "Test Task",
            description = "Test Description",
            referenceId = 2L,
            referenceType = ReferenceType.CASE,
            visibility = TaskVisibility.PUBLIC,
            dueDate = LocalDateTime.now(),
            completedDate = null,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            inProgressDate = LocalDateTime.now(),
            companyId = 3,
            caseMilestone = "",
            caseStatus = "",
            progress = "",
            instruction = "",
            usefulLinks = "",
            plannedStartDate = LocalDateTime.now(),
            startDate = LocalDateTime.now(),
            expectedDueDate = LocalDateTime.now(),
            companyType = TaskCompanyType.EXPERT,
            partner = null,
            assignee = mutableListOf(),
            reminders = mutableListOf(),
            taskTemplate = taskTemplate
        )
        task.createdBy = 2

        val taskDetailsList = listOf(TaskDetails(
            id = 1L, name = "Test Task",
            description = "Test Description",
            createdBy = createdBy,
            referenceType =ReferenceType.CASE,
            referenceId = 2,
            visibility = TaskVisibility.PUBLIC,
            dueDate = 1111,
            overDueDays = 2,
            completedDate = 1122,
            priority = "",
            status = TaskStatus.IN_PROGRESS,
            category = listOf(),
            assignee = mutableListOf(),
            createdAt = 1123,
            reminders = listOf(),
            usefulLinks = "",
            instruction = "",
            progress = "",
            caseMilestone = "",
            plannedStartDate = 12345,
            startDate = 23456,
            expectedDueDate = 34567,
            isActive = true
        ))


        every { taskWorkflowEntity.taskTemplates} returns mutableListOf(taskTemplate)
        every { taskTemplate.task } returns task
        every { taskTemplate.workflow } returns taskWorkflowEntity
        every { taskTemplate.displayOrder } returns 1
        every { taskRepository.searchByDueDateCriteria(any()) } returns listOf(task)
        every { caseRepository.findById(any()) } returns Optional.empty()

        val result = taskService.getTaskDetails(searchFilter, authenticatedUser)

        assertNotNull(result)
        assertEquals(taskDetailsList.size, result?.size)
        assertEquals(taskDetailsList.get(0).name, result?.get(0)?.name)
        assertEquals(taskDetailsList.get(0).description, result?.get(0)?.description)
        assertEquals(taskDetailsList.get(0).id, result?.get(0)?.id)
        assertEquals(taskDetailsList.get(0).isActive, result?.get(0)?.isActive)
    }

}
