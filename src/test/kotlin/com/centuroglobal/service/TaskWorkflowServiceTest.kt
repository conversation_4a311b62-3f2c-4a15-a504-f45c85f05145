package com.centuroglobal.service

import com.centuroglobal.service.task.TaskWorkflowService
import com.centuroglobal.shared.client.PythonApiClient
import com.centuroglobal.shared.data.entity.*
import com.centuroglobal.shared.data.entity.task.TaskTemplateEntity
import com.centuroglobal.shared.data.entity.task.TaskWorkflowEntity
import com.centuroglobal.shared.data.enums.*
import com.centuroglobal.shared.data.enums.task.TaskStatus
import com.centuroglobal.shared.data.pojo.UserAccess
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.repository.task.TaskTemplateRepository
import com.centuroglobal.shared.repository.task.TaskWorkflowRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.UserProfileUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TaskWorkflowServiceTest {

    private val taskWorkflowRepository: TaskWorkflowRepository = mockk()
    private val taskTemplateRepository: TaskTemplateRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val corporateUserRepository: CorporateUserRepository = mockk()
    private val taskAssigneeRepository: TaskAssigneeRepository = mockk()
    private val caseRepository: CaseRepository = mockk()
    private val taskRepository: TaskRepository = mockk()
    private val partnerRepository: PartnerRepository = mockk()
    private val pyApiClient: PythonApiClient = mockk()
    private val userProfileUtil: UserProfileUtil = mockk()
    private val caseMilestonesRepository: CaseMilestonesRepository = mockk()
    private val caseStatusHistoryRepository: CaseStatusHistoryRepository = mockk()

    private lateinit var taskWorkflowService: TaskWorkflowService

    private val corporateEntity = CorporateEntity(
        1,
        "String",
        "US",
        CorporateStatus.ACTIVE,
        true,
        null,
        1L,
        listOf(),
        listOf(),
        1L)
    private val accountEntity = AccountEntity(
        corporateEntity.id,
        "accountEntity",
        AccountStatus.PENDING_VERIFICATION,
        null,
        null,
        corporateEntity
    )
    private val bandsEntity = BandsEntity(
        corporateEntity.id,
        "bands1",
        null,
        BandStatus.ACTIVE,
        null,
        null,
        null,
        null
    )
    private val corporateUserEntity = CorporateUserEntity(
        corporateEntity,
        setOf(accountEntity),
        "jobTitle",
        false,
        listOf(),
        bandsEntity,
        mutableListOf(
            NotificationPreferencesEntity(
            1L, NotificationType.CASE_GCHAT_EMAIL, true,
            corporateUser = CorporateUserEntity()
        )
        ),
        null)

    private val authenticatedUser: AuthenticatedUser = mockk()

    @BeforeEach
    fun setup() {

        taskWorkflowService = TaskWorkflowService(
            taskWorkflowRepository,
            taskTemplateRepository,
            loginAccountRepository,
            corporateUserRepository,
            taskAssigneeRepository,
            caseRepository,
            taskRepository,
            partnerRepository,
            pyApiClient,
            userProfileUtil,
            caseMilestonesRepository,
            caseStatusHistoryRepository
        )


        corporateUserEntity.id = 1L

        // Setup common authenticatedUser mocks
        every { authenticatedUser.userId } returns 1L
        every { authenticatedUser.companyId } returns 1L
        every { authenticatedUser.email } returns "<EMAIL>"
        every { authenticatedUser.userType } returns UserType.CORPORATE.name
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.visibilities } returns listOf(UserAccess("USER", mutableListOf("FULL")))
    }

    @Test
    fun deleteWorkflowTasks() {
        // Arrange
        val referenceId = 1L
        val referenceType = "CASE"
        val statuses = listOf(TaskStatus.NOT_STARTED, TaskStatus.IN_PROGRESS)

        val task1 = mockk<TaskEntity>()
        val task2 = mockk<TaskEntity>()
        val task3 = mockk<TaskEntity>()

        every { task1.status } returns TaskStatus.NOT_STARTED
        every { task2.status } returns TaskStatus.IN_PROGRESS
        every { task3.status } returns TaskStatus.COMPLETED

        val workflow = mockk<TaskWorkflowEntity>()

        every { workflow.taskTemplates } returns mutableListOf(
            mockk<TaskTemplateEntity> {
                every { task } returns task1
            },
            mockk<TaskTemplateEntity> {
                every { task } returns task2
            },
            mockk<TaskTemplateEntity> {
                every { task } returns task3
            }
        )

        every { taskWorkflowRepository.findByReferenceIdAndReferenceType(referenceId, referenceType) } returns workflow
        every { taskRepository.deleteAll(any()) } returns Unit

        // Act
        taskWorkflowService.deleteWorkflowTasks(referenceId, referenceType, statuses)

        // Assert
        verify(exactly = 1) { taskRepository.deleteAll(listOf(task1, task2)) }
        verify(exactly = 0) { taskRepository.deleteAll(listOf(task3)) }
    }

    @Test
    fun `populateMilestone should create milestone when prev and current milestones are same and isDelete is true`() {
        // Arrange
        val caseId = 1L
        val isDelete = true
        val sameMilestone = "MILESTONE_1"
        val differentMilestone = "MILESTONE_2"

        val prevTask = mockk<TaskEntity>()
        val currentTask = mockk<TaskEntity>()
        val nextTask = mockk<TaskEntity>()

        every { prevTask.caseMilestone } returns sameMilestone
        every { currentTask.caseMilestone } returns sameMilestone
        every { nextTask.caseMilestone } returns differentMilestone

        val caseEntity = mockk<CaseEntity>()
        every { caseRepository.getReferenceById(caseId) } returns caseEntity
        every { caseMilestonesRepository.save(any()) } returns mockk()

        val tasks = listOf(prevTask, currentTask, nextTask)

        // Act
        taskWorkflowService.populateMilestone(tasks, caseId, isDelete)

        // Assert
        verify(exactly = 1) { caseMilestonesRepository.save(any()) }
        verify(exactly = 1) { caseRepository.getReferenceById(caseId) }
    }

    @Test
    fun `populateMilestone should delete milestone when prev and current milestones are same and isDelete is false`() {
        // Arrange
        val caseId = 1L
        val isDelete = false
        val sameMilestone = "MILESTONE_1"
        val differentMilestone = "MILESTONE_2"

        val prevTask = mockk<TaskEntity>()
        val currentTask = mockk<TaskEntity>()
        val nextTask = mockk<TaskEntity>()

        every { prevTask.caseMilestone } returns sameMilestone
        every { currentTask.caseMilestone } returns sameMilestone
        every { nextTask.caseMilestone } returns differentMilestone

        val caseEntity = mockk<CaseEntity>()
        val milestone = mockk<com.centuroglobal.shared.data.entity.case.CaseMilestonesEntity>()

        every { caseRepository.getReferenceById(caseId) } returns caseEntity
        every { caseMilestonesRepository.findTopByCaseAndMilestoneKeyOrderByCreatedDateDesc(caseEntity, sameMilestone) } returns milestone
        every { caseMilestonesRepository.delete(milestone) } returns Unit

        val tasks = listOf(prevTask, currentTask, nextTask)

        // Act
        taskWorkflowService.populateMilestone(tasks, caseId, isDelete)

        // Assert
        verify(exactly = 1) { caseMilestonesRepository.delete(milestone) }
        verify(exactly = 1) { caseRepository.getReferenceById(caseId) }
    }
}
