package com.centuroglobal.service


import com.centuroglobal.shared.data.entity.ExpertUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.MasterContentEntity
import com.centuroglobal.shared.data.entity.ValidationTokenEntity
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.data.enums.UserType
import com.centuroglobal.shared.data.enums.ValidationState
import com.centuroglobal.shared.data.enums.ValidationType
import com.centuroglobal.shared.data.pojo.TokenResult
import com.centuroglobal.shared.data.pojo.VerifiedUser
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.*
import com.centuroglobal.shared.security.JwtHelper
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Value
import java.time.LocalDateTime
import java.util.*

class TokenVerificationServiceTest {

    private val validationTokenRepository: ValidationTokenRepository = mockk()
    private val loginAccountRepository: LoginAccountRepository = mockk()
    private val masterContentRepository: MasterContentRepository = mockk()
    private val adminAuthoritiesRepository: AdminAuthoritiesRepository = mockk()
    private val jwtHelper: JwtHelper = mockk()
    private val authService: AuthService = mockk()
    private val corporateUserRepository: CorporateUserRepository = mockk()
    private val expertUserRepository: ExpertUserRepository = mockk()
    private val verifiedUser: VerifiedUser = mockk()

    private lateinit var tokenVerificationService: TokenVerificationService

    @Value("\${app.web-url}")
    private val webUrl: String = "http://localhost"

    @BeforeEach
    fun setup() {
        tokenVerificationService = TokenVerificationService(
            webUrl,
            validationTokenRepository,
            loginAccountRepository,
            masterContentRepository,
            adminAuthoritiesRepository,
            jwtHelper,
            authService,
            corporateUserRepository,
            expertUserRepository
        )
    }

    @Test
    fun `createToken should save a new token`() {
        val loginAccount = mockk<LoginAccountEntity> {
            every { id } returns 1L
        }
        val validationToken = mockk<ValidationTokenEntity>()

        every { validationTokenRepository.save(any()) } returns validationToken

        val result = tokenVerificationService.createToken(loginAccount, ValidationType.INVITE_EXPERT)

        assertNotNull(result)
        verify { validationTokenRepository.save(any()) }
    }

    @Test
    fun `validateCode should return VerifiedUser when code is valid`() {

        val loginAccount  = ExpertUserEntity(
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string")
        val verifiedUser = VerifiedUser(
            email = "<EMAIL>",
            firstName = "",
            lastName = "",
            userType = UserType.EXPERT,
            jobTitle ="",
            metadata = mapOf(),
            termsAndCondition = "",
            contractDetails = null
        )

        every { tokenVerificationService.getUserFromToken("validCode") } returns loginAccount

        every { loginAccountRepository.findById(any()) } returns Optional.of(loginAccount)

        every { validationTokenRepository.findByCodeAndState(any(), any()) } returns ValidationTokenEntity(
            id = 1,
            userId = 1,
            type = ValidationType.INVITE_EXPERT,
            code = "",
            state = ValidationState.VERIFIED,
            expiryDate = LocalDateTime.now().plusDays(9)
        )

        val mockContentList = mutableListOf<MasterContentEntity>()

        every { masterContentRepository.findAll() } returns mockContentList

        mockkObject(VerifiedUser.ModelMapper)
        every {
            VerifiedUser.ModelMapper.fromUserEntity(loginAccount, mockContentList)
        } returns verifiedUser

        val result = tokenVerificationService.validateCode("validCode")

        assertEquals(verifiedUser, result)

        unmockkObject(VerifiedUser.ModelMapper)
    }

    @Test
    fun `getUserFromToken should throw exception when token is invalid`() {
        val code = "invalidCode"

        every { validationTokenRepository.findByCodeAndState(any(), any()) } returns ValidationTokenEntity(
            id = 1,
            userId = 1,
            type = ValidationType.INVITE_EXPERT,
            code = "",
            state = ValidationState.VERIFIED,
            expiryDate = LocalDateTime.now().minusDays(9)
        )

        assertThrows<ApplicationException> {
            tokenVerificationService.getUserFromToken(code)
        }
    }

    @Test
    fun `generateLoginToken should return TokenResult`() {

        var loginAccount = ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        )
        loginAccount.firstName = "John"
        loginAccount.lastName = "Doe"
        loginAccount.userRoles= mutableListOf()
        loginAccount.role = Role.ROLE_EXPERT


        val tokenResult = mockk<TokenResult>()

        every { expertUserRepository.findById(any()) } returns Optional.of(ExpertUserEntity(
            id = 1L,
            contactEmail = "<EMAIL>",
            contactNumber = "9123",
            contactWebsite = "1@test",
            displayName = "test",
            expertType = "ACTIVE",
            jobTitle = "string"
        ))

        every { loginAccountRepository.saveAndFlush(any()) } returns mockk()

        every { adminAuthoritiesRepository.findAllByUserId(any()) } returns mutableListOf()

        every { authService.generateTokenResult(any(), any(), any(), any(), any(), any(), any()) } returns tokenResult

        every { jwtHelper.generateClaims(any(),any(),any(),any(),any(),any()) } returns mutableMapOf()

        val result = tokenVerificationService.generateLoginToken(loginAccount)

        assertNotNull(result)
        assertEquals(tokenResult, result)
    }

    @Test
    fun `invalidateUnusedToken should set tokens to INVALID state`() {
        val userId = 1L
        val validationTokens = listOf(mockk<ValidationTokenEntity> {
            every { state = ValidationState.INVALID } just runs
        })

        every { validationTokenRepository.findAllByUserIdAndStateInAndType(userId, any(), any()) } returns validationTokens

        tokenVerificationService.invalidateUnusedToken(userId, ValidationType.INVITE_EXPERT)

        verify { validationTokens.forEach { it.state = ValidationState.INVALID } }
    }

    @Test
    fun `getValidTokenEntity should throw exception when token is expired`() {
        val code = "expiredCode"
        val validationToken = mockk<ValidationTokenEntity> {
            every { expiryDate } returns LocalDateTime.now().minusDays(1)
        }

        every { validationTokenRepository.findByCodeAndState(code, ValidationState.EMAIL_SENT) } returns validationToken

        assertThrows<ApplicationException> {
            tokenVerificationService.getValidTokenEntity(code)
        }
    }
}
