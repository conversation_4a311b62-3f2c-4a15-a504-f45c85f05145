package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.repository.findByIdOrNull

class TourServiceTest {

    private lateinit var tourService: TourService
    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var authenticatedUser: AuthenticatedUser

    @BeforeEach
    fun setup() {
        loginAccountRepository = mockk()
        authenticatedUser = mockk()
        tourService = TourService(loginAccountRepository)
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `getTour should return tour status when user exists`() {
        val userId = 1L
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true) {
            every { tour } returns true
        }

        every { authenticatedUser.userId } returns userId
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccount

        val result = tourService.getTour(authenticatedUser)

        assertTrue(result)
        verify { loginAccountRepository.findByIdOrNull(userId) }
    }

    @Test
    fun `getTour should throw ApplicationException when user not found`() {
        val userId = 1L

        every { authenticatedUser.userId } returns userId
        every { loginAccountRepository.findByIdOrNull(userId) } returns null

        val exception = assertThrows<ApplicationException> {
            tourService.getTour(authenticatedUser)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `updateTour should update and return tour status when user exists`() {
        val userId = 1L
        val tourStatus = true
        val loginAccount = mockk<LoginAccountEntity>(relaxed = true)

        every { authenticatedUser.userId } returns userId
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccount
        every { loginAccountRepository.save(loginAccount) } returns loginAccount

        val result = tourService.updateTour(authenticatedUser, tourStatus)

        assertEquals(tourStatus, result)
        verify { loginAccountRepository.save(loginAccount) }
    }

    @Test
    fun `updateTour should throw ApplicationException when user not found`() {
        val userId = 1L
        val tourStatus = true

        every { authenticatedUser.userId } returns userId
        every { loginAccountRepository.findByIdOrNull(userId) } returns null

        val exception = assertThrows<ApplicationException> {
            tourService.updateTour(authenticatedUser, tourStatus)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }
}
