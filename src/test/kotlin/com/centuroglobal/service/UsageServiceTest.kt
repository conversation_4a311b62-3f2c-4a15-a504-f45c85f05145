package com.centuroglobal.service

import com.centuroglobal.shared.data.ErrorCode
import com.centuroglobal.shared.data.entity.AIChatEntity
import com.centuroglobal.shared.data.entity.FeatureSessionEntity
import com.centuroglobal.shared.data.entity.dto.usage.AIChatDto
import com.centuroglobal.shared.data.entity.dto.usage.ClientDocDto
import com.centuroglobal.shared.data.entity.dto.usage.CostOfLivingDto
import com.centuroglobal.shared.data.entity.subscription.usage.CostOfLivingUsageEntity
import com.centuroglobal.shared.data.pojo.CorporateUserProfile
import com.centuroglobal.shared.data.pojo.UsageLogSearchFilter
import com.centuroglobal.shared.data.pojo.usage.AIChatUsageLogResponse
import com.centuroglobal.shared.data.pojo.usage.AIChatUsageLogSearchFilter
import com.centuroglobal.shared.data.pojo.usage.DocRepoUsageLogResponse
import com.centuroglobal.shared.data.pojo.usage.DocRepoUsageLogSearchFilter
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.AIChatRepository
import com.centuroglobal.shared.repository.ClientDocRepository
import com.centuroglobal.shared.repository.FeatureSessionRepository
import com.centuroglobal.shared.repository.subscription.usage.CostOfLivingUsageRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import com.centuroglobal.util.UserProfileUtil
import io.mockk.Called
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import java.time.LocalDateTime
import java.util.*

class UsageServiceTest {
    private lateinit var usageService: UsageService
    private lateinit var featureSessionRepository: FeatureSessionRepository
    private lateinit var userProfileUtil: UserProfileUtil
    private lateinit var aiChatRepository: AIChatRepository
    private lateinit var clientDocRepository: ClientDocRepository
    private lateinit var costOfLivingUsageRepository: CostOfLivingUsageRepository
    private lateinit var authenticatedUser: AuthenticatedUser
    private lateinit var userActionService: UserActionService

    @BeforeEach
    fun setup() {
        featureSessionRepository = mockk()
        userProfileUtil = mockk()
        aiChatRepository = mockk()
        clientDocRepository = mockk()
        costOfLivingUsageRepository = mockk()
        authenticatedUser = mockk()
        userActionService = mockk()

        usageService = UsageService(
            featureSessionRepository,
            userProfileUtil,
            aiChatRepository,
            clientDocRepository,
            costOfLivingUsageRepository,
            userActionService
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Test
    fun `closeSession should return true when session exists and endTime is null`() {
        // Arrange
        val sessionId = "test-session"
        val userId = 1L
        val sessionEntity = FeatureSessionEntity(
            referenceId = "1",
            startTime = LocalDateTime.now(),
            sessionId = sessionId,
            referenceType = "PLAYBOOK"
        )
        every { authenticatedUser.userId } returns userId
        every { featureSessionRepository.findBySessionIdAndCreatedBy(sessionId, userId) } returns sessionEntity
        every { featureSessionRepository.save(any()) } returns sessionEntity

        // Act
        val result = usageService.closeSession(sessionId, authenticatedUser)

        // Assert
        assertTrue(result)
        assertNotNull(sessionEntity.endTime)
        verify { featureSessionRepository.save(sessionEntity) }
    }

    @Test
    fun `closeSession should return false when session exists but already ended`() {
        // Arrange
        val sessionId = "test-session"
        val userId = 1L
        val sessionEntity = FeatureSessionEntity(
            referenceId = "1",
            startTime = LocalDateTime.now(),
            sessionId = sessionId,
            referenceType = "PLAYBOOK",
            endTime = LocalDateTime.now()
        )

        every { authenticatedUser.userId } returns userId
        every { featureSessionRepository.findBySessionIdAndCreatedBy(sessionId, userId) } returns sessionEntity

        // Act
        val result = usageService.closeSession(sessionId, authenticatedUser)

        // Assert
        assertFalse(result)
        verify(exactly = 0) { featureSessionRepository.save(any()) }
    }

    @Test
    fun `closeSession should throw ApplicationException when session not found`() {
        // Arrange
        val sessionId = "test-session"
        val userId = 1L

        every { authenticatedUser.userId } returns userId
        every { featureSessionRepository.findBySessionIdAndCreatedBy(sessionId, userId) } returns null

        // Act & Assert
        val exception = assertThrows<ApplicationException> {
            usageService.closeSession(sessionId, authenticatedUser)
        }
        assertEquals(ErrorCode.NOT_FOUND, exception.error)
    }

    @Test
    fun `closeSessionByType should close COST_OF_LIVING session successfully`() {
        // Arrange
        val feature = "COST_OF_LIVING"
        val userId = 1L

        every { authenticatedUser.userId } returns userId
        every { userActionService.create(any()) } returns mockk(relaxed = true)

        // Act
        val result = usageService.closeSessionByType(feature, UUID.randomUUID().toString(), authenticatedUser)

        // Assert
        assertTrue(result!!)
        verify {
            userActionService.create(any())
        }
    }

    @Test
    fun `aiChat should return paged result of chat usage logs`() {
        // Arrange
        val filter = AIChatUsageLogSearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        val userId = 1L
        val chatEntity = mockk<AIChatEntity>()
        val page = mockk<Page<AIChatEntity>>()
        val pageOfAIChatDto = PageImpl(listOf<AIChatDto>())

        every { page.map<AIChatEntity>(any()) } returns page
        every { page.toList() } returns listOf(chatEntity)
        every { aiChatRepository.searchByCriteria(filter, pageRequest) } returns pageOfAIChatDto
        every { chatEntity.createdBy } returns userId

        // Act
        val result = usageService.aiChat(filter, pageRequest)

        // Assert
        verify {
            aiChatRepository.searchByCriteria(filter, pageRequest)
        }
        assertNotNull(result)
    }

    @Test
    fun `aiChat should handle empty result`() {
        // Arrange
        val filter = AIChatUsageLogSearchFilter()
        val pageRequest = PageRequest.of(0, 10)
        val page = mockk<Page<AIChatUsageLogResponse>>()
        var pageOfAIChatDto = PageImpl(emptyList<AIChatDto>())
        val userId = 1L

        every { page.map<AIChatUsageLogResponse>(any()) } returns page
        every { page.toList() } returns emptyList()
        every { aiChatRepository.searchByCriteria(filter, pageRequest) } returns pageOfAIChatDto

        val corporateProfile = mockk<CorporateUserProfile>()
        val chatDto = mockk<AIChatDto>()
        val chatResponse = mockk<AIChatUsageLogResponse>()

        every { corporateProfile.countryCode } returns "US"

        every { chatDto.createdBy } returns userId
        every { chatDto.createdDate } returns LocalDateTime.now()
        every { chatDto.question } returns "test question"
        every { chatDto.partnerName } returns "test partner"

        pageOfAIChatDto = PageImpl(listOf(chatDto))

        every { aiChatRepository.searchByCriteria(filter, pageRequest) } returns pageOfAIChatDto
        every { page.map<AIChatUsageLogResponse>(any()) } returns page
        every { page.toList() } returns listOf(chatResponse)
        every { userProfileUtil.retrieveCorporateProfile(userId) } returns corporateProfile

        // Act
        val result = usageService.aiChat(filter, pageRequest)

        // Assert
        verify { aiChatRepository.searchByCriteria(filter, pageRequest) }
        assertNotNull(result)
    }

    @Test
    fun `aiChat should handle repository exception`() {
        // Arrange
        val filter = AIChatUsageLogSearchFilter()
        val pageRequest = PageRequest.of(0, 10)

        every { aiChatRepository.searchByCriteria(filter, pageRequest) } throws RuntimeException("Database error")

        // Act & Assert
        assertThrows<RuntimeException> {
            usageService.aiChat(filter, pageRequest)
        }

        verify { aiChatRepository.searchByCriteria(filter, pageRequest) }
    }

    @Test
    fun `docRepo should return paged result of document repository usage logs`() {
        // Arrange
        val filter = DocRepoUsageLogSearchFilter(null, null, null)
        val pageRequest = PageRequest.of(0, 10)
        val docRepoDto = object : ClientDocDto {
            override val id: Long = 1L
            override val docType: String = "PASSPORT"
            override val docName: String = "passport.pdf"
            override val size: Long = 1024L
            override val corporateName: String = "Test Corp"
            override val country: String = "US"
            override val createdBy: String = "<EMAIL>"
            override val createdDate: LocalDateTime = LocalDateTime.now()
            override val expiryDate: LocalDateTime? = LocalDateTime.now().plusYears(1)
            override val partnerName: String = "Test Partner"
        }

        // Create PageImpl with a single dto
        val pageContent = listOf<ClientDocDto>(docRepoDto)
        val page = PageImpl(pageContent, pageRequest, pageContent.size.toLong())

        every { clientDocRepository.searchLogsByCriteria(filter, pageRequest) } returns page

        // Act
        val result = usageService.docRepo(filter, pageRequest)

        // Assert
        verify {
            clientDocRepository.searchLogsByCriteria(filter, pageRequest)
            DocRepoUsageLogResponse.ModelMapper.from(docRepoDto)
        }
        assertNotNull(result)
        assertEquals(1, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(1, result.totalPages)
    }

    @Test
    fun `docRepo should handle empty result`() {
        // Arrange
        val filter = DocRepoUsageLogSearchFilter(null, null, null)
        val pageRequest = PageRequest.of(0, 10)

        // Create empty PageImpl
        val page = PageImpl(emptyList<ClientDocDto>(), pageRequest, 0)

        every { clientDocRepository.searchLogsByCriteria(filter, pageRequest) } returns page

        // Act
        val result = usageService.docRepo(filter, pageRequest)

        // Assert
        verify { clientDocRepository.searchLogsByCriteria(filter, pageRequest) }
        assertNotNull(result)
        assertEquals(0, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(0, result.totalPages)
        assertTrue(result.rows.isEmpty())
    }

    @Test
    fun `docRepo should handle multiple items in page`() {
        // Arrange
        val filter = DocRepoUsageLogSearchFilter(null, null, null)
        val pageRequest = PageRequest.of(0, 10)

        val docRepoDto1 = object : ClientDocDto {
            override val id: Long = 1L
            override val docType: String = "PASSPORT"
            override val docName: String = "passport.pdf"
            override val size: Long = 1024L
            override val corporateName: String = "Test Corp"
            override val country: String = "US"
            override val createdBy: String = "<EMAIL>"
            override val createdDate: LocalDateTime = LocalDateTime.now()
            override val expiryDate: LocalDateTime? = LocalDateTime.now().plusYears(1)
            override val partnerName: String = "Test Partner"
        }

        val docRepoDto2 = object : ClientDocDto {
            override val id: Long = 2L
            override val docType: String = "VISA"
            override val docName: String = "visa.pdf"
            override val size: Long = 2048L
            override val corporateName: String = "Another Corp"
            override val country: String = "UK"
            override val createdBy: String = "<EMAIL>"
            override val createdDate: LocalDateTime = LocalDateTime.now()
            override val expiryDate: LocalDateTime? = LocalDateTime.now().plusMonths(6)
            override val partnerName: String = "Another Partner"
        }


        // Create PageImpl with multiple dtos
        val pageContent = listOf(docRepoDto1, docRepoDto2)
        val page = PageImpl(pageContent, pageRequest, pageContent.size.toLong())

        every { clientDocRepository.searchLogsByCriteria(filter, pageRequest) } returns page

        // Act
        val result = usageService.docRepo(filter, pageRequest)

        // Assert
        verify {
            clientDocRepository.searchLogsByCriteria(filter, pageRequest)
            DocRepoUsageLogResponse.ModelMapper.from(docRepoDto1)
            DocRepoUsageLogResponse.ModelMapper.from(docRepoDto2)
        }
        assertNotNull(result)
        assertEquals(2, result.totalElements)
        assertEquals(0, result.currentPage)
        assertEquals(1, result.totalPages)
        assertEquals(2, result.rows.size)
    }

    @Test
    fun `docRepo should handle repository exception`() {
        // Arrange
        val filter = DocRepoUsageLogSearchFilter(null, null, null)
        val pageRequest = PageRequest.of(0, 10)

        every {
            clientDocRepository.searchLogsByCriteria(filter, pageRequest)
        } throws RuntimeException("Database error")

        // Act & Assert
        assertThrows<RuntimeException> {
            usageService.docRepo(filter, pageRequest)
        }

        verify { clientDocRepository.searchLogsByCriteria(filter, pageRequest) }
    }

    @Test
    fun `costOfLiving should return paged results when data exists`() {
        // Arrange
        val filter = UsageLogSearchFilter(
            from = LocalDateTime.now().minusDays(7),
            to = LocalDateTime.now(),
            country = "US",
            corporate = 1L,
            partnerId = 1L,
            isPartner = true
        )
        val pageRequest = PageRequest.of(0, 10)

        val costOfLivingDto = mockk<CostOfLivingDto> {
            every { id } returns 1L
            every { corporateName } returns "Test Corp"
            every { bandName } returns "Senior"
            every { country } returns "US"
            every { createdBy } returns "John Doe"
            every { createdDate } returns LocalDateTime.now()
            every { endTimestamp } returns LocalDateTime.now()
            every { partnerName } returns "Partner Corp"
        }

        val page = PageImpl(listOf(costOfLivingDto))

        every { costOfLivingUsageRepository.searchByCriteria(filter, pageRequest) } returns page

        // Act
        val result = usageService.costOfLiving(filter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(1, result.totalElements)
        assertEquals(1, result.rows.size)

        with(result.rows[0]) {
            assertEquals("Test Corp", corporateName)
            assertEquals("Senior", costOfLivingDto.bandName)
            assertEquals("US", country)
            assertEquals("John Doe", costOfLivingDto.createdBy)
            assertEquals("Partner Corp", partnerName)
        }

        verify(exactly = 1) { costOfLivingUsageRepository.searchByCriteria(filter, pageRequest) }
    }

    @Test
    fun `costOfLiving should return empty page when no data exists`() {
        // Arrange
        val filter = UsageLogSearchFilter(
            country = "NONEXISTENT",
            isPartner = false
        )
        val pageRequest = PageRequest.of(0, 10)

        val emptyPage = PageImpl(emptyList<CostOfLivingDto>())
        every { costOfLivingUsageRepository.searchByCriteria(filter, pageRequest) } returns emptyPage

        // Act
        val result = usageService.costOfLiving(filter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(0, result.totalElements)
        assertTrue(result.rows.isEmpty())

        verify(exactly = 1) { costOfLivingUsageRepository.searchByCriteria(filter, pageRequest) }
    }

    @Test
    fun `costOfLiving should handle pagination correctly`() {
        // Arrange
        val filter = UsageLogSearchFilter(isPartner = true)
        val pageSize = 2
        val pageRequest = PageRequest.of(1, pageSize)

        val dtos = (1..2).map { index ->
            mockk<CostOfLivingDto> {
                every { id } returns index.toLong()
                every { corporateName } returns "Corp $index"
                every { bandName } returns "Band $index"
                every { country } returns "Country $index"
                every { createdBy } returns "User $index"
                every { createdDate } returns LocalDateTime.now()
                every { endTimestamp } returns LocalDateTime.now()
                every { partnerName } returns "Partner $index"
            }
        }

        val page = PageImpl(dtos, pageRequest, 5)
        every { costOfLivingUsageRepository.searchByCriteria(filter, pageRequest) } returns page

        // Act
        val result = usageService.costOfLiving(filter, pageRequest)

        // Assert
        assertNotNull(result)
        assertEquals(5, result.totalElements)
        assertEquals(2, result.rows.size)
        assertEquals(1, result.currentPage)

        verify(exactly = 1) { costOfLivingUsageRepository.searchByCriteria(filter, pageRequest) }
    }
}
