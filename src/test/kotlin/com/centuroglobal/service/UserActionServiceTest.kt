package com.centuroglobal.service

import com.centuroglobal.data.payload.UserActionRequest
import com.centuroglobal.shared.data.entity.UserActionEntity
import com.centuroglobal.shared.data.pojo.UserActionResult
import com.centuroglobal.shared.repository.UserActionRepository
import com.centuroglobal.shared.util.TimeUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class UserActionServiceTest {

    private lateinit var userActionRepository: UserActionRepository
    private lateinit var userActionService: UserActionService

    @BeforeEach
    fun setUp() {
        userActionRepository = mockk(relaxed = true)
        userActionService = UserActionService(userActionRepository)
    }

    @Test
    fun `addList should save all user actions and return true`() {
        val request = listOf(
            UserActionRequest(
                location = "Location1",
                browser = "Browser1",
                startTime = 123456789L,
                endTime = 123456799L,
                os = "OS1",
                additionalData = "Data1",
                action = "Action1",
                source = "Source1"
            )
        )
        val userId = 1L

        every { userActionRepository.saveAll(any<List<UserActionEntity>>()) } returns listOf()

        val result = userActionService.addList(request, userId)

        assertTrue(result)
        verify { userActionRepository.saveAll(any<List<UserActionEntity>>()) }
    }

    @Test
    fun `addList should throw exception when repository fails`() {
        val request = listOf(
            UserActionRequest(
                location = "Location1",
                browser = "Browser1",
                startTime = 123456789L,
                endTime = 123456799L,
                os = "OS1",
                additionalData = "Data1",
                action = "Action1",
                source = "Source1"
            )
        )
        val userId = 1L

        every { userActionRepository.saveAll(any<List<UserActionEntity>>()) } throws Exception("DB Error")

        assertThrows<Exception> {
            userActionService.addList(request, userId)
        }
    }

    @Test
    fun `create should save user action and return entity`() {
        val request = UserActionResult(
            location = "Location1",
            browser = "Browser1",
            startTime = TimeUtil.fromInstantMillis(123456789L),
            endTime = TimeUtil.fromInstantMillis(123456799L),
            os = "OS1",
            additionalData = "Data1",
            action = "Action1",
            source = "Source1",
            userId = 1L,
            createdBy = 1L
        )
        val savedEntity = mockk<UserActionEntity>(relaxed = true)

        every { userActionRepository.save(any<UserActionEntity>()) } returns savedEntity

        val result = userActionService.create(request)

        assertEquals(savedEntity, result)
        verify { userActionRepository.save(any<UserActionEntity>()) }
    }

    @Test
    fun `retrieve should return list of user actions`() {
        val userActions = mutableListOf<UserActionEntity>(mockk(relaxed = true))

        every { userActionRepository.findAll() } returns userActions

        val result = userActionService.retrieve()

        assertEquals(userActions, result)
        verify { userActionRepository.findAll() }
    }
}
