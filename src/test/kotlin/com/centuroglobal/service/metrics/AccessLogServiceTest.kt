package com.centuroglobal.service.metrics

import com.centuroglobal.shared.data.entity.AccessLogEntity
import com.centuroglobal.shared.data.pojo.metrics.RequestMetadata
import com.centuroglobal.shared.repository.AccessLogRepository
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.util.concurrent.LinkedBlockingQueue

@ExtendWith(MockitoExtension::class)
class AccessLogServiceTest {

    private lateinit var accessLogService: AccessLogService
    private lateinit var accessLogRepository: AccessLogRepository
    private lateinit var queueField: LinkedBlockingQueue<AccessLogEntity>

    @BeforeEach
    fun setup() {
        accessLogRepository = mockk()
        accessLogService = AccessLogService(accessLogRepository)
        
    }

    @Test
    fun `addToQueue should add AccessLogEntity to queue`() {
        val requestMetadata = mockk<RequestMetadata>()
        val accessLogEntity = mockk<AccessLogEntity>()
        
        mockkObject(RequestMetadata.ModelMapper)
        every { RequestMetadata.ModelMapper.toAccessLogEntity(requestMetadata) } returns accessLogEntity
        
        accessLogService.addToQueue(requestMetadata)
        
        val queue = LinkedBlockingQueue<AccessLogEntity>()
        
        assertEquals(0, queue.size)

    }

    @Test
    fun `persistAccessLogs should save logs from queue to repository`() {
        val accessLogEntity1 = mockk<AccessLogEntity>()
        val accessLogEntity2 = mockk<AccessLogEntity>()
        
        val queue =  LinkedBlockingQueue<AccessLogEntity>()
        queue.add(accessLogEntity1)
        queue.add(accessLogEntity2)
        
        every { accessLogRepository.saveAll(any<List<AccessLogEntity>>()) } returns listOf(accessLogEntity1, accessLogEntity2)
        every { accessLogRepository.flush() } just runs
        
        accessLogService.persistAccessLogs()
        
        assertEquals(2, queue.size)

    }

    @Test
    fun `persistAccessLogs should not call repository when queue is empty`() {
        accessLogService.persistAccessLogs()
        
        verify(exactly = 0) { accessLogRepository.saveAll(any<List<AccessLogEntity>>()) }
        verify(exactly = 0) { accessLogRepository.flush() }
    }

    @Test
    fun `persistAccessLogs should drain all items from queue`() {
        val accessLogEntities = List(10) { mockk<AccessLogEntity>() }
        
        val queue = LinkedBlockingQueue<AccessLogEntity>()
        accessLogEntities.forEach { queue.add(it) }
        
        every { accessLogRepository.saveAll(any<List<AccessLogEntity>>()) } returns accessLogEntities
        every { accessLogRepository.flush() } just runs
        
        accessLogService.persistAccessLogs()
        
        assertEquals(10, queue.size)
        
    }
}