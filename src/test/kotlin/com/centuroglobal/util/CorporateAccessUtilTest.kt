package com.centuroglobal.util

import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.CorporateRepository
import com.centuroglobal.shared.repository.CorporateUserRepository
import com.centuroglobal.shared.security.AuthenticatedUser
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.*

class CorporateAccessUtilTest {

    private lateinit var corporateUserRepository: CorporateUserRepository
    private lateinit var corporateRepository: CorporateRepository
    private lateinit var corporateAccessUtil: CorporateAccessUtil
    private lateinit var authenticatedUser: AuthenticatedUser
    private lateinit var corporateEntity: CorporateEntity
    private lateinit var corporateUserEntity: CorporateUserEntity

    @BeforeEach
    fun setup() {
        corporateUserRepository = mockk()
        corporateRepository = mockk()
        corporateAccessUtil = CorporateAccessUtil(corporateUserRepository, corporateRepository)
        authenticatedUser = mockk()
        corporateEntity = mockk()
        corporateUserEntity = mockk()
        
        mockkObject(UserAccessUtil)
    }

    @Test
    fun `getUser returns user when authenticated as admin`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUserEntity)
        
        // Act
        val result = corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        
        // Assert
        assertEquals(corporateUserEntity, result)
        verify { corporateUserRepository.findById(userId) }
    }

    @Test
    fun `getUser returns user when authenticated as super admin`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        
        every { authenticatedUser.role } returns Role.ROLE_SUPER_ADMIN.name
        every { corporateUserRepository.findById(userId) } returns Optional.of(corporateUserEntity)
        
        // Act
        val result = corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        
        // Assert
        assertEquals(corporateUserEntity, result)
        verify { corporateUserRepository.findById(userId) }
    }

    @Test
    fun `getUser throws exception when user not found for admin role`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        
        every { authenticatedUser.role } returns Role.ROLE_ADMIN.name
        every { corporateUserRepository.findById(userId) } returns Optional.empty()
        
        // Act & Assert
        val exception = assertThrows(ApplicationException::class.java) {
            corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        }
        
        verify { corporateUserRepository.findById(userId) }
    }

    @Test
    fun `getUser returns user when authenticated as partner`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        val partnerId = 2L
        
        every { authenticatedUser.role } returns Role.ROLE_PARTNER.name
        every { authenticatedUser.partnerId } returns partnerId
        every { corporateUserRepository.findByIdAndCorporatePartnerId(userId, partnerId) } returns corporateUserEntity
        
        // Act
        val result = corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        
        // Assert
        assertEquals(corporateUserEntity, result)
        verify { corporateUserRepository.findByIdAndCorporatePartnerId(userId, partnerId) }
    }

    @Test
    fun `getUser throws exception when user not found for partner role`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        val partnerId = 2L
        
        every { authenticatedUser.role } returns Role.ROLE_PARTNER.name
        every { authenticatedUser.partnerId } returns partnerId
        every { corporateUserRepository.findByIdAndCorporatePartnerId(userId, partnerId) } returns null
        
        // Act & Assert
        val exception = assertThrows(ApplicationException::class.java) {
            corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        }
        
        verify { corporateUserRepository.findByIdAndCorporatePartnerId(userId, partnerId) }
    }

    @Test
    fun `getUser returns user when user has FULL access`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        val companyId = 3L
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns companyId
        every { UserAccessUtil.getAccessesByKey(authenticatedUser, "USER") } returns mutableListOf("FULL")
        every { corporateRepository.getReferenceById(companyId) } returns corporateEntity
        every { corporateUserRepository.findByIdAndCorporate(requestUserId, corporateEntity) } returns corporateUserEntity
        
        // Act
        val result = corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        
        // Assert
        assertEquals(corporateUserEntity, result)
        verify { corporateRepository.getReferenceById(companyId) }
        verify { corporateUserRepository.findByIdAndCorporate(requestUserId, corporateEntity) }
    }

    @Test
    fun `getUser throws exception when user with FULL access not found`() {
        // Arrange
        val requestUserId = 1L
        val userId = 1L
        val companyId = 3L
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns companyId
        every { UserAccessUtil.getAccessesByKey(authenticatedUser, "USER") } returns mutableListOf("FULL")
        every { corporateRepository.getReferenceById(companyId) } returns corporateEntity
        every { corporateUserRepository.findByIdAndCorporate(requestUserId, corporateEntity) } returns null
        
        // Act & Assert
        val exception = assertThrows(ApplicationException::class.java) {
            corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        }
        
        verify { corporateRepository.getReferenceById(companyId) }
        verify { corporateUserRepository.findByIdAndCorporate(requestUserId, corporateEntity) }
    }

    @Test
    fun `getUser returns user when user has REPORTEES access and requested user is self`() {
        // Arrange
        val authenticatedUserId = 4L
        val userId = 2L
        val companyId = 3L
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns companyId
        every { authenticatedUser.userId } returns authenticatedUserId
        every { UserAccessUtil.getAccessesByKey(authenticatedUser, "USER") } returns mutableListOf("REPORTEES")
        every { corporateRepository.getReferenceById(companyId) } returns corporateEntity
        every { corporateUserRepository.findIdByManagersManagerId(authenticatedUserId) } returns mutableListOf()
        every { corporateUserRepository.findByIdAndCorporate(authenticatedUserId, corporateEntity) } returns corporateUserEntity
        
        // Act
        val result = corporateAccessUtil.getUser(authenticatedUserId, authenticatedUser, userId)
        
        // Assert
        assertEquals(corporateUserEntity, result)
        verify { corporateRepository.getReferenceById(companyId) }
        verify { corporateUserRepository.findIdByManagersManagerId(authenticatedUserId) }
        verify { corporateUserRepository.findByIdAndCorporate(authenticatedUserId, corporateEntity) }
    }

    @Test
    fun `getUser throws exception when user has REPORTEES access but requested user is not a reportee or self`() {
        // Arrange
        val requestUserId = 1L
        val userId = 2L
        val companyId = 3L
        val authenticatedUserId = 4L
        val reporterEntity = mockk<CorporateUserEntity>()
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns companyId
        every { authenticatedUser.userId } returns authenticatedUserId
        every { UserAccessUtil.getAccessesByKey(authenticatedUser, "USER") } returns mutableListOf("REPORTEES")
        every { corporateRepository.getReferenceById(companyId) } returns corporateEntity
        every { reporterEntity.id } returns 5L // Different ID than requestUserId
        every { corporateUserRepository.findIdByManagersManagerId(authenticatedUserId) } returns mutableListOf()
        
        // Act & Assert
        val exception = assertThrows(ApplicationException::class.java) {
            corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        }
        
        verify { corporateRepository.getReferenceById(companyId) }
        verify { corporateUserRepository.findIdByManagersManagerId(authenticatedUserId) }
    }

    @Test
    fun `getUser returns user when user has OWN access and requested user is self`() {
        // Arrange
        val authenticatedUserId = 4L
        val userId = 2L
        val companyId = 3L
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns companyId
        every { authenticatedUser.userId } returns authenticatedUserId
        every { UserAccessUtil.getAccessesByKey(authenticatedUser, "USER") } returns mutableListOf("OWN")
        every { corporateRepository.getReferenceById(companyId) } returns corporateEntity
        every { corporateUserRepository.findByIdAndCorporate(authenticatedUserId, corporateEntity) } returns corporateUserEntity
        
        // Act
        val result = corporateAccessUtil.getUser(authenticatedUserId, authenticatedUser, userId)
        
        // Assert
        assertEquals(corporateUserEntity, result)
        verify { corporateRepository.getReferenceById(companyId) }
        verify { corporateUserRepository.findByIdAndCorporate(authenticatedUserId, corporateEntity) }
    }

    @Test
    fun `getUser throws exception when user has invalid access`() {
        // Arrange
        val requestUserId = 1L
        val userId = 2L
        val companyId = 3L
        val authenticatedUserId = 4L
        
        every { authenticatedUser.role } returns Role.ROLE_CORPORATE.name
        every { authenticatedUser.companyId } returns companyId
        every { authenticatedUser.userId } returns authenticatedUserId
        every { UserAccessUtil.getAccessesByKey(authenticatedUser, "USER") } returns mutableListOf("INVALID_ACCESS")
        every { corporateRepository.getReferenceById(companyId) } returns corporateEntity
        
        // Act & Assert
        val exception = assertThrows(ApplicationException::class.java) {
            corporateAccessUtil.getUser(requestUserId, authenticatedUser, userId)
        }
        
        verify { corporateRepository.getReferenceById(companyId) }
    }
}