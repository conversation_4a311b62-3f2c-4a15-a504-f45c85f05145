package com.centuroglobal.util

import com.centuroglobal.shared.data.entity.CorporateEntity
import com.centuroglobal.shared.data.entity.CorporateUserEntity
import com.centuroglobal.shared.data.entity.LoginAccountEntity
import com.centuroglobal.shared.data.entity.PartnerEntity
import com.centuroglobal.shared.data.enums.AccountStatus
import com.centuroglobal.shared.data.enums.PartnerCaseType
import com.centuroglobal.shared.data.enums.Role
import com.centuroglobal.shared.exception.ApplicationException
import com.centuroglobal.shared.repository.LoginAccountRepository
import com.centuroglobal.shared.service.aws.AwsS3Service
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.data.repository.findByIdOrNull

class UserProfileUtilTest {

    private lateinit var loginAccountRepository: LoginAccountRepository
    private lateinit var awsS3Service: AwsS3Service
    private lateinit var userProfileUtil: UserProfileUtil
    private lateinit var loginAccountEntity: LoginAccountEntity
    private lateinit var corporateUserEntity: CorporateUserEntity
    private lateinit var corporateEntity: CorporateEntity
    private lateinit var partnerEntity: PartnerEntity

    @BeforeEach
    fun setup() {
        loginAccountRepository = mockk()
        awsS3Service = mockk()
        userProfileUtil = UserProfileUtil(loginAccountRepository, awsS3Service)
        loginAccountEntity = mockk(relaxed = true)
        corporateUserEntity = mockk(relaxed = true)
        corporateEntity = mockk(relaxed = true)
        partnerEntity = mockk(relaxed = true)
        
        // Common setup for LoginAccountEntity
        every { loginAccountEntity.id } returns 1L
        every { loginAccountEntity.email } returns "<EMAIL>"
        every { loginAccountEntity.firstName } returns "John"
        every { loginAccountEntity.lastName } returns "Doe"
        every { loginAccountEntity.status } returns AccountStatus.ACTIVE
        every { loginAccountEntity.role } returns Role.ROLE_CORPORATE
        every { loginAccountEntity.countryCode } returns "US"
        every { loginAccountEntity.profilePhotoUrl } returns "profile/photo/url"
        
        // Setup for CorporateUserEntity
        every { corporateUserEntity.id } returns 1L
        every { corporateUserEntity.email } returns "<EMAIL>"
        every { corporateUserEntity.firstName } returns "John"
        every { corporateUserEntity.lastName } returns "Doe"
        every { corporateUserEntity.status } returns AccountStatus.ACTIVE
        every { corporateUserEntity.role } returns Role.ROLE_CORPORATE
        every { corporateUserEntity.countryCode } returns "US"
        every { corporateUserEntity.profilePhotoUrl } returns "profile/photo/url"
        every { corporateUserEntity.corporate } returns corporateEntity
        
        // Setup for CorporateEntity
        every { corporateEntity.name } returns "Test Company"
        every { corporateEntity.partner } returns partnerEntity
        
        // Setup for PartnerEntity
        every { partnerEntity.name } returns "Test Partner"
        
        // Setup for S3 service
        every { awsS3Service.getProfilePicUrl(any()) } returns "https://s3.example.com/profile/photo/url"
    }

    @Test
    fun `retrieveProfile with userId and companyName returns UserProfile`() {
        // Arrange
        val userId = 1L
        val companyName = "Test Company"
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccountEntity
        
        // Act
        val result = userProfileUtil.retrieveProfile(userId, companyName)
        
        // Assert
        assertNotNull(result)
        assertEquals(userId, result.id)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals(Role.ROLE_CORPORATE, result.role)
        assertEquals("US", result.countryCode)
        assertEquals("https://s3.example.com/profile/photo/url", result.profilePictureFullUrl)
        assertEquals(companyName, result.companyName)
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
        verify { awsS3Service.getProfilePicUrl("profile/photo/url") }
    }

    @Test
    fun `retrieveProfile with userId and companyName throws exception when user not found`() {
        // Arrange
        val userId = 1L
        val companyName = "Test Company"
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns null
        
        // Act & Assert
        val exception = assertThrows(ApplicationException::class.java) {
            userProfileUtil.retrieveProfile(userId, companyName)
        }
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
    }

    @Test
    fun `retrieveProfile with user and companyName returns UserProfile`() {
        // Arrange
        val companyName = "Test Company"
        
        // Act
        val result = userProfileUtil.retrieveProfile(loginAccountEntity, companyName)
        
        // Assert
        assertNotNull(result)
        assertEquals(1L, result.id)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals(Role.ROLE_CORPORATE, result.role)
        assertEquals("US", result.countryCode)
        assertEquals("https://s3.example.com/profile/photo/url", result.profilePictureFullUrl)
        assertEquals(companyName, result.companyName)
        
        verify { awsS3Service.getProfilePicUrl("profile/photo/url") }
    }

    @Test
    fun `retrieveProfile with userId only returns UserProfile`() {
        // Arrange
        val userId = 1L
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccountEntity
        
        // Act
        val result = userProfileUtil.retrieveProfile(userId)
        
        // Assert
        assertNotNull(result)
        assertEquals(userId, result.id)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals(Role.ROLE_CORPORATE, result.role)
        assertEquals("US", result.countryCode)
        assertEquals("https://s3.example.com/profile/photo/url", result.profilePictureFullUrl)
        assertEquals(null, result.companyName)
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
        verify { awsS3Service.getProfilePicUrl("profile/photo/url") }
    }

    @Test
    fun `retrieveProfileWithUserType returns UserProfile with userType`() {
        // Arrange
        val userType = PartnerCaseType.CG
        
        // Act
        val result = userProfileUtil.retrieveProfileWithUserType(loginAccountEntity, userType)
        
        // Assert
        assertNotNull(result)
        assertEquals(1L, result.id)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals(Role.ROLE_CORPORATE, result.role)
        assertEquals("US", result.countryCode)
        assertEquals("https://s3.example.com/profile/photo/url", result.profilePictureFullUrl)
        assertEquals(null, result.companyName)
        assertEquals(userType, result.userType)
        
        verify { awsS3Service.getProfilePicUrl("profile/photo/url") }
    }

    @Test
    fun `retrieveCorporateProfile returns CorporateUserProfile`() {
        // Arrange
        val userId = 1L
        val companyName = "Test Company"
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns corporateUserEntity
        
        // Act
        val result = userProfileUtil.retrieveCorporateProfile(userId, companyName)
        
        // Assert
        assertNotNull(result)
        assertEquals(1L, result.id)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals(Role.ROLE_CORPORATE, result.role)
        assertEquals("US", result.countryCode)
        assertEquals("https://s3.example.com/profile/photo/url", result.profilePictureFullUrl)
        assertEquals(companyName, result.companyName)
        assertEquals("Test Partner", result.partnerName)
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
        verify { awsS3Service.getProfilePicUrl("profile/photo/url") }
    }

    @Test
    fun `retrieveCorporateProfile with null companyName uses corporate name`() {
        // Arrange
        val userId = 1L
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns corporateUserEntity
        
        // Act
        val result = userProfileUtil.retrieveCorporateProfile(userId)
        
        // Assert
        assertNotNull(result)
        assertEquals(1L, result.id)
        assertEquals("<EMAIL>", result.email)
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals(AccountStatus.ACTIVE, result.status)
        assertEquals(Role.ROLE_CORPORATE, result.role)
        assertEquals("US", result.countryCode)
        assertEquals("https://s3.example.com/profile/photo/url", result.profilePictureFullUrl)
        assertEquals("Test Company", result.companyName)
        assertEquals("Test Partner", result.partnerName)
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
        verify { awsS3Service.getProfilePicUrl("profile/photo/url") }
    }

    @Test
    fun `retrieveCorporateProfile throws exception when user not found`() {
        // Arrange
        val userId = 1L
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns null
        
        // Act & Assert
        val exception = assertThrows(Exception::class.java) {
            userProfileUtil.retrieveCorporateProfile(userId)
        }
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
    }

    @Test
    fun `retrieveCorporateProfile throws exception when user is not a corporate user`() {
        // Arrange
        val userId = 1L
        
        every { loginAccountRepository.findByIdOrNull(userId) } returns loginAccountEntity
        
        // Act & Assert
        assertThrows(ClassCastException::class.java) {
            userProfileUtil.retrieveCorporateProfile(userId)
        }
        
        verify { loginAccountRepository.findByIdOrNull(userId) }
    }
}