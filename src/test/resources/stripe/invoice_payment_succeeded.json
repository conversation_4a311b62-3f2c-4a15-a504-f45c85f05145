{"id": "in_1GzOGzEuYPEMgXLiZ40oZCpe", "object": "invoice", "account_country": "GB", "account_name": "<PERSON>", "amount_due": 500, "amount_paid": 500, "amount_remaining": 0, "application_fee_amount": null, "attempt_count": 1, "attempted": true, "auto_advance": false, "billing_reason": "subscription_create", "charge": "ch_1GzOGzEuYPEMgXLiMBF3rB0w", "collection_method": "charge_automatically", "created": **********, "currency": "gbp", "custom_fields": null, "customer": "cus_00000000000000", "customer_address": null, "customer_email": "<EMAIL>", "customer_name": null, "customer_phone": null, "customer_shipping": null, "customer_tax_exempt": "none", "customer_tax_ids": [], "default_payment_method": null, "default_source": null, "default_tax_rates": [], "description": null, "discount": null, "due_date": null, "ending_balance": 0, "footer": null, "hosted_invoice_url": "https://pay.stripe.com/invoice/acct_1GhwQeEuYPEMgXLi/invst_HYVHsKtSdQNfBO0KxJTJbQI0qst3Ixi", "invoice_pdf": "https://pay.stripe.com/invoice/acct_1GhwQeEuYPEMgXLi/invst_HYVHsKtSdQNfBO0KxJTJbQI0qst3Ixi/pdf", "lines": {"object": "list", "data": [{"id": "il_1GzOGzEuYPEMgXLiZpD0n45H", "object": "line_item", "amount": 500, "currency": "gbp", "description": "1 × Monthly Subscription (at £5.00 / month)", "discountable": true, "livemode": false, "metadata": {}, "period": {"end": **********, "start": **********}, "plan": {"id": "price_HNZOkC5OM0nt71", "object": "plan", "active": true, "aggregate_usage": null, "amount": 500, "amount_decimal": "500", "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "tiers": null, "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"id": "price_HNZOkC5OM0nt71", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "gbp", "livemode": false, "lookup_key": null, "metadata": {}, "nickname": null, "product": "prod_HNZOmy01ceq1uV", "recurring": {"aggregate_usage": null, "interval": "month", "interval_count": 1, "trial_period_days": null, "usage_type": "licensed"}, "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 500, "unit_amount_decimal": "500"}, "proration": false, "quantity": 1, "subscription": "sub_HYVHcKCkr4dSRk", "subscription_item": "si_HYVHzUh88FPQaJ", "tax_amounts": [], "tax_rates": [], "type": "subscription"}], "has_more": false, "total_count": 1, "url": "/v1/invoices/in_1GzOGzEuYPEMgXLiZ40oZCpe/lines"}, "livemode": false, "metadata": {}, "next_payment_attempt": null, "number": "21BA91C5-0002", "paid": true, "payment_intent": {"id": "pi_1GzOGzEuYPEMgXLillrp7LRl", "object": "payment_intent", "amount": 500, "amount_capturable": 0, "amount_received": 500, "application": null, "application_fee_amount": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"id": "ch_1GzOGzEuYPEMgXLiMBF3rB0w", "object": "charge", "amount": 500, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": null, "balance_transaction": "txn_1GzOH0EuYPEMgXLiOdyi3SSj", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": "Stripe", "captured": true, "created": **********, "currency": "gbp", "customer": "cus_00000000000000", "description": "Subscription creation", "destination": null, "dispute": null, "disputed": false, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": "in_1GzOGzEuYPEMgXLiZ40oZCpe", "livemode": false, "metadata": {}, "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "risk_score": 18, "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_1GzOGzEuYPEMgXLillrp7LRl", "payment_method": "pm_1Gxrl4EuYPEMgXLiLEmDdYfP", "payment_method_details": {"card": {"brand": "visa", "checks": {"address_line1_check": null, "address_postal_code_check": null, "cvc_check": null}, "country": "US", "exp_month": 7, "exp_year": 2024, "fingerprint": "gX9m4wXrBPej9wiM", "funding": "credit", "installments": null, "last4": "4242", "network": "visa", "three_d_secure": null, "wallet": null}, "type": "card"}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/acct_1GhwQeEuYPEMgXLi/ch_1GzOGzEuYPEMgXLiMBF3rB0w/rcpt_HYVHPLXnG2L0mthgcBZBkfcpRDwgmm1", "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/ch_1GzOGzEuYPEMgXLiMBF3rB0w/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": null, "statement_descriptor_suffix": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}], "has_more": false, "total_count": 1, "url": "/v1/charges?payment_intent=pi_1GzOGzEuYPEMgXLillrp7LRl"}, "client_secret": "pi_1GzOGzEuYPEMgXLillrp7LRl_secret_AOtx8Fv7zGL2brQC2s3Wjjex7", "confirmation_method": "automatic", "created": **********, "currency": "gbp", "customer": "cus_HUNfrxUUqzx14h", "description": "Subscription creation", "invoice": "in_1GzOGzEuYPEMgXLiZ40oZCpe", "last_payment_error": null, "livemode": false, "metadata": {}, "next_action": null, "on_behalf_of": null, "payment_method": "pm_1Gxrl4EuYPEMgXLiLEmDdYfP", "payment_method_options": {"card": {"installments": null, "network": null, "request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "receipt_email": null, "review": null, "setup_future_usage": null, "shipping": null, "source": null, "statement_descriptor": null, "statement_descriptor_suffix": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}, "period_end": **********, "period_start": **********, "post_payment_credit_notes_amount": 0, "pre_payment_credit_notes_amount": 0, "receipt_number": null, "starting_balance": 0, "statement_descriptor": null, "status": "paid", "status_transitions": {"finalized_at": **********, "marked_uncollectible_at": null, "paid_at": 1593442074, "voided_at": null}, "subscription": "sub_HYVHcKCkr4dSRk", "subtotal": 500, "tax": null, "tax_percent": null, "total": 500, "total_tax_amounts": [], "transfer_data": null, "webhooks_delivered_at": **********}