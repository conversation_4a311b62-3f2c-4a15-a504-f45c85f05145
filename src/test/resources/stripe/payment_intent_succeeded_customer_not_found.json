{"amount": 500, "amount_capturable": 0, "amount_received": 500, "application": null, "application_fee_amount": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"alternate_statement_descriptors": null, "amount": 500, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": null, "authorization_code": null, "balance_transaction": "txn_1GyJ8IEuYPEMgXLiEQ157UkH", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": "TOP UP", "captured": true, "created": 1593183986, "currency": "gbp", "customer": "cus_00000000000001", "description": null, "destination": null, "dispute": null, "disputed": false, "failure_code": null, "failure_message": null, "fraud_details": {"stripe_report": null, "user_report": null}, "id": "ch_1GyJ8IEuYPEMgXLi4x7hCYpN", "invoice": null, "level3": null, "livemode": false, "metadata": {}, "object": "charge", "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "risk_score": 17, "rule": null, "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_1GyJ8HEuYPEMgXLiqYT9h8vI", "payment_method": "pm_1GyJ8HEuYPEMgXLiaNnywAcW", "payment_method_details": {"ach_credit_transfer": null, "ach_debit": null, "acss_debit": null, "alipay": null, "au_becs_debit": null, "bacs_debit": null, "bancontact": null, "bitcoin": null, "card": {"brand": "visa", "checks": {"address_line1_check": null, "address_postal_code_check": null, "cvc_check": "pass"}, "country": "US", "description": null, "exp_month": 4, "exp_year": 2024, "fingerprint": "gX9m4wXrBPej9wiM", "funding": "credit", "iin": null, "installments": null, "issuer": null, "last4": "4242", "moto": null, "network": "visa", "three_d_secure": null, "wallet": null}, "card_present": null, "eps": null, "fpx": null, "giropay": null, "ideal": null, "interac_present": null, "klarna": null, "multibanco": null, "p24": null, "sepa_credit_transfer": null, "sepa_debit": null, "sofort": null, "stripe_account": null, "type": "card", "wechat": null}, "receipt_email": "<EMAIL>", "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/acct_1GhwQeEuYPEMgXLi/ch_1GyJ8IEuYPEMgXLi4x7hCYpN/rcpt_HXNuiSiJw2YzXtyiTNAS60Koo8mH3yZ", "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "url": "/v1/charges/ch_1GyJ8IEuYPEMgXLi4x7hCYpN/refunds", "request_options": null, "request_params": null}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "Top up", "statement_descriptor_suffix": null, "status": "succeeded", "transfer": null, "transfer_data": null, "transfer_group": null}], "has_more": false, "url": "/v1/charges?payment_intent=pi_1GyJ8HEuYPEMgXLiqYT9h8vI", "request_options": null, "request_params": null}, "client_secret": "pi_1GyJ8HEuYPEMgXLiqYT9h8vI_secret_LAOB2T6Ou4bdwjwr4ZWzrmAWn", "confirmation_method": "automatic", "created": **********, "currency": "gbp", "customer": "cus_00000000000001", "description": null, "id": "pi_1GyJ8HEuYPEMgXLiqYT9h8vI", "invoice": null, "last_payment_error": null, "livemode": false, "metadata": {}, "next_action": null, "object": "payment_intent", "on_behalf_of": null, "payment_method": "pm_1GyJ8HEuYPEMgXLiaNnywAcW", "payment_method_options": {"bancontact": null, "card": {"installments": null, "network": null, "request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "receipt_email": "<EMAIL>", "review": null, "setup_future_usage": "on_session", "shipping": null, "source": null, "statement_descriptor": "Top up", "statement_descriptor_suffix": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}