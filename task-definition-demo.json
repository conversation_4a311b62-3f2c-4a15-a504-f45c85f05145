{"containerDefinitions": [{"name": "cg-api-server-ecs-demo", "image": "162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-demo:latest", "cpu": 0, "portMappings": [{"name": "cg-api-server-ecs-demo-8080-tcp", "containerPort": 8080, "hostPort": 8080, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::cg-goglobal-demo/env/java.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "readonlyRootFilesystem": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/cg-api-server-ecs-demo", "awslogs-create-group": "true", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs"}}}], "family": "cg-api-server-ecs-demo", "taskRoleArn": "arn:aws:iam::162682813712:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::162682813712:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "cpu": "1224", "memory": "1500", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}