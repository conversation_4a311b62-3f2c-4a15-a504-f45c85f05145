{"containerDefinitions": [{"name": "cg-api-server-ecs-uat", "image": "162682813712.dkr.ecr.eu-west-2.amazonaws.com/cg-web-api-uat:latest", "cpu": 0, "portMappings": [{"name": "cg-api-server-ecs-uat-8080-tcp", "containerPort": 8080, "hostPort": 8080, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "environmentFiles": [{"value": "arn:aws:s3:::cg-goglobal-uat/env/java.env", "type": "s3"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/cg-api-server-ecs-uat", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs"}}, "readonlyRootFilesystem": false}], "family": "cg-api-server-ecs-uat", "taskRoleArn": "arn:aws:iam::162682813712:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::162682813712:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "cpu": "1224", "memory": "1800", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}